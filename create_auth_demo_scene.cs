// Unity Editor脚本，用于创建认证演示场景
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.SceneManagement;

public class CreateAuthDemoScene
{
    [MenuItem("DigitalHuman/Create Auth Demo Scene")]
    public static void CreateScene()
    {
        // 创建新场景
        var scene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
        
        // 创建主摄像机
        var cameraGO = new GameObject("Main Camera");
        var camera = cameraGO.AddComponent<Camera>();
        camera.backgroundColor = Color.black;
        camera.clearFlags = CameraClearFlags.SolidColor;
        cameraGO.tag = "MainCamera";
        
        // 创建UI根对象
        var uiRootGO = new GameObject("UI Root");
        var uiDocument = uiRootGO.AddComponent<UIDocument>();
        
        // 设置UI文档引用
        string uiPath = "Assets/UI/AuthDemo.uxml";
        var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uiPath);
        if (visualTreeAsset != null)
        {
            uiDocument.visualTreeAsset = visualTreeAsset;
        }
        
        // 添加认证演示管理器
        var authDemoManager = uiRootGO.AddComponent<DigitalHuman.UI.AuthDemoUIManager>();
        
        // 创建认证管理器对象
        var authManagerGO = new GameObject("Authentication Manager");
        var authManager = authManagerGO.AddComponent<DigitalHuman.Core.Authentication.AuthenticationManager>();
        
        // 保存场景
        string scenePath = "Assets/Scenes/AuthDemoScene.unity";
        EditorSceneManager.SaveScene(scene, scenePath);
        
        Debug.Log($"认证演示场景已创建: {scenePath}");
        Debug.Log("场景包含:");
        Debug.Log("- 主摄像机");
        Debug.Log("- UI根对象 (AuthDemoUIManager)");
        Debug.Log("- 认证管理器");
        Debug.Log("请在Unity中打开场景并运行测试");
    }
}