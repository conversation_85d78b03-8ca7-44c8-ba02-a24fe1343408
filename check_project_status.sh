#!/bin/bash

# 数字人管理系统 - 项目状态检查脚本
# 用于验证项目完整性和构建准备状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查项目文件结构
check_project_structure() {
    log_info "检查项目文件结构..."
    
    local required_files=(
        "Assets/Scripts/Editor/BuildScript.cs"
        "Assets/Scenes/MinimalMVPScene.unity"
        "Assets/Scripts/Core/Logging/LogManager.cs"
        "Assets/Scripts/Core/Logging/ILogManager.cs"
        "Assets/Scripts/MVP/MinimalMVPManager.cs"
        "ProjectSettings/ProjectSettings.asset"
        "Packages/manifest.json"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "找到: $file"
        else
            log_error "缺失: $file"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "项目文件结构完整"
        return 0
    else
        log_error "发现 ${#missing_files[@]} 个缺失文件"
        return 1
    fi
}

# 检查日志系统组件
check_logging_system() {
    log_info "检查日志系统组件..."
    
    local logging_files=(
        "Assets/Scripts/Core/Logging/LogManager.cs"
        "Assets/Scripts/Core/Logging/Logger.cs"
        "Assets/Scripts/Core/Logging/LogEntry.cs"
        "Assets/Scripts/Core/Logging/LogConfiguration.cs"
        "Assets/Scripts/Core/Logging/Writers/FileLogWriter.cs"
        "Assets/Scripts/Core/Logging/Writers/ConsoleLogWriter.cs"
        "Assets/Scripts/Core/Logging/Formatters/JsonLogFormatter.cs"
        "Assets/Scripts/Core/Logging/Formatters/PlainTextLogFormatter.cs"
        "Assets/Scripts/Core/Logging/Rotation/LogRotationManager.cs"
        "Assets/Scripts/Core/Logging/Performance/LogPerformanceMonitor.cs"
        "Assets/Scripts/Core/Logging/Export/LogExporter.cs"
        "Assets/Scripts/Core/Logging/Statistics/LogStatistics.cs"
    )
    
    local missing_count=0
    
    for file in "${logging_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "日志组件: $(basename "$file")"
        else
            log_error "缺失日志组件: $file"
            ((missing_count++))
        fi
    done
    
    if [ $missing_count -eq 0 ]; then
        log_success "日志系统组件完整"
        return 0
    else
        log_error "日志系统缺失 $missing_count 个组件"
        return 1
    fi
}

# 检查文档完整性
check_documentation() {
    log_info "检查文档完整性..."
    
    local doc_files=(
        "README.md"
        "docs/editor/BuildScript.md"
        "docs/logging/LogManager.md"
        "docs/logging/LogRotationManager.md"
        "docs/logging/LogPerformanceMonitor.md"
        "docs/logging/LogExporter.md"
        "docs/mvp/MinimalMVPManager.md"
    )
    
    local missing_docs=0
    
    for doc in "${doc_files[@]}"; do
        if [ -f "$doc" ]; then
            log_success "文档: $(basename "$doc")"
        else
            log_warning "缺失文档: $doc"
            ((missing_docs++))
        fi
    done
    
    if [ $missing_docs -eq 0 ]; then
        log_success "文档完整"
    else
        log_warning "缺失 $missing_docs 个文档文件"
    fi
}

# 检查构建脚本
check_build_scripts() {
    log_info "检查构建脚本..."
    
    if [ -f "build_minimal_mvp.sh" ]; then
        if [ -x "build_minimal_mvp.sh" ]; then
            log_success "构建脚本可执行: build_minimal_mvp.sh"
        else
            log_warning "构建脚本不可执行，正在修复..."
            chmod +x build_minimal_mvp.sh
            log_success "已修复构建脚本权限"
        fi
    else
        log_error "缺失构建脚本: build_minimal_mvp.sh"
        return 1
    fi
    
    if [ -f "check_project_status.sh" ]; then
        log_success "状态检查脚本: check_project_status.sh"
    fi
    
    return 0
}

# 检查Unity项目设置
check_unity_settings() {
    log_info "检查Unity项目设置..."
    
    # 检查ProjectSettings
    if [ -f "ProjectSettings/ProjectSettings.asset" ]; then
        log_success "Unity项目设置文件存在"
    else
        log_error "Unity项目设置文件缺失"
        return 1
    fi
    
    # 检查Package配置
    if [ -f "Packages/manifest.json" ]; then
        log_success "Package配置文件存在"
        
        # 检查关键包依赖
        if grep -q "com.unity.addressables" "Packages/manifest.json"; then
            log_success "Addressables包已配置"
        else
            log_warning "未找到Addressables包配置"
        fi
    else
        log_error "Package配置文件缺失"
        return 1
    fi
    
    return 0
}

# 统计项目信息
show_project_stats() {
    log_info "项目统计信息..."
    
    # 统计代码文件
    local cs_files=$(find Assets/Scripts -name "*.cs" 2>/dev/null | wc -l)
    log_info "C#脚本文件: $cs_files 个"
    
    # 统计场景文件
    local scene_files=$(find Assets/Scenes -name "*.unity" 2>/dev/null | wc -l)
    log_info "Unity场景文件: $scene_files 个"
    
    # 统计文档文件
    local doc_files=$(find docs -name "*.md" 2>/dev/null | wc -l)
    log_info "文档文件: $doc_files 个"
    
    # 项目大小
    local project_size=$(du -sh . 2>/dev/null | cut -f1)
    log_info "项目总大小: $project_size"
}

# 主函数
main() {
    echo "========================================"
    echo "  数字人管理系统 - 项目状态检查"
    echo "========================================"
    echo "检查时间: $(date)"
    echo "========================================"
    echo ""
    
    local total_checks=0
    local passed_checks=0
    
    # 执行各项检查
    if check_project_structure; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    echo ""
    
    if check_logging_system; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    echo ""
    
    check_documentation
    ((total_checks++))
    ((passed_checks++))  # 文档检查不影响构建
    
    echo ""
    
    if check_build_scripts; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    echo ""
    
    if check_unity_settings; then
        ((passed_checks++))
    fi
    ((total_checks++))
    
    echo ""
    
    show_project_stats
    
    echo ""
    echo "========================================"
    
    if [ $passed_checks -eq $total_checks ]; then
        log_success "🎉 项目状态检查通过 ($passed_checks/$total_checks)"
        echo "========================================"
        echo ""
        echo "项目已准备就绪，可以执行构建："
        echo "  ./build_minimal_mvp.sh"
        echo ""
        exit 0
    else
        log_error "❌ 项目状态检查失败 ($passed_checks/$total_checks)"
        echo "========================================"
        echo ""
        echo "请修复以上问题后重新检查"
        echo ""
        exit 1
    fi
}

# 脚本入口
main "$@"