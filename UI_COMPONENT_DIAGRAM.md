# 认证功能UI组件结构图

## 主界面组件层次结构

```
MainUIManager (主界面管理器)
├── UIDocument (UI文档)
│   └── main-root (根容器)
│       ├── top-navigation (顶部导航栏)
│       │   ├── nav-left (左侧区域)
│       │   │   ├── app-title (应用标题)
│       │   │   └── app-version (版本号)
│       │   ├── nav-center (中央区域)
│       │   │   ├── nav-home (主页按钮)
│       │   │   ├── nav-chat (对话按钮)
│       │   │   └── nav-settings (设置按钮)
│       │   └── nav-right (右侧区域)
│       │       ├── status-indicators (状态指示器组)
│       │       │   ├── network-status (网络状态)
│       │       │   │   ├── network-icon (网络图标)
│       │       │   │   └── network-text (网络文本)
│       │       │   ├── activation-status (激活状态)
│       │       │   │   ├── activation-icon (激活图标)
│       │       │   │   └── activation-text (激活文本)
│       │       │   └── auth-status (认证状态) ⭐ 新增
│       │       │       ├── auth-icon (认证图标) ⭐ 新增
│       │       │       └── auth-text (认证文本) ⭐ 新增
│       │       ├── user-info (用户信息区) ⭐ 新增
│       │       │   ├── user-display-name (用户显示名) ⭐ 新增
│       │       │   └── logout-button (登出按钮) ⭐ 新增
│       │       ├── login-button (登录按钮) ⭐ 新增
│       │       ├── quick-settings (快速设置)
│       │       └── help-button (帮助按钮)
│       ├── main-content (主内容区域)
│       │   ├── welcome-page (欢迎页面)
│       │   ├── chat-page (对话页面)
│       │   └── settings-page (设置页面)
│       ├── bottom-status (底部状态栏)
│       ├── activation-dialog (激活对话框)
│       └── login-dialog (登录对话框) ⭐ 新增
│           └── login-window (登录窗口) ⭐ 新增
│               ├── login-header (登录头部) ⭐ 新增
│               │   ├── modal-title (对话框标题) ⭐ 新增
│               │   └── login-close (关闭按钮) ⭐ 新增
│               └── login-content (登录内容) ⭐ 新增
│                   ├── login-description (登录描述) ⭐ 新增
│                   ├── login-form (登录表单) ⭐ 新增
│                   │   ├── form-field (表单字段) ⭐ 新增
│                   │   │   ├── field-label (字段标签) ⭐ 新增
│                   │   │   └── username-input (用户名输入) ⭐ 新增
│                   │   ├── form-field (表单字段) ⭐ 新增
│                   │   │   ├── field-label (字段标签) ⭐ 新增
│                   │   │   └── password-input (密码输入) ⭐ 新增
│                   │   ├── login-status (登录状态) ⭐ 新增
│                   │   │   ├── status-icon (状态图标) ⭐ 新增
│                   │   │   └── login-status-text (状态文本) ⭐ 新增
│                   │   └── login-actions (登录操作) ⭐ 新增
│                   │       ├── login-submit (登录提交) ⭐ 新增
│                   │       └── login-cancel (登录取消) ⭐ 新增
│                   └── login-help (登录帮助) ⭐ 新增
│                       └── help-text (帮助文本) ⭐ 新增
```

## 认证相关组件详细说明

### 1. 认证状态指示器 (auth-status)
```
┌─────────────────────────┐
│ ● 未登录               │  ← 红色圆点 + 状态文本
└─────────────────────────┘
```
- **位置**: 顶部导航栏右侧状态指示器组
- **状态**: 🔴未登录 / 🟢已登录
- **样式类**: `.auth-not-logged-in` / `.auth-logged-in`

### 2. 登录按钮 (login-button)
```
┌─────────┐
│  登录   │  ← 蓝色按钮
└─────────┘
```
- **位置**: 顶部导航栏右侧
- **显示条件**: 用户未登录时显示
- **样式类**: `.login-button`

### 3. 用户信息区 (user-info)
```
┌─────────────────────┐
│ 管理员  [登出]      │  ← 用户名 + 灰色登出按钮
└─────────────────────┘
```
- **位置**: 顶部导航栏右侧
- **显示条件**: 用户已登录时显示
- **包含**: 用户显示名 + 登出按钮

### 4. 登录对话框 (login-dialog)
```
┌─────────────────────────────────────────────────────────────┐
│                      用户登录                          ×   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                 请输入您的登录凭据                           │
│                                                             │
│ 用户名                                                       │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 请输入用户名                                            │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 密码                                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ••••••••••••                                            │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ℹ️ 状态消息显示区域                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                  [登录]        [取消]                       │
│                                                             │
│ ─────────────────────────────────────────────────────────── │
│                                                             │
│              测试账户: admin / admin123                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 状态流转图

### 认证状态流转
```
┌─────────────┐    点击登录按钮    ┌─────────────┐
│   未登录    │ ──────────────→   │ 显示登录框   │
│ 🔴未登录    │                   │             │
│ [登录]      │                   │             │
└─────────────┘                   └─────────────┘
       ↑                                 │
       │                                 │ 输入凭据+提交
       │                                 ↓
┌─────────────┐    登出成功       ┌─────────────┐
│   已登录    │ ←──────────────   │  认证处理   │
│ 🟢已登录    │                   │ 正在登录... │
│ 用户名[登出] │                   │             │
└─────────────┘                   └─────────────┘
                                         │
                                         │ 认证成功
                                         ↓
                                  ┌─────────────┐
                                  │  登录成功   │
                                  │ ✅登录成功   │
                                  │             │
                                  └─────────────┘
```

### UI显示状态
```
未登录状态:
┌─────────────────────────────────────────────────────────┐
│ ●在线 ●已激活 🔴未登录  [登录]  ⚙ ?                      │
└─────────────────────────────────────────────────────────┘

已登录状态:
┌─────────────────────────────────────────────────────────┐
│ ●在线 ●已激活 🟢已登录  管理员 [登出]  ⚙ ?               │
└─────────────────────────────────────────────────────────┘
```

## 事件处理流程

### 登录事件流
```
用户点击登录按钮
        ↓
ShowLoginDialog()
        ↓
显示登录对话框
        ↓
用户输入凭据
        ↓
HandleLogin()
        ↓
AuthenticationManager.LoginAsync()
        ↓
OnUserLoggedIn事件触发
        ↓
UpdateAuthenticationUI()
        ↓
UI状态更新完成
```

### 登出事件流
```
用户点击登出按钮
        ↓
HandleLogout()
        ↓
AuthenticationManager.LogoutAsync()
        ↓
OnUserLoggedOut事件触发
        ↓
UpdateAuthenticationUI()
        ↓
UI状态更新完成
```

## CSS类名映射

### 认证相关样式类
```css
/* 状态指示器 */
.auth-logged-in      /* 已登录状态图标 - 绿色 */
.auth-not-logged-in  /* 未登录状态图标 - 红色 */

/* 按钮样式 */
.login-button        /* 登录按钮 - 蓝色 */
.logout-button       /* 登出按钮 - 灰色 */

/* 用户信息 */
.user-info          /* 用户信息容器 */
.user-name          /* 用户名显示 */

/* 登录对话框 */
.login-dialog       /* 登录对话框容器 */
.login-form         /* 登录表单 */
.form-field         /* 表单字段 */
.form-input         /* 表单输入框 */
.login-status       /* 登录状态消息 */
.login-actions      /* 登录操作按钮组 */

/* 状态样式 */
.status-success     /* 成功状态 - 绿色 */
.status-error       /* 错误状态 - 红色 */
.status-processing  /* 处理中状态 - 蓝色 */

/* 工具类 */
.display-none       /* 隐藏元素 */
```

## 响应式布局适配

### 大屏幕 (≥1920px)
- 字体尺寸增大
- 按钮尺寸增大
- 间距增加

### 中等屏幕 (1200px-1920px)
- 标准尺寸
- 默认布局

### 小屏幕 (≤1200px)
- 垂直布局
- 按钮宽度调整

### 竖屏模式
- 导航栏双行布局
- 元素重新排列

---

*此组件结构图展示了认证功能在MainUIManager中的完整集成架构，所有标记为⭐的组件都是新增的认证相关功能。*