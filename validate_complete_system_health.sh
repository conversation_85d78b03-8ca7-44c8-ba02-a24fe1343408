#!/bin/bash

# 系统健康管理器完整验证脚本
# 这个脚本会执行所有相关的验证和测试

echo "========================================"
echo "    系统健康管理器完整验证"
echo "========================================"
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_result() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} $2"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 1. 文件存在性检查
echo -e "${BLUE}1. 检查文件存在性${NC}"
echo "========================"

# 核心文件
files=(
    "Assets/Scripts/Core/Performance/ISystemHealthManager.cs"
    "Assets/Scripts/Core/Performance/SystemHealthManager.cs"
    "Assets/Scripts/Core/Performance/Models/SystemHealthModels.cs"
    "Assets/Tests/Editor/SystemHealthManagerTests.cs"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        check_result 0 "文件存在: $file"
    else
        check_result 1 "文件缺失: $file"
    fi
done

echo ""

# 2. 语法检查
echo -e "${BLUE}2. 语法和结构检查${NC}"
echo "========================"

# 检查接口定义
if grep -q "interface ISystemHealthManager" "Assets/Scripts/Core/Performance/ISystemHealthManager.cs"; then
    check_result 0 "ISystemHealthManager 接口定义正确"
else
    check_result 1 "ISystemHealthManager 接口定义有问题"
fi

# 检查实现类
if grep -q "class SystemHealthManager.*ISystemHealthManager" "Assets/Scripts/Core/Performance/SystemHealthManager.cs"; then
    check_result 0 "SystemHealthManager 实现类定义正确"
else
    check_result 1 "SystemHealthManager 实现类定义有问题"
fi

# 检查测试类
if grep -q "class SystemHealthManagerTests" "Assets/Tests/Editor/SystemHealthManagerTests.cs"; then
    check_result 0 "SystemHealthManagerTests 测试类定义正确"
else
    check_result 1 "SystemHealthManagerTests 测试类定义有问题"
fi

echo ""

# 3. 接口方法检查
echo -e "${BLUE}3. 接口方法完整性检查${NC}"
echo "========================"

interface_methods=(
    "RegisterComponentMonitor"
    "UnregisterComponentMonitor"
    "PerformHealthCheck"
    "TriggerComponentRecovery"
    "GetExceptionStatistics"
    "GetComponentHealthReport"
    "ClearExceptionHistory"
    "SetupGlobalExceptionHandler"
    "RemoveGlobalExceptionHandler"
)

for method in "${interface_methods[@]}"; do
    if grep -q "$method" "Assets/Scripts/Core/Performance/ISystemHealthManager.cs"; then
        check_result 0 "接口方法存在: $method"
    else
        check_result 1 "接口方法缺失: $method"
    fi
done

echo ""

# 4. 实现方法检查
echo -e "${BLUE}4. 实现方法完整性检查${NC}"
echo "========================"

for method in "${interface_methods[@]}"; do
    if grep -q "public.*$method" "Assets/Scripts/Core/Performance/SystemHealthManager.cs"; then
        check_result 0 "实现方法存在: $method"
    else
        check_result 1 "实现方法缺失: $method"
    fi
done

echo ""

# 5. 测试方法检查
echo -e "${BLUE}5. 测试方法完整性检查${NC}"
echo "========================"

test_count=$(grep -c "\[Test\]" "Assets/Tests/Editor/SystemHealthManagerTests.cs")
unity_test_count=$(grep -c "\[UnityTest\]" "Assets/Tests/Editor/SystemHealthManagerTests.cs")
total_tests=$((test_count + unity_test_count))

if [ $total_tests -ge 20 ]; then
    check_result 0 "测试方法数量充足: $total_tests 个"
else
    check_result 1 "测试方法数量不足: $total_tests 个 (建议 ≥ 20)"
fi

# 检查关键测试方法
key_tests=(
    "Initialize_ShouldSetupHealthManager"
    "RegisterComponentMonitor_ShouldAddComponentToMonitoring"
    "PerformHealthCheck_ShouldReturnValidReport"
    "TriggerComponentRecovery_WithValidComponent_ShouldExecuteRecovery"
    "GetExceptionStatistics_ShouldReturnValidStatistics"
)

for test in "${key_tests[@]}"; do
    if grep -q "$test" "Assets/Tests/Editor/SystemHealthManagerTests.cs"; then
        check_result 0 "关键测试存在: $test"
    else
        check_result 1 "关键测试缺失: $test"
    fi
done

echo ""

# 6. 数据模型检查
echo -e "${BLUE}6. 数据模型完整性检查${NC}"
echo "========================"

models=(
    "SystemException"
    "ComponentFailureInfo"
    "RecoveryActionInfo"
    "SystemHealthReport"
    "ComponentHealthInfo"
    "ExceptionStatistics"
)

for model in "${models[@]}"; do
    if grep -q "class $model" "Assets/Scripts/Core/Performance/Models/SystemHealthModels.cs"; then
        check_result 0 "数据模型存在: $model"
    else
        check_result 1 "数据模型缺失: $model"
    fi
done

echo ""

# 7. 枚举检查
echo -e "${BLUE}7. 枚举类型完整性检查${NC}"
echo "========================"

enums=(
    "SystemHealthStatus"
    "ComponentHealthStatus"
    "RecoveryActionType"
    "ExceptionSeverity"
    "ComponentFailureSeverity"
)

for enum in "${enums[@]}"; do
    if grep -q "enum $enum" "Assets/Scripts/Core/Performance/Models/SystemHealthModels.cs"; then
        check_result 0 "枚举类型存在: $enum"
    else
        check_result 1 "枚举类型缺失: $enum"
    fi
done

echo ""

# 8. 事件系统检查
echo -e "${BLUE}8. 事件系统完整性检查${NC}"
echo "========================"

events=(
    "OnHealthStatusChanged"
    "OnExceptionCaptured"
    "OnComponentFailureDetected"
    "OnRecoveryActionExecuted"
)

for event in "${events[@]}"; do
    if grep -q "$event" "Assets/Scripts/Core/Performance/ISystemHealthManager.cs"; then
        check_result 0 "事件定义存在: $event"
    else
        check_result 1 "事件定义缺失: $event"
    fi
done

echo ""

# 9. 集成检查
echo -e "${BLUE}9. 项目集成完整性检查${NC}"
echo "========================"

# 检查 TestRunner 集成
if grep -q "RunSystemHealthManagerTests" "Assets/Tests/Editor/TestRunner.cs"; then
    check_result 0 "TestRunner 集成完成"
else
    check_result 1 "TestRunner 集成缺失"
fi

# 检查 QuickValidationTests 集成
if grep -q "ValidateSystemHealthManagerBasicFunctionality" "Assets/Tests/Editor/QuickValidationTests.cs"; then
    check_result 0 "QuickValidationTests 集成完成"
else
    check_result 1 "QuickValidationTests 集成缺失"
fi

echo ""

# 10. 验证脚本检查
echo -e "${BLUE}10. 验证脚本完整性检查${NC}"
echo "========================"

scripts=(
    "validate_system_health_manager_tests.sh"
    "generate_system_health_test_report.sh"
)

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        check_result 0 "验证脚本存在: $script"
    else
        check_result 1 "验证脚本缺失: $script"
    fi
done

echo ""

# 11. 运行子验证脚本
echo -e "${BLUE}11. 运行子验证脚本${NC}"
echo "========================"

if [ -f "validate_system_health_manager_tests.sh" ]; then
    echo "运行系统健康管理器测试验证..."
    ./validate_system_health_manager_tests.sh > /dev/null 2>&1
    check_result $? "系统健康管理器测试验证"
else
    check_result 1 "系统健康管理器测试验证脚本不存在"
fi

if [ -f "generate_system_health_test_report.sh" ]; then
    echo "生成测试覆盖率报告..."
    ./generate_system_health_test_report.sh > /dev/null 2>&1
    check_result $? "测试覆盖率报告生成"
else
    check_result 1 "测试覆盖率报告生成脚本不存在"
fi

echo ""

# 12. 代码质量检查
echo -e "${BLUE}12. 代码质量检查${NC}"
echo "========================"

# 检查中文注释
chinese_comments=$(grep -c "//.*[\u4e00-\u9fa5]" "Assets/Scripts/Core/Performance/SystemHealthManager.cs" 2>/dev/null || echo 0)
if [ $chinese_comments -gt 10 ]; then
    check_result 0 "中文注释充足: $chinese_comments 条"
else
    check_result 1 "中文注释不足: $chinese_comments 条"
fi

# 检查代码行数
impl_lines=$(wc -l < "Assets/Scripts/Core/Performance/SystemHealthManager.cs")
test_lines=$(wc -l < "Assets/Tests/Editor/SystemHealthManagerTests.cs")

if [ $impl_lines -gt 500 ]; then
    check_result 0 "实现代码行数合理: $impl_lines 行"
else
    check_result 1 "实现代码行数不足: $impl_lines 行"
fi

if [ $test_lines -gt 400 ]; then
    check_result 0 "测试代码行数充足: $test_lines 行"
else
    check_result 1 "测试代码行数不足: $test_lines 行"
fi

echo ""

# 13. 最终统计
echo -e "${BLUE}13. 验证结果统计${NC}"
echo "========================"

echo "总检查项数: $TOTAL_CHECKS"
echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"

success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
echo "成功率: $success_rate%"

echo ""

# 14. 最终评估
echo -e "${BLUE}14. 最终评估${NC}"
echo "========================"

if [ $success_rate -ge 95 ]; then
    echo -e "${GREEN}✅ 优秀${NC} - 系统健康管理器实现质量优秀"
    final_result=0
elif [ $success_rate -ge 85 ]; then
    echo -e "${YELLOW}⚠️  良好${NC} - 系统健康管理器实现质量良好，有少量改进空间"
    final_result=0
elif [ $success_rate -ge 70 ]; then
    echo -e "${YELLOW}⚠️  一般${NC} - 系统健康管理器实现基本完成，需要改进"
    final_result=1
else
    echo -e "${RED}❌ 不足${NC} - 系统健康管理器实现存在重大问题，需要重新检查"
    final_result=1
fi

echo ""

# 15. 改进建议
if [ $FAILED_CHECKS -gt 0 ]; then
    echo -e "${BLUE}15. 改进建议${NC}"
    echo "========================"
    echo "1. 检查并修复失败的验证项"
    echo "2. 确保所有必需的文件都存在"
    echo "3. 验证代码语法和结构正确性"
    echo "4. 补充缺失的测试用例"
    echo "5. 完善代码注释和文档"
    echo ""
fi

# 16. 生成详细报告
echo -e "${BLUE}16. 生成详细报告${NC}"
echo "========================"

if [ -f "SYSTEM_HEALTH_MANAGER_TESTING_COMPLETION_REPORT.md" ]; then
    echo -e "${GREEN}✓${NC} 详细完成报告已存在: SYSTEM_HEALTH_MANAGER_TESTING_COMPLETION_REPORT.md"
else
    echo -e "${YELLOW}⚠️${NC} 详细完成报告不存在，建议生成"
fi

echo ""
echo "========================================"
echo "    验证完成"
echo "========================================"
echo "结束时间: $(date)"
echo ""

if [ $final_result -eq 0 ]; then
    echo -e "${GREEN}🎉 系统健康管理器验证通过！${NC}"
else
    echo -e "${RED}❌ 系统健康管理器验证失败，请检查上述问题。${NC}"
fi

exit $final_result