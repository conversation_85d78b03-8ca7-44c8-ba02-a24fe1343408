#!/bin/bash

echo "=== 数字人管理系统增强版集成验证 ==="
echo "验证时间: $(date)"
echo ""

# 检查核心脚本文件
echo "🔍 检查核心脚本文件..."
core_files=(
    "Assets/Scripts/MVP/MinimalMVPManager.cs:基础MVP管理器"
    "Assets/Scripts/MVP/EnhancedMVPManager.cs:增强版MVP管理器"
    "Assets/Scripts/Core/Authentication/AuthenticationManager.cs:认证管理器"
    "Assets/Scripts/Core/Authentication/IAuthenticationManager.cs:认证接口"
    "Assets/Scripts/Core/DataSync/DataSyncManager.cs:数据同步管理器"
    "Assets/Scripts/Core/DataSync/IDataSyncManager.cs:数据同步接口"
    "Assets/Scripts/Core/Logging/LogManager.cs:日志管理器"
    "Assets/Scripts/UI/SimpleStatusUI.cs:状态UI"
    "Assets/Scripts/Editor/BuildScript.cs:构建脚本"
)

missing_files=0
for item in "${core_files[@]}"; do
    file=$(echo $item | cut -d':' -f1)
    name=$(echo $item | cut -d':' -f2)
    if [ -f "$file" ]; then
        echo "  ✅ $name"
    else
        echo "  ❌ $name (文件: $file)"
        missing_files=$((missing_files + 1))
    fi
done

echo ""
echo "核心文件检查完成: $missing_files 个文件缺失"

# 检查测试文件
echo ""
echo "🧪 检查测试文件..."
test_files=(
    "Assets/Tests/Editor/CoreTests.asmdef:测试程序集定义"
    "Assets/Tests/Editor/MinimalMVPManagerTests.cs:MVP管理器测试"
    "Assets/Tests/Editor/AuthenticationManagerTests.cs:认证管理器测试"
    "Assets/Tests/Editor/TestRunner.cs:测试运行器"
)

missing_tests=0
for item in "${test_files[@]}"; do
    file=$(echo $item | cut -d':' -f1)
    name=$(echo $item | cut -d':' -f2)
    if [ -f "$file" ]; then
        echo "  ✅ $name"
    else
        echo "  ❌ $name (文件: $file)"
        missing_tests=$((missing_tests + 1))
    fi
done

echo ""
echo "测试文件检查完成: $missing_tests 个文件缺失"

# 统计代码行数
echo ""
echo "📊 代码统计..."
total_cs_files=$(find Assets/Scripts -name "*.cs" | wc -l)
total_test_files=$(find Assets/Tests -name "*.cs" | wc -l)
total_lines=$(find Assets/Scripts Assets/Tests -name "*.cs" -exec wc -l {} + | tail -1 | awk '{print $1}')

echo "  C# 脚本文件: $total_cs_files 个"
echo "  测试文件: $total_test_files 个"
echo "  总代码行数: $total_lines 行"

# 检查功能模块完整性
echo ""
echo "🔧 检查功能模块完整性..."

# 认证系统
if [ -f "Assets/Scripts/Core/Authentication/AuthenticationManager.cs" ] && 
   [ -f "Assets/Scripts/Core/Authentication/IAuthenticationManager.cs" ]; then
    echo "  ✅ 认证系统模块完整"
else
    echo "  ❌ 认证系统模块不完整"
fi

# 数据同步系统
if [ -f "Assets/Scripts/Core/DataSync/DataSyncManager.cs" ] && 
   [ -f "Assets/Scripts/Core/DataSync/IDataSyncManager.cs" ]; then
    echo "  ✅ 数据同步系统模块完整"
else
    echo "  ❌ 数据同步系统模块不完整"
fi

# 日志系统
if [ -f "Assets/Scripts/Core/Logging/LogManager.cs" ] && 
   [ -f "Assets/Scripts/Core/Logging/ILogManager.cs" ]; then
    echo "  ✅ 日志系统模块完整"
else
    echo "  ❌ 日志系统模块不完整"
fi

# MVP系统
if [ -f "Assets/Scripts/MVP/MinimalMVPManager.cs" ] && 
   [ -f "Assets/Scripts/MVP/EnhancedMVPManager.cs" ]; then
    echo "  ✅ MVP系统模块完整"
else
    echo "  ❌ MVP系统模块不完整"
fi

# 检查构建能力
echo ""
echo "🏗️ 检查构建能力..."

# 检查Unity路径
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
if [ -f "$UNITY_PATH" ]; then
    echo "  ✅ Unity编辑器已安装"
    echo "  Unity版本: $($UNITY_PATH -version 2>/dev/null | head -1 || echo '无法获取版本')"
else
    echo "  ❌ Unity编辑器未找到"
fi

# 检查场景文件
if [ -f "Assets/Scenes/MinimalMVPScene.unity" ]; then
    echo "  ✅ 基础MVP场景存在"
else
    echo "  ⚠️ 基础MVP场景缺失"
fi

# 检查项目设置
if [ -f "ProjectSettings/ProjectSettings.asset" ]; then
    echo "  ✅ 项目设置文件存在"
else
    echo "  ❌ 项目设置文件缺失"
fi

# 检查包管理
if [ -f "Packages/manifest.json" ]; then
    echo "  ✅ 包管理文件存在"
else
    echo "  ❌ 包管理文件缺失"
fi

# 生成集成状态报告
echo ""
echo "📋 生成集成状态报告..."

total_modules=4
complete_modules=0

if [ -f "Assets/Scripts/Core/Authentication/AuthenticationManager.cs" ]; then
    complete_modules=$((complete_modules + 1))
fi

if [ -f "Assets/Scripts/Core/DataSync/DataSyncManager.cs" ]; then
    complete_modules=$((complete_modules + 1))
fi

if [ -f "Assets/Scripts/Core/Logging/LogManager.cs" ]; then
    complete_modules=$((complete_modules + 1))
fi

if [ -f "Assets/Scripts/MVP/EnhancedMVPManager.cs" ]; then
    complete_modules=$((complete_modules + 1))
fi

completion_rate=$((complete_modules * 100 / total_modules))

echo ""
echo "=== 集成状态总结 ==="
echo "模块完成度: $complete_modules/$total_modules ($completion_rate%)"
echo "核心文件缺失: $missing_files 个"
echo "测试文件缺失: $missing_tests 个"
echo "代码文件总数: $total_cs_files 个"
echo "测试覆盖率: $(echo "scale=1; $total_test_files * 100 / $total_cs_files" | bc)%"

if [ $completion_rate -ge 80 ]; then
    echo "✅ 系统集成状态良好，可以进行构建测试"
elif [ $completion_rate -ge 60 ]; then
    echo "⚠️ 系统集成基本完成，建议完善缺失模块"
else
    echo "❌ 系统集成不完整，需要补充核心模块"
fi

echo ""
echo "=== 下一步建议 ==="
if [ $missing_files -eq 0 ] && [ $missing_tests -le 2 ]; then
    echo "1. 🚀 运行构建测试: Build/Build Enhanced MVP"
    echo "2. 🧪 运行单元测试: DigitalHuman/Tests/Run All Core Tests"
    echo "3. 🎯 创建增强版场景: Build/Create Enhanced Scene"
elif [ $missing_files -le 2 ]; then
    echo "1. 📝 补充缺失的测试文件"
    echo "2. 🔧 完善功能模块集成"
    echo "3. 🧪 运行基础测试验证"
else
    echo "1. 📁 检查并恢复缺失的核心文件"
    echo "2. 🔄 重新运行集成脚本"
    echo "3. 📋 查看详细错误日志"
fi

echo ""
echo "=== 验证完成 ==="
echo "验证时间: $(date)"
