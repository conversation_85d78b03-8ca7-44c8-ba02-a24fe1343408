#!/bin/bash

echo "=== 构建主界面应用程序 ==="

# 设置Unity路径
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"

# 设置项目路径
PROJECT_PATH="$(pwd)"

# 设置构建输出路径
BUILD_PATH="$PROJECT_PATH/Builds/MainUI/DigitalAvatarMainUI.app"

# 创建构建目录
mkdir -p "$(dirname "$BUILD_PATH")"

echo "1. 准备构建环境..."
echo "✅ Unity路径: $UNITY_PATH"
echo "✅ 项目路径: $PROJECT_PATH"
echo "✅ 构建路径: $BUILD_PATH"

echo ""
echo "2. 开始构建主界面应用程序..."

# 创建临时构建脚本
cat > /tmp/BuildMainUI.cs << 'EOF'
using UnityEngine;
using UnityEditor;
using System.IO;

public class BuildMainUI
{
    [MenuItem("Build/Build Main UI App")]
    public static void BuildMainUIApp()
    {
        Debug.Log("[BuildMainUI] 开始构建主界面应用程序");
        
        // 设置构建场景
        string[] scenes = {
            "Assets/Scenes/MainUIScene.unity"
        };
        
        // 设置构建选项
        BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();
        buildPlayerOptions.scenes = scenes;
        buildPlayerOptions.locationPathName = "Builds/MainUI/DigitalAvatarMainUI.app";
        buildPlayerOptions.target = BuildTarget.StandaloneOSX;
        buildPlayerOptions.options = BuildOptions.None;
        
        Debug.Log("[BuildMainUI] 构建场景: " + string.Join(", ", scenes));
        Debug.Log("[BuildMainUI] 构建目标: " + buildPlayerOptions.locationPathName);
        
        // 执行构建
        var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
        
        if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
        {
            Debug.Log("[BuildMainUI] 构建成功！");
            Debug.Log("[BuildMainUI] 构建大小: " + report.summary.totalSize + " bytes");
        }
        else
        {
            Debug.LogError("[BuildMainUI] 构建失败: " + report.summary.result);
        }
    }
}
EOF

# 将构建脚本复制到项目中
cp /tmp/BuildMainUI.cs Assets/Scripts/Editor/BuildMainUI.cs

echo "✅ 构建脚本已创建"

# 执行Unity构建
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "$PROJECT_PATH" \
    -executeMethod BuildMainUI.BuildMainUIApp \
    -logFile build_mainui_app.log

BUILD_RESULT=$?

echo ""
echo "3. 检查构建结果..."

if [ $BUILD_RESULT -eq 0 ] && [ -d "$BUILD_PATH" ]; then
    echo "✅ 构建成功！"
    echo "📱 应用程序位置: $BUILD_PATH"
    
    # 显示应用程序大小
    APP_SIZE=$(du -sh "$BUILD_PATH" | cut -f1)
    echo "📊 应用程序大小: $APP_SIZE"
    
    echo ""
    echo "🚀 启动应用程序测试..."
    open "$BUILD_PATH"
    
else
    echo "❌ 构建失败"
    echo "📋 查看构建日志: cat build_mainui_app.log"
    
    # 显示最近的错误信息
    if [ -f build_mainui_app.log ]; then
        echo ""
        echo "最近的错误信息:"
        tail -20 build_mainui_app.log | grep -E "(error|Error|ERROR|failed|Failed|FAILED)"
    fi
fi

# 清理临时文件
rm -f /tmp/BuildMainUI.cs
rm -f Assets/Scripts/Editor/BuildMainUI.cs

echo ""
echo "=== 主界面应用程序构建完成 ==="

exit $BUILD_RESULT
