{"name": "DigitalHuman.Tests.Editor", "rootNamespace": "DigitalHuman.Tests.Editor", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "DigitalHuman.Core", "DigitalHuman.MVP", "DigitalHuman.UI"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}