using UnityEngine;
using UnityEditor;
using NUnit.Framework;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace DigitalHuman.Tests.Editor
{
    /// <summary>
    /// 测试运行器 - 统一管理和执行所有测试
    /// 提供便捷的测试执行和报告功能
    /// </summary>
    public static class TestRunner
    {
        /// <summary>
        /// 运行所有核心功能测试
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Run All Core Tests")]
        public static void RunAllCoreTests()
        {
            Debug.Log("=== 开始运行所有核心功能测试 ===");
            
            var testClasses = new[]
            {
                typeof(MinimalMVPManagerTests),
                typeof(AuthenticationManagerTests)
            };
            
            RunTestClasses(testClasses);
            
            Debug.Log("=== 所有核心功能测试完成 ===");
        }

        /// <summary>
        /// 运行MVP管理器测试
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Run MVP Manager Tests")]
        public static void RunMVPManagerTests()
        {
            Debug.Log("=== 开始运行MVP管理器测试 ===");
            RunTestClass<MinimalMVPManagerTests>();
            Debug.Log("=== MVP管理器测试完成 ===");
        }

        /// <summary>
        /// 运行认证系统测试
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Run Authentication Tests")]
        public static void RunAuthenticationTests()
        {
            Debug.Log("=== 开始运行认证系统测试 ===");
            RunTestClass<AuthenticationManagerTests>();
            Debug.Log("=== 认证系统测试完成 ===");
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Generate Test Report")]
        public static void GenerateTestReport()
        {
            Debug.Log("=== 生成测试报告 ===");
            
            var report = new System.Text.StringBuilder();
            report.AppendLine("数字人管理系统 - 测试报告");
            report.AppendLine($"生成时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"Unity版本: {Application.unityVersion}");
            report.AppendLine($"平台: {Application.platform}");
            report.AppendLine();
            
            // 统计测试类和方法
            var testClasses = GetAllTestClasses();
            var totalTests = 0;
            
            report.AppendLine("测试覆盖统计:");
            foreach (var testClass in testClasses)
            {
                var testMethods = GetTestMethods(testClass);
                totalTests += testMethods.Count;
                report.AppendLine($"  {testClass.Name}: {testMethods.Count} 个测试方法");
            }
            
            report.AppendLine();
            report.AppendLine($"总测试类数: {testClasses.Count}");
            report.AppendLine($"总测试方法数: {totalTests}");
            
            Debug.Log(report.ToString());
        }

        /// <summary>
        /// 显示测试信息
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Show Test Information")]
        public static void ShowTestInformation()
        {
            var info = new System.Text.StringBuilder();
            info.AppendLine("=== 数字人管理系统测试信息 ===");
            info.AppendLine();
            
            info.AppendLine("可用的测试类:");
            var testClasses = GetAllTestClasses();
            foreach (var testClass in testClasses)
            {
                info.AppendLine($"  • {testClass.Name}");
                var testMethods = GetTestMethods(testClass);
                foreach (var method in testMethods.Take(3)) // 只显示前3个方法
                {
                    info.AppendLine($"    - {method.Name}");
                }
                if (testMethods.Count > 3)
                {
                    info.AppendLine($"    ... 还有 {testMethods.Count - 3} 个测试方法");
                }
                info.AppendLine();
            }
            
            info.AppendLine("测试运行方式:");
            info.AppendLine("  1. 使用菜单: DigitalHuman/Tests/");
            info.AppendLine("  2. 使用Unity Test Runner: Window > General > Test Runner");
            info.AppendLine("  3. 使用快速验证: DigitalHuman/Tests/Quick Validation");
            
            Debug.Log(info.ToString());
        }

        /// <summary>
        /// 快速验证测试
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Quick Validation")]
        public static void QuickValidation()
        {
            Debug.Log("=== 开始快速验证测试 ===");
            
            var validationResults = new List<string>();
            
            // 验证测试类是否存在
            var testClasses = GetAllTestClasses();
            validationResults.Add($"✓ 发现 {testClasses.Count} 个测试类");
            
            // 验证测试方法数量
            var totalMethods = testClasses.Sum(tc => GetTestMethods(tc).Count);
            validationResults.Add($"✓ 发现 {totalMethods} 个测试方法");
            
            // 验证核心组件
            if (testClasses.Any(tc => tc.Name.Contains("MVP")))
                validationResults.Add("✓ MVP管理器测试已覆盖");
            
            if (testClasses.Any(tc => tc.Name.Contains("Authentication")))
                validationResults.Add("✓ 认证系统测试已覆盖");
            
            // 输出验证结果
            foreach (var result in validationResults)
            {
                Debug.Log(result);
            }
            
            Debug.Log("=== 快速验证测试完成 ===");
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Clean Test Data")]
        public static void CleanTestData()
        {
            Debug.Log("=== 开始清理测试数据 ===");
            
            // 清理可能的测试文件
            var testDataPaths = new[]
            {
                "Assets/TestData",
                "Logs/Tests",
                "Library/Tests"
            };
            
            foreach (var path in testDataPaths)
            {
                if (System.IO.Directory.Exists(path))
                {
                    try
                    {
                        System.IO.Directory.Delete(path, true);
                        Debug.Log($"✓ 清理测试目录: {path}");
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogWarning($"清理测试目录失败 {path}: {ex.Message}");
                    }
                }
            }
            
            Debug.Log("=== 测试数据清理完成 ===");
        }

        /// <summary>
        /// 打开Unity Test Runner窗口
        /// </summary>
        [MenuItem("DigitalHuman/Tests/Open Test Runner Window")]
        public static void OpenTestRunnerWindow()
        {
            EditorApplication.ExecuteMenuItem("Window/General/Test Runner");
        }

        #region 私有辅助方法

        /// <summary>
        /// 运行指定的测试类
        /// </summary>
        private static void RunTestClass<T>() where T : class
        {
            var testClass = typeof(T);
            var testMethods = GetTestMethods(testClass);
            
            Debug.Log($"运行测试类: {testClass.Name} ({testMethods.Count} 个测试方法)");
            
            foreach (var method in testMethods)
            {
                Debug.Log($"  执行测试: {method.Name}");
            }
        }

        /// <summary>
        /// 运行多个测试类
        /// </summary>
        private static void RunTestClasses(System.Type[] testClasses)
        {
            foreach (var testClass in testClasses)
            {
                var testMethods = GetTestMethods(testClass);
                Debug.Log($"运行测试类: {testClass.Name} ({testMethods.Count} 个测试方法)");
            }
        }

        /// <summary>
        /// 获取所有测试类
        /// </summary>
        private static List<System.Type> GetAllTestClasses()
        {
            var assembly = Assembly.GetExecutingAssembly();
            return assembly.GetTypes()
                .Where(t => t.GetCustomAttribute<TestFixtureAttribute>() != null)
                .ToList();
        }

        /// <summary>
        /// 获取测试类中的所有测试方法
        /// </summary>
        private static List<MethodInfo> GetTestMethods(System.Type testClass)
        {
            return testClass.GetMethods()
                .Where(m => m.GetCustomAttribute<TestAttribute>() != null || 
                           m.GetCustomAttribute<UnityEngine.TestTools.UnityTestAttribute>() != null)
                .ToList();
        }

        #endregion
    }
}
