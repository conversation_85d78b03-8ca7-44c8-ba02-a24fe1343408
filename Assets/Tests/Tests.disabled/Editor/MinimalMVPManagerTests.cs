using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using DigitalHuman.MVP;
using System.Collections;

namespace DigitalHuman.Tests.Editor
{
    /// <summary>
    /// MinimalMVPManager 单元测试
    /// 验证MVP管理器的核心功能和生命周期管理
    /// </summary>
    [TestFixture]
    public class MinimalMVPManagerTests
    {
        private GameObject testObject;
        private MinimalMVPManager mvpManager;

        /// <summary>
        /// 测试初始化设置
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            // 创建测试对象
            testObject = new GameObject("TestMVPManager");
            mvpManager = testObject.AddComponent<MinimalMVPManager>();
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            if (testObject != null)
            {
                Object.DestroyImmediate(testObject);
            }
        }

        /// <summary>
        /// 测试MVP管理器基本初始化
        /// </summary>
        [Test]
        public void TestMVPManagerInitialization()
        {
            // 验证默认配置
            Assert.IsTrue(mvpManager.EnableDebugMode, "调试模式应该默认启用");
            Assert.AreEqual("1.0.0-MVP", mvpManager.AppVersion, "默认版本号应该正确");
            Assert.AreEqual(60, mvpManager.TargetFrameRate, "默认帧率应该是60");
            Assert.IsTrue(mvpManager.EnableVSync, "垂直同步应该默认启用");
        }

        /// <summary>
        /// 测试系统状态获取
        /// </summary>
        [Test]
        public void TestGetSystemStatus()
        {
            // 获取系统状态
            string status = mvpManager.GetSystemStatus();
            
            // 验证状态信息包含必要内容
            Assert.IsNotNull(status, "系统状态不应该为空");
            Assert.IsTrue(status.Contains("系统状态"), "状态应该包含系统状态信息");
            Assert.IsTrue(status.Contains("版本"), "状态应该包含版本信息");
            Assert.IsTrue(status.Contains("运行时间"), "状态应该包含运行时间信息");
        }

        /// <summary>
        /// 测试配置参数设置
        /// </summary>
        [Test]
        public void TestConfigurationSettings()
        {
            // 修改配置
            mvpManager.EnableDebugMode = false;
            mvpManager.AppVersion = "2.0.0-Test";
            mvpManager.TargetFrameRate = 30;
            mvpManager.EnableVSync = false;

            // 验证配置已更新
            Assert.IsFalse(mvpManager.EnableDebugMode, "调试模式应该被禁用");
            Assert.AreEqual("2.0.0-Test", mvpManager.AppVersion, "版本号应该已更新");
            Assert.AreEqual(30, mvpManager.TargetFrameRate, "帧率应该已更新");
            Assert.IsFalse(mvpManager.EnableVSync, "垂直同步应该被禁用");
        }

        /// <summary>
        /// 测试重启系统功能
        /// </summary>
        [Test]
        public void TestRestartMVPSystem()
        {
            // 执行重启
            mvpManager.RestartMVPSystem();
            
            // 验证系统状态
            string status = mvpManager.GetSystemStatus();
            Assert.IsNotNull(status, "重启后系统状态应该可用");
            
            // 验证日志输出（通过LogAssert检查）
            LogAssert.Expect(LogType.Log, "[MinimalMVP] 重启MVP系统...");
        }

        /// <summary>
        /// 测试单例行为
        /// </summary>
        [Test]
        public void TestSingletonBehavior()
        {
            // 创建第二个MVP管理器
            var secondObject = new GameObject("SecondMVPManager");
            var secondManager = secondObject.AddComponent<MinimalMVPManager>();

            // 验证重复实例被销毁的警告
            LogAssert.Expect(LogType.Warning, "[MinimalMVP] 检测到多个MinimalMVPManager实例，销毁重复实例");

            // 清理
            Object.DestroyImmediate(secondObject);
        }

        /// <summary>
        /// 测试应用程序生命周期事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestApplicationLifecycleEvents()
        {
            // 等待一帧确保初始化完成
            yield return null;

            // 模拟应用程序暂停
            mvpManager.SendMessage("OnApplicationPause", true);
            LogAssert.Expect(LogType.Log, "[MinimalMVP] 应用程序暂停状态: True");

            // 模拟应用程序获得焦点
            mvpManager.SendMessage("OnApplicationFocus", true);
            LogAssert.Expect(LogType.Log, "[MinimalMVP] 应用程序焦点状态: True");
        }

        /// <summary>
        /// 测试日志系统集成
        /// </summary>
        [Test]
        public void TestLoggingSystemIntegration()
        {
            // 验证日志系统初始化
            LogAssert.Expect(LogType.Log, "[MinimalMVP] 日志系统初始化成功");
            
            // 手动触发日志初始化
            mvpManager.SendMessage("Start");
        }

        /// <summary>
        /// 测试欢迎信息显示
        /// </summary>
        [Test]
        public void TestWelcomeMessage()
        {
            // 启用调试模式
            mvpManager.EnableDebugMode = true;
            
            // 触发欢迎信息显示
            mvpManager.SendMessage("Start");
            
            // 验证欢迎信息日志
            LogAssert.Expect(LogType.Log, "    数字人管理系统 - MVP版本");
        }

        /// <summary>
        /// 测试错误处理
        /// </summary>
        [Test]
        public void TestErrorHandling()
        {
            // 这个测试验证系统在异常情况下的稳定性
            // 由于MVP管理器设计得很稳定，我们主要验证它不会抛出异常
            
            Assert.DoesNotThrow(() => {
                mvpManager.GetSystemStatus();
                mvpManager.RestartMVPSystem();
            }, "MVP管理器的基本操作不应该抛出异常");
        }

        /// <summary>
        /// 测试性能配置应用
        /// </summary>
        [Test]
        public void TestPerformanceConfiguration()
        {
            // 设置性能参数
            mvpManager.TargetFrameRate = 120;
            mvpManager.EnableVSync = false;
            
            // 触发初始化以应用设置
            mvpManager.SendMessage("Awake");
            
            // 验证Unity设置已应用
            Assert.AreEqual(120, Application.targetFrameRate, "目标帧率应该已应用");
            Assert.AreEqual(0, QualitySettings.vSyncCount, "垂直同步应该已禁用");
        }
    }
}
