using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using DigitalHuman.Core.Authentication;
using System.Threading.Tasks;
using System.Collections;

namespace DigitalHuman.Tests.Editor
{
    /// <summary>
    /// 认证管理器单元测试
    /// 验证用户认证系统的核心功能
    /// </summary>
    [TestFixture]
    public class AuthenticationManagerTests
    {
        private AuthenticationManager authManager;
        private GameObject testObject;

        /// <summary>
        /// 测试初始化设置
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            // 创建测试对象和认证管理器
            testObject = new GameObject("TestAuthManager");
            authManager = testObject.AddComponent<AuthenticationManager>();
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            if (testObject != null)
            {
                Object.DestroyImmediate(testObject);
            }
        }

        /// <summary>
        /// 测试认证管理器初始化
        /// </summary>
        [Test]
        public void TestAuthenticationManagerInitialization()
        {
            // 验证初始状态
            Assert.IsFalse(authManager.IsLoggedIn, "初始状态应该未登录");
            Assert.IsNull(authManager.CurrentUser, "初始状态当前用户应该为空");
        }

        /// <summary>
        /// 测试成功登录
        /// </summary>
        [UnityTest]
        public IEnumerator TestSuccessfulLogin()
        {
            // 准备测试数据
            string username = "testuser";
            string password = "testpass";

            // 执行登录
            var loginTask = authManager.LoginAsync(username, password);

            // 等待异步操作完成
            while (!loginTask.IsCompleted)
            {
                yield return null;
            }

            var result = loginTask.Result;

            // 验证登录结果
            Assert.IsTrue(result.IsSuccess, "登录应该成功");
            Assert.IsNotNull(result.UserInfo, "用户信息不应该为空");
            Assert.AreEqual(username, result.UserInfo.Username, "用户名应该匹配");
            Assert.IsTrue(authManager.IsLoggedIn, "登录状态应该为已登录");
            Assert.IsNotNull(authManager.CurrentUser, "当前用户不应该为空");
        }

        /// <summary>
        /// 测试登录失败
        /// </summary>
        [UnityTest]
        public IEnumerator TestFailedLogin()
        {
            // 准备无效测试数据
            string username = "";
            string password = "";

            // 执行登录
            var loginTask = authManager.LoginAsync(username, password);

            // 等待异步操作完成
            while (!loginTask.IsCompleted)
            {
                yield return null;
            }

            var result = loginTask.Result;

            // 验证登录失败
            Assert.IsFalse(result.IsSuccess, "空用户名密码登录应该失败");
            Assert.IsNull(result.UserInfo, "失败时用户信息应该为空");
            Assert.IsFalse(authManager.IsLoggedIn, "登录状态应该为未登录");
        }

        /// <summary>
        /// 测试登出功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestLogout()
        {
            // 先登录
            var loginTask = authManager.LoginAsync("testuser", "testpass");
            while (!loginTask.IsCompleted) yield return null;

            // 验证已登录
            Assert.IsTrue(authManager.IsLoggedIn, "应该已登录");

            // 执行登出
            var logoutTask = authManager.LogoutAsync();
            while (!logoutTask.IsCompleted) yield return null;

            var result = logoutTask.Result;

            // 验证登出结果
            Assert.IsTrue(result, "登出应该成功");
            Assert.IsFalse(authManager.IsLoggedIn, "登出后状态应该为未登录");
            Assert.IsNull(authManager.CurrentUser, "登出后当前用户应该为空");
        }

        /// <summary>
        /// 测试会话验证
        /// </summary>
        [UnityTest]
        public IEnumerator TestValidateSession()
        {
            // 先登录
            var loginTask = authManager.LoginAsync("testuser", "testpass");
            while (!loginTask.IsCompleted) yield return null;

            // 验证会话
            var validateTask = authManager.ValidateSessionAsync();
            while (!validateTask.IsCompleted) yield return null;

            var result = validateTask.Result;

            // 验证会话验证结果
            Assert.IsTrue(result, "有效会话验证应该成功");
        }

        /// <summary>
        /// 测试令牌刷新
        /// </summary>
        [UnityTest]
        public IEnumerator TestRefreshToken()
        {
            // 先登录
            var loginTask = authManager.LoginAsync("testuser", "testpass");
            while (!loginTask.IsCompleted) yield return null;

            // 刷新令牌
            var refreshTask = authManager.RefreshTokenAsync();
            while (!refreshTask.IsCompleted) yield return null;

            var result = refreshTask.Result;

            // 验证令牌刷新结果
            Assert.IsTrue(result, "令牌刷新应该成功");
        }

        /// <summary>
        /// 测试认证事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestAuthenticationEvents()
        {
            bool loginEventFired = false;
            bool logoutEventFired = false;
            
            // 订阅事件
            authManager.OnUserLoggedIn += (userInfo) => {
                loginEventFired = true;
                Assert.IsNotNull(userInfo, "登录事件的用户信息不应该为空");
            };
            
            authManager.OnUserLoggedOut += () => {
                logoutEventFired = true;
            };
            
            // 执行登录
            var loginTask = authManager.LoginAsync("testuser", "testpass");
            while (!loginTask.IsCompleted) yield return null;
            
            // 等待事件触发
            yield return new WaitForSeconds(0.1f);
            
            // 验证登录事件
            Assert.IsTrue(loginEventFired, "登录事件应该被触发");
            
            // 执行登出
            var logoutTask = authManager.LogoutAsync();
            while (!logoutTask.IsCompleted) yield return null;
            
            // 等待事件触发
            yield return new WaitForSeconds(0.1f);
            
            // 验证登出事件
            Assert.IsTrue(logoutEventFired, "登出事件应该被触发");
        }

        /// <summary>
        /// 测试用户信息模型
        /// </summary>
        [Test]
        public void TestUserInfoModel()
        {
            // 创建用户信息
            var userInfo = new UserInfo
            {
                UserId = "123",
                Username = "testuser",
                Email = "<EMAIL>",
                IsActive = true
            };

            // 验证用户信息
            Assert.AreEqual("123", userInfo.UserId, "用户ID应该匹配");
            Assert.AreEqual("testuser", userInfo.Username, "用户名应该匹配");
            Assert.AreEqual("testuser", userInfo.DisplayName, "显示名称应该匹配");
            Assert.AreEqual("<EMAIL>", userInfo.Email, "邮箱应该匹配");
            Assert.IsTrue(userInfo.IsActive, "用户应该是活跃状态");
        }

        /// <summary>
        /// 测试认证结果模型
        /// </summary>
        [Test]
        public void TestAuthenticationResultModel()
        {
            // 创建成功的认证结果
            var successResult = AuthenticationResult.Success(new UserInfo
            {
                UserId = "123",
                Username = "testuser"
            }, "test-token");

            Assert.IsTrue(successResult.IsSuccess, "成功结果应该标记为成功");
            Assert.IsNotNull(successResult.UserInfo, "成功结果应该包含用户信息");
            Assert.AreEqual("test-token", successResult.Token, "令牌应该匹配");

            // 创建失败的认证结果
            var failureResult = AuthenticationResult.Failure("登录失败");

            Assert.IsFalse(failureResult.IsSuccess, "失败结果应该标记为失败");
            Assert.IsNull(failureResult.UserInfo, "失败结果不应该包含用户信息");
            Assert.AreEqual("登录失败", failureResult.ErrorMessage, "错误消息应该匹配");
        }
    }
}
