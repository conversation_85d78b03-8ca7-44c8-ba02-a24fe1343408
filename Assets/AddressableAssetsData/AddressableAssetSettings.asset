%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: {fileID: 11400000, guid: 71c1913a6c0e4c2428b4879aba2c420e, type: 2}
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 0
  m_BundleLocalCatalog: 0
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_ShaderBundleNaming: 0
  m_ShaderBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 
  m_RemoteCatalogLoadPath:
    m_Id: 
  m_ContentStateBuildPath: 
  m_overridePlayerVersion: 
  m_GroupAssets:
  - {fileID: 11400000, guid: 71c1913a6c0e4c2428b4879aba2c420e, type: 2}
  m_BuildSettings:
  - {fileID: 11400000, guid: e5d17a21594f53c4e9c54183bcb2154b, type: 2}
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: d6d1de2c6b5b4c74f8b5c8e5c5c5c5c5
      m_ProfileName: Default
      m_Values:
      - m_Id: Local.BuildPath
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: Local.LoadPath
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: Remote.BuildPath
        m_Value: ServerData/[BuildTarget]
      - m_Id: Remote.LoadPath
        m_Value: http://localhost/[BuildTarget]
    m_ProfileEntryNames:
    - m_Id: Local.BuildPath
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: Local.LoadPath
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: Remote.BuildPath
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: Remote.LoadPath
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 5f5a4b42bb5044a2b746d4e42c654a6e, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 3
  m_DataBuilders:
  - {fileID: 11400000, guid: 3f5b5f07c9e77c54f8f404e17d233349, type: 2}
  - {fileID: 11400000, guid: e5d17a21594f53c4e9c54183bcb2154b, type: 2}
  - {fileID: 11400000, guid: a6a8dcc7356b8c54c9b2c8e5c5c5c5c5, type: 2}
  - {fileID: 11400000, guid: 5b2b5b5b5b5b5b5b5b5b5b5b5b5b5b5b, type: 2}
  m_ActiveProfileId: d6d1de2c6b5b4c74f8b5c8e5c5c5c5c5
  m_HostingServicesManager:
    m_HostingServiceInfos: []
    m_Settings: {fileID: 11400000}
    m_NextInstanceId: 0
    m_RegisteredServiceTypeRefs: []