/* 设置界面样式 */

.settings-scroll-view {
    flex-grow: 1;
}

.settings-group {
    margin-bottom: 20px;
    background-color: rgb(64, 64, 64);
    border-radius: 6px;
    padding: 15px;
}

.group-title {
    font-size: 14px;
    font-weight: bold;
    color: rgb(220, 220, 220);
    margin-bottom: 10px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(128, 128, 128);
    padding-bottom: 5px;
}

.group-content {
    margin-top: 10px;
}

.labeled-field {
    margin-bottom: 15px;
    flex-direction: column;
}

.field-label {
    font-size: 12px;
    color: rgb(200, 200, 200);
    margin-bottom: 5px;
}

.field-error {
    border-color: rgb(255, 100, 100);
    border-width: 2px;
}

.tab-button {
    width: 180px;
    height: 40px;
    margin-bottom: 5px;
    margin-left: 10px;
    margin-right: 10px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    -unity-text-align: middle-left;
    padding-left: 15px;
    border-radius: 4px;
}

.tab-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.tab-button.active {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
}

.button-group {
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 20px;
}

.button-group Button {
    margin-left: 10px;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 8px;
    padding-bottom: 8px;
}

.preview-button {
    background-color: rgb(0, 150, 0);
    color: rgb(255, 255, 255);
}

.test-button {
    background-color: rgb(255, 165, 0);
    color: rgb(255, 255, 255);
}

.reset-button {
    background-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
}

.export-button {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
}

.import-button {
    background-color: rgb(128, 0, 128);
    color: rgb(255, 255, 255);
}

.add-button {
    background-color: rgb(0, 150, 0);
    color: rgb(255, 255, 255);
}

.delete-button {
    background-color: rgb(220, 20, 60);
    color: rgb(255, 255, 255);
}

.config-list {
    min-height: 200px;
    background-color: rgb(48, 48, 48);
    border-width: 1px;
    border-color: rgb(128, 128, 128);
    border-radius: 4px;
    margin-bottom: 10px;
}

.config-item {
    height: 40px;
    flex-direction: row;
    align-items: center;
    padding-left: 10px;
    padding-right: 10px;
    border-bottom-width: 1px;
    border-bottom-color: rgba(128, 128, 128, 0.3);
}

.config-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.config-item.active-config {
    background-color: rgba(0, 120, 215, 0.3);
}

.config-name {
    flex-grow: 1;
    color: rgb(255, 255, 255);
    font-weight: bold;
}

.config-provider {
    color: rgb(180, 180, 180);
    margin-right: 10px;
}

.config-status {
    color: rgb(0, 200, 0);
    font-size: 10px;
}

.config-editor {
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(128, 128, 128);
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.separator {
    height: 1px;
    background-color: rgb(128, 128, 128);
    margin-top: 10px;
    margin-bottom: 10px;
}

/* 输入控件样式 */
TextField {
    background-color: rgb(48, 48, 48);
    border-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
}

TextField:focus {
    border-color: rgb(0, 120, 215);
}

DropdownField {
    background-color: rgb(48, 48, 48);
    border-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
}

Toggle {
    color: rgb(255, 255, 255);
}

Slider {
    color: rgb(255, 255, 255);
}

SliderInt {
    color: rgb(255, 255, 255);
}

Button {
    background-color: rgb(64, 64, 64);
    border-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
}

Button:hover {
    background-color: rgb(80, 80, 80);
}

Button:active {
    background-color: rgb(96, 96, 96);
}