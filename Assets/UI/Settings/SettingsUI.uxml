<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/Settings/SettingsUI.uss?fileID=7433441132597879392&amp;guid=PLACEHOLDER&amp;type=3#SettingsUI" />
    <ui:VisualElement name="settings-panel" style="display: none; position: absolute; left: 0; top: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.8);">
        <ui:VisualElement name="settings-window" style="position: absolute; left: 10%; top: 10%; right: 10%; bottom: 10%; background-color: rgb(56, 56, 56); border-left-color: rgb(128, 128, 128); border-right-color: rgb(128, 128, 128); border-top-color: rgb(128, 128, 128); border-bottom-color: rgb(128, 128, 128); border-left-width: 2px; border-right-width: 2px; border-top-width: 2px; border-bottom-width: 2px; border-top-left-radius: 8px; border-bottom-left-radius: 8px; border-top-right-radius: 8px; border-bottom-right-radius: 8px;">
            <ui:VisualElement name="header" style="height: 60px; background-color: rgb(45, 45, 45); border-bottom-width: 1px; border-bottom-color: rgb(128, 128, 128); flex-direction: row; align-items: center; justify-content: space-between; padding-left: 20px; padding-right: 20px; border-top-left-radius: 6px; border-top-right-radius: 6px;">
                <ui:Label text="设置" display-tooltip-when-elided="true" name="title" style="font-size: 18px; color: rgb(255, 255, 255); -unity-font-style: bold;" />
                <ui:Button text="×" display-tooltip-when-elided="true" name="close-button" style="width: 30px; height: 30px; font-size: 16px; background-color: rgba(255, 255, 255, 0); border-left-color: rgba(255, 255, 255, 0); border-right-color: rgba(255, 255, 255, 0); border-top-color: rgba(255, 255, 255, 0); border-bottom-color: rgba(255, 255, 255, 0); color: rgb(255, 255, 255);" />
            </ui:VisualElement>
            <ui:VisualElement name="content" style="flex-grow: 1; flex-direction: row;">
                <ui:VisualElement name="tab-container" style="width: 200px; background-color: rgb(48, 48, 48); border-right-width: 1px; border-right-color: rgb(128, 128, 128); padding-top: 10px;" />
                <ui:VisualElement name="content-container" style="flex-grow: 1; padding-left: 20px; padding-right: 20px; padding-top: 20px; padding-bottom: 20px;" />
            </ui:VisualElement>
            <ui:VisualElement name="footer" style="height: 60px; background-color: rgb(45, 45, 45); border-top-width: 1px; border-top-color: rgb(128, 128, 128); flex-direction: row; align-items: center; justify-content: flex-end; padding-left: 20px; padding-right: 20px; border-bottom-left-radius: 6px; border-bottom-right-radius: 6px;">
                <ui:Button text="重置" display-tooltip-when-elided="true" name="reset-button" style="margin-right: 10px; padding-left: 15px; padding-right: 15px; padding-top: 8px; padding-bottom: 8px;" />
                <ui:Button text="保存" display-tooltip-when-elided="true" name="save-button" style="background-color: rgb(0, 120, 215); color: rgb(255, 255, 255); padding-left: 15px; padding-right: 15px; padding-top: 8px; padding-bottom: 8px;" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>