/* 对话界面样式 */

/* 根容器 */
.chat-root {
    flex-grow: 1;
    flex-direction: row;
    background-color: rgb(32, 32, 32);
    color: rgb(255, 255, 255);
}

/* 左侧推荐问题面板 */
.recommended-panel {
    width: 280px;
    background-color: rgb(28, 28, 28);
    border-right-width: 1px;
    border-right-color: rgb(64, 64, 64);
    flex-direction: column;
}

.panel-header {
    height: 50px;
    background-color: rgb(24, 24, 24);
    border-bottom-width: 1px;
    border-bottom-color: rgb(64, 64, 64);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    padding-right: 15px;
}

.panel-title {
    font-size: 14px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.questions-scroll {
    flex-grow: 1;
}

.questions-container {
    padding: 10px;
}

.question-item {
    background-color: rgb(40, 40, 40);
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 12px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-color: rgb(64, 64, 64);
    border-right-color: rgb(64, 64, 64);
    border-top-color: rgb(64, 64, 64);
    border-bottom-color: rgb(64, 64, 64);
    transition-duration: 0.2s;
}

.question-item:hover {
    background-color: rgb(48, 48, 48);
    border-left-color: rgb(0, 120, 215);
    cursor: pointer;
}

.question-item.high-priority {
    border-left-width: 3px;
    border-left-color: rgb(255, 165, 0);
}

.question-text {
    font-size: 13px;
    color: rgb(255, 255, 255);
    margin-bottom: 5px;
    white-space: normal;
}

.question-meta {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.question-priority {
    font-size: 11px;
    color: rgb(255, 165, 0);
}

.question-usage {
    font-size: 11px;
    color: rgb(160, 160, 160);
}

.panel-footer {
    height: 50px;
    background-color: rgb(24, 24, 24);
    border-top-width: 1px;
    border-top-color: rgb(64, 64, 64);
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.add-question-button {
    height: 32px;
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-radius: 4px;
    font-size: 12px;
    width: 100%;
}

.add-question-button:hover {
    background-color: rgb(0, 100, 180);
}

/* 主对话区域 */
.chat-main {
    flex-grow: 1;
    flex-direction: column;
    min-width: 400px;
}

/* 对话历史区域 */
.chat-history {
    flex-grow: 1;
    flex-direction: column;
}

.history-header {
    height: 50px;
    background-color: rgb(28, 28, 28);
    border-bottom-width: 1px;
    border-bottom-color: rgb(64, 64, 64);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;
}

.history-title {
    font-size: 14px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.history-controls {
    flex-direction: row;
    align-items: center;
}

.search-input {
    width: 200px;
    height: 32px;
    background-color: rgb(40, 40, 40);
    border-left-color: rgb(64, 64, 64);
    border-right-color: rgb(64, 64, 64);
    border-top-color: rgb(64, 64, 64);
    border-bottom-color: rgb(64, 64, 64);
    color: rgb(255, 255, 255);
    border-radius: 4px;
    padding-left: 10px;
    padding-right: 10px;
    margin-right: 5px;
}

.search-input:focus {
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.messages-scroll {
    flex-grow: 1;
    background-color: rgb(36, 36, 36);
}

.messages-container {
    padding: 20px;
}

/* 消息样式 */
.message-item {
    flex-direction: row;
    margin-bottom: 20px;
    align-items: flex-start;
}

.message-item.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    margin-left: 12px;
}

.user-message .message-avatar {
    margin-right: 0;
    margin-left: 12px;
}

.system-avatar {
    background-color: rgb(0, 120, 215);
}

.user-avatar {
    background-color: rgb(76, 175, 80);
}

.avatar-icon {
    font-size: 20px;
    color: rgb(255, 255, 255);
}

.message-content {
    flex-grow: 1;
    max-width: 70%;
}

.user-message .message-content {
    align-items: flex-end;
}

.message-header {
    flex-direction: row;
    align-items: center;
    margin-bottom: 5px;
}

.user-message .message-header {
    flex-direction: row-reverse;
}

.sender-name {
    font-size: 12px;
    font-weight: bold;
    color: rgb(200, 200, 200);
    margin-right: 10px;
}

.user-message .sender-name {
    margin-right: 0;
    margin-left: 10px;
}

.message-time {
    font-size: 11px;
    color: rgb(160, 160, 160);
}

.message-body {
    background-color: rgb(48, 48, 48);
    border-radius: 12px;
    padding: 12px 16px;
    position: relative;
}

.user-message .message-body {
    background-color: rgb(0, 120, 215);
}

.message-text {
    font-size: 14px;
    color: rgb(255, 255, 255);
    white-space: normal;
    line-height: 1.4;
}

.message-image {
    max-width: 300px;
    max-height: 200px;
    border-radius: 8px;
    margin-top: 8px;
}

.message-actions {
    flex-direction: row;
    margin-top: 8px;
}

.message-action {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    margin-right: 5px;
    font-size: 11px;
    color: rgb(200, 200, 200);
}

.message-action:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 输入区域 */
.input-area {
    background-color: rgb(28, 28, 28);
    border-top-width: 1px;
    border-top-color: rgb(64, 64, 64);
    padding: 15px 20px;
}

.input-container {
    flex-direction: row;
    align-items: flex-end;
}

.message-input {
    flex-grow: 1;
    min-height: 40px;
    max-height: 120px;
    background-color: rgb(40, 40, 40);
    border-left-color: rgb(64, 64, 64);
    border-right-color: rgb(64, 64, 64);
    border-top-color: rgb(64, 64, 64);
    border-bottom-color: rgb(64, 64, 64);
    color: rgb(255, 255, 255);
    border-radius: 8px;
    padding: 10px 12px;
    margin-right: 10px;
    font-size: 14px;
}

.message-input:focus {
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.input-controls {
    flex-direction: row;
    align-items: center;
}

.control-button {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    margin-left: 5px;
    font-size: 16px;
    transition-duration: 0.2s;
}

.voice-button {
    background-color: rgb(76, 175, 80);
    color: rgb(255, 255, 255);
    border-left-color: rgb(76, 175, 80);
    border-right-color: rgb(76, 175, 80);
    border-top-color: rgb(76, 175, 80);
    border-bottom-color: rgb(76, 175, 80);
}

.voice-button:hover {
    background-color: rgb(56, 155, 60);
}

.voice-button.recording {
    background-color: rgb(244, 67, 54);
    animation-name: pulse;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

.attach-button {
    background-color: rgb(96, 125, 139);
    color: rgb(255, 255, 255);
    border-left-color: rgb(96, 125, 139);
    border-right-color: rgb(96, 125, 139);
    border-top-color: rgb(96, 125, 139);
    border-bottom-color: rgb(96, 125, 139);
}

.attach-button:hover {
    background-color: rgb(76, 105, 119);
}

.send-button {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    width: 60px;
    font-size: 14px;
}

.send-button:hover {
    background-color: rgb(0, 100, 180);
}

/* 语音输入状态 */
.voice-status {
    margin-top: 10px;
    background-color: rgb(40, 40, 40);
    border-radius: 8px;
    padding: 15px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.voice-indicator {
    flex-direction: row;
    align-items: center;
}

.voice-wave {
    width: 20px;
    height: 20px;
    background-color: rgb(244, 67, 54);
    border-radius: 10px;
    margin-right: 10px;
    animation-name: pulse;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

.voice-text {
    font-size: 14px;
    color: rgb(255, 255, 255);
}

.stop-voice-button {
    background-color: rgb(244, 67, 54);
    color: rgb(255, 255, 255);
    border-left-color: rgb(244, 67, 54);
    border-right-color: rgb(244, 67, 54);
    border-top-color: rgb(244, 67, 54);
    border-bottom-color: rgb(244, 67, 54);
    border-radius: 4px;
    padding: 8px 16px;
}

.stop-voice-button:hover {
    background-color: rgb(224, 47, 34);
}

/* 语音转文字显示 */
.speech-to-text {
    margin-top: 10px;
    background-color: rgb(48, 48, 48);
    border-radius: 8px;
    padding: 12px;
    flex-direction: row;
    align-items: center;
}

.stt-label {
    font-size: 12px;
    color: rgb(160, 160, 160);
    margin-right: 8px;
}

.stt-result {
    font-size: 14px;
    color: rgb(255, 255, 255);
    flex-grow: 1;
}

/* 右侧信息面板 */
.info-panel {
    width: 300px;
    background-color: rgb(28, 28, 28);
    border-left-width: 1px;
    border-left-color: rgb(64, 64, 64);
    flex-direction: column;
}

.info-section {
    border-bottom-width: 1px;
    border-bottom-color: rgb(64, 64, 64);
    padding: 15px;
}

.section-title {
    font-size: 13px;
    font-weight: bold;
    color: rgb(200, 200, 200);
    margin-bottom: 10px;
}

.info-content {
    flex-direction: column;
}

/* 日期时间信息 */
.datetime-content {
    align-items: center;
}

.date-text {
    font-size: 16px;
    font-weight: bold;
    color: rgb(255, 255, 255);
    margin-bottom: 5px;
}

.weekday-text {
    font-size: 14px;
    color: rgb(180, 180, 180);
    margin-bottom: 5px;
}

.time-text {
    font-size: 20px;
    font-weight: bold;
    color: rgb(0, 120, 215);
    font-family: 'Courier New', monospace;
}

/* 天气信息 */
.weather-main {
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
}

.weather-icon {
    font-size: 32px;
    margin-right: 15px;
}

.weather-details {
    flex-grow: 1;
}

.weather-desc {
    font-size: 16px;
    color: rgb(255, 255, 255);
    margin-bottom: 5px;
}

.weather-temp {
    font-size: 24px;
    font-weight: bold;
    color: rgb(255, 165, 0);
}

.weather-extra {
    flex-direction: column;
}

.weather-detail {
    font-size: 12px;
    color: rgb(180, 180, 180);
    margin-bottom: 3px;
}

/* TTS播放状态 */
.tts-progress {
    margin-bottom: 10px;
}

.progress-bar {
    height: 4px;
    background-color: rgb(64, 64, 64);
    border-radius: 2px;
    margin-bottom: 5px;
    position: relative;
}

.progress-fill {
    height: 100%;
    background-color: rgb(0, 120, 215);
    border-radius: 2px;
    width: 0%;
    transition-duration: 0.3s;
}

.tts-time {
    font-size: 11px;
    color: rgb(160, 160, 160);
    text-align: center;
    font-family: 'Courier New', monospace;
}

.tts-text-display {
    background-color: rgb(40, 40, 40);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
    min-height: 60px;
}

.tts-current-text {
    font-size: 13px;
    color: rgb(255, 255, 255);
    line-height: 1.4;
    white-space: normal;
}

.tts-current-text.highlighted {
    background-color: rgb(255, 255, 0);
    color: rgb(0, 0, 0);
}

.tts-controls {
    flex-direction: row;
    justify-content: center;
}

.tts-control-button {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    margin: 0 3px;
    background-color: rgb(64, 64, 64);
    color: rgb(255, 255, 255);
    border-left-color: rgb(64, 64, 64);
    border-right-color: rgb(64, 64, 64);
    border-top-color: rgb(64, 64, 64);
    border-bottom-color: rgb(64, 64, 64);
    font-size: 14px;
}

.tts-control-button:hover {
    background-color: rgb(80, 80, 80);
}

/* 系统状态 */
.status-item {
    flex-direction: row;
    align-items: center;
    margin-bottom: 8px;
}

.status-label {
    font-size: 12px;
    color: rgb(180, 180, 180);
    width: 80px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: rgb(76, 175, 80);
}

.status-indicator.offline {
    background-color: rgb(244, 67, 54);
}

.status-indicator.warning {
    background-color: rgb(255, 193, 7);
}

.status-text {
    font-size: 12px;
    color: rgb(255, 255, 255);
    flex-grow: 1;
}

/* 通用样式 */
.icon-button {
    width: 32px;
    height: 32px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    border-radius: 4px;
    font-size: 14px;
}

.icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgb(255, 255, 255);
}

.info-text {
    font-size: 14px;
    color: rgb(255, 255, 255);
}

/* 模态对话框 */
.modal-overlay {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    align-items: center;
    justify-content: center;
}

.modal-window {
    width: 500px;
    background-color: rgb(48, 48, 48);
    border-radius: 8px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
}

.modal-header {
    height: 50px;
    background-color: rgb(40, 40, 40);
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(128, 128, 128);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;
}

.modal-title {
    font-size: 16px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.close-button {
    width: 24px;
    height: 24px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    font-size: 14px;
}

.close-button:hover {
    background-color: rgb(220, 20, 60);
    color: rgb(255, 255, 255);
}

.modal-content {
    padding: 25px;
}

/* 问题表单 */
.question-form {
    margin-bottom: 25px;
}

.form-label {
    font-size: 14px;
    color: rgb(200, 200, 200);
    margin-bottom: 8px;
}

.form-input {
    height: 40px;
    background-color: rgb(32, 32, 32);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
    padding-left: 12px;
    padding-right: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.form-input:focus {
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.form-input[multiline="true"] {
    height: 80px;
}

.question-options {
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 15px;
}

.option-toggle {
    color: rgb(255, 255, 255);
}

.dialog-buttons {
    flex-direction: row;
    justify-content: flex-end;
}

.dialog-button {
    height: 36px;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 4px;
    margin-left: 10px;
    font-weight: bold;
}

.dialog-button.secondary {
    background-color: rgb(96, 96, 96);
    color: rgb(255, 255, 255);
    border-left-color: rgb(96, 96, 96);
    border-right-color: rgb(96, 96, 96);
    border-top-color: rgb(96, 96, 96);
    border-bottom-color: rgb(96, 96, 96);
}

.dialog-button.secondary:hover {
    background-color: rgb(116, 116, 116);
}

.dialog-button.primary {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.dialog-button.primary:hover {
    background-color: rgb(0, 100, 180);
}

/* 动画 */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .recommended-panel {
        width: 250px;
    }
    
    .info-panel {
        width: 280px;
    }
}

@media (max-width: 900px) {
    .recommended-panel {
        display: none;
    }
    
    .info-panel {
        width: 250px;
    }
}

@media (max-width: 600px) {
    .chat-root {
        flex-direction: column;
    }
    
    .info-panel {
        width: 100%;
        height: 200px;
        flex-direction: row;
        overflow-x: auto;
    }
    
    .info-section {
        min-width: 200px;
        border-right-width: 1px;
        border-right-color: rgb(64, 64, 64);
        border-bottom-width: 0;
    }
}