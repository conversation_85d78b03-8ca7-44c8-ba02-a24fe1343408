<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/Chat/ChatUI.uss?fileID=7433441132597879392&amp;guid=PLACEHOLDER&amp;type=3#ChatUI" />
    
    <!-- 对话界面根容器 -->
    <ui:VisualElement name="chat-root" class="chat-root">
        
        <!-- 左侧推荐问题面板 -->
        <ui:VisualElement name="recommended-panel" class="recommended-panel">
            <ui:VisualElement name="panel-header" class="panel-header">
                <ui:Label text="推荐问题" class="panel-title" />
                <ui:Button text="⚙" name="manage-questions" class="icon-button" />
            </ui:VisualElement>
            
            <ui:ScrollView name="questions-scroll" class="questions-scroll">
                <ui:VisualElement name="questions-container" class="questions-container">
                    <!-- 推荐问题将动态添加到这里 -->
                </ui:VisualElement>
            </ui:ScrollView>
            
            <ui:VisualElement name="panel-footer" class="panel-footer">
                <ui:Button text="+ 添加问题" name="add-question" class="add-question-button" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 主对话区域 -->
        <ui:VisualElement name="chat-main" class="chat-main">
            
            <!-- 对话历史区域 -->
            <ui:VisualElement name="chat-history" class="chat-history">
                <ui:VisualElement name="history-header" class="history-header">
                    <ui:Label text="对话记录" class="history-title" />
                    <ui:VisualElement name="history-controls" class="history-controls">
                        <ui:TextField placeholder-text="搜索对话..." name="search-input" class="search-input" />
                        <ui:Button text="🔍" name="search-button" class="icon-button" />
                        <ui:Button text="🗑" name="clear-history" class="icon-button" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:ScrollView name="messages-scroll" class="messages-scroll">
                    <ui:VisualElement name="messages-container" class="messages-container">
                        <!-- 对话消息将动态添加到这里 -->
                        <ui:VisualElement name="welcome-message" class="message-item system-message">
                            <ui:VisualElement name="message-avatar" class="message-avatar system-avatar">
                                <ui:Label text="🤖" class="avatar-icon" />
                            </ui:VisualElement>
                            <ui:VisualElement name="message-content" class="message-content">
                                <ui:VisualElement name="message-header" class="message-header">
                                    <ui:Label text="数字人助手" class="sender-name" />
                                    <ui:Label text="刚刚" class="message-time" />
                                </ui:VisualElement>
                                <ui:VisualElement name="message-body" class="message-body">
                                    <ui:Label text="您好！我是您的数字人助手，很高兴为您服务。您可以通过语音或文字与我对话，也可以点击左侧的推荐问题快速开始。" class="message-text" />
                                </ui:VisualElement>
                            </ui:VisualElement>
                        </ui:VisualElement>
                    </ui:VisualElement>
                </ui:ScrollView>
            </ui:VisualElement>
            
            <!-- 输入区域 -->
            <ui:VisualElement name="input-area" class="input-area">
                <ui:VisualElement name="input-container" class="input-container">
                    <ui:TextField placeholder-text="输入您的问题..." name="message-input" class="message-input" multiline="true" />
                    <ui:VisualElement name="input-controls" class="input-controls">
                        <ui:Button text="🎤" name="voice-input" class="control-button voice-button" />
                        <ui:Button text="📎" name="attach-file" class="control-button attach-button" />
                        <ui:Button text="发送" name="send-message" class="control-button send-button" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <!-- 语音输入状态 -->
                <ui:VisualElement name="voice-status" class="voice-status" style="display: none;">
                    <ui:VisualElement name="voice-indicator" class="voice-indicator">
                        <ui:VisualElement name="voice-wave" class="voice-wave" />
                        <ui:Label text="正在听取您的语音..." name="voice-text" class="voice-text" />
                    </ui:VisualElement>
                    <ui:Button text="停止" name="stop-voice" class="stop-voice-button" />
                </ui:VisualElement>
                
                <!-- 实时语音转文字显示 -->
                <ui:VisualElement name="speech-to-text" class="speech-to-text" style="display: none;">
                    <ui:Label text="识别中: " class="stt-label" />
                    <ui:Label text="" name="stt-result" class="stt-result" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 右侧信息面板 -->
        <ui:VisualElement name="info-panel" class="info-panel">
            <ui:VisualElement name="panel-header" class="panel-header">
                <ui:Label text="信息显示" class="panel-title" />
                <ui:Button text="📌" name="pin-info" class="icon-button" />
            </ui:VisualElement>
            
            <!-- 日期时间信息 -->
            <ui:VisualElement name="datetime-info" class="info-section">
                <ui:Label text="日期时间" class="section-title" />
                <ui:VisualElement name="datetime-content" class="info-content">
                    <ui:Label text="2024年1月1日" name="current-date" class="info-text date-text" />
                    <ui:Label text="星期一" name="current-weekday" class="info-text weekday-text" />
                    <ui:Label text="14:30:25" name="current-time" class="info-text time-text" />
                </ui:VisualElement>
            </ui:VisualElement>
            
            <!-- 天气信息 -->
            <ui:VisualElement name="weather-info" class="info-section">
                <ui:Label text="天气信息" class="section-title" />
                <ui:VisualElement name="weather-content" class="info-content">
                    <ui:VisualElement name="weather-main" class="weather-main">
                        <ui:Label text="☀️" name="weather-icon" class="weather-icon" />
                        <ui:VisualElement name="weather-details" class="weather-details">
                            <ui:Label text="晴天" name="weather-desc" class="weather-desc" />
                            <ui:Label text="25°C" name="weather-temp" class="weather-temp" />
                        </ui:VisualElement>
                    </ui:VisualElement>
                    <ui:VisualElement name="weather-extra" class="weather-extra">
                        <ui:Label text="湿度: 60%" name="weather-humidity" class="weather-detail" />
                        <ui:Label text="风速: 3m/s" name="weather-wind" class="weather-detail" />
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
            
            <!-- TTS播放状态 -->
            <ui:VisualElement name="tts-status" class="info-section">
                <ui:Label text="语音播放" class="section-title" />
                <ui:VisualElement name="tts-content" class="info-content">
                    <ui:VisualElement name="tts-progress" class="tts-progress">
                        <ui:VisualElement name="progress-bar" class="progress-bar">
                            <ui:VisualElement name="progress-fill" class="progress-fill" />
                        </ui:VisualElement>
                        <ui:Label text="00:00 / 00:00" name="tts-time" class="tts-time" />
                    </ui:VisualElement>
                    <ui:VisualElement name="tts-text-display" class="tts-text-display">
                        <ui:Label text="当前播放内容将在这里显示..." name="tts-current-text" class="tts-current-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="tts-controls" class="tts-controls">
                        <ui:Button text="⏸" name="tts-pause" class="tts-control-button" />
                        <ui:Button text="⏹" name="tts-stop" class="tts-control-button" />
                        <ui:Button text="🔊" name="tts-volume" class="tts-control-button" />
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
            
            <!-- 系统状态 -->
            <ui:VisualElement name="system-status" class="info-section">
                <ui:Label text="系统状态" class="section-title" />
                <ui:VisualElement name="status-content" class="info-content">
                    <ui:VisualElement name="status-item" class="status-item">
                        <ui:Label text="AI服务" class="status-label" />
                        <ui:VisualElement name="status-indicator online" class="status-indicator" />
                        <ui:Label text="在线" name="ai-status-text" class="status-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="status-item" class="status-item">
                        <ui:Label text="语音引擎" class="status-label" />
                        <ui:VisualElement name="status-indicator online" class="status-indicator" />
                        <ui:Label text="就绪" name="voice-status-text" class="status-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="status-item" class="status-item">
                        <ui:Label text="网络连接" class="status-label" />
                        <ui:VisualElement name="status-indicator online" class="status-indicator" />
                        <ui:Label text="正常" name="network-status-text" class="status-text" />
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        
    </ui:VisualElement>
    
    <!-- 添加问题对话框 -->
    <ui:VisualElement name="add-question-dialog" class="modal-overlay" style="display: none;">
        <ui:VisualElement name="question-dialog-window" class="modal-window">
            <ui:VisualElement name="dialog-header" class="modal-header">
                <ui:Label text="添加推荐问题" class="modal-title" />
                <ui:Button text="×" name="close-question-dialog" class="close-button" />
            </ui:VisualElement>
            
            <ui:VisualElement name="dialog-content" class="modal-content">
                <ui:VisualElement name="question-form" class="question-form">
                    <ui:Label text="问题内容" class="form-label" />
                    <ui:TextField placeholder-text="输入问题内容..." name="question-text" class="form-input" />
                    
                    <ui:Label text="回答内容" class="form-label" />
                    <ui:TextField placeholder-text="输入回答内容..." name="answer-text" class="form-input" multiline="true" />
                    
                    <ui:VisualElement name="question-options" class="question-options">
                        <ui:Toggle text="设为高优先级" name="high-priority" class="option-toggle" />
                        <ui:Toggle text="支持语音播放" name="voice-enabled" class="option-toggle" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:VisualElement name="dialog-buttons" class="dialog-buttons">
                    <ui:Button text="取消" name="cancel-question" class="dialog-button secondary" />
                    <ui:Button text="添加" name="confirm-question" class="dialog-button primary" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
    
</ui:UXML>