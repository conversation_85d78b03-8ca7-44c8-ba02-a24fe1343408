/* 最小MVP样式表 */

.root-container {
    flex-grow: 1;
    background-color: rgb(45, 45, 45);
    padding: 20px;
    justify-content: space-between;
}

.header {
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: rgb(60, 60, 60);
    border-radius: 10px;
}

.title-label {
    font-size: 32px;
    color: rgb(255, 255, 255);
    -unity-font-style: bold;
    margin-bottom: 10px;
    -unity-text-align: middle-center;
}

.version-label {
    font-size: 16px;
    color: rgb(180, 180, 180);
    -unity-text-align: middle-center;
}

.content {
    flex-grow: 1;
    justify-content: space-around;
}

.status-panel, .time-panel, .test-panel {
    background-color: rgb(55, 55, 55);
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    border-left-width: 4px;
    border-left-color: rgb(0, 150, 255);
}

.panel-title {
    font-size: 18px;
    color: rgb(0, 150, 255);
    -unity-font-style: bold;
    margin-bottom: 10px;
}

.status-text, .time-text {
    font-size: 16px;
    color: rgb(220, 220, 220);
    padding: 10px;
    background-color: rgb(40, 40, 40);
    border-radius: 4px;
}

.test-button {
    background-color: rgb(0, 150, 255);
    color: rgb(255, 255, 255);
    border-radius: 6px;
    padding: 12px 24px;
    font-size: 16px;
    -unity-font-style: bold;
    border-width: 0;
    margin-top: 10px;
}

.test-button:hover {
    background-color: rgb(0, 120, 200);
}

.test-button:active {
    background-color: rgb(0, 100, 180);
}

.footer {
    margin-top: 30px;
    padding: 15px;
    background-color: rgb(35, 35, 35);
    border-radius: 6px;
    align-items: center;
}

.footer-text {
    font-size: 12px;
    color: rgb(150, 150, 150);
    -unity-text-align: middle-center;
}