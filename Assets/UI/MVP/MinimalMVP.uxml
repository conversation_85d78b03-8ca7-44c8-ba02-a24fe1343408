<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/MVP/MinimalMVP.uss?fileID=7433441132597879392&amp;guid=PLACEHOLDER&amp;type=3#MinimalMVP" />
    <ui:VisualElement name="root-container" class="root-container">
        <ui:VisualElement name="header" class="header">
            <ui:Label text="数字人管理系统 MVP" display-tooltip-when-elided="true" name="title-label" class="title-label" />
            <ui:Label text="v1.0.0-MVP" display-tooltip-when-elided="true" name="version-label" class="version-label" />
        </ui:VisualElement>
        
        <ui:VisualElement name="content" class="content">
            <ui:VisualElement name="status-panel" class="status-panel">
                <ui:Label text="系统状态" display-tooltip-when-elided="true" class="panel-title" />
                <ui:Label text="正在初始化..." display-tooltip-when-elided="true" name="status-label" class="status-text" />
            </ui:VisualElement>
            
            <ui:VisualElement name="time-panel" class="time-panel">
                <ui:Label text="当前时间" display-tooltip-when-elided="true" class="panel-title" />
                <ui:Label text="--:--:--" display-tooltip-when-elided="true" name="time-label" class="time-text" />
            </ui:VisualElement>
            
            <ui:VisualElement name="test-panel" class="test-panel">
                <ui:Label text="功能测试" display-tooltip-when-elided="true" class="panel-title" />
                <ui:Button text="执行测试" display-tooltip-when-elided="true" name="test-button" class="test-button" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <ui:VisualElement name="footer" class="footer">
            <ui:Label text="© 2024 数字人管理系统 - 最小MVP版本" display-tooltip-when-elided="true" class="footer-text" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>