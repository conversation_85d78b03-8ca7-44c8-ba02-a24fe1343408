/* 认证演示样式 */

/* 根容器 */
.auth-demo-root {
    flex-grow: 1;
    background-color: rgb(32, 32, 32);
    color: rgb(255, 255, 255);
    font-size: 14px;
    min-height: 100%;
}

/* 演示头部 */
.demo-header {
    height: 80px;
    background-color: rgb(24, 24, 24);
    border-bottom-width: 2px;
    border-bottom-color: rgb(0, 120, 215);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 30px;
    padding-right: 30px;
}

.demo-title {
    font-size: 24px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.auth-status-area {
    flex-direction: row;
    align-items: center;
}

/* 状态指示器 */
.status-indicator {
    flex-direction: row;
    align-items: center;
    margin-right: 20px;
}

.status-icon {
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin-right: 8px;
}

.auth-logged-in {
    background-color: rgb(0, 200, 0);
}

.auth-not-logged-in {
    background-color: rgb(220, 20, 60);
}

.status-text {
    font-size: 14px;
    color: rgb(200, 200, 200);
    font-weight: bold;
}

/* 用户信息 */
.user-info {
    flex-direction: row;
    align-items: center;
    margin-right: 15px;
}

.user-name {
    font-size: 14px;
    color: rgb(255, 255, 255);
    margin-right: 10px;
    font-weight: bold;
}

.logout-button {
    height: 32px;
    padding-left: 12px;
    padding-right: 12px;
    background-color: rgb(64, 64, 64);
    color: rgb(200, 200, 200);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    border-radius: 4px;
    font-size: 12px;
}

.logout-button:hover {
    background-color: rgb(80, 80, 80);
    color: rgb(255, 255, 255);
}

/* 登录按钮 */
.login-button {
    height: 36px;
    padding-left: 20px;
    padding-right: 20px;
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
}

.login-button:hover {
    background-color: rgb(0, 100, 180);
}

/* 主要内容 */
.demo-content {
    flex-grow: 1;
    padding: 40px;
    flex-direction: row;
    justify-content: space-around;
}

.demo-info, .demo-instructions {
    width: 45%;
    background-color: rgb(40, 40, 40);
    border-radius: 8px;
    padding: 30px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-color: rgb(64, 64, 64);
    border-right-color: rgb(64, 64, 64);
    border-top-color: rgb(64, 64, 64);
    border-bottom-color: rgb(64, 64, 64);
}

.section-title {
    font-size: 20px;
    font-weight: bold;
    color: rgb(0, 120, 215);
    margin-bottom: 20px;
}

.feature-list, .instruction-list {
    flex-direction: column;
}

.feature-item, .instruction-item {
    font-size: 16px;
    color: rgb(220, 220, 220);
    margin-bottom: 12px;
    padding: 8px;
    background-color: rgb(48, 48, 48);
    border-radius: 4px;
}

/* 模态对话框 */
.modal-overlay {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    align-items: center;
    justify-content: center;
}

.modal-window {
    width: 450px;
    background-color: rgb(48, 48, 48);
    border-radius: 12px;
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.modal-header {
    height: 60px;
    background-color: rgb(40, 40, 40);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(128, 128, 128);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 25px;
    padding-right: 25px;
}

.modal-title {
    font-size: 18px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.close-button {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    font-size: 16px;
    border-radius: 15px;
}

.close-button:hover {
    background-color: rgb(220, 20, 60);
    color: rgb(255, 255, 255);
}

.modal-content {
    padding: 30px;
}

/* 登录表单 */
.login-description {
    font-size: 16px;
    color: rgb(200, 200, 200);
    text-align: center;
    margin-bottom: 30px;
}

.login-form {
    margin-bottom: 25px;
}

.form-field {
    margin-bottom: 20px;
}

.field-label {
    font-size: 14px;
    color: rgb(200, 200, 200);
    margin-bottom: 8px;
    font-weight: bold;
}

.form-input {
    height: 40px;
    background-color: rgb(32, 32, 32);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
    padding-left: 15px;
    padding-right: 15px;
    border-radius: 6px;
    font-size: 14px;
}

.form-input:focus {
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
}

/* 登录状态 */
.login-status {
    flex-direction: row;
    align-items: center;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.login-status.status-success {
    background-color: rgba(0, 200, 0, 0.2);
    border-left-color: rgb(0, 200, 0);
    border-right-color: rgb(0, 200, 0);
    border-top-color: rgb(0, 200, 0);
    border-bottom-color: rgb(0, 200, 0);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
}

.login-status.status-error {
    background-color: rgba(220, 20, 60, 0.2);
    border-left-color: rgb(220, 20, 60);
    border-right-color: rgb(220, 20, 60);
    border-top-color: rgb(220, 20, 60);
    border-bottom-color: rgb(220, 20, 60);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
}

.login-status.status-processing {
    background-color: rgba(0, 120, 215, 0.2);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
}

.status-message {
    font-size: 14px;
    color: rgb(255, 255, 255);
    margin-left: 10px;
    font-weight: bold;
}

/* 登录操作按钮 */
.login-actions {
    flex-direction: row;
    justify-content: space-between;
}

.login-button.primary {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    height: 40px;
    padding-left: 25px;
    padding-right: 25px;
    border-radius: 6px;
    font-weight: bold;
}

.login-button.primary:hover {
    background-color: rgb(0, 100, 180);
}

.login-button.secondary {
    background-color: rgb(64, 64, 64);
    color: rgb(200, 200, 200);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    height: 40px;
    padding-left: 25px;
    padding-right: 25px;
    border-radius: 6px;
}

.login-button.secondary:hover {
    background-color: rgb(80, 80, 80);
    color: rgb(255, 255, 255);
}

/* 帮助信息 */
.login-help {
    border-top-width: 1px;
    border-top-color: rgb(64, 64, 64);
    padding-top: 20px;
    text-align: center;
}

.help-text {
    font-size: 12px;
    color: rgb(160, 160, 160);
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 800px) {
    .demo-content {
        flex-direction: column;
        align-items: center;
    }
    
    .demo-info, .demo-instructions {
        width: 90%;
        margin-bottom: 20px;
    }
    
    .modal-window {
        width: 90%;
        max-width: 400px;
    }
}