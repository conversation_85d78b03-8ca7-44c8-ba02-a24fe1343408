<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/Main/MainUI.uss?fileID=7433441132597879392&amp;guid=PLACEHOLDER&amp;type=3#MainUI" />
    
    <!-- 主界面根容器 -->
    <ui:VisualElement name="main-root" class="main-root">
        
        <!-- 顶部导航栏 -->
        <ui:VisualElement name="top-navigation" class="top-navigation">
            <ui:VisualElement name="nav-left" class="nav-section">
                <ui:Label text="数字人对话系统" name="app-title" class="app-title" />
                <ui:Label text="v1.0.0" name="app-version" class="app-version" />
            </ui:VisualElement>
            
            <ui:VisualElement name="nav-center" class="nav-section nav-center">
                <ui:Button text="主页" name="nav-home" class="nav-button active" />
                <ui:Button text="对话" name="nav-chat" class="nav-button" />
                <ui:Button text="设置" name="nav-settings" class="nav-button" />
            </ui:VisualElement>
            
            <ui:VisualElement name="nav-right" class="nav-section">
                <ui:VisualElement name="status-indicators" class="status-indicators">
                    <ui:VisualElement name="network-status" class="status-indicator">
                        <ui:VisualElement name="network-icon" class="status-icon network-online" />
                        <ui:Label text="在线" name="network-text" class="status-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="activation-status" class="status-indicator">
                        <ui:VisualElement name="activation-icon" class="status-icon activation-active" />
                        <ui:Label text="已激活" name="activation-text" class="status-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="auth-status" class="status-indicator">
                        <ui:VisualElement name="auth-icon" class="status-icon auth-not-logged-in" />
                        <ui:Label text="未登录" name="auth-text" class="status-text" />
                    </ui:VisualElement>
                    <ui:VisualElement name="sync-status" class="status-indicator">
                        <ui:VisualElement name="sync-icon" class="status-icon sync-idle" />
                        <ui:Label text="同步空闲" name="sync-text" class="status-text" />
                    </ui:VisualElement>
                </ui:VisualElement>
                <ui:VisualElement name="user-info" class="user-info" style="display: none;">
                    <ui:Label text="用户名" name="user-display-name" class="user-name" />
                    <ui:Button text="登出" name="logout-button" class="logout-button" />
                </ui:VisualElement>
                <ui:Button text="登录" name="login-button" class="login-button" />
                <ui:Button text="⚙" name="quick-settings" class="icon-button" />
                <ui:Button text="?" name="help-button" class="icon-button" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 主内容区域 -->
        <ui:VisualElement name="main-content" class="main-content">
            
            <!-- 欢迎页面 -->
            <ui:VisualElement name="welcome-page" class="page-container active-page">
                <ui:VisualElement name="welcome-header" class="welcome-header">
                    <ui:Label text="欢迎使用数字人对话系统" name="welcome-title" class="welcome-title" />
                    <ui:Label text="开始与您的数字人助手进行智能对话" name="welcome-subtitle" class="welcome-subtitle" />
                </ui:VisualElement>
                
                <ui:VisualElement name="quick-actions" class="quick-actions">
                    <ui:Button text="开始对话" name="start-chat" class="action-button primary" />
                    <ui:Button text="语音设置" name="voice-settings" class="action-button secondary" />
                    <ui:Button text="数字人设置" name="avatar-settings" class="action-button secondary" />
                </ui:VisualElement>
                
                <ui:VisualElement name="system-info" class="system-info">
                    <ui:VisualElement name="info-grid" class="info-grid">
                        <ui:VisualElement name="info-item" class="info-item">
                            <ui:Label text="系统状态" class="info-label" />
                            <ui:Label text="正常运行" name="system-status" class="info-value status-normal" />
                        </ui:VisualElement>
                        <ui:VisualElement name="info-item" class="info-item">
                            <ui:Label text="数字人模式" class="info-label" />
                            <ui:Label text="3D模型" name="render-mode" class="info-value" />
                        </ui:VisualElement>
                        <ui:VisualElement name="info-item" class="info-item">
                            <ui:Label text="语音引擎" class="info-label" />
                            <ui:Label text="已连接" name="voice-engine" class="info-value status-normal" />
                        </ui:VisualElement>
                        <ui:VisualElement name="info-item" class="info-item">
                            <ui:Label text="AI服务" class="info-label" />
                            <ui:Label text="已连接" name="ai-service" class="info-value status-normal" />
                        </ui:VisualElement>
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
            
            <!-- 对话页面 -->
            <ui:VisualElement name="chat-page" class="page-container">
                <!-- 对话界面内容将由ChatUIManager管理 -->
                <ui:Instance template="project://database/Assets/UI/Chat/ChatUI.uxml" />
            </ui:VisualElement>
            
            <!-- 设置页面 -->
            <ui:VisualElement name="settings-page" class="page-container">
                <ui:Label text="设置界面已在其他组件中实现" class="placeholder-text" />
            </ui:VisualElement>
            
        </ui:VisualElement>
        
        <!-- 底部状态栏 -->
        <ui:VisualElement name="bottom-status" class="bottom-status">
            <ui:VisualElement name="status-left" class="status-section">
                <ui:Label text="就绪" name="app-status" class="status-text" />
                <ui:VisualElement name="separator" class="status-separator" />
                <ui:Label text="内存使用: 256MB" name="memory-usage" class="status-text" />
            </ui:VisualElement>
            
            <ui:VisualElement name="status-center" class="status-section">
                <ui:Label text="" name="current-time" class="status-text time-display" />
            </ui:VisualElement>
            
            <ui:VisualElement name="status-right" class="status-section">
                <ui:Label text="FPS: 60" name="fps-counter" class="status-text" />
                <ui:VisualElement name="separator" class="status-separator" />
                <ui:Label text="版本 1.0.0" name="version-info" class="status-text" />
            </ui:VisualElement>
        </ui:VisualElement>
        
    </ui:VisualElement>
    
    <!-- 激活对话框 -->
    <ui:VisualElement name="activation-dialog" class="modal-overlay" style="display: none;">
        <ui:VisualElement name="activation-window" class="modal-window">
            <ui:VisualElement name="activation-header" class="modal-header">
                <ui:Label text="设备激活" class="modal-title" />
                <ui:Button text="×" name="activation-close" class="close-button" />
            </ui:VisualElement>
            
            <ui:VisualElement name="activation-content" class="modal-content">
                <ui:Label text="请输入激活码以激活设备" class="activation-description" />
                
                <ui:VisualElement name="activation-form" class="activation-form">
                    <ui:TextField placeholder-text="请输入激活码" name="activation-code" class="activation-input" />
                    <ui:Button text="激活" name="activate-button" class="activation-button" />
                </ui:VisualElement>
                
                <ui:VisualElement name="activation-info" class="activation-info">
                    <ui:Label text="设备ID: " name="device-id-label" class="info-text" />
                    <ui:Label text="XXXX-XXXX-XXXX-XXXX" name="device-id" class="info-value" />
                </ui:VisualElement>
                
                <ui:VisualElement name="mvp-mode" class="mvp-mode">
                    <ui:Toggle text="MVP模式（开发测试）" name="mvp-toggle" class="mvp-toggle" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
    
    <!-- 登录对话框 -->
    <ui:VisualElement name="login-dialog" class="modal-overlay" style="display: none;">
        <ui:VisualElement name="login-window" class="modal-window">
            <ui:VisualElement name="login-header" class="modal-header">
                <ui:Label text="用户登录" class="modal-title" />
                <ui:Button text="×" name="login-close" class="close-button" />
            </ui:VisualElement>
            
            <ui:VisualElement name="login-content" class="modal-content">
                <ui:Label text="请输入您的登录凭据" class="login-description" />
                
                <ui:VisualElement name="login-form" class="login-form">
                    <ui:VisualElement name="form-field" class="form-field">
                        <ui:Label text="用户名" class="field-label" />
                        <ui:TextField placeholder-text="请输入用户名" name="username-input" class="form-input" />
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="form-field" class="form-field">
                        <ui:Label text="密码" class="field-label" />
                        <ui:TextField placeholder-text="请输入密码" name="password-input" class="form-input" password="true" />
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="login-status" class="login-status" style="display: none;">
                        <ui:VisualElement name="status-icon" class="status-icon" />
                        <ui:Label text="" name="login-status-text" class="status-message" />
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="login-actions" class="login-actions">
                        <ui:Button text="登录" name="login-submit" class="login-button primary" />
                        <ui:Button text="取消" name="login-cancel" class="login-button secondary" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:VisualElement name="login-help" class="login-help">
                    <ui:Label text="测试账户: admin / admin123" class="help-text" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
    
</ui:UXML>