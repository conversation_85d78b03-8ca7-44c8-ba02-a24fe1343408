/* 主界面样式 */

/* 根容器 */
.main-root {
    flex-grow: 1;
    background-color: rgb(32, 32, 32);
    color: rgb(255, 255, 255);
    font-size: 14px;
}

/* 顶部导航栏 */
.top-navigation {
    height: 60px;
    background-color: rgb(24, 24, 24);
    border-bottom-width: 1px;
    border-bottom-color: rgb(64, 64, 64);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;
}

.nav-section {
    flex-direction: row;
    align-items: center;
}

.nav-center {
    justify-content: center;
}

.app-title {
    font-size: 18px;
    font-weight: bold;
    color: rgb(255, 255, 255);
    margin-right: 10px;
}

.app-version {
    font-size: 12px;
    color: rgb(160, 160, 160);
    background-color: rgb(64, 64, 64);
    padding-left: 6px;
    padding-right: 6px;
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 3px;
}

.nav-button {
    height: 36px;
    margin-left: 5px;
    margin-right: 5px;
    padding-left: 20px;
    padding-right: 20px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    border-radius: 4px;
    transition-duration: 0.2s;
}

.nav-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgb(255, 255, 255);
}

.nav-button.active {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
}

.status-indicators {
    flex-direction: row;
    margin-right: 15px;
}

.status-indicator {
    flex-direction: row;
    align-items: center;
    margin-left: 15px;
}

.status-icon {
    width: 12px;
    height: 12px;
    border-radius: 6px;
    margin-right: 5px;
}

.network-online {
    background-color: rgb(0, 200, 0);
}

.network-offline {
    background-color: rgb(200, 0, 0);
}

.activation-active {
    background-color: rgb(0, 200, 0);
}

.activation-inactive {
    background-color: rgb(255, 165, 0);
}

.auth-logged-in {
    background-color: rgb(0, 200, 0);
}

.auth-not-logged-in {
    background-color: rgb(200, 0, 0);
}

.status-text {
    font-size: 12px;
    color: rgb(200, 200, 200);
}

.icon-button {
    width: 32px;
    height: 32px;
    margin-left: 5px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    border-radius: 4px;
    font-size: 16px;
}

.icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgb(255, 255, 255);
}

/* 主内容区域 */
.main-content {
    flex-grow: 1;
    position: relative;
}

.page-container {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: none;
    padding: 30px;
}

.page-container.active-page {
    display: flex;
}

/* 欢迎页面 */
.welcome-header {
    align-items: center;
    margin-bottom: 40px;
}

.welcome-title {
    font-size: 32px;
    font-weight: bold;
    color: rgb(255, 255, 255);
    margin-bottom: 10px;
    text-align: center;
}

.welcome-subtitle {
    font-size: 16px;
    color: rgb(180, 180, 180);
    text-align: center;
}

.quick-actions {
    flex-direction: row;
    justify-content: center;
    margin-bottom: 50px;
}

.action-button {
    height: 48px;
    margin-left: 10px;
    margin-right: 10px;
    padding-left: 30px;
    padding-right: 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    transition-duration: 0.2s;
}

.action-button.primary {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.action-button.primary:hover {
    background-color: rgb(0, 100, 180);
}

.action-button.secondary {
    background-color: rgb(64, 64, 64);
    color: rgb(255, 255, 255);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
}

.action-button.secondary:hover {
    background-color: rgb(80, 80, 80);
}

.system-info {
    background-color: rgb(40, 40, 40);
    border-radius: 8px;
    padding: 25px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-color: rgb(64, 64, 64);
    border-right-color: rgb(64, 64, 64);
    border-top-color: rgb(64, 64, 64);
    border-bottom-color: rgb(64, 64, 64);
}

.info-grid {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}

.info-item {
    width: 48%;
    margin-bottom: 20px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: rgb(48, 48, 48);
    border-radius: 6px;
}

.info-label {
    font-size: 14px;
    color: rgb(180, 180, 180);
}

.info-value {
    font-size: 14px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.status-normal {
    color: rgb(0, 200, 0);
}

.status-warning {
    color: rgb(255, 165, 0);
}

.status-error {
    color: rgb(220, 20, 60);
}

/* 占位符文本 */
.placeholder-text {
    font-size: 18px;
    color: rgb(128, 128, 128);
    text-align: center;
    align-self: center;
    margin-top: 100px;
}

/* 底部状态栏 */
.bottom-status {
    height: 32px;
    background-color: rgb(24, 24, 24);
    border-top-width: 1px;
    border-top-color: rgb(64, 64, 64);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    padding-right: 15px;
}

.status-section {
    flex-direction: row;
    align-items: center;
}

.status-separator {
    width: 1px;
    height: 16px;
    background-color: rgb(64, 64, 64);
    margin-left: 10px;
    margin-right: 10px;
}

.time-display {
    font-weight: bold;
    color: rgb(255, 255, 255);
}

/* 模态对话框 */
.modal-overlay {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    align-items: center;
    justify-content: center;
}

.modal-window {
    width: 400px;
    background-color: rgb(48, 48, 48);
    border-radius: 8px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
}

.modal-header {
    height: 50px;
    background-color: rgb(40, 40, 40);
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(128, 128, 128);
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;
}

.modal-title {
    font-size: 16px;
    font-weight: bold;
    color: rgb(255, 255, 255);
}

.close-button {
    width: 24px;
    height: 24px;
    background-color: rgba(255, 255, 255, 0);
    border-left-color: rgba(255, 255, 255, 0);
    border-right-color: rgba(255, 255, 255, 0);
    border-top-color: rgba(255, 255, 255, 0);
    border-bottom-color: rgba(255, 255, 255, 0);
    color: rgb(200, 200, 200);
    font-size: 14px;
}

.close-button:hover {
    background-color: rgb(220, 20, 60);
    color: rgb(255, 255, 255);
}

.modal-content {
    padding: 25px;
}

/* 激活对话框 */
.activation-description {
    font-size: 14px;
    color: rgb(200, 200, 200);
    text-align: center;
    margin-bottom: 25px;
}

.activation-form {
    margin-bottom: 25px;
}

.activation-input {
    height: 40px;
    margin-bottom: 15px;
    background-color: rgb(32, 32, 32);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
    padding-left: 12px;
    padding-right: 12px;
    border-radius: 4px;
}

.activation-input:focus {
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.activation-button {
    height: 40px;
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-radius: 4px;
    font-weight: bold;
}

.activation-button:hover {
    background-color: rgb(0, 100, 180);
}

.activation-info {
    background-color: rgb(40, 40, 40);
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    flex-direction: row;
    align-items: center;
}

.info-text {
    font-size: 12px;
    color: rgb(180, 180, 180);
    margin-right: 10px;
}

.info-value {
    font-size: 12px;
    font-weight: bold;
    color: rgb(255, 255, 255);
    font-family: 'Courier New', monospace;
}

.mvp-mode {
    border-top-width: 1px;
    border-top-color: rgb(64, 64, 64);
    padding-top: 15px;
}

.mvp-toggle {
    color: rgb(255, 165, 0);
}

/* 认证相关样式 */
.login-button {
    height: 32px;
    margin-left: 10px;
    padding-left: 15px;
    padding-right: 15px;
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.login-button:hover {
    background-color: rgb(0, 100, 180);
}

.user-info {
    flex-direction: row;
    align-items: center;
    margin-left: 10px;
}

.user-name {
    font-size: 12px;
    color: rgb(255, 255, 255);
    margin-right: 10px;
    font-weight: bold;
}

.logout-button {
    height: 24px;
    padding-left: 8px;
    padding-right: 8px;
    background-color: rgb(64, 64, 64);
    color: rgb(200, 200, 200);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    border-radius: 3px;
    font-size: 10px;
}

.logout-button:hover {
    background-color: rgb(80, 80, 80);
    color: rgb(255, 255, 255);
}

/* 登录对话框样式 */
.login-description {
    font-size: 14px;
    color: rgb(200, 200, 200);
    text-align: center;
    margin-bottom: 25px;
}

.login-form {
    margin-bottom: 20px;
}

.form-field {
    margin-bottom: 15px;
}

.field-label {
    font-size: 12px;
    color: rgb(200, 200, 200);
    margin-bottom: 5px;
}

.form-input {
    height: 36px;
    background-color: rgb(32, 32, 32);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    color: rgb(255, 255, 255);
    padding-left: 12px;
    padding-right: 12px;
    border-radius: 4px;
    font-size: 14px;
}

.form-input:focus {
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
}

.login-status {
    flex-direction: row;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.login-status.status-success {
    background-color: rgba(0, 200, 0, 0.2);
    border-left-color: rgb(0, 200, 0);
    border-right-color: rgb(0, 200, 0);
    border-top-color: rgb(0, 200, 0);
    border-bottom-color: rgb(0, 200, 0);
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
}

.login-status.status-error {
    background-color: rgba(220, 20, 60, 0.2);
    border-left-color: rgb(220, 20, 60);
    border-right-color: rgb(220, 20, 60);
    border-top-color: rgb(220, 20, 60);
    border-bottom-color: rgb(220, 20, 60);
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
}

.login-status.status-processing {
    background-color: rgba(0, 120, 215, 0.2);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-width: 1px;
}

.status-message {
    font-size: 12px;
    color: rgb(255, 255, 255);
    margin-left: 8px;
}

.login-actions {
    flex-direction: row;
    justify-content: space-between;
}

.login-button.primary {
    background-color: rgb(0, 120, 215);
    color: rgb(255, 255, 255);
    border-left-color: rgb(0, 120, 215);
    border-right-color: rgb(0, 120, 215);
    border-top-color: rgb(0, 120, 215);
    border-bottom-color: rgb(0, 120, 215);
    height: 36px;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 4px;
    font-weight: bold;
}

.login-button.primary:hover {
    background-color: rgb(0, 100, 180);
}

.login-button.secondary {
    background-color: rgb(64, 64, 64);
    color: rgb(200, 200, 200);
    border-left-color: rgb(128, 128, 128);
    border-right-color: rgb(128, 128, 128);
    border-top-color: rgb(128, 128, 128);
    border-bottom-color: rgb(128, 128, 128);
    height: 36px;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 4px;
}

.login-button.secondary:hover {
    background-color: rgb(80, 80, 80);
    color: rgb(255, 255, 255);
}

.login-help {
    border-top-width: 1px;
    border-top-color: rgb(64, 64, 64);
    padding-top: 15px;
    text-align: center;
}

.help-text {
    font-size: 11px;
    color: rgb(160, 160, 160);
    font-style: italic;
}

/* 显示/隐藏工具类 */
.display-none {
    display: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .info-item {
        width: 100%;
    }
    
    .quick-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-button {
        margin-bottom: 10px;
        width: 200px;
    }
}

@media (max-height: 800px) {
    .welcome-title {
        font-size: 24px;
    }
    
    .welcome-subtitle {
        font-size: 14px;
    }
    
    .system-info {
        padding: 15px;
    }
}

/* 大屏优化 */
@media (min-width: 1920px) {
    .welcome-title {
        font-size: 48px;
    }
    
    .welcome-subtitle {
        font-size: 20px;
    }
    
    .action-button {
        height: 60px;
        font-size: 18px;
        padding-left: 40px;
        padding-right: 40px;
    }
    
    .info-item {
        padding: 20px;
    }
    
    .info-label, .info-value {
        font-size: 16px;
    }
}

/* 竖屏优化 */
@media (orientation: portrait) {
    .top-navigation {
        height: 80px;
        flex-direction: column;
        padding-top: 10px;
        padding-bottom: 10px;
    }
    
    .nav-section {
        margin-bottom: 5px;
    }
    
    .nav-center {
        order: -1;
        margin-bottom: 10px;
    }
    
    .quick-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-button {
        width: 80%;
        margin-bottom: 15px;
    }
    
    .info-grid {
        flex-direction: column;
    }
    
    .info-item {
        width: 100%;
    }
}