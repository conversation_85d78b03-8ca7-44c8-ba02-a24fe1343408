using UnityEngine;
using DigitalHuman.MVP;

namespace DigitalHuman
{
    /// <summary>
    /// 测试场景管理器 - 用于演示和测试MVP功能
    /// </summary>
    public class TestSceneManager : MonoBehaviour
    {
        [Header("MVP组件引用")]
        /// <summary>
        /// MVP管理器引用
        /// </summary>
        public MinimalMVPManager mvpManager;
        
        [Header("测试配置")]
        /// <summary>
        /// 是否自动启动测试
        /// </summary>
        public bool autoStartTest = true;
        
        /// <summary>
        /// 测试间隔时间（秒）
        /// </summary>
        public float testInterval = 5.0f;
        
        /// <summary>
        /// 下次测试时间
        /// </summary>
        private float nextTestTime;
        
        /// <summary>
        /// 启动时初始化
        /// </summary>
        void Start()
        {
            Debug.Log("[TestSceneManager] 测试场景管理器启动");
            
            // 查找MVP管理器
            if (mvpManager == null)
            {
                mvpManager = FindObjectOfType<MinimalMVPManager>();
            }
            
            if (mvpManager == null)
            {
                Debug.LogWarning("[TestSceneManager] 未找到MinimalMVPManager，创建新实例");
                CreateMVPManager();
            }
            
            // 设置下次测试时间
            nextTestTime = Time.time + testInterval;
            
            Debug.Log("[TestSceneManager] 测试场景管理器初始化完成");
        }
        
        /// <summary>
        /// 创建MVP管理器
        /// </summary>
        private void CreateMVPManager()
        {
            GameObject mvpObject = new GameObject("MinimalMVPManager");
            mvpManager = mvpObject.AddComponent<MinimalMVPManager>();
            
            Debug.Log("[TestSceneManager] 已创建MinimalMVPManager实例");
        }
        
        /// <summary>
        /// 更新循环
        /// </summary>
        void Update()
        {
            // 自动测试功能
            if (autoStartTest && Time.time >= nextTestTime)
            {
                RunPeriodicTest();
                nextTestTime = Time.time + testInterval;
            }
            
            // 键盘快捷键
            HandleKeyboardInput();
        }
        
        /// <summary>
        /// 处理键盘输入
        /// </summary>
        private void HandleKeyboardInput()
        {
            // 按 T 键运行测试
            if (Input.GetKeyDown(KeyCode.T))
            {
                RunManualTest();
            }
            
            // 按 R 键重启MVP系统
            if (Input.GetKeyDown(KeyCode.R))
            {
                RestartMVPSystem();
            }
            
            // 按 S 键显示系统状态
            if (Input.GetKeyDown(KeyCode.S))
            {
                ShowSystemStatus();
            }
            
            // 按 Q 键退出应用程序
            if (Input.GetKeyDown(KeyCode.Q))
            {
                QuitApplication();
            }
        }
        
        /// <summary>
        /// 运行周期性测试
        /// </summary>
        private void RunPeriodicTest()
        {
            Debug.Log("[TestSceneManager] 运行周期性测试...");
            
            if (mvpManager != null)
            {
                string status = mvpManager.GetSystemStatus();
                Debug.Log($"[TestSceneManager] {status}");
            }
            else
            {
                Debug.LogWarning("[TestSceneManager] MVP管理器不可用");
            }
        }
        
        /// <summary>
        /// 运行手动测试
        /// </summary>
        private void RunManualTest()
        {
            Debug.Log("[TestSceneManager] 运行手动测试...");
            
            if (mvpManager != null)
            {
                string status = mvpManager.GetSystemStatus();
                Debug.Log($"[TestSceneManager] 系统状态: {status}");
                
                // 显示系统信息
                ShowSystemInfo();
            }
            else
            {
                Debug.LogError("[TestSceneManager] MVP管理器不可用，无法运行测试");
            }
        }
        
        /// <summary>
        /// 重启MVP系统
        /// </summary>
        private void RestartMVPSystem()
        {
            Debug.Log("[TestSceneManager] 重启MVP系统...");
            
            if (mvpManager != null)
            {
                mvpManager.RestartMVPSystem();
            }
            else
            {
                Debug.LogError("[TestSceneManager] MVP管理器不可用，无法重启");
            }
        }
        
        /// <summary>
        /// 显示系统状态
        /// </summary>
        private void ShowSystemStatus()
        {
            Debug.Log("[TestSceneManager] 显示系统状态...");
            
            if (mvpManager != null)
            {
                string status = mvpManager.GetSystemStatus();
                Debug.Log($"[TestSceneManager] {status}");
            }
            
            ShowSystemInfo();
        }
        
        /// <summary>
        /// 显示系统信息
        /// </summary>
        private void ShowSystemInfo()
        {
            string systemInfo = $@"
=== 系统信息 ===
平台: {Application.platform}
Unity版本: {Application.unityVersion}
目标帧率: {Application.targetFrameRate}
当前帧率: {(1.0f / Time.deltaTime):F1} FPS
内存使用: {(System.GC.GetTotalMemory(false) / 1024 / 1024):F1} MB
运行时间: {Time.time:F1} 秒
场景名称: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}
===============";
            
            Debug.Log(systemInfo);
        }
        
        /// <summary>
        /// 退出应用程序
        /// </summary>
        private void QuitApplication()
        {
            Debug.Log("[TestSceneManager] 退出应用程序...");
            
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
        
        /// <summary>
        /// GUI显示
        /// </summary>
        void OnGUI()
        {
            // 显示控制面板
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("数字人管理系统 MVP 测试面板", GUI.skin.label);
            GUILayout.Space(10);
            
            if (GUILayout.Button("运行测试 (T)"))
            {
                RunManualTest();
            }
            
            if (GUILayout.Button("重启系统 (R)"))
            {
                RestartMVPSystem();
            }
            
            if (GUILayout.Button("显示状态 (S)"))
            {
                ShowSystemStatus();
            }
            
            if (GUILayout.Button("退出应用 (Q)"))
            {
                QuitApplication();
            }
            
            GUILayout.Space(10);
            
            // 显示当前状态
            if (mvpManager != null)
            {
                GUILayout.Label($"状态: {mvpManager.GetSystemStatus()}");
            }
            else
            {
                GUILayout.Label("状态: MVP管理器未找到");
            }
            
            GUILayout.Label($"FPS: {(1.0f / Time.deltaTime):F1}");
            GUILayout.Label($"运行时间: {Time.time:F1}s");
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}