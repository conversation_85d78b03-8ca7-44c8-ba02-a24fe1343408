using UnityEngine;
using System;
using System.Threading.Tasks;
using DigitalHuman.Core.Authentication;
using DigitalHuman.Core.DataSync;
using DigitalHuman.Core.Logging;
using AuthLogger = DigitalHuman.Core.Logging.ILogger;

namespace DigitalHuman.MVP
{
    /// <summary>
    /// 增强版MVP管理器 - 集成更多核心功能模块
    /// 在MinimalMVPManager基础上添加认证、数据同步等功能
    /// </summary>
    public class EnhancedMVPManager : MinimalMVPManager
    {
        [Header("功能模块配置")]
        /// <summary>
        /// 是否启用认证系统
        /// </summary>
        public bool EnableAuthentication = true;
        
        /// <summary>
        /// 是否启用数据同步
        /// </summary>
        public bool EnableDataSync = true;
        
        /// <summary>
        /// 是否启用UI管理
        /// </summary>
        public bool EnableUIManagement = true;
        
        /// <summary>
        /// 是否自动登录演示用户
        /// </summary>
        public bool AutoLoginDemoUser = true;

        [Header("演示配置")]
        /// <summary>
        /// 演示用户名
        /// </summary>
        public string DemoUsername = "demo_user";
        
        /// <summary>
        /// 演示密码
        /// </summary>
        public string DemoPassword = "demo_pass";

        // 核心管理器引用
        private IAuthenticationManager authManager;
        private IDataSyncManager dataSyncManager;
        private AuthLogger enhancedLogger;
        
        // 初始化状态
        private bool modulesInitialized = false;
        private int initializedModuleCount = 0;
        private int totalModuleCount = 0;

        /// <summary>
        /// 增强版系统启动初始化
        /// </summary>
        protected override void Awake()
        {
            // 调用基类初始化
            base.Awake();

            // 初始化增强功能
            InitializeEnhancedFeatures();
        }

        /// <summary>
        /// 增强版系统启动后配置
        /// </summary>
        protected override void Start()
        {
            // 调用基类启动
            base.Start();

            // 启动增强功能
            StartEnhancedFeatures();
        }

        /// <summary>
        /// 初始化增强功能
        /// </summary>
        private void InitializeEnhancedFeatures()
        {
            try
            {
                if (EnableDebugMode)
                {
                    Debug.Log("[EnhancedMVP] 开始初始化增强功能...");
                }

                // 计算需要初始化的模块数量
                totalModuleCount = 0;
                if (EnableAuthentication) totalModuleCount++;
                if (EnableDataSync) totalModuleCount++;
                if (EnableUIManagement) totalModuleCount++;

                // 初始化日志系统
                InitializeEnhancedLogging();

                // 初始化认证系统
                if (EnableAuthentication)
                {
                    InitializeAuthenticationSystem();
                }

                // 初始化数据同步系统
                if (EnableDataSync)
                {
                    InitializeDataSyncSystem();
                }

                // 初始化UI管理系统
                if (EnableUIManagement)
                {
                    InitializeUIManagementSystem();
                }

                modulesInitialized = true;

                if (EnableDebugMode)
                {
                    Debug.Log($"[EnhancedMVP] 增强功能初始化完成 ({initializedModuleCount}/{totalModuleCount} 个模块)");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 增强功能初始化失败: {ex.Message}");
                modulesInitialized = false;
            }
        }

        /// <summary>
        /// 启动增强功能
        /// </summary>
        private async void StartEnhancedFeatures()
        {
            if (!modulesInitialized)
            {
                Debug.LogWarning("[EnhancedMVP] 模块未初始化，跳过启动增强功能");
                return;
            }

            try
            {
                // 自动登录演示用户
                if (EnableAuthentication && AutoLoginDemoUser)
                {
                    await PerformDemoLogin();
                }

                // 启动数据同步
                if (EnableDataSync && dataSyncManager != null)
                {
                    await StartDataSync();
                }

                enhancedLogger?.Info("增强版MVP系统启动完成");

                // 启动运行时测试UI
                StartRuntimeTestUI();

                // 显示系统状态
                ShowEnhancedSystemStatus();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 启动增强功能失败: {ex.Message}");
                enhancedLogger?.Error($"启动增强功能失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化增强日志系统
        /// </summary>
        private void InitializeEnhancedLogging()
        {
            try
            {
                var logManager = LogManager.Instance;
                if (logManager != null)
                {
                    enhancedLogger = logManager.GetLogger("EnhancedMVP");
                    enhancedLogger.Info($"增强版MVP管理器启动 - 版本: {AppVersion}");
                    
                    if (EnableDebugMode)
                    {
                        Debug.Log("[EnhancedMVP] 增强日志系统初始化成功");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 增强日志系统初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化认证系统
        /// </summary>
        private void InitializeAuthenticationSystem()
        {
            try
            {
                authManager = AuthenticationManager.Instance as IAuthenticationManager;
                if (authManager != null)
                {
                    // 订阅认证事件
                    authManager.OnUserLoggedIn += OnUserLoggedIn;
                    authManager.OnUserLoggedOut += OnUserLoggedOut;
                    
                    initializedModuleCount++;
                    
                    if (EnableDebugMode)
                    {
                        Debug.Log("[EnhancedMVP] 认证系统初始化成功");
                    }
                    
                    enhancedLogger?.Info("认证系统初始化成功");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 认证系统初始化失败: {ex.Message}");
                enhancedLogger?.Error($"认证系统初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化数据同步系统
        /// </summary>
        private async void InitializeDataSyncSystem()
        {
            try
            {
                dataSyncManager = DataSyncManager.Instance as IDataSyncManager;
                if (dataSyncManager != null)
                {
                    // 初始化数据同步
                    await dataSyncManager.InitializeAsync();

                    initializedModuleCount++;

                    if (EnableDebugMode)
                    {
                        Debug.Log("[EnhancedMVP] 数据同步系统初始化成功");
                    }

                    enhancedLogger?.Info("数据同步系统初始化成功");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 数据同步系统初始化失败: {ex.Message}");
                enhancedLogger?.Error($"数据同步系统初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化UI管理系统
        /// </summary>
        private void InitializeUIManagementSystem()
        {
            try
            {
                // 这里可以初始化UI管理器
                // var uiManager = MainUIManager.Instance;
                
                initializedModuleCount++;
                
                if (EnableDebugMode)
                {
                    Debug.Log("[EnhancedMVP] UI管理系统初始化成功");
                }
                
                enhancedLogger?.Info("UI管理系统初始化成功");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] UI管理系统初始化失败: {ex.Message}");
                enhancedLogger?.Error($"UI管理系统初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行演示登录
        /// </summary>
        private async Task PerformDemoLogin()
        {
            if (authManager == null) return;

            try
            {
                if (EnableDebugMode)
                {
                    Debug.Log($"[EnhancedMVP] 开始演示登录: {DemoUsername}");
                }

                var result = await authManager.LoginAsync(DemoUsername, DemoPassword);
                
                if (result.IsSuccess)
                {
                    if (EnableDebugMode)
                    {
                        Debug.Log($"[EnhancedMVP] 演示登录成功: {result.UserInfo.DisplayName}");
                    }
                    
                    enhancedLogger?.Info($"演示用户登录成功: {result.UserInfo.DisplayName}");
                }
                else
                {
                    Debug.LogWarning($"[EnhancedMVP] 演示登录失败: {result.ErrorMessage}");
                    enhancedLogger?.Warning($"演示用户登录失败: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 演示登录异常: {ex.Message}");
                enhancedLogger?.Error($"演示登录异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动数据同步
        /// </summary>
        private async Task StartDataSync()
        {
            if (dataSyncManager == null) return;

            try
            {
                if (EnableDebugMode)
                {
                    Debug.Log("[EnhancedMVP] 开始数据同步...");
                }

                var result = await dataSyncManager.StartSyncAsync(DigitalHuman.Core.DataSync.Models.SyncType.Full);
                
                if (result.IsSuccess)
                {
                    if (EnableDebugMode)
                    {
                        Debug.Log($"[EnhancedMVP] 数据同步完成，耗时: {result.Duration.TotalSeconds:F2}秒");
                    }

                    enhancedLogger?.Info($"数据同步完成，耗时: {result.Duration.TotalSeconds:F2}秒");
                }
                else
                {
                    Debug.LogWarning($"[EnhancedMVP] 数据同步失败: {result.ErrorMessage}");
                    enhancedLogger?.Warning($"数据同步失败: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 数据同步异常: {ex.Message}");
                enhancedLogger?.Error($"数据同步异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 用户登录事件处理
        /// </summary>
        private void OnUserLoggedIn(UserInfo userInfo)
        {
            if (EnableDebugMode)
            {
                Debug.Log($"[EnhancedMVP] 用户登录事件: {userInfo.DisplayName}");
            }
            
            enhancedLogger?.Info($"用户登录: {userInfo.DisplayName} ({userInfo.Username})");
        }

        /// <summary>
        /// 用户登出事件处理
        /// </summary>
        private void OnUserLoggedOut()
        {
            if (EnableDebugMode)
            {
                Debug.Log("[EnhancedMVP] 用户登出事件");
            }
            
            enhancedLogger?.Info("用户登出");
        }

        /// <summary>
        /// 启动主界面UI系统
        /// </summary>
        private void StartRuntimeTestUI()
        {
            try
            {
                if (EnableDebugMode)
                {
                    Debug.Log("[EnhancedMVP] 开始启动主界面UI系统");
                }

                // 第一优先级：MainUIManager (完整的主界面系统)
                bool uiCreated = false;
                var mainUIType = System.Type.GetType("DigitalHuman.UI.MainUIManager");
                if (mainUIType != null)
                {
                    // 创建主UI根对象
                    GameObject mainUIObject = new GameObject("Main UI Root");
                    DontDestroyOnLoad(mainUIObject);

                    // 添加UIDocument组件
                    var uiDocument = mainUIObject.AddComponent<UnityEngine.UIElements.UIDocument>();

                    // 尝试加载主UI文档
                    var mainUIAsset = UnityEngine.Resources.Load<UnityEngine.UIElements.VisualTreeAsset>("UI/Main/MainUI");
                    if (mainUIAsset == null)
                    {
                        // 尝试从Assets路径加载
                        #if UNITY_EDITOR
                        mainUIAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<UnityEngine.UIElements.VisualTreeAsset>("Assets/UI/Main/MainUI.uxml");
                        #endif
                    }

                    if (mainUIAsset != null)
                    {
                        uiDocument.visualTreeAsset = mainUIAsset;

                        // 添加MainUIManager组件
                        mainUIObject.AddComponent(mainUIType);

                        if (EnableDebugMode)
                        {
                            Debug.Log("[EnhancedMVP] 主界面UI系统已启动");
                        }
                        enhancedLogger?.Info("主界面UI系统已启动");
                        uiCreated = true;
                    }
                    else
                    {
                        Debug.LogWarning("[EnhancedMVP] 未找到主UI文档，销毁UI对象");
                        DestroyImmediate(mainUIObject);
                    }
                }

                // 第二优先级：ImmediateUI (简单可靠的备用UI)
                if (!uiCreated)
                {
                    var immediateUIType = System.Type.GetType("DigitalHuman.UI.ImmediateUI");
                    if (immediateUIType != null)
                    {
                        // 直接在当前MVP管理器上添加UI组件
                        gameObject.AddComponent(immediateUIType);

                        if (EnableDebugMode)
                        {
                            Debug.Log("[EnhancedMVP] 立即UI已添加到MVP管理器");
                        }
                        enhancedLogger?.Info("立即UI已启动");
                        uiCreated = true;
                    }
                }

                // 第三优先级：ForceUI (最可靠的测试UI)
                if (!uiCreated)
                {
                    var forceUIType = System.Type.GetType("DigitalHuman.UI.ForceUI");
                    if (forceUIType != null)
                    {
                        GameObject forceUIObject = new GameObject("ForceUI");
                        forceUIObject.AddComponent(forceUIType);
                        DontDestroyOnLoad(forceUIObject);

                        if (EnableDebugMode)
                        {
                            Debug.Log("[EnhancedMVP] 强制UI已启动");
                        }
                        enhancedLogger?.Info("强制UI已启动");
                        uiCreated = true;
                    }
                }

                // 第二优先级：DebugTestUI
                if (!uiCreated)
                {
                    var debugUIType = System.Type.GetType("DigitalHuman.UI.DebugTestUI");
                    if (debugUIType != null)
                    {
                        GameObject debugUIObject = new GameObject("DebugTestUI");
                        debugUIObject.AddComponent(debugUIType);
                        DontDestroyOnLoad(debugUIObject);

                        if (EnableDebugMode)
                        {
                            Debug.Log("[EnhancedMVP] 调试测试UI已启动");
                        }
                        enhancedLogger?.Info("调试测试UI已启动");
                        uiCreated = true;
                    }
                }

                // 第二优先级：SimpleTestUI
                if (!uiCreated)
                {
                    var simpleUIType = System.Type.GetType("DigitalHuman.UI.SimpleTestUI");
                    if (simpleUIType != null)
                    {
                        GameObject testUIObject = new GameObject("SimpleTestUI");
                        testUIObject.AddComponent(simpleUIType);
                        DontDestroyOnLoad(testUIObject);

                        if (EnableDebugMode)
                        {
                            Debug.Log("[EnhancedMVP] 简单测试UI已启动");
                        }
                        enhancedLogger?.Info("简单测试UI已启动");
                        uiCreated = true;
                    }
                }

                // 第三优先级：RuntimeTestUI
                if (!uiCreated)
                {
                    var runtimeUIType = System.Type.GetType("DigitalHuman.UI.RuntimeTestUI");
                    if (runtimeUIType != null)
                    {
                        GameObject runtimeUIObject = new GameObject("RuntimeTestUI");
                        runtimeUIObject.AddComponent(runtimeUIType);
                        DontDestroyOnLoad(runtimeUIObject);

                        if (EnableDebugMode)
                        {
                            Debug.Log("[EnhancedMVP] 运行时测试UI已启动");
                        }
                        enhancedLogger?.Info("运行时测试UI已启动");
                        uiCreated = true;
                    }
                }

                // 如果都没有找到
                if (!uiCreated && EnableDebugMode)
                {
                    Debug.LogWarning("[EnhancedMVP] 未找到任何UI类型，跳过UI启动");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[EnhancedMVP] 启动运行时测试UI失败: {ex.Message}");
                enhancedLogger?.Error($"启动运行时测试UI失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示增强系统状态
        /// </summary>
        private void ShowEnhancedSystemStatus()
        {
            if (!EnableDebugMode) return;

            var status = GetEnhancedSystemStatus();
            Debug.Log("========================================");
            Debug.Log("    数字人管理系统 - 增强版");
            Debug.Log(status);
            Debug.Log("========================================");
        }

        /// <summary>
        /// 获取增强系统状态
        /// </summary>
        public string GetEnhancedSystemStatus()
        {
            var baseStatus = GetSystemStatus();
            var enhancedInfo = new System.Text.StringBuilder();
            
            enhancedInfo.AppendLine(baseStatus);
            enhancedInfo.AppendLine();
            enhancedInfo.AppendLine("=== 增强功能状态 ===");
            enhancedInfo.AppendLine($"模块初始化: {(modulesInitialized ? "完成" : "失败")} ({initializedModuleCount}/{totalModuleCount})");
            enhancedInfo.AppendLine($"认证系统: {(EnableAuthentication ? (authManager?.IsLoggedIn == true ? "已登录" : "未登录") : "禁用")}");
            enhancedInfo.AppendLine($"数据同步: {(EnableDataSync ? "启用" : "禁用")}");
            enhancedInfo.AppendLine($"UI管理: {(EnableUIManagement ? "启用" : "禁用")}");
            
            if (authManager?.IsLoggedIn == true)
            {
                enhancedInfo.AppendLine($"当前用户: {authManager.CurrentUser?.DisplayName}");
            }
            
            return enhancedInfo.ToString();
        }

        /// <summary>
        /// 简单的UI显示（用于调试和验证）
        /// </summary>
        void OnGUI()
        {
            if (!EnableDebugMode) return;

            // 设置GUI样式
            GUI.skin.label.fontSize = 16;
            GUI.skin.button.fontSize = 14;

            // 显示系统状态
            GUILayout.BeginArea(new Rect(20, 20, 400, 600));

            GUILayout.Label("=== 数字人管理系统 MVP ===");
            GUILayout.Space(10);

            GUILayout.Label($"版本: {AppVersion}");
            GUILayout.Label($"运行时间: {Time.time:F1} 秒");
            GUILayout.Label($"帧率: {(1.0f / Time.deltaTime):F0} FPS");
            GUILayout.Space(10);

            GUILayout.Label("=== 系统状态 ===");
            GUILayout.Label($"认证系统: {(authManager != null ? "已加载" : "未加载")}");
            GUILayout.Label($"数据同步: {(dataSyncManager != null ? "已加载" : "未加载")}");
            GUILayout.Label($"日志系统: {(enhancedLogger != null ? "已加载" : "未加载")}");
            GUILayout.Space(10);

            if (authManager != null)
            {
                GUILayout.Label($"用户状态: {(authManager.IsLoggedIn ? "已登录" : "未登录")}");
                if (authManager.IsLoggedIn && authManager.CurrentUser != null)
                {
                    GUILayout.Label($"用户: {authManager.CurrentUser.DisplayName}");
                }
            }

            GUILayout.Space(20);

            // 功能按钮
            if (GUILayout.Button("数字人列表", GUILayout.Height(40)))
            {
                Debug.Log("[MainUI] 点击数字人列表按钮");
            }

            if (GUILayout.Button("添加数字人", GUILayout.Height(40)))
            {
                Debug.Log("[MainUI] 点击添加数字人按钮");
            }

            if (GUILayout.Button("系统设置", GUILayout.Height(40)))
            {
                Debug.Log("[MainUI] 点击系统设置按钮");
            }

            if (GUILayout.Button("语音设置", GUILayout.Height(40)))
            {
                Debug.Log("[MainUI] 点击语音设置按钮");
            }

            GUILayout.Space(20);

            GUILayout.Label("=== 调试信息 ===");
            GUILayout.Label($"屏幕分辨率: {Screen.width}x{Screen.height}");
            GUILayout.Label($"平台: {Application.platform}");
            GUILayout.Label($"Unity版本: {Application.unityVersion}");

            GUILayout.EndArea();
        }

        /// <summary>
        /// 组件销毁时的清理
        /// </summary>
        protected override void OnDestroy()
        {
            // 取消事件订阅
            if (authManager != null)
            {
                authManager.OnUserLoggedIn -= OnUserLoggedIn;
                authManager.OnUserLoggedOut -= OnUserLoggedOut;
            }

            enhancedLogger?.Info("增强版MVP管理器销毁");

            base.OnDestroy();
        }
    }
}
