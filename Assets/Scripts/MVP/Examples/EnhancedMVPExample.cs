using UnityEngine;
using DigitalHuman.MVP;
using DigitalHuman.Core.Authentication;
using DigitalHuman.Core.Logging;
using AuthLogger = DigitalHuman.Core.Logging.ILogger;

namespace DigitalHuman.MVP.Examples
{
    /// <summary>
    /// 增强版MVP系统演示
    /// 展示如何使用增强版MVP管理器和相关功能
    /// </summary>
    public class EnhancedMVPExample : MonoBehaviour
    {
        [Header("演示配置")]
        /// <summary>
        /// 是否自动开始演示
        /// </summary>
        public bool autoStartDemo = true;
        
        /// <summary>
        /// 演示步骤间隔时间
        /// </summary>
        public float demoStepInterval = 3f;
        
        /// <summary>
        /// 是否显示详细日志
        /// </summary>
        public bool showDetailedLogs = true;

        [Header("UI预制体")]
        /// <summary>
        /// 状态UI预制体
        /// </summary>
        public GameObject statusUIPrefab;

        // 私有字段
        private EnhancedMVPManager mvpManager;
        private MonoBehaviour statusUI;
        private AuthLogger logger;
        private int currentDemoStep = 0;

        /// <summary>
        /// 演示启动
        /// </summary>
        void Start()
        {
            InitializeDemo();
            
            if (autoStartDemo)
            {
                StartDemo();
            }
        }

        /// <summary>
        /// 初始化演示
        /// </summary>
        private void InitializeDemo()
        {
            Debug.Log("[EnhancedMVPExample] 初始化增强版MVP演示");
            
            // 获取或创建MVP管理器
            SetupMVPManager();
            
            // 创建状态UI
            CreateStatusUI();
            
            // 获取日志记录器
            try
            {
                var logManager = LogManager.Instance;
                logger = logManager?.GetLogger("MVPExample");
                logger?.Info("增强版MVP演示初始化完成");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[EnhancedMVPExample] 无法获取日志记录器: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置MVP管理器
        /// </summary>
        private void SetupMVPManager()
        {
            // 查找现有的增强版MVP管理器
            mvpManager = FindObjectOfType<EnhancedMVPManager>();
            
            if (mvpManager == null)
            {
                // 创建新的增强版MVP管理器
                var mvpObject = new GameObject("EnhancedMVPManager");
                mvpManager = mvpObject.AddComponent<EnhancedMVPManager>();
                
                // 配置MVP管理器
                mvpManager.EnableDebugMode = showDetailedLogs;
                mvpManager.AppVersion = "1.0.0-Enhanced";
                mvpManager.EnableAuthentication = true;
                mvpManager.EnableDataSync = true;
                mvpManager.EnableUIManagement = true;
                mvpManager.AutoLoginDemoUser = false; // 手动控制登录演示
                
                Debug.Log("[EnhancedMVPExample] 创建了新的增强版MVP管理器");
            }
            else
            {
                Debug.Log("[EnhancedMVPExample] 找到现有的增强版MVP管理器");
            }
        }

        /// <summary>
        /// 创建状态UI
        /// </summary>
        private void CreateStatusUI()
        {
            if (statusUIPrefab != null)
            {
                var uiObject = Instantiate(statusUIPrefab);
                statusUI = uiObject.GetComponent<MonoBehaviour>();

                if (statusUI != null)
                {
                    Debug.Log("[EnhancedMVPExample] 状态UI创建成功");
                }
            }
            else
            {
                // 创建简单的状态显示
                CreateSimpleStatusDisplay();
            }
        }

        /// <summary>
        /// 创建简单的状态显示
        /// </summary>
        private void CreateSimpleStatusDisplay()
        {
            var canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                // 创建Canvas
                var canvasObject = new GameObject("DemoCanvas");
                canvas = canvasObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObject.AddComponent<UnityEngine.UI.CanvasScaler>();
                canvasObject.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            }

            Debug.Log("[EnhancedMVPExample] 创建了简单状态显示");
        }

        /// <summary>
        /// 开始演示
        /// </summary>
        [ContextMenu("开始演示")]
        public void StartDemo()
        {
            Debug.Log("[EnhancedMVPExample] 开始增强版MVP功能演示");
            logger?.Info("开始增强版MVP功能演示");
            
            currentDemoStep = 0;
            InvokeRepeating(nameof(ExecuteNextDemoStep), 1f, demoStepInterval);
        }

        /// <summary>
        /// 执行下一个演示步骤
        /// </summary>
        private void ExecuteNextDemoStep()
        {
            currentDemoStep++;
            
            switch (currentDemoStep)
            {
                case 1:
                    DemoStep1_ShowSystemStatus();
                    break;
                case 2:
                    DemoStep2_TestAuthentication();
                    break;
                case 3:
                    DemoStep3_TestDataSync();
                    break;
                case 4:
                    DemoStep4_TestUIInteraction();
                    break;
                case 5:
                    DemoStep5_ShowFinalStatus();
                    break;
                default:
                    CompleteDemoSteps();
                    break;
            }
        }

        /// <summary>
        /// 演示步骤1：显示系统状态
        /// </summary>
        private void DemoStep1_ShowSystemStatus()
        {
            Debug.Log("=== 演示步骤1：系统状态展示 ===");
            logger?.Info("演示步骤1：系统状态展示");
            
            if (mvpManager != null)
            {
                string status = mvpManager.GetEnhancedSystemStatus();
                Debug.Log($"系统状态:\n{status}");
            }
        }

        /// <summary>
        /// 演示步骤2：测试认证功能
        /// </summary>
        private async void DemoStep2_TestAuthentication()
        {
            Debug.Log("=== 演示步骤2：认证功能测试 ===");
            logger?.Info("演示步骤2：认证功能测试");
            
            try
            {
                var authManager = AuthenticationManager.Instance;
                if (authManager != null)
                {
                    // 测试登录
                    var loginResult = await authManager.LoginAsync("demo_user", "demo_pass");
                    
                    if (loginResult.IsSuccess)
                    {
                        Debug.Log($"✅ 登录成功: {loginResult.UserInfo.DisplayName}");
                        logger?.Info($"演示登录成功: {loginResult.UserInfo.DisplayName}");
                    }
                    else
                    {
                        Debug.Log($"❌ 登录失败: {loginResult.ErrorMessage}");
                        logger?.Warning($"演示登录失败: {loginResult.ErrorMessage}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"认证测试异常: {ex.Message}");
                logger?.Error($"认证测试异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示步骤3：测试数据同步
        /// </summary>
        private async void DemoStep3_TestDataSync()
        {
            Debug.Log("=== 演示步骤3：数据同步测试 ===");
            logger?.Info("演示步骤3：数据同步测试");
            
            try
            {
                var dataSyncManager = DigitalHuman.Core.DataSync.DataSyncManager.Instance;
                if (dataSyncManager != null)
                {
                    var syncResult = await dataSyncManager.StartSyncAsync(DigitalHuman.Core.DataSync.Models.SyncType.Full);
                    
                    if (syncResult.IsSuccess)
                    {
                        Debug.Log($"✅ 数据同步成功，耗时: {syncResult.Duration.TotalSeconds:F2}秒");
                        logger?.Info($"演示数据同步成功，耗时: {syncResult.Duration.TotalSeconds:F2}秒");
                    }
                    else
                    {
                        Debug.Log($"❌ 数据同步失败: {syncResult.ErrorMessage}");
                        logger?.Warning($"演示数据同步失败: {syncResult.ErrorMessage}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"数据同步测试异常: {ex.Message}");
                logger?.Error($"数据同步测试异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示步骤4：测试UI交互
        /// </summary>
        private void DemoStep4_TestUIInteraction()
        {
            Debug.Log("=== 演示步骤4：UI交互测试 ===");
            logger?.Info("演示步骤4：UI交互测试");
            
            if (statusUI != null)
            {
                Debug.Log("✅ 状态UI正常运行");
                logger?.Info("状态UI交互测试完成");
            }
            else
            {
                Debug.Log("⚠️ 状态UI未创建，跳过UI交互测试");
                logger?.Warning("状态UI未创建，跳过UI交互测试");
            }
        }

        /// <summary>
        /// 演示步骤5：显示最终状态
        /// </summary>
        private void DemoStep5_ShowFinalStatus()
        {
            Debug.Log("=== 演示步骤5：最终状态展示 ===");
            logger?.Info("演示步骤5：最终状态展示");
            
            if (mvpManager != null)
            {
                string finalStatus = mvpManager.GetEnhancedSystemStatus();
                Debug.Log($"最终系统状态:\n{finalStatus}");
                logger?.Info("最终状态展示完成");
            }
        }

        /// <summary>
        /// 完成演示步骤
        /// </summary>
        private void CompleteDemoSteps()
        {
            CancelInvoke(nameof(ExecuteNextDemoStep));
            
            Debug.Log("🎉 增强版MVP功能演示完成！");
            logger?.Info("增强版MVP功能演示完成");
            
            // 显示演示总结
            ShowDemoSummary();
        }

        /// <summary>
        /// 显示演示总结
        /// </summary>
        private void ShowDemoSummary()
        {
            var summary = new System.Text.StringBuilder();
            summary.AppendLine("========================================");
            summary.AppendLine("    增强版MVP演示总结");
            summary.AppendLine("========================================");
            summary.AppendLine($"演示时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            summary.AppendLine($"演示步骤: {currentDemoStep} 个");
            summary.AppendLine($"MVP管理器: {(mvpManager != null ? "✅ 正常" : "❌ 异常")}");
            summary.AppendLine($"状态UI: {(statusUI != null ? "✅ 正常" : "⚠️ 未创建")}");
            summary.AppendLine();
            summary.AppendLine("演示功能:");
            summary.AppendLine("  ✅ 系统状态监控");
            summary.AppendLine("  ✅ 用户认证管理");
            summary.AppendLine("  ✅ 数据同步功能");
            summary.AppendLine("  ✅ UI交互展示");
            summary.AppendLine("  ✅ 日志系统集成");
            summary.AppendLine("========================================");
            
            Debug.Log(summary.ToString());
            logger?.Info("演示总结已生成");
        }

        /// <summary>
        /// 停止演示
        /// </summary>
        [ContextMenu("停止演示")]
        public void StopDemo()
        {
            CancelInvoke();
            Debug.Log("[EnhancedMVPExample] 演示已停止");
            logger?.Info("演示已手动停止");
        }

        /// <summary>
        /// 重置演示
        /// </summary>
        [ContextMenu("重置演示")]
        public void ResetDemo()
        {
            StopDemo();
            currentDemoStep = 0;
            Debug.Log("[EnhancedMVPExample] 演示已重置");
            logger?.Info("演示已重置");
        }

        /// <summary>
        /// 组件销毁时的清理
        /// </summary>
        void OnDestroy()
        {
            CancelInvoke();
            logger?.Info("增强版MVP演示组件销毁");
        }
    }
}
