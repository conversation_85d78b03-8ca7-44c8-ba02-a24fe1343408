using UnityEngine;

namespace DigitalHuman.MVP.Examples
{
    /// <summary>
    /// MinimalMVPManager 使用示例
    /// 展示如何正确使用和扩展 MinimalMVPManager
    /// </summary>
    public class MinimalMVPManagerExample : MonoBehaviour
    {
        [Header("示例配置")]
        [SerializeField] private bool autoStartExample = true;
        [SerializeField] private float statusCheckInterval = 5f;
        
        private MinimalMVPManager mvpManager;
        
        /// <summary>
        /// 示例启动
        /// </summary>
        void Start()
        {
            if (autoStartExample)
            {
                StartExample();
            }
        }
        
        /// <summary>
        /// 开始示例演示
        /// </summary>
        [ContextMenu("开始示例")]
        public void StartExample()
        {
            Debug.Log("[MVPExample] 开始 MinimalMVPManager 使用示例");
            
            // 1. 查找或创建 MVP 管理器
            SetupMVPManager();
            
            // 2. 配置 MVP 管理器
            ConfigureMVPManager();
            
            // 3. 开始状态监控
            StartStatusMonitoring();
            
            Debug.Log("[MVPExample] 示例启动完成");
        }
        
        /// <summary>
        /// 设置MVP管理器
        /// </summary>
        private void SetupMVPManager()
        {
            // 查找现有的 MVP 管理器
            mvpManager = FindObjectOfType<MinimalMVPManager>();
            
            if (mvpManager == null)
            {
                // 如果不存在，创建一个新的
                GameObject mvpObject = new GameObject("MinimalMVPManager");
                mvpManager = mvpObject.AddComponent<MinimalMVPManager>();
                Debug.Log("[MVPExample] 创建了新的 MinimalMVPManager 实例");
            }
            else
            {
                Debug.Log("[MVPExample] 找到现有的 MinimalMVPManager 实例");
            }
        }
        
        /// <summary>
        /// 配置MVP管理器
        /// </summary>
        private void ConfigureMVPManager()
        {
            if (mvpManager == null) return;
            
            // 根据构建类型配置调试模式
            #if UNITY_EDITOR
                mvpManager.EnableDebugMode = true;
                mvpManager.AppVersion = $"{Application.version}-Editor";
            #elif DEVELOPMENT_BUILD
                mvpManager.EnableDebugMode = true;
                mvpManager.AppVersion = $"{Application.version}-Development";
            #else
                mvpManager.EnableDebugMode = false;
                mvpManager.AppVersion = $"{Application.version}-Release";
            #endif
            
            Debug.Log($"[MVPExample] MVP管理器配置完成 - 调试模式: {mvpManager.EnableDebugMode}, 版本: {mvpManager.AppVersion}");
        }
        
        /// <summary>
        /// 开始状态监控
        /// </summary>
        private void StartStatusMonitoring()
        {
            if (statusCheckInterval > 0)
            {
                InvokeRepeating(nameof(CheckSystemStatus), 0f, statusCheckInterval);
                Debug.Log($"[MVPExample] 开始状态监控，检查间隔: {statusCheckInterval}秒");
            }
        }
        
        /// <summary>
        /// 检查系统状态
        /// </summary>
        private void CheckSystemStatus()
        {
            if (mvpManager == null)
            {
                Debug.LogWarning("[MVPExample] MVP管理器实例丢失");
                return;
            }
            
            string status = mvpManager.GetSystemStatus();
            Debug.Log($"[MVPExample] 系统状态检查: {status}");
            
            // 检查系统健康状况
            CheckSystemHealth();
        }
        
        /// <summary>
        /// 检查系统健康状况
        /// </summary>
        private void CheckSystemHealth()
        {
            // 检查内存使用
            long memoryUsage = System.GC.GetTotalMemory(false);
            float memoryMB = memoryUsage / 1024f / 1024f;
            
            if (memoryMB > 100f) // 如果内存使用超过100MB
            {
                Debug.LogWarning($"[MVPExample] 内存使用较高: {memoryMB:F2} MB");
            }
            
            // 检查帧率
            float currentFPS = 1f / Time.unscaledDeltaTime;
            if (currentFPS < 30f)
            {
                Debug.LogWarning($"[MVPExample] 帧率较低: {currentFPS:F1} FPS");
            }
        }
        
        /// <summary>
        /// 重启系统示例
        /// </summary>
        [ContextMenu("重启系统")]
        public void RestartSystemExample()
        {
            if (mvpManager == null)
            {
                Debug.LogError("[MVPExample] MVP管理器实例不存在");
                return;
            }
            
            Debug.Log("[MVPExample] 开始重启系统示例");
            
            // 重启 MVP 系统
            mvpManager.RestartMVPSystem();
            
            Debug.Log("[MVPExample] 系统重启完成");
        }
        
        /// <summary>
        /// 获取系统信息示例
        /// </summary>
        [ContextMenu("获取系统信息")]
        public void GetSystemInfoExample()
        {
            if (mvpManager == null)
            {
                Debug.LogError("[MVPExample] MVP管理器实例不存在");
                return;
            }
            
            Debug.Log("[MVPExample] === 系统信息 ===");
            
            // 获取基本状态
            string status = mvpManager.GetSystemStatus();
            Debug.Log($"状态: {status}");
            
            // 获取系统信息
            Debug.Log($"Unity版本: {Application.unityVersion}");
            Debug.Log($"运行平台: {Application.platform}");
            Debug.Log($"目标帧率: {Application.targetFrameRate}");
            Debug.Log($"垂直同步: {QualitySettings.vSyncCount}");
            
            // 获取性能信息
            long memoryUsage = System.GC.GetTotalMemory(false);
            Debug.Log($"内存使用: {memoryUsage / 1024 / 1024} MB");
            Debug.Log($"当前帧率: {1f / Time.unscaledDeltaTime:F1} FPS");
            
            Debug.Log("[MVPExample] === 系统信息结束 ===");
        }
        
        /// <summary>
        /// 测试异常处理示例
        /// </summary>
        [ContextMenu("测试异常处理")]
        public void TestExceptionHandlingExample()
        {
            Debug.Log("[MVPExample] 开始异常处理测试");
            
            try
            {
                // 模拟一个可能出错的操作
                SimulateRiskyOperation();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[MVPExample] 捕获异常: {ex.Message}");
                
                // 尝试恢复操作
                RecoverFromError();
            }
        }
        
        /// <summary>
        /// 模拟风险操作
        /// </summary>
        private void SimulateRiskyOperation()
        {
            // 随机决定是否抛出异常
            if (Random.Range(0f, 1f) > 0.5f)
            {
                throw new System.InvalidOperationException("模拟的操作异常");
            }
            
            Debug.Log("[MVPExample] 风险操作执行成功");
        }
        
        /// <summary>
        /// 从错误中恢复
        /// </summary>
        private void RecoverFromError()
        {
            Debug.Log("[MVPExample] 开始错误恢复流程");
            
            // 检查 MVP 管理器状态
            if (mvpManager != null)
            {
                string status = mvpManager.GetSystemStatus();
                Debug.Log($"[MVPExample] 当前系统状态: {status}");
                
                // 如果需要，可以重启系统
                if (status.Contains("未初始化"))
                {
                    Debug.Log("[MVPExample] 检测到系统未初始化，尝试重启");
                    mvpManager.RestartMVPSystem();
                }
            }
            
            Debug.Log("[MVPExample] 错误恢复完成");
        }
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void OnDestroy()
        {
            // 停止状态监控
            CancelInvoke(nameof(CheckSystemStatus));
            
            Debug.Log("[MVPExample] 示例组件已清理");
        }
        
        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        void OnApplicationQuit()
        {
            Debug.Log("[MVPExample] 应用程序退出，停止示例");
        }
    }
}