using UnityEngine;
using System;

namespace DigitalHuman.MVP
{
    /// <summary>
    /// 最小MVP管理器 - 数字人管理系统的核心入口点
    /// 负责系统的基础初始化、配置管理和生命周期控制
    /// </summary>
    public class MinimalMVPManager : MonoBehaviour
    {
        [Header("MVP配置")]
        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public bool EnableDebugMode = true;
        
        /// <summary>
        /// 应用程序版本
        /// </summary>
        public string AppVersion = "1.0.0-MVP";
        
        /// <summary>
        /// 目标帧率
        /// </summary>
        public int TargetFrameRate = 60;
        
        /// <summary>
        /// 是否启用垂直同步
        /// </summary>
        public bool EnableVSync = true;
        
        // 私有字段
        private bool isInitialized = false;
        private DateTime startTime;
        private DigitalHuman.Core.Logging.ILogger logger;
        
        /// <summary>
        /// 系统启动时的初始化
        /// </summary>
        protected virtual void Awake()
        {
            // 确保只有一个实例
            if (FindObjectsOfType<MinimalMVPManager>().Length > 1)
            {
                Debug.LogWarning("[MinimalMVP] 检测到多个MinimalMVPManager实例，销毁重复实例");
                Destroy(gameObject);
                return;
            }
            
            // 保持跨场景存在
            DontDestroyOnLoad(gameObject);
            
            // 记录启动时间
            startTime = DateTime.Now;
            
            // 初始化系统
            InitializeMVP();
        }
        
        /// <summary>
        /// 系统启动后的配置
        /// </summary>
        protected virtual void Start()
        {
            if (EnableDebugMode)
            {
                Debug.Log($"[MinimalMVP] 数字人管理系统启动完成 - 版本: {AppVersion}");
                Debug.Log($"[MinimalMVP] 启动时间: {startTime:yyyy-MM-dd HH:mm:ss}");
                Debug.Log($"[MinimalMVP] Unity版本: {Application.unityVersion}");
                Debug.Log($"[MinimalMVP] 平台: {Application.platform}");
            }
            
            // 初始化日志系统
            InitializeLogging();
            
            // 显示欢迎信息
            ShowWelcomeMessage();
        }
        
        /// <summary>
        /// 初始化MVP系统
        /// </summary>
        private void InitializeMVP()
        {
            if (isInitialized)
            {
                return;
            }
            
            try
            {
                if (EnableDebugMode)
                {
                    Debug.Log("[MinimalMVP] 开始初始化MVP系统...");
                }
                
                // 配置Unity应用程序参数
                Application.targetFrameRate = TargetFrameRate;
                QualitySettings.vSyncCount = EnableVSync ? 1 : 0;
                
                // 配置应用程序行为
                Application.runInBackground = true;
                
                isInitialized = true;
                
                if (EnableDebugMode)
                {
                    Debug.Log("[MinimalMVP] MVP系统初始化完成");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MinimalMVP] 初始化失败: {ex.Message}");
                isInitialized = false;
            }
        }
        
        /// <summary>
        /// 初始化日志系统
        /// </summary>
        private void InitializeLogging()
        {
            try
            {
                var logManager = DigitalHuman.Core.Logging.LogManager.Instance;
                if (logManager != null)
                {
                    logger = logManager.GetLogger("MVP");
                    logger.Info($"MVP管理器启动 - 版本: {AppVersion}");
                    
                    if (EnableDebugMode)
                    {
                        Debug.Log("[MinimalMVP] 日志系统初始化成功");
                    }
                }
                else
                {
                    Debug.LogWarning("[MinimalMVP] 日志管理器未找到");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MinimalMVP] 日志系统初始化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 显示欢迎信息
        /// </summary>
        private void ShowWelcomeMessage()
        {
            if (EnableDebugMode)
            {
                Debug.Log("========================================");
                Debug.Log("    数字人管理系统 - MVP版本");
                Debug.Log($"    版本: {AppVersion}");
                Debug.Log($"    启动时间: {DateTime.Now:HH:mm:ss}");
                Debug.Log("========================================");
            }
            
            logger?.Info("数字人管理系统MVP版本启动完成");
        }
        
        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <returns>系统状态字符串</returns>
        public string GetSystemStatus()
        {
            var uptime = DateTime.Now - startTime;
            
            return $"系统状态: {(isInitialized ? "已初始化" : "未初始化")}\n" +
                   $"版本: {AppVersion}\n" +
                   $"运行时间: {uptime.TotalMinutes:F1} 分钟\n" +
                   $"帧率: {Application.targetFrameRate} FPS\n" +
                   $"垂直同步: {(QualitySettings.vSyncCount > 0 ? "启用" : "禁用")}\n" +
                   $"调试模式: {(EnableDebugMode ? "启用" : "禁用")}";
        }
        
        /// <summary>
        /// 重启MVP系统
        /// </summary>
        public void RestartMVPSystem()
        {
            if (EnableDebugMode)
            {
                Debug.Log("[MinimalMVP] 重启MVP系统...");
            }
            
            logger?.Info("重启MVP系统");
            
            isInitialized = false;
            startTime = DateTime.Now;
            InitializeMVP();
            
            if (EnableDebugMode)
            {
                Debug.Log("[MinimalMVP] MVP系统重启完成");
            }
        }
        
        /// <summary>
        /// 应用程序暂停时的处理
        /// </summary>
        /// <param name="pauseStatus">暂停状态</param>
        void OnApplicationPause(bool pauseStatus)
        {
            if (EnableDebugMode)
            {
                Debug.Log($"[MinimalMVP] 应用程序暂停状态: {pauseStatus}");
            }
            
            logger?.Info($"应用程序暂停状态变化: {pauseStatus}");
        }
        
        /// <summary>
        /// 应用程序焦点变化时的处理
        /// </summary>
        /// <param name="hasFocus">是否有焦点</param>
        void OnApplicationFocus(bool hasFocus)
        {
            if (EnableDebugMode)
            {
                Debug.Log($"[MinimalMVP] 应用程序焦点状态: {hasFocus}");
            }
            
            logger?.Info($"应用程序焦点状态变化: {hasFocus}");
        }
        
        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        void OnApplicationQuit()
        {
            var uptime = DateTime.Now - startTime;
            
            if (EnableDebugMode)
            {
                Debug.Log($"[MinimalMVP] 应用程序退出，运行时间: {uptime.TotalMinutes:F1} 分钟");
            }
            
            logger?.Info($"应用程序退出，总运行时间: {uptime.TotalMinutes:F1} 分钟");
        }
        
        /// <summary>
        /// 组件销毁时的清理
        /// </summary>
        protected virtual void OnDestroy()
        {
            if (EnableDebugMode)
            {
                Debug.Log("[MinimalMVP] MinimalMVPManager 组件销毁");
            }
        }
    }
}
