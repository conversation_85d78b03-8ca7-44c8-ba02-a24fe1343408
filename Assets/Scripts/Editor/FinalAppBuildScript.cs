using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.SceneManagement;
using System.IO;

namespace DigitalHuman.Editor
{
    public class FinalAppBuildScript
    {
        public static void BuildFinalAuthApp()
        {
            Debug.Log("[FinalApp] 开始构建最终认证应用...");
            
            try
            {
                // 创建专用场景
                CreateFinalAuthScene();
                
                // 构建设置
                string buildPath = Path.Combine(Application.dataPath, "../Builds/FinalAuthApp/DigitalHuman_Authentication.app");
                string[] scenes = { "Assets/Scenes/FinalAuthScene.unity" };
                
                // 确保构建目录存在
                string buildDir = Path.GetDirectoryName(buildPath);
                if (!Directory.Exists(buildDir))
                {
                    Directory.CreateDirectory(buildDir);
                }
                
                // 配置播放器设置
                PlayerSettings.productName = "数字人认证系统";
                PlayerSettings.companyName = "DigitalHuman Team";
                PlayerSettings.bundleVersion = "1.0.0";
                PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.authentication");
                
                // 分辨率和显示设置
                PlayerSettings.defaultIsNativeResolution = false;
                PlayerSettings.runInBackground = true;
                PlayerSettings.defaultScreenWidth = 1280;
                PlayerSettings.defaultScreenHeight = 720;
                PlayerSettings.resizableWindow = true;
                PlayerSettings.fullScreenMode = FullScreenMode.Windowed;
                
                // 优化设置 - 禁用代码剥离以避免问题
                PlayerSettings.stripEngineCode = false;
                PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Disabled);
                
                // 构建选项
                BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = buildPath,
                    target = BuildTarget.StandaloneOSX,
                    options = BuildOptions.Development
                };
                
                // 执行构建
                var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                
                if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    Debug.Log($"[FinalApp] 构建成功！位置: {buildPath}");
                    Debug.Log($"[FinalApp] 构建大小: {report.summary.totalSize} bytes");
                    EditorUtility.RevealInFinder(buildPath);
                }
                else
                {
                    Debug.LogError($"[FinalApp] 构建失败: {report.summary.result}");
                    
                    // 显示详细错误
                    foreach (var step in report.steps)
                    {
                        foreach (var message in step.messages)
                        {
                            if (message.type == UnityEngine.LogType.Error)
                            {
                                Debug.LogError($"[FinalApp] 错误: {message.content}");
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[FinalApp] 构建异常: {ex.Message}");
            }
        }
        
        private static void CreateFinalAuthScene()
        {
            Debug.Log("[FinalApp] 创建最终认证场景...");
            
            // 创建新场景
            var scene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
            
            // 创建主摄像机
            var cameraGO = new GameObject("Main Camera");
            var camera = cameraGO.AddComponent<Camera>();
            camera.backgroundColor = new Color(0.125f, 0.125f, 0.125f, 1f);
            camera.clearFlags = CameraClearFlags.SolidColor;
            cameraGO.tag = "MainCamera";
            cameraGO.transform.position = new Vector3(0, 1, -10);
            
            // 创建UI根对象
            var uiRootGO = new GameObject("UI Root");
            var uiDocument = uiRootGO.AddComponent<UIDocument>();
            
            // 尝试设置UI文档 - 优先使用主UI
            string[] uiPaths = {
                "Assets/UI/Main/MainUI.uxml",
                "Assets/UI/AuthDemo.uxml"
            };
            
            bool uiLoaded = false;
            foreach (string uiPath in uiPaths)
            {
                var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uiPath);
                if (visualTreeAsset != null)
                {
                    uiDocument.visualTreeAsset = visualTreeAsset;
                    Debug.Log($"[FinalApp] UI文档设置成功: {uiPath}");
                    uiLoaded = true;
                    break;
                }
            }
            
            if (!uiLoaded)
            {
                Debug.LogWarning("[FinalApp] 无法加载UI文档");
            }
            
            // 尝试添加UI管理器 - 优先使用主UI管理器
            string[] managerTypes = {
                "DigitalHuman.UI.MainUIManager",
                "DigitalHuman.UI.AuthDemoUIManager"
            };
            
            bool managerAdded = false;
            foreach (string managerType in managerTypes)
            {
                try
                {
                    var type = System.Type.GetType(managerType);
                    if (type != null)
                    {
                        uiRootGO.AddComponent(type);
                        Debug.Log($"[FinalApp] UI管理器添加成功: {managerType}");
                        managerAdded = true;
                        break;
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"[FinalApp] 无法添加管理器 {managerType}: {ex.Message}");
                }
            }
            
            if (!managerAdded)
            {
                Debug.LogWarning("[FinalApp] 无法添加UI管理器");
            }
            
            // 创建认证管理器对象
            var authManagerGO = new GameObject("Authentication Manager");
            try
            {
                var authManager = authManagerGO.AddComponent<DigitalHuman.Core.Authentication.AuthenticationManager>();
                Debug.Log("[FinalApp] AuthenticationManager添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FinalApp] 无法添加AuthenticationManager: {ex.Message}");
            }
            
            // 添加事件系统
            var eventSystemGO = new GameObject("Event System");
            try
            {
                var eventSystem = eventSystemGO.AddComponent<DigitalHuman.Core.EventSystem>();
                Debug.Log("[FinalApp] EventSystem添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FinalApp] 无法添加EventSystem: {ex.Message}");
            }
            
            // 添加日志管理器
            var logManagerGO = new GameObject("Log Manager");
            try
            {
                var logManager = logManagerGO.AddComponent<DigitalHuman.Core.Logging.LogManager>();
                Debug.Log("[FinalApp] LogManager添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FinalApp] 无法添加LogManager: {ex.Message}");
            }
            
            // 保存场景
            string scenePath = "Assets/Scenes/FinalAuthScene.unity";
            EditorSceneManager.SaveScene(scene, scenePath);
            
            Debug.Log($"[FinalApp] 最终认证场景已创建: {scenePath}");
        }
    }
}
