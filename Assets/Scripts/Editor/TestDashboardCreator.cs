using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using TMPro;
using DigitalHuman.UI;

namespace DigitalHuman.Editor
{
    /// <summary>
    /// 测试仪表板创建器 - 在编辑器中快速创建测试界面
    /// </summary>
    public static class TestDashboardCreator
    {
        /// <summary>
        /// 创建完整的测试仪表板UI
        /// </summary>
        [MenuItem("DigitalHuman/UI/Create Test Dashboard")]
        public static void CreateTestDashboard()
        {
            // 查找或创建Canvas
            Canvas canvas = Object.FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObject = new GameObject("Canvas");
                canvas = canvasObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObject.AddComponent<CanvasScaler>();
                canvasObject.AddComponent<GraphicRaycaster>();
                
                // 添加EventSystem
                if (Object.FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
                {
                    GameObject eventSystemObject = new GameObject("EventSystem");
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.EventSystem>();
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                }
            }
            
            // 创建主面板
            GameObject mainPanel = CreatePanel(canvas.transform, "TestDashboard", new Vector2(0, 0), new Vector2(1, 1));
            TestDashboard dashboard = mainPanel.AddComponent<TestDashboard>();
            
            // 设置主面板背景
            Image mainPanelImage = mainPanel.GetComponent<Image>();
            mainPanelImage.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);
            
            // 创建标题
            CreateTitle(mainPanel.transform, "数字人管理系统 - 测试仪表板");
            
            // 创建滚动视图
            GameObject scrollView = CreateScrollView(mainPanel.transform, "MainScrollView", 
                new Vector2(0, 0), new Vector2(1, 0.9f), new Vector2(0, 50));
            
            GameObject content = scrollView.transform.Find("Viewport/Content").gameObject;
            
            // 创建各个功能面板
            CreateSystemStatusPanel(content.transform, dashboard);
            CreateAuthenticationPanel(content.transform, dashboard);
            CreateDataSyncPanel(content.transform, dashboard);
            CreateLogPanel(content.transform, dashboard);
            CreateSystemControlPanel(content.transform, dashboard);
            
            // 设置Content的布局
            VerticalLayoutGroup contentLayout = content.GetComponent<VerticalLayoutGroup>();
            if (contentLayout == null)
            {
                contentLayout = content.AddComponent<VerticalLayoutGroup>();
            }
            contentLayout.spacing = 10;
            contentLayout.padding = new RectOffset(10, 10, 10, 10);
            contentLayout.childControlHeight = false;
            contentLayout.childControlWidth = true;
            contentLayout.childForceExpandHeight = false;
            contentLayout.childForceExpandWidth = true;
            
            // 添加ContentSizeFitter
            ContentSizeFitter contentSizeFitter = content.GetComponent<ContentSizeFitter>();
            if (contentSizeFitter == null)
            {
                contentSizeFitter = content.AddComponent<ContentSizeFitter>();
            }
            contentSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
            
            Debug.Log("测试仪表板创建完成！");
            Selection.activeGameObject = mainPanel;
        }
        
        /// <summary>
        /// 创建标题
        /// </summary>
        private static void CreateTitle(Transform parent, string titleText)
        {
            GameObject title = new GameObject("Title");
            title.transform.SetParent(parent);
            
            RectTransform titleRect = title.AddComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 0.9f);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.offsetMin = new Vector2(10, -40);
            titleRect.offsetMax = new Vector2(-10, -10);
            
            TextMeshProUGUI titleTextComponent = title.AddComponent<TextMeshProUGUI>();
            titleTextComponent.text = titleText;
            titleTextComponent.fontSize = 24;
            titleTextComponent.fontStyle = FontStyles.Bold;
            titleTextComponent.color = Color.white;
            titleTextComponent.alignment = TextAlignmentOptions.Center;
        }
        
        /// <summary>
        /// 创建系统状态面板
        /// </summary>
        private static void CreateSystemStatusPanel(Transform parent, TestDashboard dashboard)
        {
            GameObject panel = CreateSectionPanel(parent, "SystemStatusPanel", "系统状态", 200);
            
            // 创建状态显示文本
            GameObject statusText = CreateText(panel.transform, "SystemStatusText", "系统状态加载中...", 14);
            RectTransform statusRect = statusText.GetComponent<RectTransform>();
            statusRect.anchorMin = new Vector2(0, 0);
            statusRect.anchorMax = new Vector2(1, 1);
            statusRect.offsetMin = new Vector2(10, 40);
            statusRect.offsetMax = new Vector2(-10, -10);
            
            dashboard.systemStatusText = statusText.GetComponent<TextMeshProUGUI>();
        }
        
        /// <summary>
        /// 创建认证面板
        /// </summary>
        private static void CreateAuthenticationPanel(Transform parent, TestDashboard dashboard)
        {
            GameObject panel = CreateSectionPanel(parent, "AuthenticationPanel", "用户认证测试", 300);
            
            // 创建输入字段和按钮的容器
            GameObject inputContainer = new GameObject("InputContainer");
            inputContainer.transform.SetParent(panel.transform);
            SetupRectTransform(inputContainer, new Vector2(0, 0.6f), new Vector2(1, 1), new Vector2(10, -10), new Vector2(-10, -40));
            
            HorizontalLayoutGroup inputLayout = inputContainer.AddComponent<HorizontalLayoutGroup>();
            inputLayout.spacing = 10;
            inputLayout.padding = new RectOffset(0, 0, 0, 0);
            inputLayout.childControlWidth = true;
            inputLayout.childForceExpandWidth = true;
            
            // 用户名输入
            dashboard.usernameInput = CreateInputField(inputContainer.transform, "UsernameInput", "demo_user", "用户名");
            
            // 密码输入
            dashboard.passwordInput = CreateInputField(inputContainer.transform, "PasswordInput", "demo_pass", "密码");
            
            // 按钮容器
            GameObject buttonContainer = new GameObject("ButtonContainer");
            buttonContainer.transform.SetParent(panel.transform);
            SetupRectTransform(buttonContainer, new Vector2(0, 0.2f), new Vector2(1, 0.6f), new Vector2(10, 0), new Vector2(-10, 0));
            
            GridLayoutGroup buttonLayout = buttonContainer.AddComponent<GridLayoutGroup>();
            buttonLayout.cellSize = new Vector2(120, 30);
            buttonLayout.spacing = new Vector2(10, 10);
            buttonLayout.startCorner = GridLayoutGroup.Corner.UpperLeft;
            buttonLayout.startAxis = GridLayoutGroup.Axis.Horizontal;
            buttonLayout.childAlignment = TextAnchor.UpperLeft;
            
            // 创建按钮
            dashboard.loginButton = CreateButton(buttonContainer.transform, "LoginButton", "登录", Color.green);
            dashboard.logoutButton = CreateButton(buttonContainer.transform, "LogoutButton", "登出", Color.red);
            dashboard.validateSessionButton = CreateButton(buttonContainer.transform, "ValidateButton", "验证会话", Color.blue);
            dashboard.refreshTokenButton = CreateButton(buttonContainer.transform, "RefreshButton", "刷新令牌", Color.yellow);
            
            // 状态显示
            GameObject authStatusText = CreateText(panel.transform, "AuthStatusText", "认证状态加载中...", 12);
            SetupRectTransform(authStatusText, new Vector2(0, 0), new Vector2(1, 0.2f), new Vector2(10, 0), new Vector2(-10, 0));
            dashboard.authStatusText = authStatusText.GetComponent<TextMeshProUGUI>();
        }
        
        /// <summary>
        /// 创建数据同步面板
        /// </summary>
        private static void CreateDataSyncPanel(Transform parent, TestDashboard dashboard)
        {
            GameObject panel = CreateSectionPanel(parent, "DataSyncPanel", "数据同步测试", 250);
            
            // 按钮容器
            GameObject buttonContainer = new GameObject("ButtonContainer");
            buttonContainer.transform.SetParent(panel.transform);
            SetupRectTransform(buttonContainer, new Vector2(0, 0.6f), new Vector2(1, 1), new Vector2(10, -10), new Vector2(-10, -40));
            
            HorizontalLayoutGroup buttonLayout = buttonContainer.AddComponent<HorizontalLayoutGroup>();
            buttonLayout.spacing = 10;
            buttonLayout.childControlWidth = true;
            buttonLayout.childForceExpandWidth = true;
            
            // 创建按钮
            dashboard.startSyncButton = CreateButton(buttonContainer.transform, "StartSyncButton", "开始同步", Color.green);
            dashboard.stopSyncButton = CreateButton(buttonContainer.transform, "StopSyncButton", "停止同步", Color.red);
            dashboard.clearCacheButton = CreateButton(buttonContainer.transform, "ClearCacheButton", "清理缓存", Color.gray);
            
            // 进度条
            GameObject progressContainer = new GameObject("ProgressContainer");
            progressContainer.transform.SetParent(panel.transform);
            SetupRectTransform(progressContainer, new Vector2(0, 0.4f), new Vector2(1, 0.6f), new Vector2(10, 0), new Vector2(-10, 0));
            
            dashboard.syncProgressSlider = CreateSlider(progressContainer.transform, "ProgressSlider");
            dashboard.syncProgressText = CreateText(progressContainer.transform, "ProgressText", "同步进度: 0%", 12).GetComponent<TextMeshProUGUI>();
            
            // 状态显示
            GameObject syncStatusText = CreateText(panel.transform, "SyncStatusText", "同步状态加载中...", 12);
            SetupRectTransform(syncStatusText, new Vector2(0, 0), new Vector2(1, 0.4f), new Vector2(10, 0), new Vector2(-10, 0));
            dashboard.syncStatusText = syncStatusText.GetComponent<TextMeshProUGUI>();
        }

        /// <summary>
        /// 创建日志面板
        /// </summary>
        private static void CreateLogPanel(Transform parent, TestDashboard dashboard)
        {
            GameObject panel = CreateSectionPanel(parent, "LogPanel", "日志测试", 350);

            // 输入和按钮容器
            GameObject inputContainer = new GameObject("InputContainer");
            inputContainer.transform.SetParent(panel.transform);
            SetupRectTransform(inputContainer, new Vector2(0, 0.8f), new Vector2(1, 1), new Vector2(10, -10), new Vector2(-10, -40));

            HorizontalLayoutGroup inputLayout = inputContainer.AddComponent<HorizontalLayoutGroup>();
            inputLayout.spacing = 10;
            inputLayout.childControlWidth = true;
            inputLayout.childForceExpandWidth = true;

            // 日志消息输入
            dashboard.logMessageInput = CreateInputField(inputContainer.transform, "LogMessageInput", "测试日志消息", "日志消息");

            // 按钮容器
            GameObject buttonContainer = new GameObject("ButtonContainer");
            buttonContainer.transform.SetParent(panel.transform);
            SetupRectTransform(buttonContainer, new Vector2(0, 0.6f), new Vector2(1, 0.8f), new Vector2(10, 0), new Vector2(-10, 0));

            HorizontalLayoutGroup buttonLayout = buttonContainer.AddComponent<HorizontalLayoutGroup>();
            buttonLayout.spacing = 10;
            buttonLayout.childControlWidth = true;
            buttonLayout.childForceExpandWidth = true;

            // 创建日志按钮
            dashboard.logInfoButton = CreateButton(buttonContainer.transform, "LogInfoButton", "Info日志", Color.blue);
            dashboard.logWarningButton = CreateButton(buttonContainer.transform, "LogWarningButton", "Warning日志", Color.yellow);
            dashboard.logErrorButton = CreateButton(buttonContainer.transform, "LogErrorButton", "Error日志", Color.red);
            dashboard.clearLogsButton = CreateButton(buttonContainer.transform, "ClearLogsButton", "清理日志", Color.gray);

            // 日志显示区域
            GameObject logScrollView = CreateScrollView(panel.transform, "LogScrollView",
                new Vector2(0, 0.2f), new Vector2(1, 0.6f), Vector2.zero);

            GameObject logContent = logScrollView.transform.Find("Viewport/Content").gameObject;
            GameObject logDisplayText = CreateText(logContent.transform, "LogDisplayText", "日志显示区域", 10);

            // 设置日志文本的布局
            RectTransform logTextRect = logDisplayText.GetComponent<RectTransform>();
            logTextRect.anchorMin = new Vector2(0, 1);
            logTextRect.anchorMax = new Vector2(1, 1);
            logTextRect.pivot = new Vector2(0, 1);

            TextMeshProUGUI logTextComponent = logDisplayText.GetComponent<TextMeshProUGUI>();
            logTextComponent.alignment = TextAlignmentOptions.TopLeft;

            // 添加ContentSizeFitter到日志文本
            ContentSizeFitter logTextFitter = logDisplayText.AddComponent<ContentSizeFitter>();
            logTextFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

            dashboard.logScrollView = logScrollView.GetComponent<ScrollRect>();
            dashboard.logDisplayText = logTextComponent;

            // 状态显示
            GameObject logStatusText = CreateText(panel.transform, "LogStatusText", "日志状态加载中...", 12);
            SetupRectTransform(logStatusText, new Vector2(0, 0), new Vector2(1, 0.2f), new Vector2(10, 0), new Vector2(-10, 0));
            dashboard.logStatusText = logStatusText.GetComponent<TextMeshProUGUI>();
        }

        /// <summary>
        /// 创建系统控制面板
        /// </summary>
        private static void CreateSystemControlPanel(Transform parent, TestDashboard dashboard)
        {
            GameObject panel = CreateSectionPanel(parent, "SystemControlPanel", "系统控制", 150);

            // 按钮容器
            GameObject buttonContainer = new GameObject("ButtonContainer");
            buttonContainer.transform.SetParent(panel.transform);
            SetupRectTransform(buttonContainer, new Vector2(0, 0.4f), new Vector2(1, 1), new Vector2(10, -10), new Vector2(-10, -40));

            HorizontalLayoutGroup buttonLayout = buttonContainer.AddComponent<HorizontalLayoutGroup>();
            buttonLayout.spacing = 10;
            buttonLayout.childControlWidth = true;
            buttonLayout.childForceExpandWidth = true;

            // 创建按钮
            dashboard.restartSystemButton = CreateButton(buttonContainer.transform, "RestartButton", "重启系统", Color.magenta);
            dashboard.showSystemInfoButton = CreateButton(buttonContainer.transform, "SystemInfoButton", "系统信息", Color.cyan);

            // 调试模式切换
            GameObject toggleContainer = new GameObject("ToggleContainer");
            toggleContainer.transform.SetParent(panel.transform);
            SetupRectTransform(toggleContainer, new Vector2(0, 0), new Vector2(1, 0.4f), new Vector2(10, 0), new Vector2(-10, 0));

            dashboard.debugModeToggle = CreateToggle(toggleContainer.transform, "DebugModeToggle", "调试模式");
        }

        #region 辅助方法

        /// <summary>
        /// 创建面板
        /// </summary>
        private static GameObject CreatePanel(Transform parent, string name, Vector2 anchorMin, Vector2 anchorMax)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(parent);

            RectTransform rect = panel.AddComponent<RectTransform>();
            rect.anchorMin = anchorMin;
            rect.anchorMax = anchorMax;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            Image image = panel.AddComponent<Image>();
            image.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);

            return panel;
        }

        /// <summary>
        /// 创建区域面板
        /// </summary>
        private static GameObject CreateSectionPanel(Transform parent, string name, string title, float height)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(parent);

            RectTransform rect = panel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0, 0);
            rect.anchorMax = new Vector2(1, 0);
            rect.pivot = new Vector2(0.5f, 0);
            rect.sizeDelta = new Vector2(0, height);

            Image image = panel.AddComponent<Image>();
            image.color = new Color(0.3f, 0.3f, 0.3f, 0.9f);

            // 添加标题
            GameObject titleObj = CreateText(panel.transform, "Title", title, 16);
            RectTransform titleRect = titleObj.GetComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 1);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.offsetMin = new Vector2(10, -30);
            titleRect.offsetMax = new Vector2(-10, -5);

            TextMeshProUGUI titleText = titleObj.GetComponent<TextMeshProUGUI>();
            titleText.fontStyle = FontStyles.Bold;
            titleText.color = Color.white;

            return panel;
        }

        /// <summary>
        /// 创建滚动视图
        /// </summary>
        private static GameObject CreateScrollView(Transform parent, string name, Vector2 anchorMin, Vector2 anchorMax, Vector2 offset)
        {
            GameObject scrollView = new GameObject(name);
            scrollView.transform.SetParent(parent);

            SetupRectTransform(scrollView, anchorMin, anchorMax, offset, offset);

            Image scrollImage = scrollView.AddComponent<Image>();
            scrollImage.color = new Color(0.1f, 0.1f, 0.1f, 0.5f);

            ScrollRect scrollRect = scrollView.AddComponent<ScrollRect>();

            // 创建Viewport
            GameObject viewport = new GameObject("Viewport");
            viewport.transform.SetParent(scrollView.transform);
            SetupRectTransform(viewport, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            Image viewportImage = viewport.AddComponent<Image>();
            viewportImage.color = Color.clear;

            Mask viewportMask = viewport.AddComponent<Mask>();
            viewportMask.showMaskGraphic = false;

            // 创建Content
            GameObject content = new GameObject("Content");
            content.transform.SetParent(viewport.transform);
            SetupRectTransform(content, new Vector2(0, 1), new Vector2(1, 1), Vector2.zero, Vector2.zero);

            // 设置ScrollRect
            scrollRect.viewport = viewport.GetComponent<RectTransform>();
            scrollRect.content = content.GetComponent<RectTransform>();
            scrollRect.horizontal = false;
            scrollRect.vertical = true;

            return scrollView;
        }

        /// <summary>
        /// 创建文本
        /// </summary>
        private static GameObject CreateText(Transform parent, string name, string text, int fontSize)
        {
            GameObject textObj = new GameObject(name);
            textObj.transform.SetParent(parent);

            RectTransform rect = textObj.AddComponent<RectTransform>();

            TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = fontSize;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.TopLeft;

            return textObj;
        }

        /// <summary>
        /// 创建按钮
        /// </summary>
        private static Button CreateButton(Transform parent, string name, string text, Color color)
        {
            GameObject buttonObj = new GameObject(name);
            buttonObj.transform.SetParent(parent);

            RectTransform rect = buttonObj.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(120, 30);

            Image image = buttonObj.AddComponent<Image>();
            image.color = color;

            Button button = buttonObj.AddComponent<Button>();

            // 创建按钮文本
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform);

            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 12;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.Center;

            return button;
        }

        /// <summary>
        /// 创建输入字段
        /// </summary>
        private static TMP_InputField CreateInputField(Transform parent, string name, string text, string placeholder)
        {
            GameObject inputObj = new GameObject(name);
            inputObj.transform.SetParent(parent);

            RectTransform rect = inputObj.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(150, 30);

            Image image = inputObj.AddComponent<Image>();
            image.color = new Color(0.8f, 0.8f, 0.8f, 1f);

            TMP_InputField inputField = inputObj.AddComponent<TMP_InputField>();
            inputField.text = text;

            // 创建文本区域
            GameObject textArea = new GameObject("Text Area");
            textArea.transform.SetParent(inputObj.transform);
            SetupRectTransform(textArea, Vector2.zero, Vector2.one, new Vector2(5, 0), new Vector2(-5, 0));

            RectMask2D textMask = textArea.AddComponent<RectMask2D>();

            // 创建占位符文本
            GameObject placeholderObj = new GameObject("Placeholder");
            placeholderObj.transform.SetParent(textArea.transform);
            SetupRectTransform(placeholderObj, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            TextMeshProUGUI placeholderText = placeholderObj.AddComponent<TextMeshProUGUI>();
            placeholderText.text = placeholder;
            placeholderText.fontSize = 12;
            placeholderText.color = new Color(0.5f, 0.5f, 0.5f, 1f);
            placeholderText.alignment = TextAlignmentOptions.Left;

            // 创建输入文本
            GameObject inputTextObj = new GameObject("Text");
            inputTextObj.transform.SetParent(textArea.transform);
            SetupRectTransform(inputTextObj, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            TextMeshProUGUI inputText = inputTextObj.AddComponent<TextMeshProUGUI>();
            inputText.text = "";
            inputText.fontSize = 12;
            inputText.color = Color.black;
            inputText.alignment = TextAlignmentOptions.Left;

            // 设置InputField组件
            inputField.textViewport = textArea.GetComponent<RectTransform>();
            inputField.textComponent = inputText;
            inputField.placeholder = placeholderText;

            return inputField;
        }

        /// <summary>
        /// 创建滑动条
        /// </summary>
        private static Slider CreateSlider(Transform parent, string name)
        {
            GameObject sliderObj = new GameObject(name);
            sliderObj.transform.SetParent(parent);

            SetupRectTransform(sliderObj, new Vector2(0, 0.5f), new Vector2(1, 1), new Vector2(10, 0), new Vector2(-10, 0));

            Slider slider = sliderObj.AddComponent<Slider>();

            // 创建背景
            GameObject background = new GameObject("Background");
            background.transform.SetParent(sliderObj.transform);
            SetupRectTransform(background, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            Image backgroundImage = background.AddComponent<Image>();
            backgroundImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);

            // 创建填充区域
            GameObject fillArea = new GameObject("Fill Area");
            fillArea.transform.SetParent(sliderObj.transform);
            SetupRectTransform(fillArea, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            GameObject fill = new GameObject("Fill");
            fill.transform.SetParent(fillArea.transform);
            SetupRectTransform(fill, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            Image fillImage = fill.AddComponent<Image>();
            fillImage.color = Color.green;

            // 设置Slider
            slider.fillRect = fill.GetComponent<RectTransform>();
            slider.value = 0f;

            return slider;
        }

        /// <summary>
        /// 创建切换开关
        /// </summary>
        private static Toggle CreateToggle(Transform parent, string name, string text)
        {
            GameObject toggleObj = new GameObject(name);
            toggleObj.transform.SetParent(parent);

            SetupRectTransform(toggleObj, new Vector2(0, 0), new Vector2(1, 1), new Vector2(10, 0), new Vector2(-10, 0));

            Toggle toggle = toggleObj.AddComponent<Toggle>();

            // 创建背景
            GameObject background = new GameObject("Background");
            background.transform.SetParent(toggleObj.transform);
            SetupRectTransform(background, new Vector2(0, 0.5f), new Vector2(0, 0.5f), new Vector2(0, -10), new Vector2(20, 10));

            Image backgroundImage = background.AddComponent<Image>();
            backgroundImage.color = new Color(0.8f, 0.8f, 0.8f, 1f);

            // 创建勾选标记
            GameObject checkmark = new GameObject("Checkmark");
            checkmark.transform.SetParent(background.transform);
            SetupRectTransform(checkmark, Vector2.zero, Vector2.one, Vector2.zero, Vector2.zero);

            Image checkmarkImage = checkmark.AddComponent<Image>();
            checkmarkImage.color = Color.green;

            // 创建标签
            GameObject label = new GameObject("Label");
            label.transform.SetParent(toggleObj.transform);
            SetupRectTransform(label, new Vector2(0, 0), new Vector2(1, 1), new Vector2(25, 0), new Vector2(0, 0));

            TextMeshProUGUI labelText = label.AddComponent<TextMeshProUGUI>();
            labelText.text = text;
            labelText.fontSize = 12;
            labelText.color = Color.white;
            labelText.alignment = TextAlignmentOptions.Left;

            // 设置Toggle
            toggle.targetGraphic = backgroundImage;
            toggle.graphic = checkmarkImage;

            return toggle;
        }

        /// <summary>
        /// 设置RectTransform
        /// </summary>
        private static void SetupRectTransform(GameObject obj, Vector2 anchorMin, Vector2 anchorMax, Vector2 offsetMin, Vector2 offsetMax)
        {
            RectTransform rect = obj.GetComponent<RectTransform>();
            if (rect == null)
            {
                rect = obj.AddComponent<RectTransform>();
            }

            rect.anchorMin = anchorMin;
            rect.anchorMax = anchorMax;
            rect.offsetMin = offsetMin;
            rect.offsetMax = offsetMax;
        }

        #endregion
    }
}
