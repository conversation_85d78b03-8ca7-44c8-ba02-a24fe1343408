using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using UnityEditor.SceneManagement;

namespace DigitalHuman.Editor
{
    /// <summary>
    /// 创建场景UI的编辑器工具
    /// </summary>
    public class CreateSceneUI
    {
        [MenuItem("DigitalHuman/Create Scene Test UI")]
        public static void CreateTestUI()
        {
            Debug.Log("[CreateSceneUI] 开始创建场景测试UI");
            
            // 删除现有的测试UI
            var existingUI = GameObject.Find("SceneTestUI");
            if (existingUI != null)
            {
                GameObject.DestroyImmediate(existingUI);
            }
            
            // 创建主UI对象
            GameObject uiRoot = new GameObject("SceneTestUI");
            
            // 创建Canvas
            Canvas canvas = uiRoot.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 9999;
            
            CanvasScaler scaler = uiRoot.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            
            uiRoot.AddComponent<GraphicRaycaster>();
            
            // 创建EventSystem
            if (Object.FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
            {
                GameObject eventSystemObject = new GameObject("EventSystem");
                eventSystemObject.AddComponent<UnityEngine.EventSystems.EventSystem>();
                eventSystemObject.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
            }
            
            // 创建背景面板
            GameObject backgroundPanel = new GameObject("BackgroundPanel");
            backgroundPanel.transform.SetParent(uiRoot.transform, false);
            
            RectTransform bgRect = backgroundPanel.AddComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;
            
            Image bgImage = backgroundPanel.AddComponent<Image>();
            bgImage.color = new Color(1, 0, 0, 0.9f); // 红色背景
            
            // 创建状态文本
            GameObject statusTextObject = new GameObject("StatusText");
            statusTextObject.transform.SetParent(backgroundPanel.transform, false);
            
            RectTransform statusRect = statusTextObject.AddComponent<RectTransform>();
            statusRect.anchorMin = new Vector2(0, 0.3f);
            statusRect.anchorMax = new Vector2(1, 0.9f);
            statusRect.offsetMin = new Vector2(50, 0);
            statusRect.offsetMax = new Vector2(-50, 0);
            
            Text statusText = statusTextObject.AddComponent<Text>();
            statusText.text = "🎯 数字人管理系统\n场景测试界面\n\n✅ 如果您能看到这个红色界面\n说明UI系统工作正常！";
            statusText.fontSize = 32;
            statusText.color = Color.white;
            statusText.alignment = TextAnchor.MiddleCenter;
            statusText.fontStyle = FontStyle.Bold;
            statusText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            
            // 创建测试按钮
            GameObject buttonObject = new GameObject("TestButton");
            buttonObject.transform.SetParent(backgroundPanel.transform, false);
            
            RectTransform buttonRect = buttonObject.AddComponent<RectTransform>();
            buttonRect.anchoredPosition = new Vector2(0, -200);
            buttonRect.sizeDelta = new Vector2(300, 80);
            
            Image buttonImage = buttonObject.AddComponent<Image>();
            buttonImage.color = Color.green;
            
            Button button = buttonObject.AddComponent<Button>();
            
            // 按钮文本
            GameObject buttonTextObject = new GameObject("ButtonText");
            buttonTextObject.transform.SetParent(buttonObject.transform, false);
            
            RectTransform buttonTextRect = buttonTextObject.AddComponent<RectTransform>();
            buttonTextRect.anchorMin = Vector2.zero;
            buttonTextRect.anchorMax = Vector2.one;
            buttonTextRect.offsetMin = Vector2.zero;
            buttonTextRect.offsetMax = Vector2.zero;
            
            Text buttonText = buttonTextObject.AddComponent<Text>();
            buttonText.text = "点击测试";
            buttonText.fontSize = 24;
            buttonText.color = Color.white;
            buttonText.alignment = TextAnchor.MiddleCenter;
            buttonText.fontStyle = FontStyle.Bold;
            buttonText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            
            // 添加SceneTestUI组件
            DigitalHuman.UI.SceneTestUI sceneUI = uiRoot.AddComponent<DigitalHuman.UI.SceneTestUI>();
            sceneUI.canvas = canvas;
            sceneUI.statusText = statusText;
            sceneUI.testButton = button;
            
            // 标记场景为已修改
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            
            Debug.Log("[CreateSceneUI] 场景测试UI创建完成");
            
            // 选中创建的UI对象
            Selection.activeGameObject = uiRoot;
        }
        
        [MenuItem("DigitalHuman/Save Enhanced Scene")]
        public static void SaveEnhancedScene()
        {
            // 确保有测试UI
            if (GameObject.Find("SceneTestUI") == null)
            {
                CreateTestUI();
            }
            
            // 保存场景
            string scenePath = "Assets/Scenes/EnhancedMVPScene.unity";
            EditorSceneManager.SaveScene(EditorSceneManager.GetActiveScene(), scenePath);
            
            Debug.Log($"[CreateSceneUI] 增强版场景已保存到: {scenePath}");
        }
    }
}
