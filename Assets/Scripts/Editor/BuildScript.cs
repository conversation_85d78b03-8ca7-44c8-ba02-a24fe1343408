using UnityEngine;
using UnityEditor;
using UnityEditor.Build.Reporting;
using System.IO;

namespace DigitalHuman.Editor
{
    /// <summary>
    /// 构建脚本，用于创建可执行的应用程序
    /// </summary>
    public class BuildScript
    {
        /// <summary>
        /// 构建macOS应用程序
        /// </summary>
        [MenuItem("Build/Build macOS App")]
        public static void BuildMacOSApp()
        {
            BuildMacOSAppInternal();
        }
        
        /// <summary>
        /// 命令行构建macOS应用程序
        /// </summary>
        public static void BuildMacOSFromCommandLine()
        {
            BuildMacOSAppInternal();
        }
        
        /// <summary>
        /// 内部构建方法
        /// </summary>
        private static void BuildMacOSAppInternal()
        {
            // 构建设置
            string appName = "DigitalAvatarMVP";
            string buildPath = Path.Combine(Directory.GetCurrentDirectory(), "Builds", "macOS", appName + ".app");
            
            // 确保构建目录存在
            string buildDir = Path.GetDirectoryName(buildPath);
            if (!Directory.Exists(buildDir))
            {
                Directory.CreateDirectory(buildDir);
            }
            
            // 获取所有场景
            string[] scenes = GetScenePaths();
            
            if (scenes.Length == 0)
            {
                Debug.LogError("没有找到任何场景文件！请确保项目中至少有一个场景。");
                return;
            }
            
            // 构建选项
            BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
            {
                scenes = scenes,
                locationPathName = buildPath,
                target = BuildTarget.StandaloneOSX,
                options = BuildOptions.None
            };
            
            Debug.Log($"开始构建macOS应用程序...");
            Debug.Log($"构建路径: {buildPath}");
            Debug.Log($"包含场景: {string.Join(", ", scenes)}");
            
            // 执行构建
            BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
            BuildSummary summary = report.summary;

            if (summary.result == BuildResult.Succeeded)
            {
                Debug.Log($"构建成功！");
                Debug.Log($"构建大小: {summary.totalSize} bytes");
                Debug.Log($"构建时间: {summary.totalTime}");
                Debug.Log($"应用程序位置: {buildPath}");

                // 在Finder中显示构建结果
                EditorUtility.RevealInFinder(buildPath);
            }
            else
            {
                Debug.LogError($"构建失败: {summary.result}");
            }
        }
        
        /// <summary>
        /// 获取所有场景路径
        /// </summary>
        /// <returns>场景路径数组</returns>
        private static string[] GetScenePaths()
        {
            // 首先尝试从Build Settings获取场景
            EditorBuildSettingsScene[] buildScenes = EditorBuildSettings.scenes;
            if (buildScenes.Length > 0)
            {
                string[] scenePaths = new string[buildScenes.Length];
                for (int i = 0; i < buildScenes.Length; i++)
                {
                    scenePaths[i] = buildScenes[i].path;
                }
                return scenePaths;
            }
            
            // 如果Build Settings中没有场景，搜索项目中的所有场景
            string[] allScenes = System.IO.Directory.GetFiles("Assets", "*.unity", SearchOption.AllDirectories);
            
            if (allScenes.Length > 0)
            {
                Debug.Log($"在Build Settings中没有找到场景，使用项目中的所有场景: {string.Join(", ", allScenes)}");
                return allScenes;
            }
            
            return new string[0];
        }
        
        /// <summary>
        /// 构建增强版MVP应用
        /// </summary>
        [MenuItem("Build/Build Enhanced MVP")]
        public static void BuildEnhancedMVP()
        {
            Debug.Log("[BuildScript] 开始构建增强版MVP应用...");

            try
            {
                // 准备增强版场景
                PrepareEnhancedSceneForBuild();

                // 确保构建目录存在
                string buildPath = Path.Combine(Directory.GetCurrentDirectory(), "Builds", "EnhancedMVP");
                if (!Directory.Exists(buildPath))
                {
                    Directory.CreateDirectory(buildPath);
                }

                // 设置应用名称和路径
                string appName = "DigitalHumanEnhanced";
                string fullBuildPath = Path.Combine(buildPath, appName + ".app");

                // 获取场景
                string[] scenes = GetScenePaths();
                if (scenes.Length == 0)
                {
                    Debug.LogError("没有找到任何场景文件！");
                    return;
                }

                // 设置构建参数
                BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = fullBuildPath,
                    target = BuildTarget.StandaloneOSX,
                    options = BuildOptions.None
                };

                Debug.Log($"构建路径: {fullBuildPath}");
                Debug.Log($"包含场景: {string.Join(", ", scenes)}");

                // 执行构建
                BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                BuildSummary summary = report.summary;

                if (summary.result == BuildResult.Succeeded)
                {
                    Debug.Log($"[BuildScript] 增强版MVP构建成功！");
                    Debug.Log($"构建大小: {summary.totalSize / (1024 * 1024):F1} MB");
                    Debug.Log($"构建时间: {summary.totalTime.TotalSeconds:F1} 秒");
                    Debug.Log($"输出路径: {fullBuildPath}");

                    // 显示构建完成通知
                    EditorUtility.DisplayDialog("构建完成",
                        $"增强版MVP应用构建成功！\n\n" +
                        $"大小: {summary.totalSize / (1024 * 1024):F1} MB\n" +
                        $"路径: {fullBuildPath}", "确定");

                    // 在Finder中显示构建结果
                    EditorUtility.RevealInFinder(fullBuildPath);
                }
                else
                {
                    Debug.LogError($"[BuildScript] 增强版MVP构建失败: {summary.result}");
                    EditorUtility.DisplayDialog("构建失败",
                        $"增强版MVP应用构建失败！\n\n错误: {summary.result}", "确定");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[BuildScript] 增强版MVP构建异常: {ex.Message}");
                EditorUtility.DisplayDialog("构建异常",
                    $"构建过程中发生异常！\n\n{ex.Message}", "确定");
            }
        }

        /// <summary>
        /// 创建默认场景
        /// </summary>
        [MenuItem("Build/Create Default Scene")]
        public static void CreateDefaultScene()
        {
            // 创建新场景
            var scene = UnityEditor.SceneManagement.EditorSceneManager.NewScene(
                UnityEditor.SceneManagement.NewSceneSetup.DefaultGameObjects,
                UnityEditor.SceneManagement.NewSceneMode.Single);

            // 添加MVP管理器
            GameObject mvpManager = new GameObject("MVP Manager");
            mvpManager.AddComponent<DigitalHuman.MVP.MinimalMVPManager>();

            // 保存场景
            string scenePath = "Assets/Scenes/MVPScene.unity";
            string sceneDir = Path.GetDirectoryName(scenePath);
            if (!Directory.Exists(sceneDir))
            {
                Directory.CreateDirectory(sceneDir);
            }

            UnityEditor.SceneManagement.EditorSceneManager.SaveScene(scene, scenePath);

            // 添加到Build Settings
            EditorBuildSettingsScene[] buildScenes = EditorBuildSettings.scenes;
            EditorBuildSettingsScene[] newBuildScenes = new EditorBuildSettingsScene[buildScenes.Length + 1];
            System.Array.Copy(buildScenes, newBuildScenes, buildScenes.Length);
            newBuildScenes[buildScenes.Length] = new EditorBuildSettingsScene(scenePath, true);
            EditorBuildSettings.scenes = newBuildScenes;

            Debug.Log($"已创建默认场景: {scenePath}");
            Debug.Log("场景已添加到Build Settings中");
        }

        /// <summary>
        /// 创建增强版场景
        /// </summary>
        [MenuItem("Build/Create Enhanced Scene")]
        public static void CreateEnhancedScene()
        {
            // 创建新场景
            var scene = UnityEditor.SceneManagement.EditorSceneManager.NewScene(
                UnityEditor.SceneManagement.NewSceneSetup.DefaultGameObjects,
                UnityEditor.SceneManagement.NewSceneMode.Single);

            // 添加增强版MVP管理器
            GameObject mvpManager = new GameObject("Enhanced MVP Manager");
            mvpManager.AddComponent<DigitalHuman.MVP.EnhancedMVPManager>();

            // 添加演示组件
            GameObject demoObject = new GameObject("Enhanced MVP Demo");
            demoObject.AddComponent<DigitalHuman.MVP.Examples.EnhancedMVPExample>();

            // 保存场景
            string scenePath = "Assets/Scenes/EnhancedMVPScene.unity";
            string sceneDir = Path.GetDirectoryName(scenePath);
            if (!Directory.Exists(sceneDir))
            {
                Directory.CreateDirectory(sceneDir);
            }

            UnityEditor.SceneManagement.EditorSceneManager.SaveScene(scene, scenePath);

            // 添加到Build Settings
            EditorBuildSettingsScene[] buildScenes = EditorBuildSettings.scenes;
            EditorBuildSettingsScene[] newBuildScenes = new EditorBuildSettingsScene[buildScenes.Length + 1];
            System.Array.Copy(buildScenes, newBuildScenes, buildScenes.Length);
            newBuildScenes[buildScenes.Length] = new EditorBuildSettingsScene(scenePath, true);
            EditorBuildSettings.scenes = newBuildScenes;

            Debug.Log($"已创建增强版场景: {scenePath}");
            Debug.Log("场景已添加到Build Settings中");
        }

        /// <summary>
        /// 创建带测试仪表板的增强版场景
        /// </summary>
        [MenuItem("Build/Create Enhanced Scene with Test Dashboard")]
        public static void CreateEnhancedSceneWithTestDashboard()
        {
            // 创建新场景
            var scene = UnityEditor.SceneManagement.EditorSceneManager.NewScene(
                UnityEditor.SceneManagement.NewSceneSetup.DefaultGameObjects,
                UnityEditor.SceneManagement.NewSceneMode.Single);

            // 添加增强版MVP管理器
            GameObject mvpManager = new GameObject("Enhanced MVP Manager");
            var enhancedMVP = mvpManager.AddComponent<DigitalHuman.MVP.EnhancedMVPManager>();

            // 配置MVP管理器
            enhancedMVP.EnableDebugMode = true;
            enhancedMVP.EnableAuthentication = true;
            enhancedMVP.EnableDataSync = true;
            enhancedMVP.EnableUIManagement = true;
            enhancedMVP.AutoLoginDemoUser = false; // 通过测试界面手动控制

            // 添加演示组件
            GameObject demoObject = new GameObject("Enhanced MVP Demo");
            demoObject.AddComponent<DigitalHuman.MVP.Examples.EnhancedMVPExample>();

            // 创建测试仪表板UI
            DigitalHuman.Editor.TestDashboardCreator.CreateTestDashboard();

            // 保存场景
            string scenePath = "Assets/Scenes/EnhancedMVPTestScene.unity";
            string sceneDir = Path.GetDirectoryName(scenePath);
            if (!Directory.Exists(sceneDir))
            {
                Directory.CreateDirectory(sceneDir);
            }

            UnityEditor.SceneManagement.EditorSceneManager.SaveScene(scene, scenePath);

            // 添加到Build Settings
            EditorBuildSettingsScene[] buildScenes = EditorBuildSettings.scenes;

            // 检查是否已存在
            bool sceneExists = false;
            for (int i = 0; i < buildScenes.Length; i++)
            {
                if (buildScenes[i].path == scenePath)
                {
                    sceneExists = true;
                    break;
                }
            }

            if (!sceneExists)
            {
                EditorBuildSettingsScene[] newBuildScenes = new EditorBuildSettingsScene[buildScenes.Length + 1];
                System.Array.Copy(buildScenes, newBuildScenes, buildScenes.Length);
                newBuildScenes[buildScenes.Length] = new EditorBuildSettingsScene(scenePath, true);
                EditorBuildSettings.scenes = newBuildScenes;
            }

            Debug.Log($"已创建带测试仪表板的增强版场景: {scenePath}");
            Debug.Log("场景已添加到Build Settings中");
            Debug.Log("测试仪表板UI已创建，可以通过界面测试所有功能");
        }

        /// <summary>
        /// 为构建准备增强版场景
        /// </summary>
        private static void PrepareEnhancedSceneForBuild()
        {
            Debug.Log("[BuildScript] 准备增强版场景用于构建");

            try
            {
                // 打开或创建增强版场景
                string scenePath = "Assets/Scenes/EnhancedMVPScene.unity";

                // 如果场景不存在，创建它
                if (!System.IO.File.Exists(scenePath))
                {
                    CreateEnhancedScene();
                }

                // 打开场景
                UnityEditor.SceneManagement.EditorSceneManager.OpenScene(scenePath);

                // 确保场景中有测试UI
                if (GameObject.Find("SceneTestUI") == null)
                {
                    Debug.Log("[BuildScript] 在场景中创建测试UI");
                    DigitalHuman.Editor.CreateSceneUI.CreateTestUI();
                }

                // 保存场景
                UnityEditor.SceneManagement.EditorSceneManager.SaveScene(
                    UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene());

                Debug.Log("[BuildScript] 增强版场景准备完成");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[BuildScript] 准备场景失败: {ex.Message}");
            }
        }
    }
}
