# 认证管理系统

## 概述

认证管理系统提供了完整的用户身份验证和会话管理功能，支持用户登录、登出、令牌管理和会话验证等核心功能。

## 核心组件

### IAuthenticationManager 接口

定义了认证管理器的核心接口，包括：

- 用户登录/登出
- 会话验证
- 令牌管理
- 认证状态管理

### AuthenticationManager 实现

认证管理器的具体实现，提供：

- 模拟认证（MVP模式）
- 会话持久化
- 事件通知
- 自动令牌刷新

### 数据模型

- **UserInfo**: 用户信息模型
- **AuthenticationResult**: 认证结果模型
- **AuthenticationStatus**: 认证状态枚举

## 功能特性

### 1. 用户认证

```csharp
var authManager = AuthenticationManager.Instance;
var result = await authManager.LoginAsync("username", "password");

if (result.IsSuccess)
{
    Debug.Log($"登录成功: {result.UserInfo.Username}");
}
```

### 2. 会话管理

```csharp
// 验证当前会话
var isValid = await authManager.ValidateSessionAsync();

// 刷新令牌
var refreshed = await authManager.RefreshTokenAsync();

// 获取认证令牌
var token = authManager.GetAuthToken();
```

### 3. 事件监听

```csharp
authManager.OnUserLoggedIn += (userInfo) => {
    Debug.Log($"用户登录: {userInfo.Username}");
};

authManager.OnUserLoggedOut += () => {
    Debug.Log("用户登出");
};

authManager.OnAuthenticationStatusChanged += (status) => {
    Debug.Log($"认证状态: {status}");
};
```

## MVP模式支持

在MVP模式下，认证系统提供模拟认证功能：

- 默认用户名: `admin`
- 默认密码: `admin123`
- 自动生成模拟令牌
- 本地会话存储

## 配置选项

```csharp
[Header("认证配置")]
public bool EnableOfflineMode = true;           // 启用离线模式
public int SessionTimeoutMinutes = 60;          // 会话超时时间
public bool AutoRefreshToken = true;            // 自动刷新令牌
```

## 使用示例

### 基本使用

```csharp
public class LoginController : MonoBehaviour
{
    private AuthenticationManager authManager;
    
    void Start()
    {
        authManager = AuthenticationManager.Instance;
        authManager.OnUserLoggedIn += OnUserLoggedIn;
    }
    
    public async void Login(string username, string password)
    {
        var result = await authManager.LoginAsync(username, password);
        
        if (result.IsSuccess)
        {
            // 登录成功处理
            Debug.Log("登录成功");
        }
        else
        {
            // 登录失败处理
            Debug.LogError($"登录失败: {result.ErrorMessage}");
        }
    }
    
    private void OnUserLoggedIn(UserInfo userInfo)
    {
        Debug.Log($"欢迎 {userInfo.DisplayName}!");
    }
}
```

### 高级功能

```csharp
public class AdvancedAuthExample : MonoBehaviour
{
    private AuthenticationManager authManager;
    
    void Start()
    {
        authManager = AuthenticationManager.Instance;
        
        // 监听所有认证事件
        authManager.OnUserLoggedIn += OnUserLoggedIn;
        authManager.OnUserLoggedOut += OnUserLoggedOut;
        authManager.OnAuthenticationStatusChanged += OnStatusChanged;
    }
    
    public async void CheckUserPermissions()
    {
        if (authManager.IsLoggedIn)
        {
            var user = authManager.CurrentUser;
            
            if (user.HasRole("Admin"))
            {
                Debug.Log("用户具有管理员权限");
            }
            
            if (user.HasPermission("FullAccess"))
            {
                Debug.Log("用户具有完全访问权限");
            }
        }
    }
    
    public async void RefreshUserSession()
    {
        var isValid = await authManager.ValidateSessionAsync();
        
        if (!isValid)
        {
            Debug.Log("会话已过期，请重新登录");
            // 跳转到登录页面
        }
    }
}
```

## 安全考虑

1. **令牌管理**: 自动处理令牌过期和刷新
2. **会话安全**: 支持会话超时和自动清理
3. **数据加密**: 敏感信息加密存储
4. **错误处理**: 完善的异常处理和错误恢复

## 扩展点

### 自定义认证提供者

```csharp
public class CustomAuthProvider : IAuthenticationProvider
{
    public async Task<AuthenticationResult> AuthenticateAsync(string username, string password)
    {
        // 实现自定义认证逻辑
        return AuthenticationResult.Success(userInfo, token, refreshToken, expiresAt);
    }
}
```

### 自定义用户信息

```csharp
public class ExtendedUserInfo : UserInfo
{
    public string Department { get; set; }
    public List<string> Groups { get; set; }
    public Dictionary<string, object> CustomAttributes { get; set; }
}
```

## 测试

使用 `AuthenticationExample` 组件进行功能测试：

1. 将组件添加到场景中
2. 配置测试参数
3. 运行场景查看测试结果

## 集成指南

### 与数据同步集成

认证系统与数据同步系统无缝集成：

- 用户登录后自动启用数据同步
- 用户登出后自动禁用数据同步
- 提供认证令牌用于API调用

### 与UI系统集成

```csharp
public class LoginUI : MonoBehaviour
{
    private AuthenticationManager authManager;
    
    void Start()
    {
        authManager = AuthenticationManager.Instance;
        authManager.OnUserLoggedIn += OnLoginSuccess;
        authManager.OnAuthenticationStatusChanged += UpdateUI;
    }
    
    private void OnLoginSuccess(UserInfo userInfo)
    {
        // 更新UI显示用户信息
        userNameText.text = userInfo.DisplayName;
        loginPanel.SetActive(false);
        mainPanel.SetActive(true);
    }
}
```

## 故障排除

### 常见问题

1. **登录失败**: 检查用户名密码是否正确
2. **会话过期**: 检查会话超时配置
3. **令牌刷新失败**: 检查网络连接和服务器状态

### 调试技巧

1. 启用调试模式查看详细日志
2. 使用认证示例组件进行测试
3. 检查PlayerPrefs中的会话数据

## 版本历史

- v1.0.0: 初始版本，支持基本认证功能
- v1.1.0: 添加MVP模式支持
- v1.2.0: 集成数据同步功能

## 许可证

本组件遵循项目整体许可证。