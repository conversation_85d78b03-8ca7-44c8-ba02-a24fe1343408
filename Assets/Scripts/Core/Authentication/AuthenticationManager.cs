using System;
using System.Threading.Tasks;
using UnityEngine;

namespace DigitalHuman.Core.Authentication
{
    /// <summary>
    /// 简化的认证管理器，用于MVP版本
    /// </summary>
    public class AuthenticationManager : MonoBehaviour
    {
        private static AuthenticationManager _instance;
        
        /// <summary>
        /// 单例实例
        /// </summary>
        public static AuthenticationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<AuthenticationManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("AuthenticationManager");
                        _instance = go.AddComponent<AuthenticationManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// 是否已认证
        /// </summary>
        public bool IsAuthenticated { get; private set; } = false;

        /// <summary>
        /// 是否已登录（别名）
        /// </summary>
        public bool IsLoggedIn => IsAuthenticated;
        
        /// <summary>
        /// 当前用户ID
        /// </summary>
        public string CurrentUserId { get; private set; } = "";
        
        /// <summary>
        /// 当前用户名
        /// </summary>
        public string CurrentUsername { get; private set; } = "";

        /// <summary>
        /// 当前用户（别名）
        /// </summary>
        public UserInfo CurrentUser => GetUserInfo();
        
        /// <summary>
        /// 认证状态变化事件
        /// </summary>
        public event Action<bool> OnAuthenticationChanged;
        
        /// <summary>
        /// 登录事件
        /// </summary>
        public event Action<string> OnLoginSuccess;
        
        /// <summary>
        /// 登出事件
        /// </summary>
        public event Action OnLogout;

        /// <summary>
        /// 用户登录事件
        /// </summary>
        public event Action<UserInfo> OnUserLoggedIn;

        /// <summary>
        /// 用户登出事件
        /// </summary>
        public event Action OnUserLoggedOut;

        /// <summary>
        /// 认证状态变化事件
        /// </summary>
        public event Action<AuthenticationStatus> OnAuthenticationStatusChanged;
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录结果</returns>
        public async Task<AuthenticationResult> LoginAsync(string username, string password)
        {
            try
            {
                // 简化版本：模拟登录过程
                await Task.Delay(500); // 模拟网络延迟

                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    IsAuthenticated = true;
                    CurrentUserId = Guid.NewGuid().ToString();
                    CurrentUsername = username;

                    var userInfo = GetUserInfo();

                    OnAuthenticationChanged?.Invoke(true);
                    OnLoginSuccess?.Invoke(username);
                    OnUserLoggedIn?.Invoke(userInfo);
                    OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.Authenticated);

                    Debug.Log($"[AuthenticationManager] 用户 {username} 登录成功");

                    return AuthenticationResult.Success(userInfo, "mock-token");
                }
                else
                {
                    Debug.LogWarning("[AuthenticationManager] 用户名或密码为空");
                    return AuthenticationResult.Failure("用户名或密码为空");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AuthenticationManager] 登录失败: {ex.Message}");
                return AuthenticationResult.Failure($"登录失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 登出
        /// </summary>
        public async Task<bool> LogoutAsync()
        {
            try
            {
                await Task.Delay(100); // 模拟网络延迟

                IsAuthenticated = false;
                CurrentUserId = "";
                CurrentUsername = "";

                OnAuthenticationChanged?.Invoke(false);
                OnLogout?.Invoke();
                OnUserLoggedOut?.Invoke();
                OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.NotAuthenticated);

                Debug.Log("[AuthenticationManager] 用户已登出");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AuthenticationManager] 登出失败: {ex.Message}");
                return false;
            }
        }


        
        /// <summary>
        /// 验证当前认证状态
        /// </summary>
        /// <returns>是否有效</returns>
        public async Task<bool> ValidateAuthenticationAsync()
        {
            try
            {
                // 简化版本：直接返回当前状态
                await Task.Delay(100);
                return IsAuthenticated;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AuthenticationManager] 验证认证状态失败: {ex.Message}");
                return false;
            }
        }


        

        
        /// <summary>
        /// 验证当前会话是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateSessionAsync()
        {
            try
            {
                await Task.Delay(100); // 模拟网络延迟

                if (IsAuthenticated)
                {
                    Debug.Log("[AuthenticationManager] 会话验证成功");
                    return true;
                }
                else
                {
                    Debug.LogWarning("[AuthenticationManager] 会话无效");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AuthenticationManager] 会话验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 刷新认证令牌
        /// </summary>
        /// <returns>刷新结果</returns>
        public async Task<bool> RefreshTokenAsync()
        {
            try
            {
                await Task.Delay(200); // 模拟网络延迟

                if (IsAuthenticated)
                {
                    Debug.Log("[AuthenticationManager] 令牌刷新成功");
                    return true;
                }
                else
                {
                    Debug.LogWarning("[AuthenticationManager] 用户未登录，无法刷新令牌");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AuthenticationManager] 令牌刷新失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取认证令牌
        /// </summary>
        /// <returns>认证令牌</returns>
        public string GetAuthToken()
        {
            if (IsAuthenticated)
            {
                return $"mock-token-{CurrentUserId}";
            }
            return null;
        }

        /// <summary>
        /// 清除认证信息
        /// </summary>
        public void ClearAuthenticationData()
        {
            try
            {
                IsAuthenticated = false;
                CurrentUserId = "";
                CurrentUsername = "";

                Debug.Log("[AuthenticationManager] 认证信息已清除");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AuthenticationManager] 清除认证信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        public bool HasPermission(string permission)
        {
            // 简化版本：已认证用户拥有所有权限
            return IsAuthenticated;
        }
        


        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        public UserInfo GetUserInfo()
        {
            if (IsAuthenticated)
            {
                return new UserInfo
                {
                    UserId = CurrentUserId,
                    Username = CurrentUsername,
                    Email = $"{CurrentUsername}@example.com",
                    IsActive = true
                };
            }
            return null;
        }
    }
    
    /// <summary>
    /// 用户信息类
    /// </summary>
    [Serializable]
    public class UserInfo
    {
        public string UserId;
        public string Username;
        public string Email;
        public bool IsActive;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName => Username;
    }
}
