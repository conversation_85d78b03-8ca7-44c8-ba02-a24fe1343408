using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 内存统计信息
    /// </summary>
    [Serializable]
    public class MemoryStatistics
    {
        /// <summary>
        /// 当前内存使用量（MB）
        /// </summary>
        public float CurrentUsage;
        
        /// <summary>
        /// 峰值内存使用量（MB）
        /// </summary>
        public float PeakUsage;
        
        /// <summary>
        /// 平均内存使用量（MB）
        /// </summary>
        public float AverageUsage;
        
        /// <summary>
        /// 垃圾回收次数
        /// </summary>
        public int GCCount;
        
        /// <summary>
        /// 最后一次垃圾回收时间
        /// </summary>
        public DateTime LastGCTime;
        
        /// <summary>
        /// 监控开始时间
        /// </summary>
        public DateTime StartTime;
        
        /// <summary>
        /// 监控持续时间（秒）
        /// </summary>
        public float MonitorDuration;
        
        /// <summary>
        /// 内存使用历史记录
        /// </summary>
        public List<MemoryUsageRecord> UsageHistory;
        
        public MemoryStatistics()
        {
            UsageHistory = new List<MemoryUsageRecord>();
            StartTime = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 内存使用记录
    /// </summary>
    [Serializable]
    public class MemoryUsageRecord
    {
        /// <summary>
        /// 记录时间
        /// </summary>
        public DateTime Timestamp;
        
        /// <summary>
        /// 内存使用量（MB）
        /// </summary>
        public float Usage;
        
        /// <summary>
        /// 事件类型
        /// </summary>
        public string EventType;
        
        public MemoryUsageRecord(float usage, string eventType = "")
        {
            Timestamp = DateTime.Now;
            Usage = usage;
            EventType = eventType;
        }
    }
    
    /// <summary>
    /// 内存泄漏信息
    /// </summary>
    [Serializable]
    public class MemoryLeakInfo
    {
        /// <summary>
        /// 泄漏对象名称
        /// </summary>
        public string ObjectName;
        
        /// <summary>
        /// 泄漏对象类型
        /// </summary>
        public string ObjectType;
        
        /// <summary>
        /// 泄漏大小（MB）
        /// </summary>
        public float LeakSize;
        
        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectionTime;
        
        /// <summary>
        /// 泄漏描述
        /// </summary>
        public string Description;
        
        /// <summary>
        /// 严重程度
        /// </summary>
        public MemoryLeakSeverity Severity;
        
        public MemoryLeakInfo(string objectName, string objectType, float leakSize, string description, MemoryLeakSeverity severity)
        {
            ObjectName = objectName;
            ObjectType = objectType;
            LeakSize = leakSize;
            Description = description;
            Severity = severity;
            DetectionTime = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 内存警告信息
    /// </summary>
    [Serializable]
    public class MemoryWarningInfo
    {
        /// <summary>
        /// 警告类型
        /// </summary>
        public MemoryWarningType WarningType;
        
        /// <summary>
        /// 当前内存使用量（MB）
        /// </summary>
        public float CurrentUsage;
        
        /// <summary>
        /// 阈值（MB）
        /// </summary>
        public float Threshold;
        
        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message;
        
        /// <summary>
        /// 警告时间
        /// </summary>
        public DateTime WarningTime;
        
        public MemoryWarningInfo(MemoryWarningType warningType, float currentUsage, float threshold, string message)
        {
            WarningType = warningType;
            CurrentUsage = currentUsage;
            Threshold = threshold;
            Message = message;
            WarningTime = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 内存监控信息
    /// </summary>
    [Serializable]
    public class MemoryMonitorInfo
    {
        /// <summary>
        /// 对象名称
        /// </summary>
        public string ObjectName;
        
        /// <summary>
        /// 对象类型
        /// </summary>
        public string ObjectType;
        
        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisterTime;
        
        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessTime;
        
        /// <summary>
        /// 是否仍然存活
        /// </summary>
        public bool IsAlive;
        
        /// <summary>
        /// 预估内存占用（MB）
        /// </summary>
        public float EstimatedMemoryUsage;
        
        public MemoryMonitorInfo(string objectName, string objectType)
        {
            ObjectName = objectName;
            ObjectType = objectType;
            RegisterTime = DateTime.Now;
            LastAccessTime = DateTime.Now;
            IsAlive = true;
            EstimatedMemoryUsage = 0f;
        }
    }
    
    /// <summary>
    /// 内存泄漏严重程度
    /// </summary>
    public enum MemoryLeakSeverity
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,
        
        /// <summary>
        /// 中等
        /// </summary>
        Medium,
        
        /// <summary>
        /// 高
        /// </summary>
        High,
        
        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }
    
    /// <summary>
    /// 内存警告类型
    /// </summary>
    public enum MemoryWarningType
    {
        /// <summary>
        /// 内存使用量过高
        /// </summary>
        HighUsage,
        
        /// <summary>
        /// 内存增长过快
        /// </summary>
        RapidGrowth,
        
        /// <summary>
        /// 垃圾回收频繁
        /// </summary>
        FrequentGC,
        
        /// <summary>
        /// 可能的内存泄漏
        /// </summary>
        PossibleLeak
    }
}