using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 系统健康状态
    /// </summary>
    public enum SystemHealthStatus
    {
        /// <summary>
        /// 健康
        /// </summary>
        Healthy,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical,
        
        /// <summary>
        /// 未知
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// 组件健康状态
    /// </summary>
    public enum ComponentHealthStatus
    {
        /// <summary>
        /// 健康
        /// </summary>
        Healthy,
        
        /// <summary>
        /// 降级
        /// </summary>
        Degraded,
        
        /// <summary>
        /// 故障
        /// </summary>
        Failed,
        
        /// <summary>
        /// 恢复中
        /// </summary>
        Recovering,
        
        /// <summary>
        /// 未知
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// 恢复操作类型
    /// </summary>
    public enum RecoveryActionType
    {
        /// <summary>
        /// 重启组件
        /// </summary>
        RestartComponent,
        
        /// <summary>
        /// 重新初始化
        /// </summary>
        Reinitialize,
        
        /// <summary>
        /// 清理资源
        /// </summary>
        CleanupResources,
        
        /// <summary>
        /// 重置配置
        /// </summary>
        ResetConfiguration,
        
        /// <summary>
        /// 自定义操作
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// 系统异常信息
    /// </summary>
    [Serializable]
    public class SystemException
    {
        /// <summary>
        /// 异常ID
        /// </summary>
        public string ExceptionId;
        
        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType;
        
        /// <summary>
        /// 异常消息
        /// </summary>
        public string Message;
        
        /// <summary>
        /// 堆栈跟踪
        /// </summary>
        public string StackTrace;
        
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp;
        
        /// <summary>
        /// 来源组件
        /// </summary>
        public string SourceComponent;
        
        /// <summary>
        /// 严重程度
        /// </summary>
        public ExceptionSeverity Severity;
        
        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled;
        
        /// <summary>
        /// 处理方式
        /// </summary>
        public string HandlingMethod;
        
        public SystemException(Exception ex, string sourceComponent, ExceptionSeverity severity)
        {
            ExceptionId = Guid.NewGuid().ToString();
            ExceptionType = ex.GetType().Name;
            Message = ex.Message;
            StackTrace = ex.StackTrace;
            Timestamp = DateTime.Now;
            SourceComponent = sourceComponent;
            Severity = severity;
            IsHandled = false;
            HandlingMethod = "";
        }
    }
    
    /// <summary>
    /// 组件故障信息
    /// </summary>
    [Serializable]
    public class ComponentFailureInfo
    {
        /// <summary>
        /// 组件名称
        /// </summary>
        public string ComponentName;
        
        /// <summary>
        /// 组件类型
        /// </summary>
        public string ComponentType;
        
        /// <summary>
        /// 故障类型
        /// </summary>
        public string FailureType;
        
        /// <summary>
        /// 故障描述
        /// </summary>
        public string Description;
        
        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectionTime;
        
        /// <summary>
        /// 故障严重程度
        /// </summary>
        public ComponentFailureSeverity Severity;
        
        /// <summary>
        /// 是否可自动恢复
        /// </summary>
        public bool IsAutoRecoverable;
        
        /// <summary>
        /// 恢复尝试次数
        /// </summary>
        public int RecoveryAttempts;
        
        /// <summary>
        /// 最大恢复尝试次数
        /// </summary>
        public int MaxRecoveryAttempts;
        
        public ComponentFailureInfo(string componentName, string componentType, string failureType, string description, ComponentFailureSeverity severity)
        {
            ComponentName = componentName;
            ComponentType = componentType;
            FailureType = failureType;
            Description = description;
            DetectionTime = DateTime.Now;
            Severity = severity;
            IsAutoRecoverable = true;
            RecoveryAttempts = 0;
            MaxRecoveryAttempts = 3;
        }
    }
    
    /// <summary>
    /// 恢复操作信息
    /// </summary>
    [Serializable]
    public class RecoveryActionInfo
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        public string ActionId;
        
        /// <summary>
        /// 目标组件
        /// </summary>
        public string TargetComponent;
        
        /// <summary>
        /// 操作类型
        /// </summary>
        public RecoveryActionType ActionType;
        
        /// <summary>
        /// 操作描述
        /// </summary>
        public string Description;
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime;
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime;
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage;
        
        /// <summary>
        /// 执行持续时间（毫秒）
        /// </summary>
        public long DurationMs => EndTime.HasValue ? (long)(EndTime.Value - StartTime).TotalMilliseconds : 0;
        
        public RecoveryActionInfo(string targetComponent, RecoveryActionType actionType, string description)
        {
            ActionId = Guid.NewGuid().ToString();
            TargetComponent = targetComponent;
            ActionType = actionType;
            Description = description;
            StartTime = DateTime.Now;
            IsSuccessful = false;
            ErrorMessage = "";
        }
        
        /// <summary>
        /// 标记操作完成
        /// </summary>
        /// <param name="successful">是否成功</param>
        /// <param name="errorMessage">错误消息</param>
        public void Complete(bool successful, string errorMessage = "")
        {
            EndTime = DateTime.Now;
            IsSuccessful = successful;
            ErrorMessage = errorMessage;
        }
    }
    
    /// <summary>
    /// 系统健康报告
    /// </summary>
    [Serializable]
    public class SystemHealthReport
    {
        /// <summary>
        /// 报告生成时间
        /// </summary>
        public DateTime ReportTime;
        
        /// <summary>
        /// 整体健康状态
        /// </summary>
        public SystemHealthStatus OverallStatus;
        
        /// <summary>
        /// 组件健康信息列表
        /// </summary>
        public List<ComponentHealthInfo> ComponentHealthInfos;
        
        /// <summary>
        /// 最近异常列表
        /// </summary>
        public List<SystemException> RecentExceptions;
        
        /// <summary>
        /// 最近恢复操作列表
        /// </summary>
        public List<RecoveryActionInfo> RecentRecoveryActions;
        
        /// <summary>
        /// 系统运行时间（秒）
        /// </summary>
        public float SystemUptime;
        
        /// <summary>
        /// 健康组件数量
        /// </summary>
        public int HealthyComponentCount;
        
        /// <summary>
        /// 故障组件数量
        /// </summary>
        public int FailedComponentCount;
        
        public SystemHealthReport()
        {
            ReportTime = DateTime.Now;
            ComponentHealthInfos = new List<ComponentHealthInfo>();
            RecentExceptions = new List<SystemException>();
            RecentRecoveryActions = new List<RecoveryActionInfo>();
        }
    }
    
    /// <summary>
    /// 组件健康信息
    /// </summary>
    [Serializable]
    public class ComponentHealthInfo
    {
        /// <summary>
        /// 组件名称
        /// </summary>
        public string ComponentName;
        
        /// <summary>
        /// 组件类型
        /// </summary>
        public string ComponentType;
        
        /// <summary>
        /// 健康状态
        /// </summary>
        public ComponentHealthStatus Status;
        
        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastCheckTime;
        
        /// <summary>
        /// 运行时间（秒）
        /// </summary>
        public float Uptime;
        
        /// <summary>
        /// 故障次数
        /// </summary>
        public int FailureCount;
        
        /// <summary>
        /// 恢复次数
        /// </summary>
        public int RecoveryCount;
        
        /// <summary>
        /// 最后故障时间
        /// </summary>
        public DateTime? LastFailureTime;
        
        /// <summary>
        /// 最后恢复时间
        /// </summary>
        public DateTime? LastRecoveryTime;
        
        /// <summary>
        /// 健康检查消息
        /// </summary>
        public string HealthMessage;
        
        /// <summary>
        /// 是否有自动恢复功能
        /// </summary>
        public bool HasAutoRecovery;
        
        /// <summary>
        /// 当前恢复尝试次数
        /// </summary>
        public int RecoveryAttempts;
        
        /// <summary>
        /// 最大恢复尝试次数
        /// </summary>
        public int MaxRecoveryAttempts;
        
        public ComponentHealthInfo(string componentName, string componentType)
        {
            ComponentName = componentName;
            ComponentType = componentType;
            Status = ComponentHealthStatus.Unknown;
            LastCheckTime = DateTime.Now;
            Uptime = 0f;
            FailureCount = 0;
            RecoveryCount = 0;
            HealthMessage = "";
            HasAutoRecovery = false;
            RecoveryAttempts = 0;
            MaxRecoveryAttempts = 3;
        }
    }
    
    /// <summary>
    /// 异常统计信息
    /// </summary>
    [Serializable]
    public class ExceptionStatistics
    {
        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime;
        
        /// <summary>
        /// 总异常数量
        /// </summary>
        public int TotalExceptionCount;
        
        /// <summary>
        /// 已处理异常数量
        /// </summary>
        public int HandledExceptionCount;
        
        /// <summary>
        /// 未处理异常数量
        /// </summary>
        public int UnhandledExceptionCount;
        
        /// <summary>
        /// 按严重程度分组的异常数量
        /// </summary>
        public Dictionary<ExceptionSeverity, int> ExceptionsBySeverity;
        
        /// <summary>
        /// 按组件分组的异常数量
        /// </summary>
        public Dictionary<string, int> ExceptionsByComponent;
        
        /// <summary>
        /// 按类型分组的异常数量
        /// </summary>
        public Dictionary<string, int> ExceptionsByType;
        
        /// <summary>
        /// 最近24小时异常数量
        /// </summary>
        public int ExceptionsLast24Hours;
        
        /// <summary>
        /// 平均每小时异常数量
        /// </summary>
        public float AverageExceptionsPerHour;
        
        public ExceptionStatistics()
        {
            StartTime = DateTime.Now;
            ExceptionsBySeverity = new Dictionary<ExceptionSeverity, int>();
            ExceptionsByComponent = new Dictionary<string, int>();
            ExceptionsByType = new Dictionary<string, int>();
        }
    }
    
    /// <summary>
    /// 异常严重程度
    /// </summary>
    public enum ExceptionSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical,
        
        /// <summary>
        /// 致命错误
        /// </summary>
        Fatal
    }
    
    /// <summary>
    /// 组件故障严重程度
    /// </summary>
    public enum ComponentFailureSeverity
    {
        /// <summary>
        /// 轻微
        /// </summary>
        Minor,
        
        /// <summary>
        /// 中等
        /// </summary>
        Moderate,
        
        /// <summary>
        /// 严重
        /// </summary>
        Severe,
        
        /// <summary>
        /// 致命
        /// </summary>
        Critical
    }
}