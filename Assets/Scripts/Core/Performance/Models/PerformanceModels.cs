using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 优化类型
    /// </summary>
    public enum OptimizationType
    {
        /// <summary>
        /// 渲染优化
        /// </summary>
        RenderingOptimization,
        
        /// <summary>
        /// 内存优化
        /// </summary>
        MemoryOptimization,
        
        /// <summary>
        /// CPU优化
        /// </summary>
        CPUOptimization,
        
        /// <summary>
        /// GPU优化
        /// </summary>
        GPUOptimization,
        
        /// <summary>
        /// 帧率稳定优化
        /// </summary>
        FrameRateStabilization,
        
        /// <summary>
        /// 负载均衡
        /// </summary>
        LoadBalancing,
        
        /// <summary>
        /// 资源调度
        /// </summary>
        ResourceScheduling
    }
    
    /// <summary>
    /// 性能警告类型
    /// </summary>
    public enum PerformanceWarningType
    {
        /// <summary>
        /// 低帧率
        /// </summary>
        LowFrameRate,
        
        /// <summary>
        /// 高CPU使用率
        /// </summary>
        HighCPUUsage,
        
        /// <summary>
        /// 高GPU使用率
        /// </summary>
        HighGPUUsage,
        
        /// <summary>
        /// 高内存使用率
        /// </summary>
        HighMemoryUsage,
        
        /// <summary>
        /// 渲染时间过长
        /// </summary>
        LongRenderTime,
        
        /// <summary>
        /// 帧率不稳定
        /// </summary>
        UnstableFrameRate
    }
    
    /// <summary>
    /// 性能统计信息
    /// </summary>
    [Serializable]
    public class PerformanceStatistics
    {
        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime;
        
        /// <summary>
        /// 统计持续时间（秒）
        /// </summary>
        public float Duration;
        
        /// <summary>
        /// 当前帧率
        /// </summary>
        public float CurrentFPS;
        
        /// <summary>
        /// 平均帧率
        /// </summary>
        public float AverageFPS;
        
        /// <summary>
        /// 最低帧率
        /// </summary>
        public float MinFPS;
        
        /// <summary>
        /// 最高帧率
        /// </summary>
        public float MaxFPS;
        
        /// <summary>
        /// 帧率标准差
        /// </summary>
        public float FPSStandardDeviation;
        
        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public float CPUUsage;
        
        /// <summary>
        /// GPU使用率（百分比）
        /// </summary>
        public float GPUUsage;
        
        /// <summary>
        /// 内存使用量（MB）
        /// </summary>
        public float MemoryUsage;
        
        /// <summary>
        /// 渲染时间（毫秒）
        /// </summary>
        public float RenderTime;
        
        /// <summary>
        /// 总帧数
        /// </summary>
        public int TotalFrames;
        
        /// <summary>
        /// 丢帧数
        /// </summary>
        public int DroppedFrames;
        
        /// <summary>
        /// 丢帧率（百分比）
        /// </summary>
        public float DroppedFrameRate => TotalFrames > 0 ? (float)DroppedFrames / TotalFrames * 100f : 0f;
        
        /// <summary>
        /// 自定义指标
        /// </summary>
        public Dictionary<string, float> CustomMetrics;
        
        public PerformanceStatistics()
        {
            StartTime = DateTime.Now;
            CustomMetrics = new Dictionary<string, float>();
            MinFPS = float.MaxValue;
            MaxFPS = 0f;
        }
    }
    
    /// <summary>
    /// 性能报告
    /// </summary>
    [Serializable]
    public class PerformanceReport
    {
        /// <summary>
        /// 报告生成时间
        /// </summary>
        public DateTime ReportTime;
        
        /// <summary>
        /// 性能统计信息
        /// </summary>
        public PerformanceStatistics Statistics;
        
        /// <summary>
        /// 性能警告列表
        /// </summary>
        public List<PerformanceWarning> Warnings;
        
        /// <summary>
        /// 优化建议列表
        /// </summary>
        public List<OptimizationSuggestion> Suggestions;
        
        /// <summary>
        /// 已执行的优化操作
        /// </summary>
        public List<OptimizationAction> ExecutedOptimizations;
        
        /// <summary>
        /// 性能趋势分析
        /// </summary>
        public PerformanceTrend Trend;
        
        /// <summary>
        /// 系统信息
        /// </summary>
        public SystemInfo SystemInfo;
        
        public PerformanceReport()
        {
            ReportTime = DateTime.Now;
            Warnings = new List<PerformanceWarning>();
            Suggestions = new List<OptimizationSuggestion>();
            ExecutedOptimizations = new List<OptimizationAction>();
        }
    }
    
    /// <summary>
    /// 性能警告
    /// </summary>
    [Serializable]
    public class PerformanceWarning
    {
        /// <summary>
        /// 警告ID
        /// </summary>
        public string WarningId;
        
        /// <summary>
        /// 警告类型
        /// </summary>
        public PerformanceWarningType Type;
        
        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message;
        
        /// <summary>
        /// 当前值
        /// </summary>
        public float CurrentValue;
        
        /// <summary>
        /// 阈值
        /// </summary>
        public float Threshold;
        
        /// <summary>
        /// 严重程度
        /// </summary>
        public WarningSeverity Severity;
        
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp;
        
        /// <summary>
        /// 持续时间（秒）
        /// </summary>
        public float Duration;
        
        public PerformanceWarning(PerformanceWarningType type, string message, float currentValue, float threshold, WarningSeverity severity)
        {
            WarningId = Guid.NewGuid().ToString();
            Type = type;
            Message = message;
            CurrentValue = currentValue;
            Threshold = threshold;
            Severity = severity;
            Timestamp = DateTime.Now;
            Duration = 0f;
        }
    }
    
    /// <summary>
    /// 优化操作
    /// </summary>
    [Serializable]
    public class OptimizationAction
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        public string ActionId;
        
        /// <summary>
        /// 优化类型
        /// </summary>
        public OptimizationType Type;
        
        /// <summary>
        /// 操作描述
        /// </summary>
        public string Description;
        
        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutionTime;
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage;
        
        /// <summary>
        /// 优化前性能指标
        /// </summary>
        public Dictionary<string, float> BeforeMetrics;
        
        /// <summary>
        /// 优化后性能指标
        /// </summary>
        public Dictionary<string, float> AfterMetrics;
        
        /// <summary>
        /// 性能改善程度
        /// </summary>
        public float ImprovementPercentage;
        
        public OptimizationAction(OptimizationType type, string description)
        {
            ActionId = Guid.NewGuid().ToString();
            Type = type;
            Description = description;
            ExecutionTime = DateTime.Now;
            IsSuccessful = false;
            ErrorMessage = "";
            BeforeMetrics = new Dictionary<string, float>();
            AfterMetrics = new Dictionary<string, float>();
            ImprovementPercentage = 0f;
        }
    }
    
    /// <summary>
    /// 优化建议
    /// </summary>
    [Serializable]
    public class OptimizationSuggestion
    {
        /// <summary>
        /// 建议ID
        /// </summary>
        public string SuggestionId;
        
        /// <summary>
        /// 优化类型
        /// </summary>
        public OptimizationType Type;
        
        /// <summary>
        /// 建议标题
        /// </summary>
        public string Title;
        
        /// <summary>
        /// 建议描述
        /// </summary>
        public string Description;
        
        /// <summary>
        /// 预期改善程度
        /// </summary>
        public float ExpectedImprovement;
        
        /// <summary>
        /// 实施难度
        /// </summary>
        public DifficultyLevel Difficulty;
        
        /// <summary>
        /// 优先级
        /// </summary>
        public Priority Priority;
        
        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime;
        
        public OptimizationSuggestion(OptimizationType type, string title, string description, float expectedImprovement, DifficultyLevel difficulty, Priority priority)
        {
            SuggestionId = Guid.NewGuid().ToString();
            Type = type;
            Title = title;
            Description = description;
            ExpectedImprovement = expectedImprovement;
            Difficulty = difficulty;
            Priority = priority;
            GeneratedTime = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 性能阈值配置
    /// </summary>
    [Serializable]
    public class PerformanceThresholds
    {
        /// <summary>
        /// 最低帧率阈值
        /// </summary>
        public float MinFPSThreshold = 30f;
        
        /// <summary>
        /// 帧率警告阈值
        /// </summary>
        public float FPSWarningThreshold = 45f;
        
        /// <summary>
        /// CPU使用率警告阈值（百分比）
        /// </summary>
        public float CPUWarningThreshold = 80f;
        
        /// <summary>
        /// GPU使用率警告阈值（百分比）
        /// </summary>
        public float GPUWarningThreshold = 85f;
        
        /// <summary>
        /// 内存使用率警告阈值（百分比）
        /// </summary>
        public float MemoryWarningThreshold = 80f;
        
        /// <summary>
        /// 渲染时间警告阈值（毫秒）
        /// </summary>
        public float RenderTimeWarningThreshold = 16.67f; // 60FPS对应的帧时间
        
        /// <summary>
        /// 帧率稳定性阈值（标准差）
        /// </summary>
        public float FPSStabilityThreshold = 5f;
        
        /// <summary>
        /// 丢帧率警告阈值（百分比）
        /// </summary>
        public float DroppedFrameRateThreshold = 5f;
    }
    
    /// <summary>
    /// 性能趋势分析
    /// </summary>
    [Serializable]
    public class PerformanceTrend
    {
        /// <summary>
        /// 帧率趋势
        /// </summary>
        public TrendDirection FPSTrend;
        
        /// <summary>
        /// CPU使用率趋势
        /// </summary>
        public TrendDirection CPUTrend;
        
        /// <summary>
        /// GPU使用率趋势
        /// </summary>
        public TrendDirection GPUTrend;
        
        /// <summary>
        /// 内存使用率趋势
        /// </summary>
        public TrendDirection MemoryTrend;
        
        /// <summary>
        /// 渲染时间趋势
        /// </summary>
        public TrendDirection RenderTimeTrend;
        
        /// <summary>
        /// 整体性能趋势
        /// </summary>
        public TrendDirection OverallTrend;
        
        /// <summary>
        /// 趋势分析时间窗口（秒）
        /// </summary>
        public float AnalysisWindow;
        
        /// <summary>
        /// 趋势置信度（0-1）
        /// </summary>
        public float Confidence;
    }
    
    /// <summary>
    /// 系统信息
    /// </summary>
    [Serializable]
    public class SystemInfo
    {
        /// <summary>
        /// 操作系统
        /// </summary>
        public string OperatingSystem;
        
        /// <summary>
        /// 处理器信息
        /// </summary>
        public string ProcessorType;
        
        /// <summary>
        /// 处理器核心数
        /// </summary>
        public int ProcessorCount;
        
        /// <summary>
        /// 系统内存大小（MB）
        /// </summary>
        public int SystemMemorySize;
        
        /// <summary>
        /// 显卡信息
        /// </summary>
        public string GraphicsDeviceName;
        
        /// <summary>
        /// 显存大小（MB）
        /// </summary>
        public int GraphicsMemorySize;
        
        /// <summary>
        /// Unity版本
        /// </summary>
        public string UnityVersion;
        
        /// <summary>
        /// 目标帧率
        /// </summary>
        public int TargetFrameRate;
        
        /// <summary>
        /// 垂直同步设置
        /// </summary>
        public int VSyncCount;
        
        /// <summary>
        /// 渲染管线
        /// </summary>
        public string RenderPipeline;
    }
    
    /// <summary>
    /// 警告严重程度
    /// </summary>
    public enum WarningSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }
    
    /// <summary>
    /// 趋势方向
    /// </summary>
    public enum TrendDirection
    {
        /// <summary>
        /// 改善
        /// </summary>
        Improving,
        
        /// <summary>
        /// 稳定
        /// </summary>
        Stable,
        
        /// <summary>
        /// 恶化
        /// </summary>
        Degrading,
        
        /// <summary>
        /// 未知
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// 实施难度
    /// </summary>
    public enum DifficultyLevel
    {
        /// <summary>
        /// 简单
        /// </summary>
        Easy,
        
        /// <summary>
        /// 中等
        /// </summary>
        Medium,
        
        /// <summary>
        /// 困难
        /// </summary>
        Hard,
        
        /// <summary>
        /// 非常困难
        /// </summary>
        VeryHard
    }
    
    /// <summary>
    /// 优先级
    /// </summary>
    public enum Priority
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,
        
        /// <summary>
        /// 中等
        /// </summary>
        Medium,
        
        /// <summary>
        /// 高
        /// </summary>
        High,
        
        /// <summary>
        /// 紧急
        /// </summary>
        Critical
    }
}