using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Profiling;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 内存管理器实现
    /// </summary>
    public class MemoryManager : SingletonManager<MemoryManager>, IMemoryManager
    {
        [Header("内存监控配置")]
        [SerializeField] private float _memoryCheckInterval = 1.0f;
        [SerializeField] private float _memoryWarningThreshold = 512.0f; // MB
        [SerializeField] private float _memoryCriticalThreshold = 1024.0f; // MB
        [SerializeField] private int _maxHistoryRecords = 1000;
        [SerializeField] private bool _autoGarbageCollection = true;
        [SerializeField] private float _autoGCThreshold = 256.0f; // MB
        
        private MemoryStatistics _statistics;
        private Dictionary<object, MemoryMonitorInfo> _monitoredObjects;
        private Coroutine _memoryMonitorCoroutine;
        private float _lastMemoryUsage;
        private int _lastGCCount;
        private bool _isLeakDetectionEnabled = true;
        
        /// <summary>
        /// 当前内存使用量（MB）
        /// </summary>
        public float CurrentMemoryUsage => GetCurrentMemoryUsage();
        
        /// <summary>
        /// 峰值内存使用量（MB）
        /// </summary>
        public float PeakMemoryUsage => _statistics?.PeakUsage ?? 0f;
        
        /// <summary>
        /// 内存泄漏检测是否启用
        /// </summary>
        public bool IsLeakDetectionEnabled
        {
            get => _isLeakDetectionEnabled;
            set => _isLeakDetectionEnabled = value;
        }
        
        /// <summary>
        /// 内存使用量变化事件
        /// </summary>
        public event Action<float> OnMemoryUsageChanged;
        
        /// <summary>
        /// 内存泄漏检测事件
        /// </summary>
        public event Action<MemoryLeakInfo> OnMemoryLeakDetected;
        
        /// <summary>
        /// 内存警告事件
        /// </summary>
        public event Action<MemoryWarningInfo> OnMemoryWarning;
        
        /// <summary>
        /// 初始化内存管理器
        /// </summary>
        protected override void OnInitializeInternal()
        {
            _statistics = new MemoryStatistics();
            _monitoredObjects = new Dictionary<object, MemoryMonitorInfo>();
            _lastMemoryUsage = GetCurrentMemoryUsage();
            _lastGCCount = GC.CollectionCount(0);
            
            // 开始内存监控协程
            _memoryMonitorCoroutine = StartCoroutine(MemoryMonitorCoroutine());
            
            Debug.Log($"[MemoryManager] 内存管理器初始化完成，当前内存使用量: {_lastMemoryUsage:F2} MB");
        }
        
        /// <summary>
        /// 清理内存管理器
        /// </summary>
        protected override void OnCleanupInternal()
        {
            if (_memoryMonitorCoroutine != null)
            {
                StopCoroutine(_memoryMonitorCoroutine);
                _memoryMonitorCoroutine = null;
            }
            
            _monitoredObjects?.Clear();
            _statistics = null;
            
            Debug.Log("[MemoryManager] 内存管理器清理完成");
        }
        
        /// <summary>
        /// 执行垃圾回收
        /// </summary>
        public void ForceGarbageCollection()
        {
            try
            {
                var beforeGC = GetCurrentMemoryUsage();
                
                // 执行垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var afterGC = GetCurrentMemoryUsage();
                var freedMemory = beforeGC - afterGC;
                
                _statistics.GCCount++;
                _statistics.LastGCTime = DateTime.Now;
                
                // 记录垃圾回收事件
                _statistics.UsageHistory.Add(new MemoryUsageRecord(afterGC, $"GC - 释放 {freedMemory:F2} MB"));
                
                Debug.Log($"[MemoryManager] 垃圾回收完成，释放内存: {freedMemory:F2} MB");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MemoryManager] 垃圾回收失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清理未使用的资源
        /// </summary>
        public void CleanupUnusedResources()
        {
            try
            {
                var beforeCleanup = GetCurrentMemoryUsage();
                
                // 清理Unity资源
                Resources.UnloadUnusedAssets();
                
                // 执行垃圾回收
                ForceGarbageCollection();
                
                var afterCleanup = GetCurrentMemoryUsage();
                var freedMemory = beforeCleanup - afterCleanup;
                
                // 记录清理事件
                _statistics.UsageHistory.Add(new MemoryUsageRecord(afterCleanup, $"资源清理 - 释放 {freedMemory:F2} MB"));
                
                Debug.Log($"[MemoryManager] 资源清理完成，释放内存: {freedMemory:F2} MB");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MemoryManager] 资源清理失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取内存使用统计信息
        /// </summary>
        public MemoryStatistics GetMemoryStatistics()
        {
            if (_statistics == null) return null;
            
            // 更新统计信息
            _statistics.CurrentUsage = GetCurrentMemoryUsage();
            _statistics.MonitorDuration = (float)(DateTime.Now - _statistics.StartTime).TotalSeconds;
            
            // 计算平均使用量
            if (_statistics.UsageHistory.Count > 0)
            {
                _statistics.AverageUsage = _statistics.UsageHistory.Average(r => r.Usage);
            }
            
            return _statistics;
        }
        
        /// <summary>
        /// 注册内存监控对象
        /// </summary>
        public void RegisterMemoryMonitor(object obj, string name)
        {
            if (obj == null) return;
            
            try
            {
                var monitorInfo = new MemoryMonitorInfo(name, obj.GetType().Name);
                _monitoredObjects[obj] = monitorInfo;
                
                Debug.Log($"[MemoryManager] 注册内存监控对象: {name} ({obj.GetType().Name})");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MemoryManager] 注册内存监控对象失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 取消注册内存监控对象
        /// </summary>
        public void UnregisterMemoryMonitor(object obj)
        {
            if (obj == null) return;
            
            try
            {
                if (_monitoredObjects.ContainsKey(obj))
                {
                    var monitorInfo = _monitoredObjects[obj];
                    _monitoredObjects.Remove(obj);
                    
                    Debug.Log($"[MemoryManager] 取消注册内存监控对象: {monitorInfo.ObjectName}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MemoryManager] 取消注册内存监控对象失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取内存监控报告
        /// </summary>
        public List<MemoryMonitorInfo> GetMemoryMonitorReport()
        {
            var report = new List<MemoryMonitorInfo>();
            
            try
            {
                // 检查监控对象的存活状态
                var keysToRemove = new List<object>();
                
                foreach (var kvp in _monitoredObjects)
                {
                    var obj = kvp.Key;
                    var info = kvp.Value;
                    
                    // 检查对象是否仍然存活
                    if (obj == null || (obj is UnityEngine.Object unityObj && unityObj == null))
                    {
                        info.IsAlive = false;
                        keysToRemove.Add(obj);
                        
                        // 检测可能的内存泄漏
                        if (_isLeakDetectionEnabled)
                        {
                            CheckForMemoryLeak(info);
                        }
                    }
                    else
                    {
                        info.LastAccessTime = DateTime.Now;
                        info.IsAlive = true;
                    }
                    
                    report.Add(info);
                }
                
                // 移除已销毁的对象
                foreach (var key in keysToRemove)
                {
                    _monitoredObjects.Remove(key);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MemoryManager] 获取内存监控报告失败: {ex.Message}");
            }
            
            return report;
        }
        
        /// <summary>
        /// 获取当前内存使用量（MB）
        /// </summary>
        private float GetCurrentMemoryUsage()
        {
            return GC.GetTotalMemory(false) / (1024f * 1024f);
        }
        
        /// <summary>
        /// 内存监控协程
        /// </summary>
        private IEnumerator MemoryMonitorCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(_memoryCheckInterval);
                
                try
                {
                    UpdateMemoryStatistics();
                    CheckMemoryWarnings();
                    
                    if (_autoGarbageCollection)
                    {
                        CheckAutoGarbageCollection();
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[MemoryManager] 内存监控协程异常: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 更新内存统计信息
        /// </summary>
        private void UpdateMemoryStatistics()
        {
            var currentUsage = GetCurrentMemoryUsage();
            
            // 更新峰值使用量
            if (currentUsage > _statistics.PeakUsage)
            {
                _statistics.PeakUsage = currentUsage;
            }
            
            // 记录内存使用变化
            if (Math.Abs(currentUsage - _lastMemoryUsage) > 1.0f) // 变化超过1MB才记录
            {
                _statistics.UsageHistory.Add(new MemoryUsageRecord(currentUsage));
                
                // 限制历史记录数量
                if (_statistics.UsageHistory.Count > _maxHistoryRecords)
                {
                    _statistics.UsageHistory.RemoveAt(0);
                }
                
                OnMemoryUsageChanged?.Invoke(currentUsage);
                _lastMemoryUsage = currentUsage;
            }
            
            // 检查垃圾回收次数变化
            var currentGCCount = GC.CollectionCount(0);
            if (currentGCCount != _lastGCCount)
            {
                _statistics.GCCount = currentGCCount;
                _statistics.LastGCTime = DateTime.Now;
                _lastGCCount = currentGCCount;
            }
        }
        
        /// <summary>
        /// 检查内存警告
        /// </summary>
        private void CheckMemoryWarnings()
        {
            var currentUsage = GetCurrentMemoryUsage();
            
            // 检查内存使用量过高
            if (currentUsage > _memoryCriticalThreshold)
            {
                var warning = new MemoryWarningInfo(
                    MemoryWarningType.HighUsage,
                    currentUsage,
                    _memoryCriticalThreshold,
                    $"内存使用量过高: {currentUsage:F2} MB (阈值: {_memoryCriticalThreshold:F2} MB)"
                );
                OnMemoryWarning?.Invoke(warning);
            }
            else if (currentUsage > _memoryWarningThreshold)
            {
                var warning = new MemoryWarningInfo(
                    MemoryWarningType.HighUsage,
                    currentUsage,
                    _memoryWarningThreshold,
                    $"内存使用量警告: {currentUsage:F2} MB (阈值: {_memoryWarningThreshold:F2} MB)"
                );
                OnMemoryWarning?.Invoke(warning);
            }
            
            // 检查内存增长过快
            if (_statistics.UsageHistory.Count >= 10)
            {
                var recentRecords = _statistics.UsageHistory.TakeLast(10).ToList();
                var growthRate = (recentRecords.Last().Usage - recentRecords.First().Usage) / 10f;
                
                if (growthRate > 10.0f) // 每次检查增长超过10MB
                {
                    var warning = new MemoryWarningInfo(
                        MemoryWarningType.RapidGrowth,
                        currentUsage,
                        growthRate,
                        $"内存增长过快: {growthRate:F2} MB/次"
                    );
                    OnMemoryWarning?.Invoke(warning);
                }
            }
        }
        
        /// <summary>
        /// 检查自动垃圾回收
        /// </summary>
        private void CheckAutoGarbageCollection()
        {
            var currentUsage = GetCurrentMemoryUsage();
            
            if (currentUsage > _autoGCThreshold)
            {
                Debug.Log($"[MemoryManager] 触发自动垃圾回收，当前内存使用量: {currentUsage:F2} MB");
                ForceGarbageCollection();
            }
        }
        
        /// <summary>
        /// 检查内存泄漏
        /// </summary>
        private void CheckForMemoryLeak(MemoryMonitorInfo info)
        {
            try
            {
                var timeSinceRegister = DateTime.Now - info.RegisterTime;
                
                // 如果对象存在时间很短就被销毁，可能不是泄漏
                if (timeSinceRegister.TotalMinutes < 1)
                {
                    return;
                }
                
                // 简单的泄漏检测逻辑
                var severity = MemoryLeakSeverity.Low;
                var description = $"对象 {info.ObjectName} 可能存在内存泄漏";
                
                if (timeSinceRegister.TotalMinutes > 30)
                {
                    severity = MemoryLeakSeverity.High;
                    description += $"，存在时间: {timeSinceRegister.TotalMinutes:F1} 分钟";
                }
                
                var leakInfo = new MemoryLeakInfo(
                    info.ObjectName,
                    info.ObjectType,
                    info.EstimatedMemoryUsage,
                    description,
                    severity
                );
                
                OnMemoryLeakDetected?.Invoke(leakInfo);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MemoryManager] 内存泄漏检测失败: {ex.Message}");
            }
        }
    }
}