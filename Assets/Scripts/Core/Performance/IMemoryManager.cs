using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 内存管理器接口
    /// </summary>
    public interface IMemoryManager : IManager
    {
        /// <summary>
        /// 当前内存使用量（MB）
        /// </summary>
        float CurrentMemoryUsage { get; }
        
        /// <summary>
        /// 峰值内存使用量（MB）
        /// </summary>
        float PeakMemoryUsage { get; }
        
        /// <summary>
        /// 内存泄漏检测是否启用
        /// </summary>
        bool IsLeakDetectionEnabled { get; set; }
        
        /// <summary>
        /// 内存使用量变化事件
        /// </summary>
        event Action<float> OnMemoryUsageChanged;
        
        /// <summary>
        /// 内存泄漏检测事件
        /// </summary>
        event Action<MemoryLeakInfo> OnMemoryLeakDetected;
        
        /// <summary>
        /// 内存警告事件
        /// </summary>
        event Action<MemoryWarningInfo> OnMemoryWarning;
        
        /// <summary>
        /// 执行垃圾回收
        /// </summary>
        void ForceGarbageCollection();
        
        /// <summary>
        /// 清理未使用的资源
        /// </summary>
        void CleanupUnusedResources();
        
        /// <summary>
        /// 获取内存使用统计信息
        /// </summary>
        /// <returns>内存统计信息</returns>
        MemoryStatistics GetMemoryStatistics();
        
        /// <summary>
        /// 注册内存监控对象
        /// </summary>
        /// <param name="obj">要监控的对象</param>
        /// <param name="name">对象名称</param>
        void RegisterMemoryMonitor(object obj, string name);
        
        /// <summary>
        /// 取消注册内存监控对象
        /// </summary>
        /// <param name="obj">要取消监控的对象</param>
        void UnregisterMemoryMonitor(object obj);
        
        /// <summary>
        /// 获取内存监控报告
        /// </summary>
        /// <returns>监控报告</returns>
        List<MemoryMonitorInfo> GetMemoryMonitorReport();
    }
}