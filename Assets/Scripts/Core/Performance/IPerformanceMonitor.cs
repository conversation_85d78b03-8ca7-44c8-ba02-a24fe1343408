using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 性能监控器接口
    /// </summary>
    public interface IPerformanceMonitor : IManager
    {
        /// <summary>
        /// 当前帧率
        /// </summary>
        float CurrentFPS { get; }
        
        /// <summary>
        /// 平均帧率
        /// </summary>
        float AverageFPS { get; }
        
        /// <summary>
        /// 最低帧率
        /// </summary>
        float MinFPS { get; }
        
        /// <summary>
        /// 最高帧率
        /// </summary>
        float MaxFPS { get; }
        
        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        float CPUUsage { get; }
        
        /// <summary>
        /// GPU使用率（百分比）
        /// </summary>
        float GPUUsage { get; }
        
        /// <summary>
        /// 渲染时间（毫秒）
        /// </summary>
        float RenderTime { get; }
        
        /// <summary>
        /// 性能监控是否启用
        /// </summary>
        bool IsMonitoringEnabled { get; set; }
        
        /// <summary>
        /// 自动优化是否启用
        /// </summary>
        bool IsAutoOptimizationEnabled { get; set; }
        
        /// <summary>
        /// 帧率变化事件
        /// </summary>
        event Action<float> OnFPSChanged;
        
        /// <summary>
        /// 性能警告事件
        /// </summary>
        event Action<PerformanceWarning> OnPerformanceWarning;
        
        /// <summary>
        /// 优化操作执行事件
        /// </summary>
        event Action<OptimizationAction> OnOptimizationExecuted;
        
        /// <summary>
        /// 开始性能监控
        /// </summary>
        void StartMonitoring();
        
        /// <summary>
        /// 停止性能监控
        /// </summary>
        void StopMonitoring();
        
        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计</returns>
        PerformanceStatistics GetPerformanceStatistics();
        
        /// <summary>
        /// 获取详细性能报告
        /// </summary>
        /// <returns>性能报告</returns>
        PerformanceReport GetDetailedReport();
        
        /// <summary>
        /// 执行性能优化
        /// </summary>
        /// <param name="optimizationType">优化类型</param>
        /// <returns>优化是否成功</returns>
        bool ExecuteOptimization(OptimizationType optimizationType);
        
        /// <summary>
        /// 注册性能指标监控
        /// </summary>
        /// <param name="metricName">指标名称</param>
        /// <param name="metricProvider">指标提供者</param>
        void RegisterMetric(string metricName, Func<float> metricProvider);
        
        /// <summary>
        /// 取消注册性能指标监控
        /// </summary>
        /// <param name="metricName">指标名称</param>
        void UnregisterMetric(string metricName);
        
        /// <summary>
        /// 获取自定义指标值
        /// </summary>
        /// <param name="metricName">指标名称</param>
        /// <returns>指标值</returns>
        float GetMetricValue(string metricName);
        
        /// <summary>
        /// 设置性能阈值
        /// </summary>
        /// <param name="thresholds">性能阈值配置</param>
        void SetPerformanceThresholds(PerformanceThresholds thresholds);
        
        /// <summary>
        /// 获取性能阈值
        /// </summary>
        /// <returns>性能阈值配置</returns>
        PerformanceThresholds GetPerformanceThresholds();
        
        /// <summary>
        /// 重置性能统计
        /// </summary>
        void ResetStatistics();
        
        /// <summary>
        /// 启用/禁用特定优化功能
        /// </summary>
        /// <param name="optimizationType">优化类型</param>
        /// <param name="enabled">是否启用</param>
        void SetOptimizationEnabled(OptimizationType optimizationType, bool enabled);
    }
}