using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Performance
{
    /// <summary>
    /// 系统健康管理器接口
    /// </summary>
    public interface ISystemHealthManager : IManager
    {
        /// <summary>
        /// 系统整体健康状态
        /// </summary>
        SystemHealthStatus OverallHealthStatus { get; }
        
        /// <summary>
        /// 自动恢复是否启用
        /// </summary>
        bool IsAutoRecoveryEnabled { get; set; }
        
        /// <summary>
        /// 异常捕获是否启用
        /// </summary>
        bool IsExceptionCaptureEnabled { get; set; }
        
        /// <summary>
        /// 系统健康状态变化事件
        /// </summary>
        event Action<SystemHealthStatus> OnHealthStatusChanged;
        
        /// <summary>
        /// 异常捕获事件
        /// </summary>
        event Action<SystemException> OnExceptionCaptured;
        
        /// <summary>
        /// 组件故障检测事件
        /// </summary>
        event Action<ComponentFailureInfo> OnComponentFailureDetected;
        
        /// <summary>
        /// 自动恢复执行事件
        /// </summary>
        event Action<RecoveryActionInfo> OnRecoveryActionExecuted;
        
        /// <summary>
        /// 注册组件健康监控
        /// </summary>
        /// <param name="component">要监控的组件</param>
        /// <param name="healthChecker">健康检查函数</param>
        /// <param name="recoveryAction">恢复操作</param>
        void RegisterComponentMonitor(IManager component, Func<bool> healthChecker, Action recoveryAction = null);
        
        /// <summary>
        /// 取消注册组件监控
        /// </summary>
        /// <param name="component">要取消监控的组件</param>
        void UnregisterComponentMonitor(IManager component);
        
        /// <summary>
        /// 执行系统健康检查
        /// </summary>
        /// <returns>健康检查报告</returns>
        SystemHealthReport PerformHealthCheck();
        
        /// <summary>
        /// 手动触发组件恢复
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <returns>恢复是否成功</returns>
        bool TriggerComponentRecovery(string componentName);
        
        /// <summary>
        /// 获取异常统计信息
        /// </summary>
        /// <returns>异常统计</returns>
        ExceptionStatistics GetExceptionStatistics();
        
        /// <summary>
        /// 获取组件健康报告
        /// </summary>
        /// <returns>组件健康报告列表</returns>
        List<ComponentHealthInfo> GetComponentHealthReport();
        
        /// <summary>
        /// 清理异常历史记录
        /// </summary>
        void ClearExceptionHistory();
        
        /// <summary>
        /// 设置全局异常处理器
        /// </summary>
        void SetupGlobalExceptionHandler();
        
        /// <summary>
        /// 移除全局异常处理器
        /// </summary>
        void RemoveGlobalExceptionHandler();
    }
}