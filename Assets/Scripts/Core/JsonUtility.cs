using System;
using System.IO;
using UnityEngine;
using Newtonsoft.Json;

namespace DigitalHuman.Core
{
    /// <summary>
    /// JSON 序列化工具类，提供对象与JSON字符串的转换
    /// </summary>
    public static class JsonUtility
    {
        private static readonly JsonSerializerSettings _settings = new JsonSerializerSettings
        {
            Formatting = Formatting.Indented,
            NullValueHandling = NullValueHandling.Ignore,
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };
        
        /// <summary>
        /// 将对象序列化为JSON字符串
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>JSON字符串</returns>
        public static string ToJson(object obj)
        {
            if (obj == null)
            {
                Debug.LogWarning("[JsonUtility] 尝试序列化空对象");
                return "null";
            }
            
            try
            {
                string json = JsonConvert.SerializeObject(obj, _settings);
                Debug.Log($"[JsonUtility] 对象序列化成功: {obj.GetType().Name}");
                return json;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonUtility] 对象序列化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 将JSON字符串反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化的对象</returns>
        public static T FromJson<T>(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                Debug.LogWarning("[JsonUtility] 尝试反序列化空JSON字符串");
                return default(T);
            }
            
            try
            {
                T obj = JsonConvert.DeserializeObject<T>(json, _settings);
                Debug.Log($"[JsonUtility] JSON反序列化成功: {typeof(T).Name}");
                return obj;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonUtility] JSON反序列化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 将对象保存为JSON文件
        /// </summary>
        /// <param name="obj">要保存的对象</param>
        /// <param name="filePath">文件路径</param>
        public static void SaveToFile(object obj, string filePath)
        {
            if (obj == null)
            {
                Debug.LogWarning("[JsonUtility] 尝试保存空对象到文件");
                return;
            }
            
            try
            {
                string json = ToJson(obj);
                string directory = Path.GetDirectoryName(filePath);
                
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                File.WriteAllText(filePath, json);
                Debug.Log($"[JsonUtility] 对象保存到文件成功: {filePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonUtility] 保存对象到文件失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 从JSON文件加载对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载的对象</returns>
        public static T LoadFromFile<T>(string filePath)
        {
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"[JsonUtility] 文件不存在: {filePath}");
                return default(T);
            }
            
            try
            {
                string json = File.ReadAllText(filePath);
                T obj = FromJson<T>(json);
                Debug.Log($"[JsonUtility] 从文件加载对象成功: {filePath}");
                return obj;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonUtility] 从文件加载对象失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 验证JSON字符串格式是否正确
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>是否有效</returns>
        public static bool IsValidJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                return false;
            }
            
            try
            {
                JsonConvert.DeserializeObject(json);
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 格式化JSON字符串
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>格式化后的JSON字符串</returns>
        public static string FormatJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                return json;
            }
            
            try
            {
                object obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, _settings);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonUtility] JSON格式化失败: {ex.Message}");
                return json;
            }
        }
    }
}