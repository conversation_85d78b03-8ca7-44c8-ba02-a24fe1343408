using System;
using UnityEngine;

namespace DigitalHuman.Core
{
    /// <summary>
    /// 管理器基础接口，定义所有管理器的通用行为
    /// </summary>
    public interface IManager
    {
        /// <summary>
        /// 管理器是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        void Initialize();
        
        /// <summary>
        /// 清理管理器资源
        /// </summary>
        void Cleanup();
        
        /// <summary>
        /// 管理器初始化完成事件
        /// </summary>
        event Action OnInitialized;
        
        /// <summary>
        /// 管理器清理完成事件
        /// </summary>
        event Action OnCleanup;
    }
}