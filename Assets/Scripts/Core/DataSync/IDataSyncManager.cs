using System;
using System.Threading.Tasks;
using DigitalHuman.Core.DataSync.Models;

namespace DigitalHuman.Core.DataSync
{
    /// <summary>
    /// 数据同步管理器接口 - 定义数据同步的核心功能
    /// </summary>
    public interface IDataSyncManager
    {
        /// <summary>
        /// 同步状态变化事件
        /// </summary>
        event Action<SyncStatus> OnSyncStatusChanged;
        
        /// <summary>
        /// 同步进度更新事件
        /// </summary>
        event Action<SyncProgress> OnSyncProgressUpdated;
        
        /// <summary>
        /// 同步冲突检测事件
        /// </summary>
        event Action<SyncConflict> OnSyncConflictDetected;
        
        /// <summary>
        /// 当前同步状态
        /// </summary>
        SyncStatus CurrentStatus { get; }
        
        /// <summary>
        /// 是否正在同步
        /// </summary>
        bool IsSyncing { get; }
        
        /// <summary>
        /// 最后同步时间
        /// </summary>
        DateTime? LastSyncTime { get; }
        
        /// <summary>
        /// 初始化数据同步管理器
        /// </summary>
        /// <returns>初始化任务</returns>
        Task InitializeAsync();
        
        /// <summary>
        /// 开始数据同步
        /// </summary>
        /// <param name="syncType">同步类型</param>
        /// <returns>同步结果</returns>
        Task<SyncResult> StartSyncAsync(SyncType syncType = SyncType.Full);
        
        /// <summary>
        /// 停止当前同步
        /// </summary>
        /// <returns>停止任务</returns>
        Task StopSyncAsync();
        
        /// <summary>
        /// 解决同步冲突
        /// </summary>
        /// <param name="conflictId">冲突ID</param>
        /// <param name="resolution">解决方案</param>
        /// <returns>解决结果</returns>
        Task<bool> ResolveSyncConflictAsync(string conflictId, ConflictResolution resolution);
        
        /// <summary>
        /// 获取同步历史记录
        /// </summary>
        /// <param name="limit">记录数量限制</param>
        /// <returns>同步历史</returns>
        Task<SyncHistory[]> GetSyncHistoryAsync(int limit = 10);
        
        /// <summary>
        /// 清理同步缓存
        /// </summary>
        /// <returns>清理任务</returns>
        Task ClearSyncCacheAsync();
    }
}