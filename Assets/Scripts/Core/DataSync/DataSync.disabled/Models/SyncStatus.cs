namespace DigitalHuman.Core.DataSync.Models
{
    /// <summary>
    /// 同步状态枚举
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized,
        
        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle,
        
        /// <summary>
        /// 正在同步
        /// </summary>
        Syncing,
        
        /// <summary>
        /// 同步成功
        /// </summary>
        Success,
        
        /// <summary>
        /// 同步失败
        /// </summary>
        Failed,
        
        /// <summary>
        /// 同步冲突
        /// </summary>
        Conflict,
        
        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled
    }
    
    /// <summary>
    /// 同步状态扩展方法
    /// </summary>
    public static class SyncStatusExtensions
    {
        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <param name="status">同步状态</param>
        /// <returns>状态描述</returns>
        public static string GetDescription(this SyncStatus status)
        {
            return status switch
            {
                SyncStatus.NotInitialized => "未初始化",
                SyncStatus.Idle => "空闲",
                SyncStatus.Syncing => "正在同步",
                SyncStatus.Success => "同步成功",
                SyncStatus.Failed => "同步失败",
                SyncStatus.Conflict => "存在冲突",
                SyncStatus.Paused => "已暂停",
                SyncStatus.Cancelled => "已取消",
                _ => "未知状态"
            };
        }
        
        /// <summary>
        /// 判断是否为活跃状态
        /// </summary>
        /// <param name="status">同步状态</param>
        /// <returns>是否活跃</returns>
        public static bool IsActive(this SyncStatus status)
        {
            return status == SyncStatus.Syncing;
        }
        
        /// <summary>
        /// 判断是否为完成状态
        /// </summary>
        /// <param name="status">同步状态</param>
        /// <returns>是否完成</returns>
        public static bool IsCompleted(this SyncStatus status)
        {
            return status == SyncStatus.Success || 
                   status == SyncStatus.Failed || 
                   status == SyncStatus.Cancelled;
        }
    }
}