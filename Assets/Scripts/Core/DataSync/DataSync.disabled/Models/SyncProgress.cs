using System;

namespace DigitalHuman.Core.DataSync.Models
{
    /// <summary>
    /// 同步进度信息
    /// </summary>
    [Serializable]
    public class SyncProgress
    {
        /// <summary>
        /// 当前步骤
        /// </summary>
        public string CurrentStep { get; set; }
        
        /// <summary>
        /// 总进度百分比 (0-100)
        /// </summary>
        public float ProgressPercentage { get; set; }
        
        /// <summary>
        /// 已完成项目数
        /// </summary>
        public int CompletedItems { get; set; }
        
        /// <summary>
        /// 总项目数
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// 当前同步的数据类型
        /// </summary>
        public string DataType { get; set; }
        
        /// <summary>
        /// 预计剩余时间（秒）
        /// </summary>
        public int EstimatedRemainingSeconds { get; set; }
        
        /// <summary>
        /// 同步速度（项目/秒）
        /// </summary>
        public float SyncSpeed { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncProgress()
        {
            CurrentStep = "准备中";
            ProgressPercentage = 0f;
            CompletedItems = 0;
            TotalItems = 0;
            DataType = "";
            EstimatedRemainingSeconds = 0;
            SyncSpeed = 0f;
            StartTime = DateTime.Now;
            LastUpdateTime = DateTime.Now;
        }
        
        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="completed">已完成数量</param>
        /// <param name="total">总数量</param>
        /// <param name="step">当前步骤</param>
        public void UpdateProgress(int completed, int total, string step = null)
        {
            CompletedItems = completed;
            TotalItems = total;
            
            if (total > 0)
            {
                ProgressPercentage = (float)completed / total * 100f;
            }
            
            if (!string.IsNullOrEmpty(step))
            {
                CurrentStep = step;
            }
            
            LastUpdateTime = DateTime.Now;
            
            // 计算同步速度和预计剩余时间
            var elapsed = LastUpdateTime - StartTime;
            if (elapsed.TotalSeconds > 0 && completed > 0)
            {
                SyncSpeed = completed / (float)elapsed.TotalSeconds;
                
                if (SyncSpeed > 0 && total > completed)
                {
                    EstimatedRemainingSeconds = (int)((total - completed) / SyncSpeed);
                }
            }
        }
        
        /// <summary>
        /// 获取格式化的进度文本
        /// </summary>
        /// <returns>进度文本</returns>
        public string GetProgressText()
        {
            if (TotalItems > 0)
            {
                return $"{CompletedItems}/{TotalItems} ({ProgressPercentage:F1}%)";
            }
            return $"{ProgressPercentage:F1}%";
        }
        
        /// <summary>
        /// 获取格式化的剩余时间文本
        /// </summary>
        /// <returns>剩余时间文本</returns>
        public string GetRemainingTimeText()
        {
            if (EstimatedRemainingSeconds <= 0)
            {
                return "计算中...";
            }
            
            var timeSpan = TimeSpan.FromSeconds(EstimatedRemainingSeconds);
            
            if (timeSpan.TotalHours >= 1)
            {
                return $"{timeSpan.Hours}小时{timeSpan.Minutes}分钟";
            }
            else if (timeSpan.TotalMinutes >= 1)
            {
                return $"{timeSpan.Minutes}分钟{timeSpan.Seconds}秒";
            }
            else
            {
                return $"{timeSpan.Seconds}秒";
            }
        }
    }
}