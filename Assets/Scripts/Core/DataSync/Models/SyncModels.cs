using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.DataSync.Models
{
    /// <summary>
    /// 同步类型枚举
    /// </summary>
    public enum SyncType
    {
        /// <summary>
        /// 完整同步
        /// </summary>
        Full,
        
        /// <summary>
        /// 增量同步
        /// </summary>
        Incremental,
        
        /// <summary>
        /// 仅上传
        /// </summary>
        UploadOnly,
        
        /// <summary>
        /// 仅下载
        /// </summary>
        DownloadOnly
    }
    
    /// <summary>
    /// 冲突解决方案枚举
    /// </summary>
    public enum ConflictResolution
    {
        /// <summary>
        /// 使用本地版本
        /// </summary>
        UseLocal,
        
        /// <summary>
        /// 使用远程版本
        /// </summary>
        UseRemote,
        
        /// <summary>
        /// 手动合并
        /// </summary>
        ManualMerge,
        
        /// <summary>
        /// 跳过此项
        /// </summary>
        Skip
    }
    
    /// <summary>
    /// 同步结果
    /// </summary>
    [Serializable]
    public class SyncResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 同步的项目数量
        /// </summary>
        public int SyncedItemsCount { get; set; }
        
        /// <summary>
        /// 冲突数量
        /// </summary>
        public int ConflictsCount { get; set; }
        
        /// <summary>
        /// 跳过的项目数量
        /// </summary>
        public int SkippedItemsCount { get; set; }
        
        /// <summary>
        /// 同步开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 同步结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 同步耗时
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;
        
        /// <summary>
        /// 详细信息
        /// </summary>
        public Dictionary<string, object> Details { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncResult()
        {
            Details = new Dictionary<string, object>();
            StartTime = DateTime.Now;
            EndTime = DateTime.Now;
        }
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="syncedCount">同步数量</param>
        /// <param name="conflictsCount">冲突数量</param>
        /// <returns>同步结果</returns>
        public static SyncResult Success(int syncedCount, int conflictsCount = 0)
        {
            return new SyncResult
            {
                IsSuccess = true,
                SyncedItemsCount = syncedCount,
                ConflictsCount = conflictsCount,
                EndTime = DateTime.Now
            };
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>同步结果</returns>
        public static SyncResult Failure(string errorMessage)
        {
            return new SyncResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                EndTime = DateTime.Now
            };
        }
    }
    
    /// <summary>
    /// 同步冲突信息
    /// </summary>
    [Serializable]
    public class SyncConflict
    {
        /// <summary>
        /// 冲突ID
        /// </summary>
        public string ConflictId { get; set; }
        
        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
        
        /// <summary>
        /// 数据键
        /// </summary>
        public string DataKey { get; set; }
        
        /// <summary>
        /// 本地版本
        /// </summary>
        public object LocalVersion { get; set; }
        
        /// <summary>
        /// 远程版本
        /// </summary>
        public object RemoteVersion { get; set; }
        
        /// <summary>
        /// 本地修改时间
        /// </summary>
        public DateTime LocalModifiedTime { get; set; }
        
        /// <summary>
        /// 远程修改时间
        /// </summary>
        public DateTime RemoteModifiedTime { get; set; }
        
        /// <summary>
        /// 冲突描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncConflict()
        {
            ConflictId = Guid.NewGuid().ToString();
        }
    }
    
    /// <summary>
    /// 同步历史记录
    /// </summary>
    [Serializable]
    public class SyncHistory
    {
        /// <summary>
        /// 同步ID
        /// </summary>
        public string SyncId { get; set; }
        
        /// <summary>
        /// 同步类型
        /// </summary>
        public SyncType SyncType { get; set; }
        
        /// <summary>
        /// 同步状态
        /// </summary>
        public SyncStatus Status { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 同步结果
        /// </summary>
        public SyncResult Result { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncHistory()
        {
            SyncId = Guid.NewGuid().ToString();
            StartTime = DateTime.Now;
        }
        
        /// <summary>
        /// 获取持续时间
        /// </summary>
        /// <returns>持续时间</returns>
        public TimeSpan? GetDuration()
        {
            if (EndTime.HasValue)
            {
                return EndTime.Value - StartTime;
            }
            return null;
        }
        
        /// <summary>
        /// 获取格式化的持续时间文本
        /// </summary>
        /// <returns>持续时间文本</returns>
        public string GetDurationText()
        {
            var duration = GetDuration();
            if (duration.HasValue)
            {
                if (duration.Value.TotalMinutes >= 1)
                {
                    return $"{duration.Value.Minutes}分{duration.Value.Seconds}秒";
                }
                else
                {
                    return $"{duration.Value.Seconds}秒";
                }
            }
            return "进行中";
        }
    }
}