using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.DataSync.Models;
using DigitalHuman.Core.Authentication;

namespace DigitalHuman.Core.DataSync
{
    /// <summary>
    /// 数据同步管理器 - 负责用户数据的云端同步
    /// </summary>
    public class DataSyncManager : Mono<PERSON><PERSON><PERSON>our, IDataSyncManager
    {
        [Header("同步配置")]
        [SerializeField] private bool autoSyncEnabled = true;
        [SerializeField] private int autoSyncIntervalMinutes = 30;
        [SerializeField] private int maxRetryAttempts = 3;
        [SerializeField] private float retryDelaySeconds = 5f;
        
        // 单例实例
        private static DataSyncManager _instance;
        public static DataSyncManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<DataSyncManager>();
                    if (_instance == null)
                    {
                        var go = new GameObject("DataSyncManager");
                        _instance = go.AddComponent<DataSyncManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        
        // 事件
        public event Action<SyncStatus> OnSyncStatusChanged;
        public event Action<SyncProgress> OnSyncProgressUpdated;
        public event Action<SyncConflict> OnSyncConflictDetected;
        
        // 属性
        public SyncStatus CurrentStatus { get; private set; } = SyncStatus.NotInitialized;
        public bool IsSyncing => CurrentStatus == SyncStatus.Syncing;
        public DateTime? LastSyncTime { get; private set; }
        
        // 私有字段
        private SyncProgress currentProgress;
        private List<SyncConflict> pendingConflicts;
        private List<SyncHistory> syncHistory;
        private System.Threading.CancellationTokenSource cancellationTokenSource;
        private AuthenticationManager authManager;
        private bool isInitialized = false;
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            // 初始化集合
            pendingConflicts = new List<SyncConflict>();
            syncHistory = new List<SyncHistory>();
            currentProgress = new SyncProgress();
        }
        
        private void Start()
        {
            _ = InitializeAsync();
        }
        
        /// <summary>
        /// 初始化数据同步管理器
        /// </summary>
        public async Task InitializeAsync()
        {
            if (isInitialized) return;
            
            try
            {
                Debug.Log("初始化数据同步管理器...");
                
                // 获取认证管理器
                authManager = AuthenticationManager.Instance;
                if (authManager == null)
                {
                    Debug.LogWarning("认证管理器未找到，数据同步功能可能受限");
                }
                
                // 加载同步历史
                await LoadSyncHistoryAsync();
                
                // 设置状态
                SetSyncStatus(SyncStatus.Idle);
                
                isInitialized = true;
                Debug.Log("数据同步管理器初始化完成");
                
                // 如果启用自动同步，开始定时同步
                if (autoSyncEnabled)
                {
                    StartAutoSync();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"数据同步管理器初始化失败: {ex.Message}");
                SetSyncStatus(SyncStatus.Failed);
            }
        }
        
        /// <summary>
        /// 开始数据同步
        /// </summary>
        public async Task<SyncResult> StartSyncAsync(SyncType syncType = SyncType.Full)
        {
            if (IsSyncing)
            {
                Debug.LogWarning("同步已在进行中");
                return SyncResult.Failure("同步已在进行中");
            }
            
            if (authManager == null || !authManager.IsLoggedIn)
            {
                Debug.LogWarning("用户未登录，无法进行数据同步");
                return SyncResult.Failure("用户未登录");
            }
            
            try
            {
                Debug.Log($"开始数据同步，类型: {syncType}");
                
                // 创建取消令牌
                cancellationTokenSource = new System.Threading.CancellationTokenSource();
                
                // 创建同步历史记录
                var history = new SyncHistory
                {
                    SyncType = syncType,
                    Status = SyncStatus.Syncing,
                    StartTime = DateTime.Now
                };
                syncHistory.Insert(0, history);
                
                // 设置同步状态
                SetSyncStatus(SyncStatus.Syncing);
                
                // 重置进度
                currentProgress = new SyncProgress();
                OnSyncProgressUpdated?.Invoke(currentProgress);
                
                // 执行同步
                var result = await PerformSyncAsync(syncType, cancellationTokenSource.Token);
                
                // 更新历史记录
                history.EndTime = DateTime.Now;
                history.Status = result.IsSuccess ? SyncStatus.Success : SyncStatus.Failed;
                history.Result = result;
                
                // 设置最终状态
                SetSyncStatus(result.IsSuccess ? SyncStatus.Success : SyncStatus.Failed);
                
                if (result.IsSuccess)
                {
                    LastSyncTime = DateTime.Now;
                    Debug.Log($"数据同步完成，同步了 {result.SyncedItemsCount} 个项目");
                }
                else
                {
                    Debug.LogError($"数据同步失败: {result.ErrorMessage}");
                }
                
                return result;
            }
            catch (OperationCanceledException)
            {
                Debug.Log("数据同步已取消");
                SetSyncStatus(SyncStatus.Cancelled);
                return SyncResult.Failure("同步已取消");
            }
            catch (Exception ex)
            {
                Debug.LogError($"数据同步异常: {ex.Message}");
                SetSyncStatus(SyncStatus.Failed);
                return SyncResult.Failure($"同步异常: {ex.Message}");
            }
            finally
            {
                cancellationTokenSource?.Dispose();
                cancellationTokenSource = null;
            }
        }
        
        /// <summary>
        /// 停止当前同步
        /// </summary>
        public async Task StopSyncAsync()
        {
            if (!IsSyncing)
            {
                Debug.LogWarning("当前没有进行中的同步");
                return;
            }
            
            Debug.Log("停止数据同步...");
            
            cancellationTokenSource?.Cancel();
            
            // 等待同步停止
            int waitCount = 0;
            while (IsSyncing && waitCount < 50) // 最多等待5秒
            {
                await Task.Delay(100);
                waitCount++;
            }
            
            if (IsSyncing)
            {
                Debug.LogWarning("强制停止同步");
                SetSyncStatus(SyncStatus.Cancelled);
            }
            
            Debug.Log("数据同步已停止");
        }
        
        /// <summary>
        /// 解决同步冲突
        /// </summary>
        public async Task<bool> ResolveSyncConflictAsync(string conflictId, ConflictResolution resolution)
        {
            try
            {
                var conflict = pendingConflicts.Find(c => c.ConflictId == conflictId);
                if (conflict == null)
                {
                    Debug.LogWarning($"未找到冲突: {conflictId}");
                    return false;
                }
                
                Debug.Log($"解决同步冲突: {conflictId}, 解决方案: {resolution}");
                
                // 根据解决方案处理冲突
                bool resolved = await ProcessConflictResolutionAsync(conflict, resolution);
                
                if (resolved)
                {
                    pendingConflicts.Remove(conflict);
                    Debug.Log($"冲突已解决: {conflictId}");
                    
                    // 如果所有冲突都已解决，更新状态
                    if (pendingConflicts.Count == 0 && CurrentStatus == SyncStatus.Conflict)
                    {
                        SetSyncStatus(SyncStatus.Success);
                    }
                }
                
                return resolved;
            }
            catch (Exception ex)
            {
                Debug.LogError($"解决冲突异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取同步历史记录
        /// </summary>
        public async Task<SyncHistory[]> GetSyncHistoryAsync(int limit = 10)
        {
            await Task.CompletedTask; // 模拟异步操作
            
            int count = Math.Min(limit, syncHistory.Count);
            var result = new SyncHistory[count];
            
            for (int i = 0; i < count; i++)
            {
                result[i] = syncHistory[i];
            }
            
            return result;
        }
        
        /// <summary>
        /// 清理同步缓存
        /// </summary>
        public async Task ClearSyncCacheAsync()
        {
            try
            {
                Debug.Log("清理同步缓存...");
                
                // 模拟清理操作
                await Task.Delay(1000);
                
                // 清理内存中的数据
                pendingConflicts.Clear();
                
                // 保留最近的10条历史记录
                if (syncHistory.Count > 10)
                {
                    syncHistory.RemoveRange(10, syncHistory.Count - 10);
                }
                
                Debug.Log("同步缓存清理完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"清理同步缓存异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 执行实际的同步操作
        /// </summary>
        private async Task<SyncResult> PerformSyncAsync(SyncType syncType, System.Threading.CancellationToken cancellationToken)
        {
            var result = new SyncResult { StartTime = DateTime.Now };
            
            try
            {
                // 模拟同步过程
                var syncSteps = GetSyncSteps(syncType);
                int totalSteps = syncSteps.Count;
                int completedSteps = 0;
                
                foreach (var step in syncSteps)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    // 更新进度
                    currentProgress.UpdateProgress(completedSteps, totalSteps, step);
                    OnSyncProgressUpdated?.Invoke(currentProgress);
                    
                    // 模拟步骤执行
                    await Task.Delay(UnityEngine.Random.Range(500, 1500), cancellationToken);
                    
                    // 随机生成一些冲突（用于演示）
                    if (UnityEngine.Random.Range(0f, 1f) < 0.1f) // 10%概率产生冲突
                    {
                        var conflict = GenerateRandomConflict(step);
                        pendingConflicts.Add(conflict);
                        OnSyncConflictDetected?.Invoke(conflict);
                        result.ConflictsCount++;
                    }
                    
                    completedSteps++;
                    result.SyncedItemsCount++;
                }
                
                // 完成同步
                currentProgress.UpdateProgress(totalSteps, totalSteps, "同步完成");
                OnSyncProgressUpdated?.Invoke(currentProgress);
                
                result.EndTime = DateTime.Now;
                result.IsSuccess = true;
                
                // 如果有冲突，设置冲突状态
                if (result.ConflictsCount > 0)
                {
                    SetSyncStatus(SyncStatus.Conflict);
                }
                
                return result;
            }
            catch (OperationCanceledException)
            {
                result.EndTime = DateTime.Now;
                result.IsSuccess = false;
                result.ErrorMessage = "同步已取消";
                throw;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }
        
        /// <summary>
        /// 获取同步步骤
        /// </summary>
        private List<string> GetSyncSteps(SyncType syncType)
        {
            var steps = new List<string>();
            
            switch (syncType)
            {
                case SyncType.Full:
                    steps.AddRange(new[]
                    {
                        "检查网络连接",
                        "验证用户身份",
                        "获取远程数据清单",
                        "比较本地和远程数据",
                        "下载用户设置",
                        "下载对话历史",
                        "下载自定义配置",
                        "上传本地更改",
                        "解决数据冲突",
                        "更新本地索引"
                    });
                    break;
                    
                case SyncType.Incremental:
                    steps.AddRange(new[]
                    {
                        "检查网络连接",
                        "获取增量更新",
                        "下载新数据",
                        "上传本地更改",
                        "更新本地索引"
                    });
                    break;
                    
                case SyncType.UploadOnly:
                    steps.AddRange(new[]
                    {
                        "检查网络连接",
                        "准备上传数据",
                        "上传用户设置",
                        "上传对话历史",
                        "验证上传结果"
                    });
                    break;
                    
                case SyncType.DownloadOnly:
                    steps.AddRange(new[]
                    {
                        "检查网络连接",
                        "获取远程数据",
                        "下载用户设置",
                        "下载对话历史",
                        "更新本地数据"
                    });
                    break;
            }
            
            return steps;
        }
        
        /// <summary>
        /// 生成随机冲突（用于演示）
        /// </summary>
        private SyncConflict GenerateRandomConflict(string step)
        {
            var conflict = new SyncConflict
            {
                DataType = "用户设置",
                DataKey = $"setting_{UnityEngine.Random.Range(1, 100)}",
                Description = $"在 {step} 步骤中检测到数据冲突",
                LocalModifiedTime = DateTime.Now.AddMinutes(-UnityEngine.Random.Range(1, 60)),
                RemoteModifiedTime = DateTime.Now.AddMinutes(-UnityEngine.Random.Range(1, 60)),
                LocalVersion = "本地值",
                RemoteVersion = "远程值"
            };
            
            return conflict;
        }
        
        /// <summary>
        /// 处理冲突解决方案
        /// </summary>
        private async Task<bool> ProcessConflictResolutionAsync(SyncConflict conflict, ConflictResolution resolution)
        {
            // 模拟处理时间
            await Task.Delay(500);
            
            switch (resolution)
            {
                case ConflictResolution.UseLocal:
                    Debug.Log($"使用本地版本解决冲突: {conflict.DataKey}");
                    return true;
                    
                case ConflictResolution.UseRemote:
                    Debug.Log($"使用远程版本解决冲突: {conflict.DataKey}");
                    return true;
                    
                case ConflictResolution.ManualMerge:
                    Debug.Log($"手动合并解决冲突: {conflict.DataKey}");
                    return true;
                    
                case ConflictResolution.Skip:
                    Debug.Log($"跳过冲突: {conflict.DataKey}");
                    return true;
                    
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 设置同步状态
        /// </summary>
        private void SetSyncStatus(SyncStatus status)
        {
            if (CurrentStatus != status)
            {
                CurrentStatus = status;
                OnSyncStatusChanged?.Invoke(status);
                Debug.Log($"同步状态变更: {status.GetDescription()}");
            }
        }
        
        /// <summary>
        /// 加载同步历史
        /// </summary>
        private async Task LoadSyncHistoryAsync()
        {
            // 模拟从本地存储加载历史记录
            await Task.Delay(100);
            
            // 这里可以从PlayerPrefs或文件中加载实际的历史记录
            // 目前使用空列表
            syncHistory.Clear();
        }
        
        /// <summary>
        /// 开始自动同步
        /// </summary>
        private void StartAutoSync()
        {
            if (autoSyncEnabled && autoSyncIntervalMinutes > 0)
            {
                InvokeRepeating(nameof(AutoSyncCheck), autoSyncIntervalMinutes * 60f, autoSyncIntervalMinutes * 60f);
                Debug.Log($"自动同步已启用，间隔: {autoSyncIntervalMinutes} 分钟");
            }
        }
        
        /// <summary>
        /// 自动同步检查
        /// </summary>
        private async void AutoSyncCheck()
        {
            if (!IsSyncing && authManager != null && authManager.IsLoggedIn)
            {
                Debug.Log("执行自动同步...");
                await StartSyncAsync(SyncType.Incremental);
            }
        }
        
        /// <summary>
        /// 获取待解决的冲突列表
        /// </summary>
        public SyncConflict[] GetPendingConflicts()
        {
            return pendingConflicts.ToArray();
        }
        
        /// <summary>
        /// 获取当前同步进度
        /// </summary>
        public SyncProgress GetCurrentProgress()
        {
            return currentProgress;
        }
        
        private void OnDestroy()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
            
            CancelInvoke();
        }
    }
}