# 基础架构模块 (Base Module)

## 概述

基础架构模块提供了数字人管理系统的核心基础设施，包括单例管理器、基础接口定义和通用工具类。这些组件为整个系统提供了统一的架构基础和设计模式。

## 核心组件

### SingletonManager<T>
线程安全的泛型单例管理器基类，为系统中的各种管理器提供统一的单例模式实现。

**主要特性：**
- 线程安全的双重检查锁定模式
- 自动实例创建和GameObject管理
- 场景切换时的实例持久化
- 重复实例检测和自动清理
- 统一的初始化流程和状态管理
- 应用退出时的安全保护

**使用示例：**
```csharp
public class AudioManager : SingletonManager<AudioManager>
{
    protected override void InitializeManager()
    {
        if (IsInitialized) return;
        
        try
        {
            // 执行初始化逻辑
            InitializeAudioSources();
            LoadConfiguration();
            
            IsInitialized = true;
            Debug.Log("[AudioManager] 初始化完成");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[AudioManager] 初始化失败: {ex.Message}");
            IsInitialized = false;
            throw;
        }
    }
}

// 使用管理器
var audioManager = AudioManager.Instance;
if (audioManager != null && audioManager.IsInitialized)
{
    audioManager.PlayMusic(clip);
}
```

## 文件结构

```
Base/
├── SingletonManager.cs          # 单例管理器基类
├── Examples/                    # 使用示例
│   └── SingletonManagerExample.cs # 完整使用示例
└── README.md                    # 模块说明文档
```

## 设计原则

### 1. 统一性
所有系统管理器都继承自 `SingletonManager<T>`，确保一致的创建、初始化和生命周期管理。

### 2. 线程安全
使用双重检查锁定模式确保在多线程环境下的安全性，避免竞态条件。

### 3. 自动化
自动处理实例创建、GameObject管理、场景持久化等常见任务，减少样板代码。

### 4. 错误处理
提供统一的异常处理机制，确保初始化失败时的优雅降级。

### 5. 调试友好
提供详细的日志输出和状态检查，便于开发和调试。

## 最佳实践

### 1. 初始化检查
```csharp
public void SomeMethod()
{
    var manager = SomeManager.Instance;
    if (manager == null || !manager.IsInitialized)
    {
        Debug.LogWarning("管理器未准备就绪");
        return;
    }
    
    // 安全使用管理器
    manager.DoSomething();
}
```

### 2. 异步初始化
```csharp
protected override void InitializeManager()
{
    if (IsInitialized) return;
    
    StartCoroutine(InitializeAsync());
}

private IEnumerator InitializeAsync()
{
    // 异步初始化逻辑
    yield return LoadResourcesAsync();
    yield return ConnectToServerAsync();
    
    IsInitialized = true;
}
```

### 3. 依赖管理
```csharp
protected override void InitializeManager()
{
    if (IsInitialized) return;
    
    // 等待依赖的管理器初始化
    var configManager = ConfigurationManager.Instance;
    if (configManager == null || !configManager.IsInitialized)
    {
        Debug.LogWarning("等待配置管理器初始化");
        StartCoroutine(WaitForDependencies());
        return;
    }
    
    // 执行初始化
    PerformInitialization();
}
```

### 4. 资源清理
```csharp
protected override void OnDestroy()
{
    try
    {
        // 执行清理逻辑
        CleanupResources();
        Debug.Log($"[{GetType().Name}] 资源清理完成");
    }
    catch (Exception ex)
    {
        Debug.LogError($"[{GetType().Name}] 资源清理失败: {ex.Message}");
    }
    finally
    {
        base.OnDestroy();
    }
}
```

## 性能考虑

### 1. 延迟初始化
实例只在首次访问时创建，避免不必要的资源消耗。

### 2. 锁优化
使用双重检查锁定减少锁竞争，正常访问时无锁开销。

### 3. 内存管理
自动管理GameObject生命周期，防止内存泄漏。

## 调试工具

### 状态监控
```csharp
public static void CheckAllManagers()
{
    var managers = new[]
    {
        ("LogManager", LogManager.Instance),
        ("ConfigManager", ConfigurationManager.Instance),
        ("AudioManager", AudioManager.Instance)
    };
    
    foreach (var (name, instance) in managers)
    {
        if (instance == null)
        {
            Debug.LogWarning($"{name}: 实例为空");
        }
        else if (!instance.IsInitialized)
        {
            Debug.LogWarning($"{name}: 未初始化");
        }
        else
        {
            Debug.Log($"{name}: 正常运行");
        }
    }
}
```

## 扩展功能

### 异步单例管理器
```csharp
public abstract class AsyncSingletonManager<T> : SingletonManager<T> 
    where T : AsyncSingletonManager<T>
{
    protected override void InitializeManager()
    {
        if (IsInitialized) return;
        StartCoroutine(InitializeAsync());
    }
    
    protected abstract IEnumerator InitializeAsync();
}
```

## 相关文档

- [SingletonManager 详细文档](../../../docs/core/SingletonManager.md)
- [LogManager 日志管理器](../Logging/README.md)
- [ConfigurationManager 配置管理器](../Configuration/README.md)
- [项目架构文档](../../../../README.md)

## 版本历史

- **v1.0**: 初始实现，提供基础单例模式支持
- **v1.1**: 添加线程安全保护和重复实例检测
- **v1.2**: 增强异常处理和调试功能