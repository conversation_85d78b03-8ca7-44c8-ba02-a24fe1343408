using UnityEngine;

namespace DigitalHuman.Core.Base
{
    /// <summary>
    /// 单例管理器基类，提供线程安全的单例模式实现
    /// </summary>
    /// <typeparam name="T">管理器类型</typeparam>
    public abstract class SingletonManager<T> : MonoBehaviour where T : SingletonManager<T>
    {
        private static T _instance;
        private static readonly object _lock = new object();
        private static bool _applicationIsQuitting = false;
        
        /// <summary>
        /// 单例实例
        /// </summary>
        public static T Instance
        {
            get
            {
                if (_applicationIsQuitting)
                {
                    Debug.LogWarning($"[SingletonManager] 应用程序正在退出，无法访问 {typeof(T)} 实例");
                    return null;
                }
                
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = FindObjectOfType<T>();
                        
                        if (_instance == null)
                        {
                            GameObject singletonObject = new GameObject();
                            _instance = singletonObject.AddComponent<T>();
                            singletonObject.name = $"{typeof(T).Name} (Singleton)";
                            
                            // 确保单例对象在场景切换时不被销毁
                            DontDestroyOnLoad(singletonObject);
                        }
                        
                        // 初始化管理器
                        if (_instance != null)
                        {
                            _instance.InitializeManager();
                        }
                    }
                    
                    return _instance;
                }
            }
        }
        
        /// <summary>
        /// 管理器是否已初始化
        /// </summary>
        public bool IsInitialized { get; private set; }
        
        /// <summary>
        /// Unity Awake 生命周期
        /// </summary>
        protected virtual void Awake()
        {
            lock (_lock)
            {
                if (_instance == null)
                {
                    _instance = this as T;
                    DontDestroyOnLoad(gameObject);
                    InitializeManager();
                }
                else if (_instance != this)
                {
                    Debug.LogWarning($"[SingletonManager] 检测到重复的 {typeof(T)} 实例，销毁多余实例");
                    Destroy(gameObject);
                    return;
                }
            }
        }
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        protected virtual void InitializeManager()
        {
            if (IsInitialized)
            {
                return;
            }
            
            try
            {
                Debug.Log($"[{typeof(T).Name}] 开始初始化");
                IsInitialized = true;
                Debug.Log($"[{typeof(T).Name}] 初始化完成");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[{typeof(T).Name}] 初始化失败: {ex.Message}");
                IsInitialized = false;
                throw;
            }
        }
        
        /// <summary>
        /// Unity OnApplicationQuit 生命周期
        /// </summary>
        protected virtual void OnApplicationQuit()
        {
            _applicationIsQuitting = true;
        }
        
        /// <summary>
        /// Unity OnDestroy 生命周期
        /// </summary>
        protected virtual void OnDestroy()
        {
            lock (_lock)
            {
                if (_instance == this)
                {
                    _instance = null;
                }
            }
        }
    }
}