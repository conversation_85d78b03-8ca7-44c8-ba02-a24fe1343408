using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;

namespace DigitalHuman.Core.Configuration
{
    /// <summary>
    /// 配置管理器，负责处理配置文件的读写、加密存储和验证
    /// </summary>
    public class ConfigurationManager : SingletonManager<ConfigurationManager>
    {
        [Header("配置文件路径")]
        [SerializeField] private string _configDirectory = "Config";
        [SerializeField] private string _userConfigFileName = "user_config.json";
        [SerializeField] private string _serviceConfigFileName = "service_config.json";
        [SerializeField] private string _securityConfigFileName = "security_config.json";
        [SerializeField] private string _loggingConfigFileName = "logging_config.json";
        
        [Header("加密设置")]
        [SerializeField] private bool _enableEncryption = true;
        [SerializeField] private string _encryptionKey = "DigitalHuman2024ConfigKey123456"; // 32字符AES密钥
        
        private string ConfigDirectoryPath => Path.Combine(Application.persistentDataPath, _configDirectory);
        private string UserConfigPath => Path.Combine(ConfigDirectoryPath, _userConfigFileName);
        private string ServiceConfigPath => Path.Combine(ConfigDirectoryPath, _serviceConfigFileName);
        private string SecurityConfigPath => Path.Combine(ConfigDirectoryPath, _securityConfigFileName);
        private string LoggingConfigPath => Path.Combine(ConfigDirectoryPath, _loggingConfigFileName);
        
        /// <summary>
        /// 配置加载完成事件
        /// </summary>
        public event Action OnConfigurationLoaded;
        
        /// <summary>
        /// 配置保存完成事件
        /// </summary>
        public event Action OnConfigurationSaved;
        
        /// <summary>
        /// 配置验证失败事件
        /// </summary>
        public event Action<string> OnConfigurationValidationFailed;
        
        protected override void OnInitializeInternal()
        {
            // 确保配置目录存在
            EnsureConfigDirectoryExists();
            
            // 验证加密密钥
            ValidateEncryptionKey();
            
            Debug.Log("[ConfigurationManager] 配置管理器初始化完成");
        }
        
        protected override void OnCleanupInternal()
        {
            Debug.Log("[ConfigurationManager] 配置管理器清理完成");
        }
        
        /// <summary>
        /// 确保配置目录存在
        /// </summary>
        private void EnsureConfigDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(ConfigDirectoryPath))
                {
                    Directory.CreateDirectory(ConfigDirectoryPath);
                    Debug.Log($"[ConfigurationManager] 创建配置目录: {ConfigDirectoryPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 创建配置目录失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 验证加密密钥
        /// </summary>
        private void ValidateEncryptionKey()
        {
            if (_enableEncryption && string.IsNullOrEmpty(_encryptionKey))
            {
                throw new InvalidOperationException("启用加密时必须提供加密密钥");
            }
            
            if (_enableEncryption && _encryptionKey.Length != 32)
            {
                throw new InvalidOperationException("AES-256 加密密钥必须为32字符长度");
            }
        }        

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="config">配置对象</param>
        /// <param name="configType">配置类型</param>
        public void SaveConfiguration<T>(T config, ConfigurationType configType) where T : class
        {
            if (config == null)
            {
                Debug.LogWarning("[ConfigurationManager] 尝试保存空配置对象");
                return;
            }
            
            try
            {
                string filePath = GetConfigFilePath(configType);
                string jsonData = JsonUtility.ToJson(config);
                
                if (_enableEncryption && IsEncryptionRequired(configType))
                {
                    jsonData = EncryptData(jsonData);
                }
                
                File.WriteAllText(filePath, jsonData);
                Debug.Log($"[ConfigurationManager] 配置保存成功: {configType}");
                
                OnConfigurationSaved?.Invoke();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 保存配置失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="configType">配置类型</param>
        /// <returns>配置对象</returns>
        public T LoadConfiguration<T>(ConfigurationType configType) where T : class, new()
        {
            try
            {
                string filePath = GetConfigFilePath(configType);
                
                if (!File.Exists(filePath))
                {
                    Debug.LogWarning($"[ConfigurationManager] 配置文件不存在，创建默认配置: {configType}");
                    return CreateDefaultConfiguration<T>(configType);
                }
                
                string jsonData = File.ReadAllText(filePath);
                
                if (_enableEncryption && IsEncryptionRequired(configType))
                {
                    jsonData = DecryptData(jsonData);
                }
                
                T config = JsonUtility.FromJson<T>(jsonData);
                
                if (config == null)
                {
                    Debug.LogWarning($"[ConfigurationManager] 配置反序列化失败，使用默认配置: {configType}");
                    return CreateDefaultConfiguration<T>(configType);
                }
                
                // 验证配置
                if (!ValidateConfiguration(config, configType))
                {
                    Debug.LogWarning($"[ConfigurationManager] 配置验证失败，使用默认配置: {configType}");
                    return CreateDefaultConfiguration<T>(configType);
                }
                
                Debug.Log($"[ConfigurationManager] 配置加载成功: {configType}");
                OnConfigurationLoaded?.Invoke();
                
                return config;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 加载配置失败: {ex.Message}");
                return CreateDefaultConfiguration<T>(configType);
            }
        }
        
        /// <summary>
        /// 使用AES-256加密数据
        /// </summary>
        /// <param name="plainText">明文数据</param>
        /// <returns>加密后的数据</returns>
        public string EncryptData(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                return plainText;
            }
            
            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(_encryptionKey);
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    
                    // 生成随机IV
                    aes.GenerateIV();
                    
                    using (ICryptoTransform encryptor = aes.CreateEncryptor())
                    using (MemoryStream msEncrypt = new MemoryStream())
                    {
                        // 将IV写入流的开头
                        msEncrypt.Write(aes.IV, 0, aes.IV.Length);
                        
                        using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                        
                        byte[] encryptedData = msEncrypt.ToArray();
                        return Convert.ToBase64String(encryptedData);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 数据加密失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 使用AES-256解密数据
        /// </summary>
        /// <param name="cipherText">加密数据</param>
        /// <returns>解密后的数据</returns>
        public string DecryptData(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
            {
                return cipherText;
            }
            
            try
            {
                byte[] encryptedData = Convert.FromBase64String(cipherText);
                
                using (Aes aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(_encryptionKey);
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    
                    // 从数据开头读取IV
                    byte[] iv = new byte[16];
                    Array.Copy(encryptedData, 0, iv, 0, 16);
                    aes.IV = iv;
                    
                    using (ICryptoTransform decryptor = aes.CreateDecryptor())
                    using (MemoryStream msDecrypt = new MemoryStream(encryptedData, 16, encryptedData.Length - 16))
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 数据解密失败: {ex.Message}");
                throw;
            }
        }   
     
        /// <summary>
        /// 验证配置对象
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <param name="configType">配置类型</param>
        /// <returns>是否验证通过</returns>
        private bool ValidateConfiguration(object config, ConfigurationType configType)
        {
            if (config == null)
            {
                OnConfigurationValidationFailed?.Invoke($"配置对象为空: {configType}");
                return false;
            }
            
            try
            {
                // 根据配置类型进行特定验证
                switch (configType)
                {
                    case ConfigurationType.User:
                        return ValidateUserConfiguration(config);
                    case ConfigurationType.Service:
                        return ValidateServiceConfiguration(config);
                    case ConfigurationType.Security:
                        return ValidateSecurityConfiguration(config);
                    case ConfigurationType.Logging:
                        return ValidateLoggingConfiguration(config);
                    default:
                        return true;
                }
            }
            catch (Exception ex)
            {
                OnConfigurationValidationFailed?.Invoke($"配置验证异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 验证用户配置
        /// </summary>
        /// <param name="config">用户配置对象</param>
        /// <returns>是否验证通过</returns>
        private bool ValidateUserConfiguration(object config)
        {
            // 基础验证，具体验证逻辑将在数据模型实现时完善
            return config != null;
        }
        
        /// <summary>
        /// 验证服务配置
        /// </summary>
        /// <param name="config">服务配置对象</param>
        /// <returns>是否验证通过</returns>
        private bool ValidateServiceConfiguration(object config)
        {
            // 基础验证，具体验证逻辑将在数据模型实现时完善
            return config != null;
        }
        
        /// <summary>
        /// 验证安全配置
        /// </summary>
        /// <param name="config">安全配置对象</param>
        /// <returns>是否验证通过</returns>
        private bool ValidateSecurityConfiguration(object config)
        {
            // 基础验证，具体验证逻辑将在数据模型实现时完善
            return config != null;
        }
        
        /// <summary>
        /// 验证日志配置
        /// </summary>
        /// <param name="config">日志配置对象</param>
        /// <returns>是否验证通过</returns>
        private bool ValidateLoggingConfiguration(object config)
        {
            if (config == null)
            {
                OnConfigurationValidationFailed?.Invoke("日志配置对象为空");
                return false;
            }
            
            // 基础验证，具体验证逻辑将在LogConfiguration类中实现
            return true;
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="configType">配置类型</param>
        /// <returns>默认配置对象</returns>
        private T CreateDefaultConfiguration<T>(ConfigurationType configType) where T : class, new()
        {
            T defaultConfig = new T();
            
            // 保存默认配置到文件
            try
            {
                SaveConfiguration(defaultConfig, configType);
                Debug.Log($"[ConfigurationManager] 创建并保存默认配置: {configType}");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"[ConfigurationManager] 保存默认配置失败: {ex.Message}");
            }
            
            return defaultConfig;
        }
        
        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <param name="configType">配置类型</param>
        /// <returns>文件路径</returns>
        private string GetConfigFilePath(ConfigurationType configType)
        {
            return configType switch
            {
                ConfigurationType.User => UserConfigPath,
                ConfigurationType.Service => ServiceConfigPath,
                ConfigurationType.Security => SecurityConfigPath,
                ConfigurationType.Logging => LoggingConfigPath,
                _ => throw new ArgumentException($"未知的配置类型: {configType}")
            };
        }
        
        /// <summary>
        /// 判断配置类型是否需要加密
        /// </summary>
        /// <param name="configType">配置类型</param>
        /// <returns>是否需要加密</returns>
        private bool IsEncryptionRequired(ConfigurationType configType)
        {
            return configType switch
            {
                ConfigurationType.Service => true,  // 服务配置包含API密钥等敏感信息
                ConfigurationType.Security => true, // 安全配置需要加密
                ConfigurationType.User => false,    // 用户配置一般不包含敏感信息
                ConfigurationType.Logging => false, // 日志配置一般不包含敏感信息
                _ => false
            };
        }
        
        /// <summary>
        /// 导入配置文件
        /// </summary>
        /// <param name="filePath">源文件路径</param>
        /// <param name="configType">配置类型</param>
        public void ImportConfiguration(string filePath, ConfigurationType configType)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"配置文件不存在: {filePath}");
            }
            
            try
            {
                string targetPath = GetConfigFilePath(configType);
                File.Copy(filePath, targetPath, true);
                Debug.Log($"[ConfigurationManager] 配置导入成功: {configType}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 配置导入失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 导出配置文件
        /// </summary>
        /// <param name="configType">配置类型</param>
        /// <param name="targetPath">目标文件路径</param>
        public void ExportConfiguration(ConfigurationType configType, string targetPath)
        {
            string sourcePath = GetConfigFilePath(configType);
            
            if (!File.Exists(sourcePath))
            {
                throw new FileNotFoundException($"配置文件不存在: {sourcePath}");
            }
            
            try
            {
                string targetDirectory = Path.GetDirectoryName(targetPath);
                if (!string.IsNullOrEmpty(targetDirectory) && !Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }
                
                File.Copy(sourcePath, targetPath, true);
                Debug.Log($"[ConfigurationManager] 配置导出成功: {configType} -> {targetPath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 配置导出失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="configType">配置类型</param>
        /// <returns>重置后的配置对象</returns>
        public T ResetToDefault<T>(ConfigurationType configType) where T : class, new()
        {
            try
            {
                string filePath = GetConfigFilePath(configType);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                
                T defaultConfig = CreateDefaultConfiguration<T>(configType);
                Debug.Log($"[ConfigurationManager] 配置重置为默认值: {configType}");
                
                return defaultConfig;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 重置配置失败: {ex.Message}");
                throw;
            }
        }
    }
    
    /// <summary>
    /// 配置类型枚举
    /// </summary>
    public enum ConfigurationType
    {
        /// <summary>
        /// 用户配置
        /// </summary>
        User,
        
        /// <summary>
        /// 服务配置
        /// </summary>
        Service,
        
        /// <summary>
        /// 安全配置
        /// </summary>
        Security,
        
        /// <summary>
        /// 日志配置
        /// </summary>
        Logging
    }
}