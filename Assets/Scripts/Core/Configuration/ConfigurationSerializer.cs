using System;
using System.IO;
using UnityEngine;
using DigitalHuman.Core.Configuration.Models;

namespace DigitalHuman.Core.Configuration
{
    /// <summary>
    /// 配置序列化工具类，提供配置对象的序列化和反序列化功能
    /// </summary>
    public static class ConfigurationSerializer
    {
        /// <summary>
        /// 将配置对象序列化为JSON字符串
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="config">配置对象</param>
        /// <returns>JSON字符串</returns>
        public static string SerializeToJson<T>(T config) where T : class
        {
            if (config == null)
            {
                Debug.LogWarning("[ConfigurationSerializer] 尝试序列化空配置对象");
                return "{}";
            }
            
            try
            {
                string json = JsonUtility.ToJson(config);
                Debug.Log($"[ConfigurationSerializer] 配置序列化成功: {typeof(T).Name}");
                return json;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 配置序列化失败: {ex.Message}");
                throw new ConfigurationSerializationException($"序列化配置失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 将JSON字符串反序列化为配置对象
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>配置对象</returns>
        public static T DeserializeFromJson<T>(string json) where T : class, new()
        {
            if (string.IsNullOrEmpty(json))
            {
                Debug.LogWarning("[ConfigurationSerializer] 尝试反序列化空JSON字符串，返回默认配置");
                return new T();
            }
            
            try
            {
                T config = JsonUtility.FromJson<T>(json);
                if (config == null)
                {
                    Debug.LogWarning("[ConfigurationSerializer] JSON反序列化结果为空，返回默认配置");
                    return new T();
                }
                
                Debug.Log($"[ConfigurationSerializer] 配置反序列化成功: {typeof(T).Name}");
                return config;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 配置反序列化失败: {ex.Message}");
                throw new ConfigurationSerializationException($"反序列化配置失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 将配置对象保存到文件
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="config">配置对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="encrypt">是否加密</param>
        /// <param name="encryptionKey">加密密钥</param>
        public static void SaveToFile<T>(T config, string filePath, bool encrypt = false, string encryptionKey = null) where T : class
        {
            if (config == null)
            {
                throw new ArgumentNullException(nameof(config), "配置对象不能为空");
            }
            
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "文件路径不能为空");
            }
            
            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // 序列化配置
                string json = SerializeToJson(config);
                
                // 如果需要加密
                if (encrypt && !string.IsNullOrEmpty(encryptionKey))
                {
                    json = EncryptJson(json, encryptionKey);
                }
                
                // 写入文件
                File.WriteAllText(filePath, json);
                Debug.Log($"[ConfigurationSerializer] 配置保存到文件成功: {filePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 保存配置到文件失败: {ex.Message}");
                throw new ConfigurationSerializationException($"保存配置到文件失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 从文件加载配置对象
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <param name="decrypt">是否解密</param>
        /// <param name="encryptionKey">解密密钥</param>
        /// <returns>配置对象</returns>
        public static T LoadFromFile<T>(string filePath, bool decrypt = false, string encryptionKey = null) where T : class, new()
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "文件路径不能为空");
            }
            
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"[ConfigurationSerializer] 配置文件不存在，返回默认配置: {filePath}");
                return new T();
            }
            
            try
            {
                // 读取文件内容
                string json = File.ReadAllText(filePath);
                
                // 如果需要解密
                if (decrypt && !string.IsNullOrEmpty(encryptionKey))
                {
                    json = DecryptJson(json, encryptionKey);
                }
                
                // 反序列化配置
                T config = DeserializeFromJson<T>(json);
                Debug.Log($"[ConfigurationSerializer] 从文件加载配置成功: {filePath}");
                return config;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 从文件加载配置失败: {ex.Message}");
                throw new ConfigurationSerializationException($"从文件加载配置失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 验证JSON格式是否正确
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>是否有效</returns>
        public static bool IsValidJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                return false;
            }
            
            try
            {
                JsonUtility.FromJson<object>(json);
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 格式化JSON字符串
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>格式化后的JSON字符串</returns>
        public static string FormatJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                return json;
            }
            
            try
            {
                // Unity的JsonUtility不支持格式化，这里简化处理
                return json;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] JSON格式化失败: {ex.Message}");
                return json;
            }
        }
        
        /// <summary>
        /// 创建配置备份
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="config">配置对象</param>
        /// <param name="backupPath">备份路径</param>
        public static void CreateBackup<T>(T config, string backupPath) where T : class
        {
            if (config == null)
            {
                throw new ArgumentNullException(nameof(config), "配置对象不能为空");
            }
            
            try
            {
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string backupFileName = $"{typeof(T).Name}_backup_{timestamp}.json";
                string fullBackupPath = Path.Combine(backupPath, backupFileName);
                
                SaveToFile(config, fullBackupPath);
                Debug.Log($"[ConfigurationSerializer] 配置备份创建成功: {fullBackupPath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 创建配置备份失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 比较两个配置对象是否相同
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="config1">配置对象1</param>
        /// <param name="config2">配置对象2</param>
        /// <returns>是否相同</returns>
        public static bool AreEqual<T>(T config1, T config2) where T : class
        {
            if (config1 == null && config2 == null) return true;
            if (config1 == null || config2 == null) return false;
            
            try
            {
                string json1 = SerializeToJson(config1);
                string json2 = SerializeToJson(config2);
                return json1.Equals(json2);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 配置比较失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 深度克隆配置对象
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="config">原配置对象</param>
        /// <returns>克隆的配置对象</returns>
        public static T DeepClone<T>(T config) where T : class, new()
        {
            if (config == null) return null;
            
            try
            {
                string json = SerializeToJson(config);
                return DeserializeFromJson<T>(json);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationSerializer] 配置深度克隆失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 加密JSON字符串
        /// </summary>
        /// <param name="json">原始JSON</param>
        /// <param name="key">加密密钥</param>
        /// <returns>加密后的字符串</returns>
        private static string EncryptJson(string json, string key)
        {
            // 这里应该使用ConfigurationManager的加密方法
            // 为了避免循环依赖，暂时返回原始JSON
            // 在实际使用时，会通过ConfigurationManager进行加密
            return json;
        }
        
        /// <summary>
        /// 解密JSON字符串
        /// </summary>
        /// <param name="encryptedJson">加密的JSON</param>
        /// <param name="key">解密密钥</param>
        /// <returns>解密后的JSON字符串</returns>
        private static string DecryptJson(string encryptedJson, string key)
        {
            // 这里应该使用ConfigurationManager的解密方法
            // 为了避免循环依赖，暂时返回原始JSON
            // 在实际使用时，会通过ConfigurationManager进行解密
            return encryptedJson;
        }
    }
    
    /// <summary>
    /// 配置序列化异常类
    /// </summary>
    public class ConfigurationSerializationException : Exception
    {
        public ConfigurationSerializationException(string message) : base(message) { }
        public ConfigurationSerializationException(string message, Exception innerException) : base(message, innerException) { }
    }
}