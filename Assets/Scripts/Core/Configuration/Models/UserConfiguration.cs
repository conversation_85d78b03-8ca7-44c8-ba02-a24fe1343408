using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Configuration.Models
{
    /// <summary>
    /// 用户配置数据模型，包含用户个人偏好设置
    /// </summary>
    [Serializable]
    public class UserConfiguration
    {
        [Header("基础设置")]
        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName = "用户";
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId = "";
        
        /// <summary>
        /// 首选语言
        /// </summary>
        public string PreferredLanguage = "zh-CN";
        
        [Header("数字人设置")]
        /// <summary>
        /// 数字人渲染模式（3D模型/视频模式）
        /// </summary>
        public DigitalHumanRenderMode RenderMode = DigitalHumanRenderMode.Model3D;
        
        /// <summary>
        /// 选择的数字人模型ID
        /// </summary>
        public string SelectedDigitalHumanId = "default";
        
        /// <summary>
        /// 数字人尺寸缩放比例
        /// </summary>
        public float DigitalHumanScale = 1.0f;
        
        [Header("语音设置")]
        /// <summary>
        /// TTS音色ID
        /// </summary>
        public string TTSVoiceId = "default";
        
        /// <summary>
        /// 语音播放速度
        /// </summary>
        public float SpeechSpeed = 1.0f;
        
        /// <summary>
        /// 语音音量
        /// </summary>
        public float SpeechVolume = 0.8f;
        
        /// <summary>
        /// 是否启用语音输入
        /// </summary>
        public bool EnableVoiceInput = true;
        
        /// <summary>
        /// 麦克风灵敏度
        /// </summary>
        public float MicrophoneSensitivity = 0.5f;
        
        [Header("语言控制词设置")]
        /// <summary>
        /// 唤醒词
        /// </summary>
        public string WakeUpWord = "你好";
        
        /// <summary>
        /// 打断词
        /// </summary>
        public string InterruptWord = "停止";
        
        /// <summary>
        /// 终止词
        /// </summary>
        public string TerminateWord = "再见";
        
        /// <summary>
        /// 中文激活词
        /// </summary>
        public string ChineseActivationWord = "你好";
        
        /// <summary>
        /// 英文激活词
        /// </summary>
        public string EnglishActivationWord = "hi";
        
        /// <summary>
        /// 中文欢迎回复
        /// </summary>
        public string ChineseWelcomeResponse = "你好！我是您的数字助手，有什么可以帮助您的吗？";
        
        /// <summary>
        /// 英文欢迎回复
        /// </summary>
        public string EnglishWelcomeResponse = "Hello! I'm your digital assistant. How can I help you today?";
        
        [Header("显示设置")]
        /// <summary>
        /// 显示分辨率宽度
        /// </summary>
        public int DisplayWidth = 1920;
        
        /// <summary>
        /// 显示分辨率高度
        /// </summary>
        public int DisplayHeight = 1080;
        
        /// <summary>
        /// 是否全屏显示
        /// </summary>
        public bool IsFullScreen = false;
        
        /// <summary>
        /// 目标帧率
        /// </summary>
        public int TargetFrameRate = 60;
        
        /// <summary>
        /// 是否显示日期天气信息
        /// </summary>
        public bool ShowDateWeatherInfo = true;
        
        /// <summary>
        /// 是否显示语音转文字
        /// </summary>
        public bool ShowSpeechToText = true;
        
        /// <summary>
        /// 是否显示AI回复文字
        /// </summary>
        public bool ShowAIResponseText = true;
        
        [Header("交互设置")]
        /// <summary>
        /// 是否启用摄像头交互
        /// </summary>
        public bool EnableCameraInteraction = true;
        
        /// <summary>
        /// 是否启用情感响应
        /// </summary>
        public bool EnableEmotionResponse = true;
        
        /// <summary>
        /// 是否启用热词识别
        /// </summary>
        public bool EnableHotwordRecognition = true;
        
        /// <summary>
        /// 自定义热词配置
        /// </summary>
        public List<HotwordConfiguration> CustomHotwords = new List<HotwordConfiguration>();
        
        /// <summary>
        /// 推荐问题列表
        /// </summary>
        public List<RecommendedQuestion> RecommendedQuestions = new List<RecommendedQuestion>();
        
        [Header("隐私设置")]
        /// <summary>
        /// 是否保存对话历史
        /// </summary>
        public bool SaveConversationHistory = true;
        
        /// <summary>
        /// 是否启用云端同步
        /// </summary>
        public bool EnableCloudSync = false;
        
        /// <summary>
        /// 是否启用数据备份
        /// </summary>
        public bool EnableDataBackup = true;
        
        /// <summary>
        /// 对话历史保留天数
        /// </summary>
        public int ConversationHistoryRetentionDays = 30;
        
        [Header("高级设置")]
        /// <summary>
        /// 是否启用离线模式
        /// </summary>
        public bool EnableOfflineMode = true;
        
        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public bool EnableDebugMode = false;
        
        /// <summary>
        /// 自动保存间隔（秒）
        /// </summary>
        public int AutoSaveInterval = 300;
        
        /// <summary>
        /// 配置创建时间
        /// </summary>
        public DateTime CreatedAt = DateTime.Now;
        
        /// <summary>
        /// 配置最后修改时间
        /// </summary>
        public DateTime LastModifiedAt = DateTime.Now;
        
        /// <summary>
        /// 验证用户配置的有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            // 验证基础设置
            if (string.IsNullOrEmpty(UserName))
            {
                result.AddError("用户名不能为空");
            }
            
            if (string.IsNullOrEmpty(PreferredLanguage))
            {
                result.AddError("首选语言不能为空");
            }
            
            // 验证语音设置
            if (SpeechSpeed <= 0 || SpeechSpeed > 3.0f)
            {
                result.AddError("语音播放速度必须在0-3.0之间");
            }
            
            if (SpeechVolume < 0 || SpeechVolume > 1.0f)
            {
                result.AddError("语音音量必须在0-1.0之间");
            }
            
            if (MicrophoneSensitivity < 0 || MicrophoneSensitivity > 1.0f)
            {
                result.AddError("麦克风灵敏度必须在0-1.0之间");
            }
            
            // 验证显示设置
            if (DisplayWidth <= 0 || DisplayHeight <= 0)
            {
                result.AddError("显示分辨率必须大于0");
            }
            
            if (TargetFrameRate <= 0 || TargetFrameRate > 120)
            {
                result.AddError("目标帧率必须在1-120之间");
            }
            
            // 验证数字人设置
            if (DigitalHumanScale <= 0 || DigitalHumanScale > 5.0f)
            {
                result.AddError("数字人缩放比例必须在0-5.0之间");
            }
            
            // 验证控制词设置
            if (string.IsNullOrEmpty(WakeUpWord) || string.IsNullOrEmpty(InterruptWord) || string.IsNullOrEmpty(TerminateWord))
            {
                result.AddError("控制词不能为空");
            }
            
            // 验证保留天数
            if (ConversationHistoryRetentionDays <= 0 || ConversationHistoryRetentionDays > 365)
            {
                result.AddError("对话历史保留天数必须在1-365之间");
            }
            
            // 验证自动保存间隔
            if (AutoSaveInterval <= 0 || AutoSaveInterval > 3600)
            {
                result.AddError("自动保存间隔必须在1-3600秒之间");
            }
            
            return result;
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            UserName = "用户";
            UserId = "";
            PreferredLanguage = "zh-CN";
            RenderMode = DigitalHumanRenderMode.Model3D;
            SelectedDigitalHumanId = "default";
            DigitalHumanScale = 1.0f;
            TTSVoiceId = "default";
            SpeechSpeed = 1.0f;
            SpeechVolume = 0.8f;
            EnableVoiceInput = true;
            MicrophoneSensitivity = 0.5f;
            WakeUpWord = "你好";
            InterruptWord = "停止";
            TerminateWord = "再见";
            ChineseActivationWord = "你好";
            EnglishActivationWord = "hi";
            ChineseWelcomeResponse = "你好！我是您的数字助手，有什么可以帮助您的吗？";
            EnglishWelcomeResponse = "Hello! I'm your digital assistant. How can I help you today?";
            DisplayWidth = 1920;
            DisplayHeight = 1080;
            IsFullScreen = false;
            TargetFrameRate = 60;
            ShowDateWeatherInfo = true;
            ShowSpeechToText = true;
            ShowAIResponseText = true;
            EnableCameraInteraction = true;
            EnableEmotionResponse = true;
            EnableHotwordRecognition = true;
            CustomHotwords.Clear();
            RecommendedQuestions.Clear();
            SaveConversationHistory = true;
            EnableCloudSync = false;
            EnableDataBackup = true;
            ConversationHistoryRetentionDays = 30;
            EnableOfflineMode = true;
            EnableDebugMode = false;
            AutoSaveInterval = 300;
            LastModifiedAt = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 数字人渲染模式枚举
    /// </summary>
    public enum DigitalHumanRenderMode
    {
        /// <summary>
        /// 3D模型模式
        /// </summary>
        Model3D,
        
        /// <summary>
        /// 视频模式
        /// </summary>
        Video
    }
    
    /// <summary>
    /// 热词配置
    /// </summary>
    [Serializable]
    public class HotwordConfiguration
    {
        /// <summary>
        /// 热词关键字
        /// </summary>
        public string Keyword = "";
        
        /// <summary>
        /// 热词回复内容
        /// </summary>
        public string Response = "";
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = true;
        
        /// <summary>
        /// 优先级（数值越大优先级越高）
        /// </summary>
        public int Priority = 0;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt = DateTime.Now;
    }
    
    /// <summary>
    /// 推荐问题配置
    /// </summary>
    [Serializable]
    public class RecommendedQuestion
    {
        /// <summary>
        /// 问题文本
        /// </summary>
        public string Question = "";
        
        /// <summary>
        /// 回答内容
        /// </summary>
        public string Answer = "";
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = true;
        
        /// <summary>
        /// 显示顺序
        /// </summary>
        public int DisplayOrder = 0;
        
        /// <summary>
        /// 使用次数统计
        /// </summary>
        public int UsageCount = 0;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt = DateTime.Now;
    }
}