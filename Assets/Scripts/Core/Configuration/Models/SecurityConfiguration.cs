using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Configuration.Models
{
    /// <summary>
    /// 安全配置数据模型，包含安全相关的配置选项
    /// </summary>
    [Serializable]
    public class SecurityConfiguration
    {
        [Header("设备激活配置")]
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId = "";
        
        /// <summary>
        /// 激活码
        /// </summary>
        public string ActivationCode = "";
        
        /// <summary>
        /// 是否已激活
        /// </summary>
        public bool IsActivated = false;
        
        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivationTime = DateTime.MinValue;
        
        /// <summary>
        /// 激活过期时间
        /// </summary>
        public DateTime ExpirationTime = DateTime.MaxValue;
        
        /// <summary>
        /// 是否为MVP模式（跳过激活验证）
        /// </summary>
        public bool IsMVPMode = true;
        
        [Header("数据加密配置")]
        /// <summary>
        /// 是否启用数据加密
        /// </summary>
        public bool EnableDataEncryption = true;
        
        /// <summary>
        /// 加密算法类型
        /// </summary>
        public string EncryptionAlgorithm = "AES-256";
        
        /// <summary>
        /// 密钥派生迭代次数
        /// </summary>
        public int KeyDerivationIterations = 10000;
        
        /// <summary>
        /// 是否使用硬件加密
        /// </summary>
        public bool UseHardwareEncryption = false;
        
        [Header("访问控制配置")]
        /// <summary>
        /// 是否启用访问控制
        /// </summary>
        public bool EnableAccessControl = false;
        
        /// <summary>
        /// 允许的IP地址列表
        /// </summary>
        public List<string> AllowedIPAddresses = new List<string>();
        
        /// <summary>
        /// 禁止的IP地址列表
        /// </summary>
        public List<string> BlockedIPAddresses = new List<string>();
        
        /// <summary>
        /// 最大并发连接数
        /// </summary>
        public int MaxConcurrentConnections = 10;
        
        [Header("审计日志配置")]
        /// <summary>
        /// 是否启用审计日志
        /// </summary>
        public bool EnableAuditLogging = true;
        
        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel = "Info";
        
        /// <summary>
        /// 日志保留天数
        /// </summary>
        public int LogRetentionDays = 90;
        
        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB = 100;
        
        /// <summary>
        /// 是否启用日志加密
        /// </summary>
        public bool EnableLogEncryption = true;
        
        [Header("网络安全配置")]
        /// <summary>
        /// 是否启用HTTPS强制
        /// </summary>
        public bool ForceHTTPS = true;
        
        /// <summary>
        /// TLS最低版本
        /// </summary>
        public string MinTLSVersion = "1.2";
        
        /// <summary>
        /// 是否验证SSL证书
        /// </summary>
        public bool ValidateSSLCertificates = true;
        
        /// <summary>
        /// 允许的加密套件
        /// </summary>
        public List<string> AllowedCipherSuites = new List<string>();
        
        [Header("数据保护配置")]
        /// <summary>
        /// 是否启用数据脱敏
        /// </summary>
        public bool EnableDataMasking = true;
        
        /// <summary>
        /// 敏感数据字段列表
        /// </summary>
        public List<string> SensitiveDataFields = new List<string>
        {
            "ApiKey", "Password", "Token", "CertificatePassword"
        };
        
        /// <summary>
        /// 数据备份加密密钥
        /// </summary>
        public string BackupEncryptionKey = "";
        
        /// <summary>
        /// 是否启用数据完整性校验
        /// </summary>
        public bool EnableDataIntegrityCheck = true;
        
        [Header("合规配置")]
        /// <summary>
        /// 是否启用等保合规模式
        /// </summary>
        public bool EnableComplianceMode = false;
        
        /// <summary>
        /// 等保等级
        /// </summary>
        public int ComplianceLevel = 2;
        
        /// <summary>
        /// 是否使用国密算法
        /// </summary>
        public bool UseNationalCrypto = false;
        
        /// <summary>
        /// 安全基线配置版本
        /// </summary>
        public string SecurityBaselineVersion = "1.0";
        
        [Header("威胁检测配置")]
        /// <summary>
        /// 是否启用威胁检测
        /// </summary>
        public bool EnableThreatDetection = false;
        
        /// <summary>
        /// 异常行为检测阈值
        /// </summary>
        public int AnomalyDetectionThreshold = 5;
        
        /// <summary>
        /// 是否启用实时监控
        /// </summary>
        public bool EnableRealTimeMonitoring = false;
        
        /// <summary>
        /// 安全事件通知邮箱
        /// </summary>
        public string SecurityNotificationEmail = "";
        
        /// <summary>
        /// 配置创建时间
        /// </summary>
        public DateTime CreatedAt = DateTime.Now;
        
        /// <summary>
        /// 配置最后修改时间
        /// </summary>
        public DateTime LastModifiedAt = DateTime.Now;
        
        /// <summary>
        /// 验证安全配置的有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            // 验证设备激活配置
            if (!IsMVPMode)
            {
                if (string.IsNullOrEmpty(DeviceId))
                {
                    result.AddError("设备ID不能为空");
                }
                
                if (string.IsNullOrEmpty(ActivationCode))
                {
                    result.AddError("激活码不能为空");
                }
                
                if (IsActivated && ExpirationTime <= DateTime.Now)
                {
                    result.AddError("激活已过期");
                }
            }
            
            // 验证加密配置
            if (EnableDataEncryption)
            {
                if (string.IsNullOrEmpty(EncryptionAlgorithm))
                {
                    result.AddError("加密算法不能为空");
                }
                
                if (KeyDerivationIterations <= 0 || KeyDerivationIterations > 100000)
                {
                    result.AddError("密钥派生迭代次数必须在1-100000之间");
                }
            }
            
            // 验证访问控制配置
            if (EnableAccessControl)
            {
                if (MaxConcurrentConnections <= 0 || MaxConcurrentConnections > 1000)
                {
                    result.AddError("最大并发连接数必须在1-1000之间");
                }
                
                // 验证IP地址格式
                foreach (var ip in AllowedIPAddresses)
                {
                    if (!IsValidIPAddress(ip))
                    {
                        result.AddError($"无效的允许IP地址: {ip}");
                    }
                }
                
                foreach (var ip in BlockedIPAddresses)
                {
                    if (!IsValidIPAddress(ip))
                    {
                        result.AddError($"无效的禁止IP地址: {ip}");
                    }
                }
            }
            
            // 验证审计日志配置
            if (EnableAuditLogging)
            {
                if (LogRetentionDays <= 0 || LogRetentionDays > 3650)
                {
                    result.AddError("日志保留天数必须在1-3650之间");
                }
                
                if (MaxLogFileSizeMB <= 0 || MaxLogFileSizeMB > 1024)
                {
                    result.AddError("日志文件最大大小必须在1-1024MB之间");
                }
                
                var validLogLevels = new[] { "Debug", "Info", "Warning", "Error", "Fatal" };
                if (!Array.Exists(validLogLevels, level => level.Equals(LogLevel, StringComparison.OrdinalIgnoreCase)))
                {
                    result.AddError("无效的日志级别");
                }
            }
            
            // 验证网络安全配置
            if (ForceHTTPS)
            {
                var validTLSVersions = new[] { "1.0", "1.1", "1.2", "1.3" };
                if (!Array.Exists(validTLSVersions, version => version == MinTLSVersion))
                {
                    result.AddError("无效的TLS版本");
                }
            }
            
            // 验证合规配置
            if (EnableComplianceMode)
            {
                if (ComplianceLevel < 1 || ComplianceLevel > 4)
                {
                    result.AddError("等保等级必须在1-4之间");
                }
                
                if (string.IsNullOrEmpty(SecurityBaselineVersion))
                {
                    result.AddError("安全基线配置版本不能为空");
                }
            }
            
            // 验证威胁检测配置
            if (EnableThreatDetection)
            {
                if (AnomalyDetectionThreshold <= 0 || AnomalyDetectionThreshold > 100)
                {
                    result.AddError("异常行为检测阈值必须在1-100之间");
                }
                
                if (!string.IsNullOrEmpty(SecurityNotificationEmail) && !IsValidEmail(SecurityNotificationEmail))
                {
                    result.AddError("安全事件通知邮箱格式不正确");
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 验证IP地址格式
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>是否有效</returns>
        private bool IsValidIPAddress(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress)) return false;
            
            return System.Net.IPAddress.TryParse(ipAddress, out _);
        }
        
        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        /// <param name="email">邮箱地址</param>
        /// <returns>是否有效</returns>
        private bool IsValidEmail(string email)
        {
            if (string.IsNullOrEmpty(email)) return false;
            
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 生成设备ID
        /// </summary>
        /// <returns>设备ID</returns>
        public string GenerateDeviceId()
        {
            var deviceInfo = $"{SystemInfo.deviceName}_{SystemInfo.deviceModel}_{SystemInfo.processorType}";
            var hash = System.Security.Cryptography.SHA256.Create().ComputeHash(System.Text.Encoding.UTF8.GetBytes(deviceInfo));
            DeviceId = Convert.ToBase64String(hash).Substring(0, 16);
            LastModifiedAt = DateTime.Now;
            return DeviceId;
        }
        
        /// <summary>
        /// 激活设备
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <param name="expirationTime">过期时间</param>
        /// <returns>是否激活成功</returns>
        public bool ActivateDevice(string activationCode, DateTime expirationTime)
        {
            if (string.IsNullOrEmpty(activationCode)) return false;
            
            // 在实际实现中，这里应该验证激活码的有效性
            // 目前简化处理
            ActivationCode = activationCode;
            IsActivated = true;
            ActivationTime = DateTime.Now;
            ExpirationTime = expirationTime;
            LastModifiedAt = DateTime.Now;
            
            return true;
        }
        
        /// <summary>
        /// 检查设备是否已激活且未过期
        /// </summary>
        /// <returns>是否有效激活</returns>
        public bool IsValidActivation()
        {
            if (IsMVPMode) return true;
            
            return IsActivated && DateTime.Now < ExpirationTime;
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            DeviceId = "";
            ActivationCode = "";
            IsActivated = false;
            ActivationTime = DateTime.MinValue;
            ExpirationTime = DateTime.MaxValue;
            IsMVPMode = true;
            EnableDataEncryption = true;
            EncryptionAlgorithm = "AES-256";
            KeyDerivationIterations = 10000;
            UseHardwareEncryption = false;
            EnableAccessControl = false;
            AllowedIPAddresses.Clear();
            BlockedIPAddresses.Clear();
            MaxConcurrentConnections = 10;
            EnableAuditLogging = true;
            LogLevel = "Info";
            LogRetentionDays = 90;
            MaxLogFileSizeMB = 100;
            EnableLogEncryption = true;
            ForceHTTPS = true;
            MinTLSVersion = "1.2";
            ValidateSSLCertificates = true;
            AllowedCipherSuites.Clear();
            EnableDataMasking = true;
            SensitiveDataFields = new List<string> { "ApiKey", "Password", "Token", "CertificatePassword" };
            BackupEncryptionKey = "";
            EnableDataIntegrityCheck = true;
            EnableComplianceMode = false;
            ComplianceLevel = 2;
            UseNationalCrypto = false;
            SecurityBaselineVersion = "1.0";
            EnableThreatDetection = false;
            AnomalyDetectionThreshold = 5;
            EnableRealTimeMonitoring = false;
            SecurityNotificationEmail = "";
            LastModifiedAt = DateTime.Now;
        }
    }
}