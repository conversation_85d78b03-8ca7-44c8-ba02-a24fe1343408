using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Configuration.Models
{
    /// <summary>
    /// 服务配置数据模型，包含各种外部服务的连接配置
    /// </summary>
    [Serializable]
    public class ServiceConfiguration
    {
        [Header("大模型API配置")]
        /// <summary>
        /// 大模型API配置列表
        /// </summary>
        public List<LLMApiConfiguration> LLMConfigurations = new List<LLMApiConfiguration>();
        
        /// <summary>
        /// 当前激活的大模型配置ID
        /// </summary>
        public string ActiveLLMConfigId = "";
        
        [Header("语音服务配置")]
        /// <summary>
        /// TTS服务配置
        /// </summary>
        public TTSServiceConfiguration TTSConfig = new TTSServiceConfiguration();
        
        /// <summary>
        /// ASR服务配置
        /// </summary>
        public ASRServiceConfiguration ASRConfig = new ASRServiceConfiguration();
        
        [Header("视觉服务配置")]
        /// <summary>
        /// 情感分析服务配置
        /// </summary>
        public EmotionAnalysisConfiguration EmotionConfig = new EmotionAnalysisConfiguration();
        
        /// <summary>
        /// 人脸检测服务配置
        /// </summary>
        public FaceDetectionConfiguration FaceDetectionConfig = new FaceDetectionConfiguration();
        
        [Header("天气服务配置")]
        /// <summary>
        /// 天气API配置
        /// </summary>
        public WeatherServiceConfiguration WeatherConfig = new WeatherServiceConfiguration();
        
        [Header("私有化部署配置")]
        /// <summary>
        /// 是否启用私有化部署
        /// </summary>
        public bool EnablePrivateDeployment = false;
        
        /// <summary>
        /// 私有化服务基础URL
        /// </summary>
        public string PrivateServiceBaseUrl = "";
        
        /// <summary>
        /// 私有化服务认证配置
        /// </summary>
        public PrivateAuthConfiguration PrivateAuthConfig = new PrivateAuthConfiguration();
        
        [Header("网络配置")]
        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int RequestTimeoutSeconds = 30;
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount = 3;
        
        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryIntervalMs = 1000;
        
        /// <summary>
        /// 是否启用请求缓存
        /// </summary>
        public bool EnableRequestCache = true;
        
        /// <summary>
        /// 缓存过期时间（分钟）
        /// </summary>
        public int CacheExpirationMinutes = 60;
        
        [Header("安全配置")]
        /// <summary>
        /// 是否启用SSL验证
        /// </summary>
        public bool EnableSSLVerification = true;
        
        /// <summary>
        /// 自定义证书路径
        /// </summary>
        public string CustomCertificatePath = "";
        
        /// <summary>
        /// 是否启用请求日志
        /// </summary>
        public bool EnableRequestLogging = false;
        
        /// <summary>
        /// 配置创建时间
        /// </summary>
        public DateTime CreatedAt = DateTime.Now;
        
        /// <summary>
        /// 配置最后修改时间
        /// </summary>
        public DateTime LastModifiedAt = DateTime.Now;
        
        /// <summary>
        /// 验证服务配置的有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            // 验证大模型配置
            if (LLMConfigurations.Count == 0)
            {
                result.AddWarning("未配置任何大模型API");
            }
            else
            {
                foreach (var llmConfig in LLMConfigurations)
                {
                    var llmValidation = llmConfig.Validate();
                    result.Merge(llmValidation);
                }
            }
            
            // 验证当前激活的配置
            if (!string.IsNullOrEmpty(ActiveLLMConfigId))
            {
                bool foundActiveConfig = LLMConfigurations.Exists(c => c.ConfigId == ActiveLLMConfigId);
                if (!foundActiveConfig)
                {
                    result.AddError("激活的大模型配置ID不存在");
                }
            }
            
            // 验证语音服务配置
            var ttsValidation = TTSConfig.Validate();
            result.Merge(ttsValidation);
            
            var asrValidation = ASRConfig.Validate();
            result.Merge(asrValidation);
            
            // 验证网络配置
            if (RequestTimeoutSeconds <= 0 || RequestTimeoutSeconds > 300)
            {
                result.AddError("请求超时时间必须在1-300秒之间");
            }
            
            if (RetryCount < 0 || RetryCount > 10)
            {
                result.AddError("重试次数必须在0-10之间");
            }
            
            if (RetryIntervalMs < 0 || RetryIntervalMs > 10000)
            {
                result.AddError("重试间隔必须在0-10000毫秒之间");
            }
            
            if (CacheExpirationMinutes <= 0 || CacheExpirationMinutes > 1440)
            {
                result.AddError("缓存过期时间必须在1-1440分钟之间");
            }
            
            // 验证私有化部署配置
            if (EnablePrivateDeployment)
            {
                if (string.IsNullOrEmpty(PrivateServiceBaseUrl))
                {
                    result.AddError("启用私有化部署时必须配置服务基础URL");
                }
                else if (!Uri.IsWellFormedUriString(PrivateServiceBaseUrl, UriKind.Absolute))
                {
                    result.AddError("私有化服务基础URL格式不正确");
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 获取激活的大模型配置
        /// </summary>
        /// <returns>激活的配置，如果未找到则返回null</returns>
        public LLMApiConfiguration GetActiveLLMConfiguration()
        {
            if (string.IsNullOrEmpty(ActiveLLMConfigId))
            {
                return LLMConfigurations.Count > 0 ? LLMConfigurations[0] : null;
            }
            
            return LLMConfigurations.Find(c => c.ConfigId == ActiveLLMConfigId);
        }
        
        /// <summary>
        /// 添加大模型配置
        /// </summary>
        /// <param name="config">配置对象</param>
        public void AddLLMConfiguration(LLMApiConfiguration config)
        {
            if (config == null) return;
            
            // 确保配置ID唯一
            if (string.IsNullOrEmpty(config.ConfigId))
            {
                config.ConfigId = Guid.NewGuid().ToString();
            }
            
            // 检查是否已存在相同ID的配置
            var existingConfig = LLMConfigurations.Find(c => c.ConfigId == config.ConfigId);
            if (existingConfig != null)
            {
                // 更新现有配置
                int index = LLMConfigurations.IndexOf(existingConfig);
                LLMConfigurations[index] = config;
            }
            else
            {
                // 添加新配置
                LLMConfigurations.Add(config);
            }
            
            // 如果这是第一个配置，设置为激活配置
            if (string.IsNullOrEmpty(ActiveLLMConfigId) && LLMConfigurations.Count == 1)
            {
                ActiveLLMConfigId = config.ConfigId;
            }
            
            LastModifiedAt = DateTime.Now;
        }
        
        /// <summary>
        /// 移除大模型配置
        /// </summary>
        /// <param name="configId">配置ID</param>
        public bool RemoveLLMConfiguration(string configId)
        {
            var config = LLMConfigurations.Find(c => c.ConfigId == configId);
            if (config == null) return false;
            
            LLMConfigurations.Remove(config);
            
            // 如果移除的是激活配置，重新选择激活配置
            if (ActiveLLMConfigId == configId)
            {
                ActiveLLMConfigId = LLMConfigurations.Count > 0 ? LLMConfigurations[0].ConfigId : "";
            }
            
            LastModifiedAt = DateTime.Now;
            return true;
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            LLMConfigurations.Clear();
            ActiveLLMConfigId = "";
            TTSConfig = new TTSServiceConfiguration();
            ASRConfig = new ASRServiceConfiguration();
            EmotionConfig = new EmotionAnalysisConfiguration();
            FaceDetectionConfig = new FaceDetectionConfiguration();
            WeatherConfig = new WeatherServiceConfiguration();
            EnablePrivateDeployment = false;
            PrivateServiceBaseUrl = "";
            PrivateAuthConfig = new PrivateAuthConfiguration();
            RequestTimeoutSeconds = 30;
            RetryCount = 3;
            RetryIntervalMs = 1000;
            EnableRequestCache = true;
            CacheExpirationMinutes = 60;
            EnableSSLVerification = true;
            CustomCertificatePath = "";
            EnableRequestLogging = false;
            LastModifiedAt = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 大模型API配置
    /// </summary>
    [Serializable]
    public class LLMApiConfiguration
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public string ConfigId = "";
        
        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name = "";
        
        /// <summary>
        /// API提供商
        /// </summary>
        public string Provider = "";
        
        /// <summary>
        /// API端点URL
        /// </summary>
        public string ApiEndpoint = "";
        
        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey = "";
        
        /// <summary>
        /// 模型名称
        /// </summary>
        public string ModelName = "";
        
        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int MaxTokens = 2048;
        
        /// <summary>
        /// 温度参数
        /// </summary>
        public float Temperature = 0.7f;
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = true;
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            if (string.IsNullOrEmpty(Name))
            {
                result.AddError("配置名称不能为空");
            }
            
            if (string.IsNullOrEmpty(ApiEndpoint))
            {
                result.AddError("API端点不能为空");
            }
            else if (!Uri.IsWellFormedUriString(ApiEndpoint, UriKind.Absolute))
            {
                result.AddError("API端点URL格式不正确");
            }
            
            if (string.IsNullOrEmpty(ApiKey))
            {
                result.AddError("API密钥不能为空");
            }
            
            if (string.IsNullOrEmpty(ModelName))
            {
                result.AddError("模型名称不能为空");
            }
            
            if (MaxTokens <= 0 || MaxTokens > 32768)
            {
                result.AddError("最大令牌数必须在1-32768之间");
            }
            
            if (Temperature < 0 || Temperature > 2.0f)
            {
                result.AddError("温度参数必须在0-2.0之间");
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// TTS服务配置
    /// </summary>
    [Serializable]
    public class TTSServiceConfiguration
    {
        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider = "Azure";
        
        /// <summary>
        /// API端点
        /// </summary>
        public string ApiEndpoint = "";
        
        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey = "";
        
        /// <summary>
        /// 默认音色
        /// </summary>
        public string DefaultVoice = "zh-CN-XiaoxiaoNeural";
        
        /// <summary>
        /// 音频格式
        /// </summary>
        public string AudioFormat = "audio-16khz-128kbitrate-mono-mp3";
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = true;
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            if (string.IsNullOrEmpty(Provider))
            {
                result.AddError("TTS服务提供商不能为空");
            }
            
            if (IsEnabled)
            {
                if (string.IsNullOrEmpty(ApiEndpoint))
                {
                    result.AddError("TTS API端点不能为空");
                }
                else if (!Uri.IsWellFormedUriString(ApiEndpoint, UriKind.Absolute))
                {
                    result.AddError("TTS API端点URL格式不正确");
                }
                
                if (string.IsNullOrEmpty(ApiKey))
                {
                    result.AddError("TTS API密钥不能为空");
                }
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// ASR服务配置
    /// </summary>
    [Serializable]
    public class ASRServiceConfiguration
    {
        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider = "Azure";
        
        /// <summary>
        /// API端点
        /// </summary>
        public string ApiEndpoint = "";
        
        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey = "";
        
        /// <summary>
        /// 识别语言
        /// </summary>
        public string Language = "zh-CN";
        
        /// <summary>
        /// 音频格式
        /// </summary>
        public string AudioFormat = "wav";
        
        /// <summary>
        /// 采样率
        /// </summary>
        public int SampleRate = 16000;
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = true;
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            if (string.IsNullOrEmpty(Provider))
            {
                result.AddError("ASR服务提供商不能为空");
            }
            
            if (IsEnabled)
            {
                if (string.IsNullOrEmpty(ApiEndpoint))
                {
                    result.AddError("ASR API端点不能为空");
                }
                else if (!Uri.IsWellFormedUriString(ApiEndpoint, UriKind.Absolute))
                {
                    result.AddError("ASR API端点URL格式不正确");
                }
                
                if (string.IsNullOrEmpty(ApiKey))
                {
                    result.AddError("ASR API密钥不能为空");
                }
            }
            
            if (SampleRate <= 0)
            {
                result.AddError("采样率必须大于0");
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// 情感分析服务配置
    /// </summary>
    [Serializable]
    public class EmotionAnalysisConfiguration
    {
        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider = "Custom";
        
        /// <summary>
        /// API端点
        /// </summary>
        public string ApiEndpoint = "";
        
        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey = "";
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = false;
        
        /// <summary>
        /// 分析间隔（毫秒）
        /// </summary>
        public int AnalysisIntervalMs = 1000;
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            if (IsEnabled)
            {
                if (string.IsNullOrEmpty(ApiEndpoint))
                {
                    result.AddError("情感分析API端点不能为空");
                }
                else if (!Uri.IsWellFormedUriString(ApiEndpoint, UriKind.Absolute))
                {
                    result.AddError("情感分析API端点URL格式不正确");
                }
            }
            
            if (AnalysisIntervalMs <= 0 || AnalysisIntervalMs > 10000)
            {
                result.AddError("分析间隔必须在1-10000毫秒之间");
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// 人脸检测服务配置
    /// </summary>
    [Serializable]
    public class FaceDetectionConfiguration
    {
        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider = "OpenCV";
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = true;
        
        /// <summary>
        /// 检测间隔（毫秒）
        /// </summary>
        public int DetectionIntervalMs = 100;
        
        /// <summary>
        /// 最小人脸尺寸
        /// </summary>
        public int MinFaceSize = 30;
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            if (DetectionIntervalMs <= 0 || DetectionIntervalMs > 5000)
            {
                result.AddError("检测间隔必须在1-5000毫秒之间");
            }
            
            if (MinFaceSize <= 0 || MinFaceSize > 500)
            {
                result.AddError("最小人脸尺寸必须在1-500像素之间");
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// 天气服务配置
    /// </summary>
    [Serializable]
    public class WeatherServiceConfiguration
    {
        /// <summary>
        /// 服务提供商
        /// </summary>
        public string Provider = "OpenWeatherMap";
        
        /// <summary>
        /// API端点
        /// </summary>
        public string ApiEndpoint = "https://api.openweathermap.org/data/2.5/weather";
        
        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey = "";
        
        /// <summary>
        /// 默认城市
        /// </summary>
        public string DefaultCity = "Beijing";
        
        /// <summary>
        /// 温度单位
        /// </summary>
        public string TemperatureUnit = "metric";
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled = false;
        
        /// <summary>
        /// 更新间隔（分钟）
        /// </summary>
        public int UpdateIntervalMinutes = 30;
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();
            
            if (IsEnabled)
            {
                if (string.IsNullOrEmpty(ApiEndpoint))
                {
                    result.AddError("天气API端点不能为空");
                }
                else if (!Uri.IsWellFormedUriString(ApiEndpoint, UriKind.Absolute))
                {
                    result.AddError("天气API端点URL格式不正确");
                }
                
                if (string.IsNullOrEmpty(ApiKey))
                {
                    result.AddError("天气API密钥不能为空");
                }
            }
            
            if (UpdateIntervalMinutes <= 0 || UpdateIntervalMinutes > 1440)
            {
                result.AddError("更新间隔必须在1-1440分钟之间");
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// 私有化认证配置
    /// </summary>
    [Serializable]
    public class PrivateAuthConfiguration
    {
        /// <summary>
        /// 认证类型
        /// </summary>
        public string AuthType = "ApiKey";
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username = "";
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password = "";
        
        /// <summary>
        /// 令牌
        /// </summary>
        public string Token = "";
        
        /// <summary>
        /// 证书路径
        /// </summary>
        public string CertificatePath = "";
        
        /// <summary>
        /// 证书密码
        /// </summary>
        public string CertificatePassword = "";
    }
}