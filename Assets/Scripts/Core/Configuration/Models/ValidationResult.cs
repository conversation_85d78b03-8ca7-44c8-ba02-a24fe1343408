using System;
using System.Collections.Generic;
using System.Linq;

namespace DigitalHuman.Core.Configuration.Models
{
    /// <summary>
    /// 配置验证结果类
    /// </summary>
    [Serializable]
    public class ValidationResult
    {
        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; private set; } = new List<string>();
        
        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> Warnings { get; private set; } = new List<string>();
        
        /// <summary>
        /// 是否验证通过（无错误）
        /// </summary>
        public bool IsValid => Errors.Count == 0;
        
        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => Warnings.Count > 0;
        
        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount => Errors.Count;
        
        /// <summary>
        /// 警告数量
        /// </summary>
        public int WarningCount => Warnings.Count;
        
        /// <summary>
        /// 添加错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void AddError(string error)
        {
            if (!string.IsNullOrEmpty(error) && !Errors.Contains(error))
            {
                Errors.Add(error);
            }
        }
        
        /// <summary>
        /// 添加警告信息
        /// </summary>
        /// <param name="warning">警告信息</param>
        public void AddWarning(string warning)
        {
            if (!string.IsNullOrEmpty(warning) && !Warnings.Contains(warning))
            {
                Warnings.Add(warning);
            }
        }
        
        /// <summary>
        /// 合并另一个验证结果
        /// </summary>
        /// <param name="other">要合并的验证结果</param>
        public void Merge(ValidationResult other)
        {
            if (other == null) return;
            
            foreach (var error in other.Errors)
            {
                AddError(error);
            }
            
            foreach (var warning in other.Warnings)
            {
                AddWarning(warning);
            }
        }
        
        /// <summary>
        /// 清空所有错误和警告
        /// </summary>
        public void Clear()
        {
            Errors.Clear();
            Warnings.Clear();
        }
        
        /// <summary>
        /// 获取所有错误信息的字符串表示
        /// </summary>
        /// <returns>错误信息字符串</returns>
        public string GetErrorsAsString()
        {
            return string.Join("\n", Errors);
        }
        
        /// <summary>
        /// 获取所有警告信息的字符串表示
        /// </summary>
        /// <returns>警告信息字符串</returns>
        public string GetWarningsAsString()
        {
            return string.Join("\n", Warnings);
        }
        
        /// <summary>
        /// 获取完整的验证结果摘要
        /// </summary>
        /// <returns>验证结果摘要</returns>
        public string GetSummary()
        {
            var summary = new List<string>();
            
            if (IsValid)
            {
                summary.Add("验证通过");
            }
            else
            {
                summary.Add($"验证失败，发现 {ErrorCount} 个错误");
                if (Errors.Any())
                {
                    summary.Add("错误详情：");
                    summary.AddRange(Errors.Select(e => $"- {e}"));
                }
            }
            
            if (HasWarnings)
            {
                summary.Add($"发现 {WarningCount} 个警告");
                summary.Add("警告详情：");
                summary.AddRange(Warnings.Select(w => $"- {w}"));
            }
            
            return string.Join("\n", summary);
        }
        
        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>验证结果的字符串表示</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }
}