using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core
{
    /// <summary>
    /// 事件系统实现，提供组件间的解耦通信
    /// </summary>
    public class EventSystem : SingletonManager<EventSystem>, IEventSystem
    {
        private readonly Dictionary<Type, List<Delegate>> _eventHandlers = new Dictionary<Type, List<Delegate>>();
        private readonly object _lock = new object();
        
        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        public void Subscribe<T>(Action<T> handler) where T : IEvent
        {
            if (handler == null)
            {
                Debug.LogWarning("[EventSystem] 尝试订阅空的事件处理器");
                return;
            }
            
            lock (_lock)
            {
                Type eventType = typeof(T);
                
                if (!_eventHandlers.ContainsKey(eventType))
                {
                    _eventHandlers[eventType] = new List<Delegate>();
                }
                
                _eventHandlers[eventType].Add(handler);
                Debug.Log($"[EventSystem] 订阅事件: {eventType.Name}");
            }
        }
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            if (handler == null)
            {
                Debug.LogWarning("[EventSystem] 尝试取消订阅空的事件处理器");
                return;
            }
            
            lock (_lock)
            {
                Type eventType = typeof(T);
                
                if (_eventHandlers.ContainsKey(eventType))
                {
                    _eventHandlers[eventType].Remove(handler);
                    
                    if (_eventHandlers[eventType].Count == 0)
                    {
                        _eventHandlers.Remove(eventType);
                    }
                    
                    Debug.Log($"[EventSystem] 取消订阅事件: {eventType.Name}");
                }
            }
        }
        
        /// <summary>
        /// 发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        public void Publish<T>(T eventData) where T : IEvent
        {
            if (eventData == null)
            {
                Debug.LogWarning("[EventSystem] 尝试发布空的事件数据");
                return;
            }
            
            lock (_lock)
            {
                Type eventType = typeof(T);
                
                if (_eventHandlers.ContainsKey(eventType))
                {
                    List<Delegate> handlers = new List<Delegate>(_eventHandlers[eventType]);
                    
                    foreach (Delegate handler in handlers)
                    {
                        try
                        {
                            ((Action<T>)handler)?.Invoke(eventData);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[EventSystem] 事件处理器执行失败: {ex.Message}");
                        }
                    }
                    
                    Debug.Log($"[EventSystem] 发布事件: {eventType.Name}, 处理器数量: {handlers.Count}");
                }
            }
        }
        
        /// <summary>
        /// 清理所有事件订阅
        /// </summary>
        public void ClearAllSubscriptions()
        {
            lock (_lock)
            {
                int totalSubscriptions = 0;
                foreach (var handlers in _eventHandlers.Values)
                {
                    totalSubscriptions += handlers.Count;
                }
                
                _eventHandlers.Clear();
                Debug.Log($"[EventSystem] 清理所有事件订阅，共清理 {totalSubscriptions} 个订阅");
            }
        }
        
        /// <summary>
        /// 初始化事件系统
        /// </summary>
        protected override void OnInitializeInternal()
        {
            Debug.Log("[EventSystem] 事件系统初始化");
        }
        
        /// <summary>
        /// 清理事件系统
        /// </summary>
        protected override void OnCleanupInternal()
        {
            ClearAllSubscriptions();
            Debug.Log("[EventSystem] 事件系统清理完成");
        }
    }
}