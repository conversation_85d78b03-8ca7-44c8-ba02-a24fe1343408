using System;

namespace DigitalHuman.Core
{
    /// <summary>
    /// 事件系统接口，定义组件间通信的标准
    /// </summary>
    public interface IEventSystem
    {
        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void Subscribe<T>(Action<T> handler) where T : IEvent;
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void Unsubscribe<T>(Action<T> handler) where T : IEvent;
        
        /// <summary>
        /// 发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        void Publish<T>(T eventData) where T : IEvent;
        
        /// <summary>
        /// 清理所有事件订阅
        /// </summary>
        void ClearAllSubscriptions();
    }
    
    /// <summary>
    /// 事件基础接口
    /// </summary>
    public interface IEvent
    {
        /// <summary>
        /// 事件时间戳
        /// </summary>
        DateTime Timestamp { get; }
    }
    
    /// <summary>
    /// 事件基类
    /// </summary>
    public abstract class EventBase : IEvent
    {
        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; private set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        protected EventBase()
        {
            Timestamp = DateTime.Now;
        }
    }
}