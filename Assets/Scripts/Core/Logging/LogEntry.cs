using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志条目数据模型
    /// </summary>
    [Serializable]
    public class LogEntry
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; }
        
        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 模块名称
        /// </summary>
        public string Module { get; set; }
        
        /// <summary>
        /// 线程ID
        /// </summary>
        public string ThreadId { get; set; }
        
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; }
        
        /// <summary>
        /// 关联ID，用于追踪相关日志
        /// </summary>
        public string CorrelationId { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LogEntry()
        {
            Timestamp = DateTime.Now;
            Properties = new Dictionary<string, object>();
            ThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString();
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        public LogEntry(LogLevel level, string message, string module = null, Exception exception = null) : this()
        {
            Level = level;
            Message = message;
            Module = module;
            Exception = exception;
        }
        
        /// <summary>
        /// 添加属性
        /// </summary>
        /// <param name="key">属性键</param>
        /// <param name="value">属性值</param>
        public void AddProperty(string key, object value)
        {
            if (Properties == null)
            {
                Properties = new Dictionary<string, object>();
            }
            Properties[key] = value;
        }
        
        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="key">属性键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>属性值</returns>
        public T GetProperty<T>(string key, T defaultValue = default(T))
        {
            if (Properties == null || !Properties.ContainsKey(key))
            {
                return defaultValue;
            }
            
            try
            {
                return (T)Properties[key];
            }
            catch
            {
                return defaultValue;
            }
        }
        
        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] [{Module ?? "System"}] {Message}";
        }
    }
}