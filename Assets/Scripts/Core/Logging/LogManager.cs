using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using UnityEngine;
using SystemDebug = System.Diagnostics.Debug;
using DigitalHuman.Core.Configuration;
// using DigitalHuman.Core.Logging.Performance; // 暂时禁用
// using DigitalHuman.Core.Logging.Export; // 暂时禁用
// using DigitalHuman.Core.Logging.Statistics; // 暂时禁用

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志管理器实现，提供完整的日志管理功能
    /// </summary>
    public class LogManager : SingletonManager<LogManager>, ILogManager
    {
        [Header("日志配置")]
        [SerializeField] private bool _isLoggingEnabled = true;
        [SerializeField] private LogLevel _globalMinLevel = LogLevel.Info;
        
        // 核心组件
        private LogConfiguration _configuration;
        private readonly List<ILogWriter> _writers = new List<ILogWriter>();
        private readonly ConcurrentDictionary<string, ILogger> _loggers = new ConcurrentDictionary<string, ILogger>();
        
        // 统计和监控组件 - 使用简化版本
        private readonly SimpleLogStatistics _statistics = new SimpleLogStatistics();
        private SimpleLogHealthChecker _healthChecker;
        // private LogStatisticsPersistence _statisticsPersistence;

        // 性能优化组件
        private readonly LogEntryPool _logEntryPool = LogEntryPool.Instance;
        // private readonly LogPerformanceMonitor _performanceMonitor = LogPerformanceMonitor.Instance;

        // 日志导出器 - 暂时禁用
        // private ILogExporter _logExporter;
        
        // 事件系统引用
        private IEventSystem _eventSystem;
        
        // 统计更新定时器
        private float _lastStatisticsUpdate;
        private const float STATISTICS_UPDATE_INTERVAL = 5.0f; // 每5秒更新一次统计
        
        /// <summary>
        /// 日志功能是否启用
        /// </summary>
        public bool IsLoggingEnabled
        {
            get => _isLoggingEnabled;
            set
            {
                if (_isLoggingEnabled != value)
                {
                    _isLoggingEnabled = value;
                    if (_configuration != null)
                    {
                        _configuration.IsLoggingEnabled = value;
                    }
                    
                    // 发布状态变化事件
                    _eventSystem?.Publish(new LoggingStateChangedEvent(value, "手动设置"));
                    Debug.Log($"[LogManager] 日志功能已{(value ? "启用" : "禁用")}");
                }
            }
        }
        
        /// <summary>
        /// 初始化日志管理器
        /// </summary>
        protected override void OnInitializeInternal()
        {
            try
            {
                // 获取事件系统引用
                _eventSystem = EventSystem.Instance;
                
                // 加载配置
                LoadConfiguration();
                
                // 初始化统计和监控组件
                InitializeStatisticsAndMonitoring();
                
                // 初始化默认写入器
                InitializeDefaultWriters();
                
                // 初始化日志导出器 - 暂时禁用
                // InitializeLogExporter();
                
                Debug.Log("[LogManager] 日志管理器初始化完成");
                LogInfo("日志管理器初始化完成", "LogManager");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 初始化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 清理日志管理器
        /// </summary>
        protected override void OnCleanupInternal()
        {
            try
            {
                LogInfo("日志管理器开始清理", "LogManager");
                
                // 保存最终统计数据
                SaveFinalStatistics();
                
                // 刷新所有写入器
                FlushAll();
                
                // 清理写入器
                foreach (var writer in _writers)
                {
                    try
                    {
                        writer?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[LogManager] 清理写入器失败: {ex.Message}");
                    }
                }
                _writers.Clear();
                
                // 清理日志记录器缓存
                _loggers.Clear();
                
                Debug.Log("[LogManager] 日志管理器清理完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 清理失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新方法，定期执行统计更新
        /// </summary>
        private void Update()
        {
            if (IsInitialized)
            {
                UpdateStatistics();
            }
        }
        
        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        public void Log(LogLevel level, string message, string module = null, Exception exception = null)
        {
            if (!_isLoggingEnabled || !ShouldLog(level, module))
            {
                return;
            }
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // 暂时禁用性能监控
                // if (_performanceMonitor.ShouldDegrade())
                // {
                //     var strategy = _performanceMonitor.GetDegradationStrategy();
                //     if (strategy.DropLowPriorityLogs && level < LogLevel.Warn)
                //     {
                //         return; // 丢弃低优先级日志
                //     }
                // }

                // 从对象池获取日志条目
                var entry = _logEntryPool.Get(level, message, module, exception);

                try
                {
                    // 更新统计信息
                    _statistics.RecordLog(level);

                    // 写入到所有启用的写入器
                    WriteToWriters(entry);

                    // 发布日志事件
                    _eventSystem?.Publish(new LogEvent(entry));
                }
                finally
                {
                    // 归还对象到池中
                    _logEntryPool.Return(entry);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 记录日志失败: {ex.Message}");
                _eventSystem?.Publish(new LogErrorEvent(ex, "记录日志时发生错误"));
            }
            finally
            {
                stopwatch.Stop();
                var writeTime = stopwatch.ElapsedMilliseconds;
                // _performanceMonitor.RecordWriteOperation(writeTime); // 暂时禁用
                _statistics.RecordWriteOperation(TimeSpan.FromMilliseconds(writeTime), true);
            }
        }
        
        /// <summary>
        /// 记录跟踪级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        public void LogTrace(string message, string module = null)
        {
            Log(LogLevel.Trace, message, module);
        }
        
        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        public void LogDebug(string message, string module = null)
        {
            Log(LogLevel.Debug, message, module);
        }
        
        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        public void LogInfo(string message, string module = null)
        {
            Log(LogLevel.Info, message, module);
        }
        
        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        public void LogWarn(string message, string module = null)
        {
            Log(LogLevel.Warn, message, module);
        }
        
        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        public void LogError(string message, string module = null, Exception exception = null)
        {
            Log(LogLevel.Error, message, module, exception);
        }
        
        /// <summary>
        /// 记录致命错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        public void LogFatal(string message, string module = null, Exception exception = null)
        {
            Log(LogLevel.Fatal, message, module, exception);
        }
        
        /// <summary>
        /// 获取模块化日志记录器
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>日志记录器实例</returns>
        public ILogger GetLogger(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
            {
                moduleName = "Default";
            }
            
            return _loggers.GetOrAdd(moduleName, name => 
            {
                var logger = new Logger(name, this);
                
                // 从配置中获取模块的日志级别
                LogLevel moduleLevel = _configuration?.GetModuleLevel(name) ?? _globalMinLevel;
                logger.UpdateMinLevel(moduleLevel);
                
                return logger;
            });
        }
        
        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="module">模块名称，为空则设置全局级别</param>
        public void SetLogLevel(LogLevel level, string module = null)
        {
            if (string.IsNullOrEmpty(module))
            {
                _globalMinLevel = level;
                if (_configuration != null)
                {
                    _configuration.GlobalMinLevel = level;
                }
                
                // 更新所有没有特定级别设置的日志记录器
                foreach (var kvp in _loggers)
                {
                    var logger = kvp.Value as Logger;
                    if (logger != null && (_configuration == null || !_configuration.ModuleLevels.ContainsKey(kvp.Key)))
                    {
                        logger.UpdateMinLevel(level);
                    }
                }
                
                Debug.Log($"[LogManager] 全局日志级别设置为: {level}");
            }
            else
            {
                if (_configuration != null)
                {
                    _configuration.SetModuleLevel(module, level);
                }
                
                // 更新现有的日志记录器
                if (_loggers.TryGetValue(module, out var logger))
                {
                    var typedLogger = logger as Logger;
                    typedLogger?.UpdateMinLevel(level);
                }
                
                Debug.Log($"[LogManager] 模块 {module} 日志级别设置为: {level}");
            }
            
            // 保存配置
            SaveConfiguration();
        }
        
        /// <summary>
        /// 添加日志写入器
        /// </summary>
        /// <param name="writer">日志写入器</param>
        public void AddLogWriter(ILogWriter writer)
        {
            if (writer == null)
            {
                LogWarn("尝试添加空的日志写入器", "LogManager");
                return;
            }
            
            lock (_writers)
            {
                if (!_writers.Contains(writer))
                {
                    _writers.Add(writer);
                    LogInfo($"添加日志写入器: {writer.Name}", "LogManager");
                }
            }
        }
        
        /// <summary>
        /// 移除日志写入器
        /// </summary>
        /// <param name="writer">日志写入器</param>
        public void RemoveLogWriter(ILogWriter writer)
        {
            if (writer == null)
            {
                return;
            }
            
            lock (_writers)
            {
                if (_writers.Remove(writer))
                {
                    LogInfo($"移除日志写入器: {writer.Name}", "LogManager");
                    try
                    {
                        writer.Dispose();
                    }
                    catch (Exception ex)
                    {
                        LogError($"释放日志写入器失败: {ex.Message}", "LogManager", ex);
                    }
                }
            }
        }
        
        /// <summary>
        /// 刷新所有日志写入器
        /// </summary>
        public void FlushAll()
        {
            lock (_writers)
            {
                foreach (var writer in _writers)
                {
                    try
                    {
                        writer?.Flush();
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[LogManager] 刷新写入器失败: {ex.Message}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="minLevel">最小日志级别</param>
        /// <returns>导出文件路径</returns>
        public async Task<string> ExportLogsAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null)
        {
            // 暂时禁用导出功能
            LogError("日志导出功能暂时禁用", "LogManager");
            return string.Empty;

            /*
            if (_logExporter == null)
            {
                LogError("日志导出器未初始化", "LogManager");
                return string.Empty;
            }

            try
            {
                var request = new LogExportRequest
                {
                    StartTime = startTime,
                    EndTime = endTime,
                    MinLevel = minLevel,
                    Format = _configuration?.Export?.ExportFormat ?? "json",
                    IncludeSystemInfo = _configuration?.Export?.IncludeSystemInfo ?? true,
                    Compress = false
                };

                LogInfo($"开始导出日志: {startTime} - {endTime}, 最小级别: {minLevel}", "LogManager");
                string exportPath = await _logExporter.ExportAsync(request);
                LogInfo($"日志导出完成: {exportPath}", "LogManager");

                return exportPath;
            }
            catch (Exception ex)
            {
                LogError($"导出日志失败: {ex.Message}", "LogManager", ex);
                return string.Empty;
            }
            */
        }
        
        /// <summary>
        /// 导出日志为ZIP文件
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="minLevel">最小日志级别</param>
        /// <returns>导出ZIP文件路径</returns>
        public async Task<string> ExportLogsToZipAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null)
        {
            // 暂时禁用导出功能
            LogError("日志ZIP导出功能暂时禁用", "LogManager");
            return string.Empty;

            /*
            if (_logExporter == null)
            {
                LogError("日志导出器未初始化", "LogManager");
                return string.Empty;
            }

            try
            {
                var request = new LogExportRequest
                {
                    StartTime = startTime,
                    EndTime = endTime,
                    MinLevel = minLevel,
                    Format = _configuration?.Export?.ExportFormat ?? "json",
                    IncludeSystemInfo = _configuration?.Export?.IncludeSystemInfo ?? true,
                    Compress = _configuration?.Export?.CompressExports ?? true
                };

                LogInfo($"开始导出日志ZIP: {startTime} - {endTime}, 最小级别: {minLevel}", "LogManager");
                string zipPath = await _logExporter.ExportToZipAsync(request);
                LogInfo($"日志ZIP导出完成: {zipPath}", "LogManager");

                return zipPath;
            }
            catch (Exception ex)
            {
                LogError($"导出日志ZIP失败: {ex.Message}", "LogManager", ex);
                return string.Empty;
            }
            */
        }
        
        /// <summary>
        /// 清理日志
        /// </summary>
        /// <param name="beforeTime">清理此时间之前的日志</param>
        public void ClearLogs(DateTime? beforeTime = null)
        {
            // 这个方法将在日志轮转管理系统实现时完善
            LogInfo($"清理日志请求: {beforeTime?.ToString() ?? "全部"}", "LogManager");
        }
        
        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>日志统计数据</returns>
        public SimpleLogStatistics GetLogStatistics()
        {
            return _statistics;
        }
        
        /// <summary>
        /// 获取日志健康状态
        /// </summary>
        /// <returns>健康状态检查结果</returns>
        public SimpleLogHealthStatus GetHealthStatus()
        {
            if (_healthChecker == null)
            {
                _healthChecker = new SimpleLogHealthChecker();
            }
            return _healthChecker.CheckHealth();
        }

        /// <summary>
        /// 获取最后一次健康检查结果
        /// </summary>
        /// <returns>最后一次健康检查结果</returns>
        public SimpleLogHealthStatus GetLastHealthStatus()
        {
            if (_healthChecker == null)
            {
                _healthChecker = new SimpleLogHealthChecker();
            }
            return _healthChecker.GetLastStatus();
        }
        
        /// <summary>
        /// 重置统计数据
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
            LogInfo("日志统计数据已重置", "LogManager");
        }
        
        /// <summary>
        /// 保存当前统计数据
        /// </summary>
        /// <returns>保存任务</returns>
        public async Task SaveStatisticsAsync()
        {
            // 暂时禁用统计持久化
            // if (_statisticsPersistence != null)
            // {
            //     var healthStatus = _healthChecker?.LastHealthStatus;
            //     await _statisticsPersistence.SaveCurrentSessionAsync(_statistics, healthStatus);
            // }
            await Task.CompletedTask; // 避免编译警告
        }
        
        /// <summary>
        /// 获取历史统计数据
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>历史统计快照</returns>
        public async Task<LogStatisticsSnapshot[]> GetHistoricalStatisticsAsync(int count = 10)
        {
            // 暂时返回空数组，后续实现持久化功能时完善
            await Task.CompletedTask;
            return new LogStatisticsSnapshot[0];
        }

        /// <summary>
        /// 获取指定日期的统计数据
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>统计快照</returns>
        public async Task<LogStatisticsSnapshot> GetDailyStatisticsAsync(DateTime date)
        {
            // 暂时返回当前统计的快照
            await Task.CompletedTask;
            return new LogStatisticsSnapshot
            {
                Timestamp = date,
                TotalLogs = _statistics.TotalLogs,
                ErrorCount = _statistics.ErrorCount,
                WarningCount = _statistics.WarningCount,
                InfoCount = _statistics.InfoCount,
                DebugCount = _statistics.DebugCount,
                TraceCount = _statistics.TraceCount,
                ErrorRate = _statistics.GetErrorRate(),
                LogsPerSecond = _statistics.GetLogsPerSecond()
            };
        }
        
        /// <summary>
        /// 检查是否应该记录指定级别的日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="module">模块名称</param>
        /// <returns>是否应该记录</returns>
        private bool ShouldLog(LogLevel level, string module)
        {
            if (!_isLoggingEnabled)
            {
                return false;
            }
            
            LogLevel minLevel = _globalMinLevel;
            if (_configuration != null)
            {
                minLevel = _configuration.GetModuleLevel(module);
            }
            
            return level.IsEnabled(minLevel);
        }
        
        /// <summary>
        /// 写入到所有启用的写入器
        /// </summary>
        /// <param name="entry">日志条目</param>
        private void WriteToWriters(LogEntry entry)
        {
            lock (_writers)
            {
                foreach (var writer in _writers)
                {
                    if (writer.IsEnabled && entry.Level.IsEnabled(writer.MinLevel))
                    {
                        try
                        {
                            _ = writer.WriteAsync(entry);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[LogManager] 写入器 {writer.Name} 写入失败: {ex.Message}");
                            _statistics.RecordWriteOperation(TimeSpan.Zero, false); // 记录写入失败
                            _eventSystem?.Publish(new LogErrorEvent(ex, $"写入器 {writer.Name} 写入失败", writer.Name));
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var configManager = ConfigurationManager.Instance;
                if (configManager != null)
                {
                    _configuration = configManager.LoadConfiguration<LogConfiguration>(ConfigurationType.Logging);
                    
                    // 应用配置
                    if (_configuration != null)
                    {
                        _isLoggingEnabled = _configuration.IsLoggingEnabled;
                        _globalMinLevel = _configuration.GlobalMinLevel;
                    }
                }
                
                // 如果配置为空，创建默认配置
                if (_configuration == null)
                {
                    _configuration = new LogConfiguration();
                    _configuration.ResetToDefaults();
                    SaveConfiguration();
                }
                
                Debug.Log("[LogManager] 配置加载完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 加载配置失败: {ex.Message}");
                _configuration = new LogConfiguration();
                _configuration.ResetToDefaults();
            }
        }
        
        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                var configManager = ConfigurationManager.Instance;
                if (configManager != null && _configuration != null)
                {
                    configManager.SaveConfiguration(_configuration, ConfigurationType.Logging);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 保存配置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 初始化默认写入器
        /// </summary>
        private void InitializeDefaultWriters()
        {
            // 这个方法将在写入器实现时完善
            // 目前只添加控制台输出作为临时方案
            Debug.Log("[LogManager] 默认写入器初始化完成");
        }
        
        /// <summary>
        /// 初始化日志导出器 - 暂时禁用
        /// </summary>
        private void InitializeLogExporter()
        {
            // 暂时禁用导出器功能
            Debug.Log("[LogManager] 日志导出器功能暂时禁用");
            /*
            try
            {
                var exportConfig = _configuration?.Export ?? new LogExportConfiguration();
                _logExporter = new LogExporter(exportConfig, _eventSystem);
                Debug.Log("[LogManager] 日志导出器初始化完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 初始化日志导出器失败: {ex.Message}");
            }
            */
        }
        
        /// <summary>
        /// 初始化统计和监控组件 - 简化版本
        /// </summary>
        private void InitializeStatisticsAndMonitoring()
        {
            try
            {
                // 初始化健康检查器（简化版本）
                _healthChecker = new SimpleLogHealthChecker();

                // 暂时禁用统计持久化
                // _statisticsPersistence = new LogStatisticsPersistence();

                // 启动定期统计更新
                _lastStatisticsUpdate = Time.time;

                Debug.Log("[LogManager] 统计和监控组件初始化完成（简化版本）");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 初始化统计和监控组件失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新统计信息（在Update中调用）- 简化版本
        /// </summary>
        private void UpdateStatistics()
        {
            if (Time.time - _lastStatisticsUpdate >= STATISTICS_UPDATE_INTERVAL)
            {
                try
                {
                    // 更新内存使用统计（简化版本）
                    long memoryUsage = GC.GetTotalMemory(false);
                    // int queueSize = _performanceMonitor.GetQueueSize(); // 暂时禁用
                    // int poolSize = _logEntryPool.PoolSize; // 暂时禁用

                    _statistics.UpdateMemoryUsage(memoryUsage);

                    // 执行健康检查
                    _healthChecker?.CheckHealth();

                    // 暂时禁用统计保存
                    // _ = SaveStatisticsAsync();

                    _lastStatisticsUpdate = Time.time;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[LogManager] 更新统计信息失败: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 保存最终统计数据 - 简化版本
        /// </summary>
        private void SaveFinalStatistics()
        {
            try
            {
                // 暂时禁用统计持久化
                // if (_statisticsPersistence != null)
                // {
                //     var healthStatus = _healthChecker?.CheckHealth();
                //
                //     // 保存会话快照
                //     _ = _statisticsPersistence.SaveSessionSnapshotAsync(_statistics, healthStatus);
                //
                //     // 保存每日统计
                //     _ = _statisticsPersistence.SaveDailyStatsAsync(_statistics);
                //
                //     Debug.Log("[LogManager] 最终统计数据已保存");
                // }
                Debug.Log("[LogManager] 统计数据保存功能暂时禁用");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[LogManager] 保存最终统计数据失败: {ex.Message}");
            }
        }
    }
}