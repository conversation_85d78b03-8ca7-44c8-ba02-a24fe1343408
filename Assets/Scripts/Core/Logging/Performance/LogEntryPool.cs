using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Logging.Performance
{
    /// <summary>
    /// 日志条目对象池，用于减少GC压力
    /// </summary>
    public class LogEntryPool
    {
        private static readonly Lazy<LogEntryPool> _instance = new Lazy<LogEntryPool>(() => new LogEntryPool());
        private readonly ConcurrentQueue<LogEntry> _pool = new ConcurrentQueue<LogEntry>();
        private readonly object _statsLock = new object();
        
        private int _poolSize = 0;
        private int _maxPoolSize = 1000;
        private long _totalCreated = 0;
        private long _totalReused = 0;
        private long _totalReturned = 0;
        
        /// <summary>
        /// 单例实例
        /// </summary>
        public static LogEntryPool Instance => _instance.Value;
        
        /// <summary>
        /// 当前池大小
        /// </summary>
        public int PoolSize => _poolSize;
        
        /// <summary>
        /// 最大池大小
        /// </summary>
        public int MaxPoolSize
        {
            get => _maxPoolSize;
            set => _maxPoolSize = Math.Max(1, value);
        }
        
        /// <summary>
        /// 总创建数量
        /// </summary>
        public long TotalCreated => _totalCreated;
        
        /// <summary>
        /// 总复用数量
        /// </summary>
        public long TotalReused => _totalReused;
        
        /// <summary>
        /// 总归还数量
        /// </summary>
        public long TotalReturned => _totalReturned;
        
        /// <summary>
        /// 复用率
        /// </summary>
        public double ReuseRate => _totalCreated > 0 ? (double)_totalReused / _totalCreated : 0.0;
        
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private LogEntryPool()
        {
            // 预填充一些对象
            for (int i = 0; i < 10; i++)
            {
                _pool.Enqueue(CreateNewLogEntry());
                _poolSize++;
            }
        }
        
        /// <summary>
        /// 获取日志条目对象
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        /// <returns>日志条目对象</returns>
        public LogEntry Get(LogLevel level, string message, string module = null, Exception exception = null)
        {
            LogEntry entry;
            
            if (_pool.TryDequeue(out entry))
            {
                // 从池中获取对象
                System.Threading.Interlocked.Decrement(ref _poolSize);
                System.Threading.Interlocked.Increment(ref _totalReused);
                
                // 重置对象状态
                ResetLogEntry(entry, level, message, module, exception);
            }
            else
            {
                // 创建新对象
                entry = CreateNewLogEntry();
                ResetLogEntry(entry, level, message, module, exception);
                System.Threading.Interlocked.Increment(ref _totalCreated);
            }
            
            return entry;
        }
        
        /// <summary>
        /// 归还日志条目对象到池中
        /// </summary>
        /// <param name="entry">日志条目对象</param>
        public void Return(LogEntry entry)
        {
            if (entry == null)
            {
                return;
            }
            
            // 检查池大小限制
            if (_poolSize >= _maxPoolSize)
            {
                return;
            }
            
            // 清理对象状态
            ClearLogEntry(entry);
            
            // 归还到池中
            _pool.Enqueue(entry);
            System.Threading.Interlocked.Increment(ref _poolSize);
            System.Threading.Interlocked.Increment(ref _totalReturned);
        }
        
        /// <summary>
        /// 清空对象池
        /// </summary>
        public void Clear()
        {
            while (_pool.TryDequeue(out _))
            {
                System.Threading.Interlocked.Decrement(ref _poolSize);
            }
            
            Debug.Log("[LogEntryPool] 对象池已清空");
        }
        
        /// <summary>
        /// 获取池统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public PoolStatistics GetStatistics()
        {
            lock (_statsLock)
            {
                return new PoolStatistics
                {
                    PoolSize = _poolSize,
                    MaxPoolSize = _maxPoolSize,
                    TotalCreated = _totalCreated,
                    TotalReused = _totalReused,
                    TotalReturned = _totalReturned,
                    ReuseRate = ReuseRate
                };
            }
        }
        
        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            lock (_statsLock)
            {
                _totalCreated = 0;
                _totalReused = 0;
                _totalReturned = 0;
            }
            
            Debug.Log("[LogEntryPool] 统计信息已重置");
        }
        
        /// <summary>
        /// 创建新的日志条目对象
        /// </summary>
        /// <returns>新的日志条目对象</returns>
        private LogEntry CreateNewLogEntry()
        {
            return new LogEntry();
        }
        
        /// <summary>
        /// 重置日志条目对象状态
        /// </summary>
        /// <param name="entry">日志条目对象</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        private void ResetLogEntry(LogEntry entry, LogLevel level, string message, string module, Exception exception)
        {
            entry.Timestamp = DateTime.Now;
            entry.Level = level;
            entry.Message = message;
            entry.Module = module;
            entry.Exception = exception;
            entry.ThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString();
            entry.CorrelationId = null;
            
            if (entry.Properties == null)
            {
                entry.Properties = new Dictionary<string, object>();
            }
            else
            {
                entry.Properties.Clear();
            }
        }
        
        /// <summary>
        /// 清理日志条目对象状态
        /// </summary>
        /// <param name="entry">日志条目对象</param>
        private void ClearLogEntry(LogEntry entry)
        {
            entry.Message = null;
            entry.Module = null;
            entry.Exception = null;
            entry.ThreadId = null;
            entry.CorrelationId = null;
            entry.Properties?.Clear();
        }
    }
    
    /// <summary>
    /// 对象池统计信息
    /// </summary>
    public class PoolStatistics
    {
        /// <summary>
        /// 当前池大小
        /// </summary>
        public int PoolSize { get; set; }
        
        /// <summary>
        /// 最大池大小
        /// </summary>
        public int MaxPoolSize { get; set; }
        
        /// <summary>
        /// 总创建数量
        /// </summary>
        public long TotalCreated { get; set; }
        
        /// <summary>
        /// 总复用数量
        /// </summary>
        public long TotalReused { get; set; }
        
        /// <summary>
        /// 总归还数量
        /// </summary>
        public long TotalReturned { get; set; }
        
        /// <summary>
        /// 复用率
        /// </summary>
        public double ReuseRate { get; set; }
        
        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Pool: {PoolSize}/{MaxPoolSize}, Created: {TotalCreated}, Reused: {TotalReused}, Returned: {TotalReturned}, ReuseRate: {ReuseRate:P2}";
        }
    }
}