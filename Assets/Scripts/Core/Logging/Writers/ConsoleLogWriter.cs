using System;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Logging;

namespace DigitalHuman.Core.Logging.Writers
{
    /// <summary>
    /// 控制台日志写入器 - 支持Unity控制台和系统控制台输出
    /// </summary>
    public class ConsoleLogWriter : ILogWriter, IDisposable
    {
        public string Name { get; private set; }
        public bool IsEnabled { get; set; } = true;
        public LogLevel MinLevel { get; set; } = LogLevel.Info;
        
        // 格式化器
        private readonly ILogFormatter _formatter;
        
        // 配置选项
        private bool _useUnityConsole = true;
        private bool _enableColors = true;
        
        // 状态
        private bool _disposed = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">写入器名称</param>
        /// <param name="formatter">日志格式化器</param>
        /// <param name="minLevel">最小日志级别</param>
        /// <param name="useUnityConsole">是否使用Unity控制台</param>
        /// <param name="enableColors">是否启用彩色输出</param>
        public ConsoleLogWriter(
            string name,
            ILogFormatter formatter = null,
            LogLevel minLevel = LogLevel.Info,
            bool useUnityConsole = true,
            bool enableColors = true)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            _formatter = formatter ?? new Formatters.PlainTextLogFormatter();
            MinLevel = minLevel;
            _useUnityConsole = useUnityConsole;
            _enableColors = enableColors;
            
            Debug.Log($"[ConsoleLogWriter] 控制台写入器初始化完成: {Name}");
        }
        
        /// <summary>
        /// 异步写入日志条目
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>写入任务</returns>
        public async Task WriteAsync(LogEntry entry)
        {
            if (_disposed || !IsEnabled || entry == null)
            {
                return;
            }
            
            if (!entry.Level.IsEnabled(MinLevel))
            {
                return;
            }
            
            try
            {
                await Task.Run(() => WriteToConsole(entry));
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConsoleLogWriter] 写入控制台失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 刷新缓冲区（控制台写入器无需缓冲）
        /// </summary>
        public void Flush()
        {
            // 控制台写入器无需刷新操作
        }
        
        /// <summary>
        /// 配置写入器
        /// </summary>
        /// <param name="config">配置对象</param>
        public void Configure(LogWriterConfiguration config)
        {
            if (config == null)
            {
                return;
            }
            
            try
            {
                IsEnabled = config.IsEnabled;
                MinLevel = config.MinLevel;
                
                // 更新Unity控制台设置
                if (config.Configuration.ContainsKey("useUnityConsole"))
                {
                    _useUnityConsole = Convert.ToBoolean(config.Configuration["useUnityConsole"]);
                }
                
                // 更新彩色输出设置
                if (config.Configuration.ContainsKey("enableColors"))
                {
                    _enableColors = Convert.ToBoolean(config.Configuration["enableColors"]);
                }
                
                Debug.Log($"[ConsoleLogWriter] 配置更新完成: {Name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConsoleLogWriter] 配置更新失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }
            
            _disposed = true;
            Debug.Log($"[ConsoleLogWriter] 资源释放完成: {Name}");
        }
        
        /// <summary>
        /// 写入到控制台
        /// </summary>
        /// <param name="entry">日志条目</param>
        private void WriteToConsole(LogEntry entry)
        {
            try
            {
                string formattedMessage = _formatter.Format(entry);
                
                if (_useUnityConsole)
                {
                    WriteToUnityConsole(entry, formattedMessage);
                }
                else
                {
                    WriteToSystemConsole(entry, formattedMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConsoleLogWriter] 格式化或写入失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 写入到Unity控制台
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <param name="formattedMessage">格式化后的消息</param>
        private void WriteToUnityConsole(LogEntry entry, string formattedMessage)
        {
            switch (entry.Level)
            {
                case LogLevel.Trace:
                case LogLevel.Debug:
                case LogLevel.Info:
                    Debug.Log(formattedMessage);
                    break;
                    
                case LogLevel.Warn:
                    Debug.LogWarning(formattedMessage);
                    break;
                    
                case LogLevel.Error:
                case LogLevel.Fatal:
                    if (entry.Exception != null)
                    {
                        Debug.LogException(entry.Exception);
                    }
                    else
                    {
                        Debug.LogError(formattedMessage);
                    }
                    break;
                    
                default:
                    Debug.Log(formattedMessage);
                    break;
            }
        }
        
        /// <summary>
        /// 写入到系统控制台
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <param name="formattedMessage">格式化后的消息</param>
        private void WriteToSystemConsole(LogEntry entry, string formattedMessage)
        {
            try
            {
                if (_enableColors)
                {
                    WriteColoredMessage(entry.Level, formattedMessage);
                }
                else
                {
                    Console.WriteLine(formattedMessage);
                }
            }
            catch (Exception ex)
            {
                // 如果系统控制台写入失败，回退到Unity控制台
                Debug.LogError($"[ConsoleLogWriter] 系统控制台写入失败，回退到Unity控制台: {ex.Message}");
                WriteToUnityConsole(entry, formattedMessage);
            }
        }
        
        /// <summary>
        /// 写入彩色消息到系统控制台
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息内容</param>
        private void WriteColoredMessage(LogLevel level, string message)
        {
            var originalColor = Console.ForegroundColor;
            
            try
            {
                // 根据日志级别设置颜色
                switch (level)
                {
                    case LogLevel.Trace:
                        Console.ForegroundColor = ConsoleColor.Gray;
                        break;
                    case LogLevel.Debug:
                        Console.ForegroundColor = ConsoleColor.White;
                        break;
                    case LogLevel.Info:
                        Console.ForegroundColor = ConsoleColor.Green;
                        break;
                    case LogLevel.Warn:
                        Console.ForegroundColor = ConsoleColor.Yellow;
                        break;
                    case LogLevel.Error:
                        Console.ForegroundColor = ConsoleColor.Red;
                        break;
                    case LogLevel.Fatal:
                        Console.ForegroundColor = ConsoleColor.Magenta;
                        break;
                    default:
                        Console.ForegroundColor = ConsoleColor.White;
                        break;
                }
                
                Console.WriteLine(message);
            }
            finally
            {
                // 恢复原始颜色
                Console.ForegroundColor = originalColor;
            }
        }
        
        /// <summary>
        /// 获取日志级别的颜色代码（用于终端彩色输出）
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>ANSI颜色代码</returns>
        private string GetColorCode(LogLevel level)
        {
            switch (level)
            {
                case LogLevel.Trace:
                    return "\u001b[37m"; // 白色
                case LogLevel.Debug:
                    return "\u001b[36m"; // 青色
                case LogLevel.Info:
                    return "\u001b[32m"; // 绿色
                case LogLevel.Warn:
                    return "\u001b[33m"; // 黄色
                case LogLevel.Error:
                    return "\u001b[31m"; // 红色
                case LogLevel.Fatal:
                    return "\u001b[35m"; // 紫色
                default:
                    return "\u001b[0m";  // 重置
            }
        }
        
        /// <summary>
        /// 重置颜色代码
        /// </summary>
        private const string ResetColorCode = "\u001b[0m";
    }
}
