using System;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System.Collections.Generic;
using System.Collections.Concurrent;
using UnityEngine;
using DigitalHuman.Core.Logging;

namespace DigitalHuman.Core.Logging.Writers
{
    /// <summary>
    /// 文件日志写入器 - 支持异步写入、缓冲和文件轮转
    /// </summary>
    public class FileLogWriter : ILogWriter, IDisposable
    {
        public string Name { get; private set; }
        public bool IsEnabled { get; set; } = true;
        public LogLevel MinLevel { get; set; } = LogLevel.Info;
        
        // 文件相关
        private readonly string _filePath;
        private readonly long _maxFileSize;
        private StreamWriter _fileWriter;
        private readonly object _fileLock = new object();
        
        // 格式化器
        private readonly ILogFormatter _formatter;
        
        // 异步写入
        private readonly ConcurrentQueue<LogEntry> _logQueue;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Task _writeTask;
        private readonly AutoResetEvent _logAvailableEvent;
        
        // 缓冲配置
        private readonly int _bufferSize;
        private readonly TimeSpan _flushInterval;
        
        // 状态
        private bool _disposed = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">写入器名称</param>
        /// <param name="filePath">日志文件路径</param>
        /// <param name="formatter">日志格式化器</param>
        /// <param name="bufferSize">缓冲区大小</param>
        /// <param name="flushInterval">刷新间隔</param>
        /// <param name="maxFileSize">最大文件大小</param>
        public FileLogWriter(
            string name,
            string filePath,
            ILogFormatter formatter = null,
            int bufferSize = 1000,
            TimeSpan? flushInterval = null,
            long maxFileSize = 10 * 1024 * 1024) // 默认10MB
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            _filePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
            _formatter = formatter ?? new Formatters.PlainTextLogFormatter();
            _bufferSize = bufferSize;
            _flushInterval = flushInterval ?? TimeSpan.FromSeconds(5);
            _maxFileSize = maxFileSize;

            // 创建队列和事件
            _logQueue = new ConcurrentQueue<LogEntry>();
            _logAvailableEvent = new AutoResetEvent(false);

            // 创建取消令牌
            _cancellationTokenSource = new CancellationTokenSource();
            
            // 初始化文件写入器
            InitializeFileWriter();
            
            // 启动异步写入任务
            _writeTask = Task.Run(ProcessLogEntriesAsync);
        }
        
        /// <summary>
        /// 异步写入日志条目
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>写入任务</returns>
        public async Task WriteAsync(LogEntry entry)
        {
            if (_disposed || !IsEnabled || entry == null)
            {
                return;
            }

            if (!entry.Level.IsEnabled(MinLevel))
            {
                return;
            }

            try
            {
                _logQueue.Enqueue(entry);
                _logAvailableEvent.Set();
                await Task.CompletedTask; // 保持异步接口兼容性
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 写入日志条目到队列失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 刷新缓冲区
        /// </summary>
        public void Flush()
        {
            lock (_fileLock)
            {
                try
                {
                    _fileWriter?.Flush();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[FileLogWriter] 刷新文件缓冲区失败: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 配置写入器
        /// </summary>
        /// <param name="config">配置对象</param>
        public void Configure(LogWriterConfiguration config)
        {
            if (config == null)
            {
                return;
            }
            
            try
            {
                IsEnabled = config.IsEnabled;
                MinLevel = config.MinLevel;
                
                Debug.Log($"[FileLogWriter] 配置更新完成: {Name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 配置更新失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }
            
            _disposed = true;
            
            try
            {
                // 取消操作
                _cancellationTokenSource?.Cancel();

                // 等待写入任务完成
                _writeTask?.Wait(TimeSpan.FromSeconds(5));

                // 释放事件
                _logAvailableEvent?.Dispose();

                // 取消令牌
                _cancellationTokenSource?.Dispose();

                // 关闭文件写入器
                lock (_fileLock)
                {
                    _fileWriter?.Flush();
                    _fileWriter?.Dispose();
                    _fileWriter = null;
                }

                Debug.Log($"[FileLogWriter] 资源释放完成: {Name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 资源释放失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 初始化文件写入器
        /// </summary>
        private void InitializeFileWriter()
        {
            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(_filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // 创建文件写入器
                _fileWriter = new StreamWriter(_filePath, append: true)
                {
                    AutoFlush = false
                };
                
                Debug.Log($"[FileLogWriter] 文件写入器初始化完成: {_filePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 文件写入器初始化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 异步处理日志条目
        /// </summary>
        private async Task ProcessLogEntriesAsync()
        {
            var buffer = new List<LogEntry>(_bufferSize);
            var lastFlushTime = DateTime.UtcNow;

            try
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    // 等待日志可用或超时
                    if (_logAvailableEvent.WaitOne(_flushInterval))
                    {
                        // 处理队列中的所有日志
                        while (_logQueue.TryDequeue(out LogEntry entry) && buffer.Count < _bufferSize)
                        {
                            buffer.Add(entry);
                        }
                    }

                    // 检查是否需要刷新
                    var now = DateTime.UtcNow;
                    var shouldFlush = buffer.Count >= _bufferSize ||
                                    (buffer.Count > 0 && (now - lastFlushTime) >= _flushInterval);

                    if (shouldFlush)
                    {
                        await FlushBufferAsync(buffer);
                        buffer.Clear();
                        lastFlushTime = now;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 处理日志条目异常: {ex.Message}");
            }
            finally
            {
                // 刷新剩余的缓冲区
                if (buffer.Count > 0)
                {
                    await FlushBufferAsync(buffer);
                }
            }
        }
        
        /// <summary>
        /// 刷新缓冲区到文件
        /// </summary>
        /// <param name="buffer">日志条目缓冲区</param>
        /// <returns>刷新任务</returns>
        private async Task FlushBufferAsync(List<LogEntry> buffer)
        {
            if (buffer.Count == 0)
            {
                return;
            }
            
            lock (_fileLock)
            {
                try
                {
                    // 检查文件大小是否超限
                    CheckFileSizeAndRotate();
                    
                    if (_fileWriter != null)
                    {
                        foreach (var entry in buffer)
                        {
                            string formattedLog = _formatter.Format(entry);
                            _fileWriter.WriteLine(formattedLog);
                        }
                        
                        _fileWriter.Flush();
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[FileLogWriter] 刷新缓冲区到文件失败: {ex.Message}");
                    
                    // 尝试重新初始化文件写入器
                    try
                    {
                        ReinitializeFileWriter();
                    }
                    catch (Exception reinitEx)
                    {
                        Debug.LogError($"[FileLogWriter] 重新初始化文件写入器失败: {reinitEx.Message}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 检查文件大小并轮转
        /// </summary>
        private void CheckFileSizeAndRotate()
        {
            try
            {
                if (File.Exists(_filePath))
                {
                    var fileInfo = new FileInfo(_filePath);
                    if (fileInfo.Length > _maxFileSize)
                    {
                        RotateLogFile();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 检查文件大小失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 轮转日志文件
        /// </summary>
        private void RotateLogFile()
        {
            try
            {
                _fileWriter?.Dispose();
                
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var directory = Path.GetDirectoryName(_filePath);
                var fileName = Path.GetFileNameWithoutExtension(_filePath);
                var extension = Path.GetExtension(_filePath);
                var rotatedPath = Path.Combine(directory, $"{fileName}_{timestamp}{extension}");
                
                File.Move(_filePath, rotatedPath);
                
                // 重新初始化文件写入器
                ReinitializeFileWriter();
                
                Debug.Log($"[FileLogWriter] 日志文件已轮转: {rotatedPath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 日志文件轮转失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 重新初始化文件写入器
        /// </summary>
        private void ReinitializeFileWriter()
        {
            _fileWriter?.Dispose();
            _fileWriter = new StreamWriter(_filePath, append: true)
            {
                AutoFlush = false
            };
        }
    }
}
