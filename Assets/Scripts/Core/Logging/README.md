# 日志系统 (Logging System)

## 概述

日志系统是数字人管理系统的核心基础设施，提供完整的日志记录、管理、轮转和导出功能。系统采用模块化设计，支持多种日志格式、异步写入、自动轮转和性能优化。

## 架构设计

```
┌─────────────────────────────────────────────────────────┐
│                    LogManager                           │
│                   (日志管理器)                           │
├─────────────────────────────────────────────────────────┤
│  Logger  │  Logger  │  Logger  │  ...  │  (模块化记录器) │
├─────────────────────────────────────────────────────────┤
│           ILogWriter 接口层                              │
├─────────────────────────────────────────────────────────┤
│ FileLogWriter │ ConsoleLogWriter │ CustomLogWriter │ ... │
├─────────────────────────────────────────────────────────┤
│           ILogFormatter 接口层                           │
├─────────────────────────────────────────────────────────┤
│ JsonFormatter │ PlainTextFormatter │ StructuredFormatter │
├─────────────────────────────────────────────────────────┤
│                 LogRotationManager                       │
│                  (轮转管理器)                            │
└─────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. LogManager (日志管理器)
- **文件**: `LogManager.cs`
- **功能**: 统一的日志管理入口，负责日志记录、写入器管理、配置管理
- **特性**: 单例模式、线程安全、支持模块化日志记录器

### 2. Lo<PERSON> (模块化日志记录器)
- **文件**: `Logger.cs`
- **功能**: 为特定模块提供日志记录功能
- **特性**: 支持不同日志级别、模块级别配置

### 3. LogRotationManager (日志轮转管理器)
- **文件**: `Rotation/LogRotationManager.cs`
- **功能**: 自动日志文件轮转、清理、压缩和归档
- **特性**: 基于大小和数量的轮转策略、GZip压缩、异步处理

### 4. FileLogWriter (文件日志写入器)
- **文件**: `Writers/FileLogWriter.cs`
- **功能**: 异步文件日志写入
- **特性**: 缓冲机制、背压控制、自动轮转集成

### 5. LogPerformanceMonitor (日志性能监控器)
- **文件**: `Performance/LogPerformanceMonitor.cs`
- **功能**: 实时监控日志系统性能指标
- **特性**: 写入时间统计、内存监控、队列深度跟踪、自动降级策略

### 6. 日志格式化器
- **JsonLogFormatter**: JSON格式输出，适合结构化日志分析
- **PlainTextLogFormatter**: 纯文本格式，适合人类阅读
- **StructuredLogFormatter**: 键值对格式，适合日志解析

### 7. LogEntryPool (日志条目对象池)
- **文件**: `Performance/LogEntryPool.cs`
- **功能**: 日志条目对象复用，减少GC压力
- **特性**: 线程安全、统计信息、自动管理池大小

### 8. LogExporter (日志导出器)
- **文件**: `Export/LogExporter.cs`
- **功能**: 日志文件导出和压缩
- **特性**: 按时间和级别过滤、ZIP压缩、系统信息包含、异步处理

### 9. LogStatistics (日志统计数据收集器)
- **文件**: `Statistics/LogStatistics.cs`
- **功能**: 收集和管理日志系统的各种统计信息
- **特性**: 线程安全、实时统计、性能监控、多维度数据分析

### 10. LogHealthChecker (日志健康检查器)
- **文件**: `Statistics/LogHealthChecker.cs`
- **功能**: 监控日志系统健康状态和性能指标
- **特性**: 实时健康检查、多维度指标监控、健康状态评估

### 11. LogStatisticsPersistence (统计数据持久化)
- **文件**: `Statistics/LogStatisticsPersistence.cs`
- **功能**: 统计数据的持久化存储和历史数据管理
- **特性**: 会话快照、每日统计、历史数据查询、异步存储

### 12. LogStatisticsTests (日志统计测试组件)
- **文件**: `Tests/LogStatisticsTests.cs`
- **功能**: 日志统计功能的综合测试套件
- **特性**: 基础统计测试、健康检查测试、持久化测试、压力测试

### 13. LogManagerTests (日志管理器核心功能测试)
- **文件**: `Tests/LogManagerTests.cs`
- **功能**: LogManager 核心功能的单元测试套件
- **特性**: 基础日志记录测试、级别过滤测试、模块化测试、写入器管理测试、配置管理测试

## 主要接口

### ILogManager
```csharp
public interface ILogManager : IManager
{
    bool IsLoggingEnabled { get; set; }
    void Log(LogLevel level, string message, string module = null, Exception exception = null);
    ILogger GetLogger(string moduleName);
    void SetLogLevel(LogLevel level, string module = null);
    void AddLogWriter(ILogWriter writer);
    Task<string> ExportLogsAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null);
}
```

### ILogger
```csharp
public interface ILogger
{
    string ModuleName { get; }
    LogLevel MinLevel { get; set; }
    void Trace(string message, Exception exception = null);
    void Debug(string message, Exception exception = null);
    void Info(string message, Exception exception = null);
    void Warn(string message, Exception exception = null);
    void Error(string message, Exception exception = null);
    void Fatal(string message, Exception exception = null);
    bool IsEnabled(LogLevel level);
}
```

### ILogWriter
```csharp
public interface ILogWriter : IDisposable
{
    string Name { get; }
    bool IsEnabled { get; set; }
    LogLevel MinLevel { get; set; }
    Task WriteAsync(LogEntry entry);
    void Flush();
    void Configure(LogWriterConfiguration config);
}
```

### ILogFormatter
```csharp
public interface ILogFormatter
{
    bool SupportsStructuredData { get; }
    string Format(LogEntry entry);
}
```

## 使用指南

### 基本使用

```csharp
// 获取日志管理器实例
var logManager = LogManager.Instance;

// 直接记录日志
logManager.LogInfo("系统启动完成", "System");
logManager.LogError("API调用失败", "Network", exception);

// 获取模块化日志记录器
var audioLogger = logManager.GetLogger("Audio");
audioLogger.Info("音频设备初始化完成");
audioLogger.Error("音频播放失败", exception);

var networkLogger = logManager.GetLogger("Network");
networkLogger.Debug("发送HTTP请求", exception);
networkLogger.Warn("网络连接不稳定");
```

### 配置管理

```csharp
// 设置全局日志级别
logManager.SetLogLevel(LogLevel.Debug);

// 设置模块日志级别
logManager.SetLogLevel(LogLevel.Trace, "Audio");
logManager.SetLogLevel(LogLevel.Error, "Network");

// 启用/禁用日志功能
logManager.IsLoggingEnabled = false;
```

### 自定义写入器

```csharp
// 添加文件写入器
var fileWriter = new FileLogWriter(
    name: "MainLog",
    filePath: "Logs/app.log",
    formatter: new JsonLogFormatter(),
    bufferSize: 1000,
    maxFileSize: 10 * 1024 * 1024 // 10MB
);

logManager.AddLogWriter(fileWriter);
```

### 日志轮转管理

```csharp
// 创建轮转配置
var rotationConfig = new LogRotationConfiguration
{
    Strategy = "SizeAndCount",
    MaxFileSize = "10MB",
    MaxFileCount = 30,
    ArchiveOldFiles = true,
    CompressionEnabled = true
};

// 创建轮转管理器
var rotationManager = new LogRotationManager(rotationConfig, EventSystem.Instance);

// 检查并执行轮转
bool rotated = rotationManager.CheckAndRotate("Logs/app.log");

// 手动轮转
rotationManager.RotateLogFile("Logs/app.log", "手动轮转");

// 获取统计信息
var stats = rotationManager.GetLogFileStatistics("Logs", "app");
Console.WriteLine($"总文件数: {stats.TotalFiles}");
Console.WriteLine($"总大小: {stats.GetReadableTotalSize()}");
```

### 性能监控

```csharp
// 获取性能监控器实例
var performanceMonitor = LogPerformanceMonitor.Instance;

// 获取当前性能指标
var metrics = performanceMonitor.GetMetrics();
Console.WriteLine($"总日志数: {metrics.TotalLogsWritten}");
Console.WriteLine($"平均写入时间: {metrics.AverageWriteTimeMs:F2}ms");
Console.WriteLine($"内存使用: {metrics.GetReadableMemoryUsage()}");
Console.WriteLine($"吞吐量: {metrics.ThroughputPerSecond:F2} logs/sec");

// 检查是否需要降级
if (performanceMonitor.ShouldDegrade())
{
    var strategy = performanceMonitor.GetDegradationStrategy();
    if (strategy.DropLowPriorityLogs)
    {
        logManager.SetLogLevel(LogLevel.Warn);
    }
}

// 订阅性能警告事件
eventSystem.Subscribe<LogPerformanceWarningEvent>(evt =>
{
    Console.WriteLine($"性能警告: {evt.MetricName} = {evt.CurrentValue}");
});
```

### 日志导出

```csharp
// 导出指定时间范围的日志
string exportPath = await logManager.ExportLogsAsync(
    startTime: DateTime.Now.AddDays(-7),
    endTime: DateTime.Now,
    minLevel: LogLevel.Warn
);

// 导出为ZIP文件
string zipPath = await logManager.ExportLogsToZipAsync(
    startTime: DateTime.Now.AddDays(-30),
    endTime: DateTime.Now
);

// 使用 LogExporter 直接导出
var exportConfig = new LogExportConfiguration
{
    ExportDirectory = "LogExports",
    IncludeSystemInfo = true,
    CompressExports = true
};

var logExporter = new LogExporter(exportConfig, EventSystem.Instance);

var exportRequest = new LogExportRequest
{
    StartTime = DateTime.Now.AddDays(-7),
    EndTime = DateTime.Now,
    MinLevel = LogLevel.Error,
    Format = "json",
    IncludeSystemInfo = true
};

string exportedPath = await logExporter.ExportAsync(exportRequest);
string zipPath = await logExporter.ExportToZipAsync(exportRequest);
```

### 统计功能测试

```csharp
// 添加测试组件到GameObject
var testObject = new GameObject("LogStatisticsTests");
var testComponent = testObject.AddComponent<LogStatisticsTests>();

// 运行完整测试套件
testComponent.RunAllTests();

// 运行特定测试
testComponent.TestStatisticsReset();
testComponent.StressTest();

// 获取统计信息
var statistics = logManager.GetLogStatistics();
Console.WriteLine($"总日志数: {statistics.TotalLogCount}");
Console.WriteLine($"错误率: {statistics.GetErrorRate():P2}");

// 获取健康状态
var healthStatus = logManager.GetHealthStatus();
Console.WriteLine($"健康级别: {healthStatus.Level}");
Console.WriteLine($"状态消息: {healthStatus.Message}");

// 获取历史统计
var historicalStats = await logManager.GetHistoricalStatisticsAsync(10);
foreach (var snapshot in historicalStats)
{
    Console.WriteLine($"历史记录: {snapshot.snapshotTime}, 总日志: {snapshot.totalLogCount}");
}

// 使用示例组件进行集成测试
var exampleComponent = gameObject.AddComponent<LogStatisticsTestsExample>();
// 组件会自动进行定期测试和健康检查
```

### 核心功能测试

```csharp
// 添加LogManager核心功能测试组件
var testObject = new GameObject("LogManagerTests");
var testComponent = testObject.AddComponent<LogManagerTests>();

// 配置测试参数
testComponent.runTestsOnStart = true;
testComponent.enableDetailedOutput = true;

// 运行所有测试
testComponent.RunAllTests();

// 或者在Inspector中右键选择"运行所有测试"
```

## 配置文件

### LogConfiguration 示例

```json
{
  "logging": {
    "isLoggingEnabled": true,
    "globalMinLevel": "Info",
    "moduleLevels": {
      "Audio": "Debug",
      "Network": "Warn",
      "Rendering": "Info"
    },
    "writers": [
      {
        "type": "FileLogWriter",
        "name": "MainFileWriter",
        "isEnabled": true,
        "minLevel": "Info",
        "configuration": {
          "filePath": "Logs/app.log",
          "maxFileSize": "10MB",
          "maxFiles": 30,
          "formatter": "JsonLogFormatter",
          "bufferSize": 1000,
          "flushInterval": "5s"
        }
      }
    ],
    "rotation": {
      "strategy": "SizeAndCount",
      "maxFileSize": "10MB",
      "maxFileCount": 30,
      "archiveOldFiles": true,
      "compressionEnabled": true
    },
    "performance": {
      "asyncWriting": true,
      "bufferSize": 1000,
      "flushInterval": "5s",
      "maxMemoryUsage": "50MB"
    },
    "export": {
      "exportDirectory": "LogExports",
      "includeSystemInfo": true,
      "compressExports": true,
      "maxExportSizeMB": 100,
      "exportFormat": "json"
    }
  }
}
```

## 事件系统集成

日志系统与事件系统深度集成，支持以下事件：

### LogEvent
```csharp
eventSystem.Subscribe<LogEvent>(evt =>
{
    Console.WriteLine($"新日志: [{evt.Entry.Level}] {evt.Entry.Message}");
});
```

### LogRotationEvent
```csharp
eventSystem.Subscribe<LogRotationEvent>(evt =>
{
    Console.WriteLine($"文件轮转: {evt.OldFilePath} -> {evt.NewFilePath}");
    Console.WriteLine($"原因: {evt.Reason}, 大小: {evt.FileSize} 字节");
});
```

### LogErrorEvent
```csharp
eventSystem.Subscribe<LogErrorEvent>(evt =>
{
    Console.WriteLine($"日志系统错误: {evt.Context}");
    Console.WriteLine($"异常: {evt.Exception.Message}");
});
```

## 性能优化

### 1. 异步写入
- 使用 `Channel<LogEntry>` 实现异步日志处理
- 后台线程批量写入，避免阻塞主线程
- 支持背压控制，防止内存溢出

### 2. 对象池
- `LogEntryPool` 提供日志条目对象复用
- 减少GC压力，提高性能
- 线程安全的对象获取和归还机制

### 3. 缓冲机制
- 批量写入减少I/O操作
- 可配置的缓冲区大小和刷新间隔
- 智能刷新策略

### 4. 性能监控
- `LogPerformanceMonitor` 实时监控系统性能
- 写入时间、内存使用、队列深度统计
- P95/P99 性能指标计算
- 自动降级策略建议

### 5. 内存管理
- 监控内存使用量
- 自动清理过期缓存
- 支持内存使用限制
- 基于阈值的自动优化

## 最佳实践

### 1. 日志级别使用
- **Trace**: 详细的执行流程，仅在调试时使用
- **Debug**: 调试信息，开发和测试环境使用
- **Info**: 一般信息，记录重要的业务流程
- **Warn**: 警告信息，可能的问题但不影响运行
- **Error**: 错误信息，需要关注的异常情况
- **Fatal**: 致命错误，导致系统无法继续运行

### 2. 模块化日志
```csharp
// 为每个模块创建专用的日志记录器
public class AudioManager : MonoBehaviour
{
    private static readonly ILogger Logger = LogManager.Instance.GetLogger("Audio");
    
    public void Initialize()
    {
        Logger.Info("音频管理器初始化开始");
        try
        {
            // 初始化逻辑
            Logger.Info("音频管理器初始化完成");
        }
        catch (Exception ex)
        {
            Logger.Error("音频管理器初始化失败", ex);
            throw;
        }
    }
}
```

### 3. 结构化日志
```csharp
// 使用属性记录结构化信息
var entry = new LogEntry(LogLevel.Info, "用户登录成功", "Authentication");
entry.AddProperty("userId", userId);
entry.AddProperty("loginTime", DateTime.Now);
entry.AddProperty("ipAddress", ipAddress);
```

### 4. 异常记录
```csharp
try
{
    // 业务逻辑
}
catch (Exception ex)
{
    // 记录完整的异常信息
    logger.Error("操作失败", ex);
    
    // 或者添加上下文信息
    var entry = new LogEntry(LogLevel.Error, "数据库操作失败", "Database", ex);
    entry.AddProperty("operation", "SELECT");
    entry.AddProperty("table", "users");
    entry.AddProperty("duration", stopwatch.ElapsedMilliseconds);
}
```

### 5. 性能监控
```csharp
using (var scope = logger.BeginScope("DatabaseQuery"))
{
    var stopwatch = Stopwatch.StartNew();
    try
    {
        var result = database.Query(sql);
        logger.Info($"查询完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
        return result;
    }
    catch (Exception ex)
    {
        logger.Error($"查询失败，耗时: {stopwatch.ElapsedMilliseconds}ms", ex);
        throw;
    }
}
```

## 故障排除

### 常见问题

1. **日志文件无法创建**
   - 检查目录权限
   - 确保磁盘空间充足
   - 验证文件路径格式

2. **日志写入性能问题**
   - 增加缓冲区大小
   - 调整刷新间隔
   - 检查磁盘I/O性能

3. **日志文件过大**
   - 启用日志轮转
   - 调整轮转策略
   - 启用压缩功能

4. **内存使用过高**
   - 减少缓冲区大小
   - 启用对象池
   - 调整日志级别

### 调试工具

```csharp
// 获取日志统计信息
var stats = logManager.GetLogStatistics();
Console.WriteLine($"总日志数: {stats.TotalLogCount}");
Console.WriteLine($"错误日志数: {stats.LogCountByLevel[LogLevel.Error]}");

// 获取文件统计信息
var fileStats = rotationManager.GetLogFileStatistics("Logs", "app");
Console.WriteLine($"文件总大小: {fileStats.GetReadableTotalSize()}");
Console.WriteLine($"压缩文件数: {fileStats.CompressedFiles}");
```

## 扩展开发

### 自定义写入器

```csharp
public class DatabaseLogWriter : ILogWriter
{
    public string Name => "DatabaseWriter";
    public bool IsEnabled { get; set; } = true;
    public LogLevel MinLevel { get; set; } = LogLevel.Info;
    
    public async Task WriteAsync(LogEntry entry)
    {
        // 实现数据库写入逻辑
        await database.InsertLogAsync(entry);
    }
    
    public void Flush()
    {
        // 实现刷新逻辑
    }
    
    public void Configure(LogWriterConfiguration config)
    {
        // 实现配置逻辑
    }
    
    public void Dispose()
    {
        // 实现资源清理
    }
}
```

### 自定义格式化器

```csharp
public class CustomLogFormatter : ILogFormatter
{
    public bool SupportsStructuredData => true;
    
    public string Format(LogEntry entry)
    {
        // 实现自定义格式化逻辑
        return $"[{entry.Timestamp:HH:mm:ss}] {entry.Level}: {entry.Message}";
    }
}
```

## 相关文档

- [LogManager 日志管理器文档](docs/logging/LogManager.md)
- [LogPerformanceMonitor 性能监控文档](docs/logging/LogPerformanceMonitor.md)
- [LogRotationManager 详细文档](docs/logging/LogRotationManager.md)
- [LogExporter 日志导出文档](docs/logging/LogExporter.md)
- [LogStatistics 统计数据收集器文档](docs/logging/LogStatistics.md)
- [LogStatisticsTests 统计测试文档](docs/logging/LogStatisticsTests.md)
- [LogManagerTests 核心功能测试文档](docs/logging/LogManagerTests.md)
- [配置管理系统文档](../Configuration/README.md)
- [事件系统文档](../EventSystem.md)
- [性能监控文档](../Performance/README.md)

## 版本历史

- **v1.0**: 基础日志记录功能
- **v1.1**: 添加日志轮转管理
- **v1.2**: 性能优化和对象池
- **v1.3**: 日志导出功能
- **v1.4**: 事件系统集成