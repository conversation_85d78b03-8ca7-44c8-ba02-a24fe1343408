using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Logging.Formatters
{
    /// <summary>
    /// 日志格式化器工厂类
    /// </summary>
    public static class LogFormatterFactory
    {
        private static readonly Dictionary<string, Func<ILogFormatter>> _formatterFactories = 
            new Dictionary<string, Func<ILogFormatter>>(StringComparer.OrdinalIgnoreCase);
        
        /// <summary>
        /// 静态构造函数，注册默认格式化器
        /// </summary>
        static LogFormatterFactory()
        {
            RegisterDefaultFormatters();
        }
        
        /// <summary>
        /// 创建格式化器实例
        /// </summary>
        /// <param name="formatterType">格式化器类型</param>
        /// <returns>格式化器实例</returns>
        public static ILogFormatter CreateFormatter(string formatterType)
        {
            if (string.IsNullOrEmpty(formatterType))
            {
                return new PlainTextLogFormatter();
            }
            
            if (_formatterFactories.TryGetValue(formatterType, out var factory))
            {
                try
                {
                    return factory();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[LogFormatterFactory] 创建格式化器失败: {formatterType}, 错误: {ex.Message}");
                    return new PlainTextLogFormatter();
                }
            }
            
            Debug.LogWarning($"[LogFormatterFactory] 未知的格式化器类型: {formatterType}，使用默认格式化器");
            return new PlainTextLogFormatter();
        }
        
        /// <summary>
        /// 注册格式化器工厂
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="factory">工厂函数</param>
        public static void RegisterFormatter(string typeName, Func<ILogFormatter> factory)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                throw new ArgumentException("格式化器类型名称不能为空", nameof(typeName));
            }
            
            if (factory == null)
            {
                throw new ArgumentNullException(nameof(factory));
            }
            
            _formatterFactories[typeName] = factory;
            Debug.Log($"[LogFormatterFactory] 注册格式化器: {typeName}");
        }
        
        /// <summary>
        /// 取消注册格式化器
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <returns>是否成功取消注册</returns>
        public static bool UnregisterFormatter(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                return false;
            }
            
            bool removed = _formatterFactories.Remove(typeName);
            if (removed)
            {
                Debug.Log($"[LogFormatterFactory] 取消注册格式化器: {typeName}");
            }
            
            return removed;
        }
        
        /// <summary>
        /// 获取所有已注册的格式化器类型
        /// </summary>
        /// <returns>格式化器类型列表</returns>
        public static string[] GetRegisteredFormatterTypes()
        {
            var types = new string[_formatterFactories.Count];
            _formatterFactories.Keys.CopyTo(types, 0);
            return types;
        }
        
        /// <summary>
        /// 检查格式化器类型是否已注册
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <returns>是否已注册</returns>
        public static bool IsFormatterRegistered(string typeName)
        {
            return !string.IsNullOrEmpty(typeName) && _formatterFactories.ContainsKey(typeName);
        }
        
        /// <summary>
        /// 注册默认格式化器
        /// </summary>
        private static void RegisterDefaultFormatters()
        {
            // JSON格式化器
            RegisterFormatter("JsonLogFormatter", () => new JsonLogFormatter());
            RegisterFormatter("json", () => new JsonLogFormatter());
            
            // 纯文本格式化器
            RegisterFormatter("PlainTextLogFormatter", () => new PlainTextLogFormatter());
            RegisterFormatter("plaintext", () => new PlainTextLogFormatter());
            RegisterFormatter("text", () => new PlainTextLogFormatter());
            
            // 结构化格式化器
            RegisterFormatter("StructuredLogFormatter", () => new StructuredLogFormatter());
            RegisterFormatter("structured", () => new StructuredLogFormatter());
            RegisterFormatter("keyvalue", () => new StructuredLogFormatter());
            
            // 带不同配置的格式化器变体
            RegisterFormatter("plaintext_compact", () => new PlainTextLogFormatter(
                dateTimeFormat: "HH:mm:ss.fff",
                includeThreadId: false,
                includeCorrelationId: false,
                includeProperties: false));
                
            RegisterFormatter("plaintext_detailed", () => new PlainTextLogFormatter(
                dateTimeFormat: "yyyy-MM-dd HH:mm:ss.fff",
                includeThreadId: true,
                includeCorrelationId: true,
                includeProperties: true));
                
            RegisterFormatter("structured_space", () => new StructuredLogFormatter(
                fieldSeparator: " ",
                keyValueSeparator: "=",
                quoteValues: true));
                
            RegisterFormatter("structured_comma", () => new StructuredLogFormatter(
                fieldSeparator: ", ",
                keyValueSeparator: ":",
                quoteValues: false));
        }
        
        /// <summary>
        /// 重置为默认格式化器
        /// </summary>
        public static void ResetToDefaults()
        {
            _formatterFactories.Clear();
            RegisterDefaultFormatters();
            Debug.Log("[LogFormatterFactory] 重置为默认格式化器");
        }
    }
}