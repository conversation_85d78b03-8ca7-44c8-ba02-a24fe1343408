using System;
using System.Text;
using UnityEngine;

namespace DigitalHuman.Core.Logging.Formatters
{
    /// <summary>
    /// JSON格式的日志格式化器，用于结构化日志输出
    /// </summary>
    public class JsonLogFormatter : ILogFormatter
    {
        /// <summary>
        /// 是否支持结构化数据
        /// </summary>
        public bool SupportsStructuredData => true;
        
        /// <summary>
        /// 格式化日志条目为JSON格式
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>JSON格式的日志字符串</returns>
        public string Format(LogEntry entry)
        {
            if (entry == null)
            {
                return "{}";
            }
            
            try
            {
                var jsonObject = new JsonLogEntry
                {
                    timestamp = entry.Timestamp.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    level = entry.Level.ToShortString(),
                    module = entry.Module ?? "System",
                    thread = entry.ThreadId ?? "Unknown",
                    message = entry.Message ?? "",
                    correlationId = entry.CorrelationId,
                    exception = FormatException(entry.Exception),
                    properties = entry.Properties
                };
                
                return JsonUtility.ToJson(jsonObject);
            }
            catch (Exception ex)
            {
                // 如果JSON序列化失败，返回基本格式
                return $"{{\"timestamp\":\"{DateTime.Now:yyyy-MM-ddTHH:mm:ss.fffZ}\",\"level\":\"ERROR\",\"module\":\"JsonLogFormatter\",\"message\":\"JSON格式化失败: {ex.Message}\",\"originalMessage\":\"{EscapeJsonString(entry.Message)}\"}}";
            }
        }
        
        /// <summary>
        /// 格式化异常信息
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>格式化后的异常信息</returns>
        private ExceptionInfo FormatException(Exception exception)
        {
            if (exception == null)
            {
                return null;
            }
            
            return new ExceptionInfo
            {
                type = exception.GetType().Name,
                message = exception.Message,
                stackTrace = exception.StackTrace,
                innerException = FormatException(exception.InnerException)
            };
        }
        
        /// <summary>
        /// 转义JSON字符串中的特殊字符
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>转义后的字符串</returns>
        private string EscapeJsonString(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return "";
            }
            
            var sb = new StringBuilder();
            foreach (char c in input)
            {
                switch (c)
                {
                    case '"':
                        sb.Append("\\\"");
                        break;
                    case '\\':
                        sb.Append("\\\\");
                        break;
                    case '\b':
                        sb.Append("\\b");
                        break;
                    case '\f':
                        sb.Append("\\f");
                        break;
                    case '\n':
                        sb.Append("\\n");
                        break;
                    case '\r':
                        sb.Append("\\r");
                        break;
                    case '\t':
                        sb.Append("\\t");
                        break;
                    default:
                        if (c < ' ')
                        {
                            sb.AppendFormat("\\u{0:x4}", (int)c);
                        }
                        else
                        {
                            sb.Append(c);
                        }
                        break;
                }
            }
            return sb.ToString();
        }
    }
    
    /// <summary>
    /// JSON日志条目数据结构
    /// </summary>
    [Serializable]
    internal class JsonLogEntry
    {
        public string timestamp;
        public string level;
        public string module;
        public string thread;
        public string message;
        public string correlationId;
        public ExceptionInfo exception;
        public System.Collections.Generic.Dictionary<string, object> properties;
    }
    
    /// <summary>
    /// 异常信息数据结构
    /// </summary>
    [Serializable]
    internal class ExceptionInfo
    {
        public string type;
        public string message;
        public string stackTrace;
        public ExceptionInfo innerException;
    }
}