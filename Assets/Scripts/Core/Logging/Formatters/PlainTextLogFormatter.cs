using System;
using System.Text;

namespace DigitalHuman.Core.Logging.Formatters
{
    /// <summary>
    /// 纯文本格式的日志格式化器，用于可读性日志输出
    /// </summary>
    public class PlainTextLogFormatter : ILogFormatter
    {
        private readonly string _dateTimeFormat;
        private readonly bool _includeThreadId;
        private readonly bool _includeCorrelationId;
        private readonly bool _includeProperties;
        
        /// <summary>
        /// 是否支持结构化数据
        /// </summary>
        public bool SupportsStructuredData => false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dateTimeFormat">日期时间格式</param>
        /// <param name="includeThreadId">是否包含线程ID</param>
        /// <param name="includeCorrelationId">是否包含关联ID</param>
        /// <param name="includeProperties">是否包含扩展属性</param>
        public PlainTextLogFormatter(
            string dateTimeFormat = "yyyy-MM-dd HH:mm:ss.fff",
            bool includeThreadId = true,
            bool includeCorrelationId = false,
            bool includeProperties = false)
        {
            _dateTimeFormat = dateTimeFormat;
            _includeThreadId = includeThreadId;
            _includeCorrelationId = includeCorrelationId;
            _includeProperties = includeProperties;
        }
        
        /// <summary>
        /// 格式化日志条目为纯文本格式
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>纯文本格式的日志字符串</returns>
        public string Format(LogEntry entry)
        {
            if (entry == null)
            {
                return "";
            }
            
            var sb = new StringBuilder();
            
            // 时间戳
            sb.Append($"[{entry.Timestamp.ToString(_dateTimeFormat)}]");
            
            // 日志级别
            sb.Append($" [{entry.Level.ToShortString()}]");
            
            // 模块名称
            if (!string.IsNullOrEmpty(entry.Module))
            {
                sb.Append($" [{entry.Module}]");
            }
            
            // 线程ID
            if (_includeThreadId && !string.IsNullOrEmpty(entry.ThreadId))
            {
                sb.Append($" [Thread:{entry.ThreadId}]");
            }
            
            // 关联ID
            if (_includeCorrelationId && !string.IsNullOrEmpty(entry.CorrelationId))
            {
                sb.Append($" [Correlation:{entry.CorrelationId}]");
            }
            
            // 消息内容
            sb.Append($" {entry.Message ?? ""}");
            
            // 扩展属性
            if (_includeProperties && entry.Properties != null && entry.Properties.Count > 0)
            {
                sb.Append(" [Properties:");
                bool first = true;
                foreach (var kvp in entry.Properties)
                {
                    if (!first)
                    {
                        sb.Append(", ");
                    }
                    sb.Append($"{kvp.Key}={kvp.Value}");
                    first = false;
                }
                sb.Append("]");
            }
            
            // 异常信息
            if (entry.Exception != null)
            {
                sb.AppendLine();
                sb.Append(FormatException(entry.Exception));
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 格式化异常信息
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="indent">缩进级别</param>
        /// <returns>格式化后的异常信息</returns>
        private string FormatException(Exception exception, int indent = 0)
        {
            if (exception == null)
            {
                return "";
            }
            
            var sb = new StringBuilder();
            string indentString = new string(' ', indent * 2);
            
            sb.AppendLine($"{indentString}异常类型: {exception.GetType().Name}");
            sb.AppendLine($"{indentString}异常消息: {exception.Message}");
            
            if (!string.IsNullOrEmpty(exception.StackTrace))
            {
                sb.AppendLine($"{indentString}堆栈跟踪:");
                var stackLines = exception.StackTrace.Split('\n');
                foreach (var line in stackLines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        sb.AppendLine($"{indentString}  {line.Trim()}");
                    }
                }
            }
            
            // 内部异常
            if (exception.InnerException != null)
            {
                sb.AppendLine($"{indentString}内部异常:");
                sb.Append(FormatException(exception.InnerException, indent + 1));
            }
            
            return sb.ToString();
        }
    }
}