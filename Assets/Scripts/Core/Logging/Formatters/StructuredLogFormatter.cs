using System;
using System.Text;
using System.Linq;

namespace DigitalHuman.Core.Logging.Formatters
{
    /// <summary>
    /// 结构化日志格式化器，提供键值对格式的日志输出
    /// </summary>
    public class StructuredLogFormatter : ILogFormatter
    {
        private readonly string _fieldSeparator;
        private readonly string _keyValueSeparator;
        private readonly bool _quoteValues;
        
        /// <summary>
        /// 是否支持结构化数据
        /// </summary>
        public bool SupportsStructuredData => true;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="fieldSeparator">字段分隔符</param>
        /// <param name="keyValueSeparator">键值分隔符</param>
        /// <param name="quoteValues">是否为值添加引号</param>
        public StructuredLogFormatter(
            string fieldSeparator = " ",
            string keyValueSeparator = "=",
            bool quoteValues = true)
        {
            _fieldSeparator = fieldSeparator;
            _keyValueSeparator = keyValueSeparator;
            _quoteValues = quoteValues;
        }
        
        /// <summary>
        /// 格式化日志条目为结构化格式
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>结构化格式的日志字符串</returns>
        public string Format(LogEntry entry)
        {
            if (entry == null)
            {
                return "";
            }
            
            var sb = new StringBuilder();
            
            // 基础字段
            AppendField(sb, "timestamp", entry.Timestamp.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
            AppendField(sb, "level", entry.Level.ToShortString());
            AppendField(sb, "module", entry.Module ?? "System");
            AppendField(sb, "thread", entry.ThreadId ?? "Unknown");
            AppendField(sb, "message", entry.Message ?? "");
            
            // 关联ID
            if (!string.IsNullOrEmpty(entry.CorrelationId))
            {
                AppendField(sb, "correlationId", entry.CorrelationId);
            }
            
            // 扩展属性
            if (entry.Properties != null && entry.Properties.Count > 0)
            {
                foreach (var kvp in entry.Properties)
                {
                    AppendField(sb, $"prop_{kvp.Key}", kvp.Value?.ToString() ?? "null");
                }
            }
            
            // 异常信息
            if (entry.Exception != null)
            {
                AppendField(sb, "exception_type", entry.Exception.GetType().Name);
                AppendField(sb, "exception_message", entry.Exception.Message);
                
                if (!string.IsNullOrEmpty(entry.Exception.StackTrace))
                {
                    // 压缩堆栈跟踪为单行
                    var compactStackTrace = entry.Exception.StackTrace
                        .Replace("\r\n", " | ")
                        .Replace("\n", " | ")
                        .Replace("\r", " | ");
                    AppendField(sb, "exception_stackTrace", compactStackTrace);
                }
                
                // 内部异常
                if (entry.Exception.InnerException != null)
                {
                    AppendField(sb, "inner_exception_type", entry.Exception.InnerException.GetType().Name);
                    AppendField(sb, "inner_exception_message", entry.Exception.InnerException.Message);
                }
            }
            
            return sb.ToString().TrimEnd();
        }
        
        /// <summary>
        /// 添加字段到字符串构建器
        /// </summary>
        /// <param name="sb">字符串构建器</param>
        /// <param name="key">字段键</param>
        /// <param name="value">字段值</param>
        private void AppendField(StringBuilder sb, string key, string value)
        {
            if (sb.Length > 0)
            {
                sb.Append(_fieldSeparator);
            }
            
            sb.Append(key);
            sb.Append(_keyValueSeparator);
            
            if (_quoteValues)
            {
                sb.Append($"\"{EscapeValue(value)}\"");
            }
            else
            {
                sb.Append(EscapeValue(value));
            }
        }
        
        /// <summary>
        /// 转义字段值中的特殊字符
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>转义后的值</returns>
        private string EscapeValue(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return "";
            }
            
            // 转义引号和换行符
            return value
                .Replace("\"", "\\\"")
                .Replace("\r\n", "\\n")
                .Replace("\n", "\\n")
                .Replace("\r", "\\n")
                .Replace("\t", "\\t");
        }
    }
}