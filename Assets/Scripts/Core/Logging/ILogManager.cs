using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DigitalHuman.Core.Logging.Statistics;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志管理器接口，定义日志系统的核心功能
    /// </summary>
    public interface ILogManager : IManager
    {
        /// <summary>
        /// 日志功能是否启用
        /// </summary>
        bool IsLoggingEnabled { get; set; }
        
        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        void Log(LogLevel level, string message, string module = null, Exception exception = null);
        
        /// <summary>
        /// 记录跟踪级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        void LogTrace(string message, string module = null);
        
        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        void LogDebug(string message, string module = null);
        
        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        void LogInfo(string message, string module = null);
        
        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        void LogWarn(string message, string module = null);
        
        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        void LogError(string message, string module = null, Exception exception = null);
        
        /// <summary>
        /// 记录致命错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="module">模块名称</param>
        /// <param name="exception">异常信息</param>
        void LogFatal(string message, string module = null, Exception exception = null);
        
        /// <summary>
        /// 获取模块化日志记录器
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>日志记录器实例</returns>
        ILogger GetLogger(string moduleName);
        
        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="module">模块名称，为空则设置全局级别</param>
        void SetLogLevel(LogLevel level, string module = null);
        
        /// <summary>
        /// 添加日志写入器
        /// </summary>
        /// <param name="writer">日志写入器</param>
        void AddLogWriter(ILogWriter writer);
        
        /// <summary>
        /// 移除日志写入器
        /// </summary>
        /// <param name="writer">日志写入器</param>
        void RemoveLogWriter(ILogWriter writer);
        
        /// <summary>
        /// 刷新所有日志写入器
        /// </summary>
        void FlushAll();
        
        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="minLevel">最小日志级别</param>
        /// <returns>导出文件路径</returns>
        Task<string> ExportLogsAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null);
        
        /// <summary>
        /// 导出日志为ZIP文件
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="minLevel">最小日志级别</param>
        /// <returns>导出ZIP文件路径</returns>
        Task<string> ExportLogsToZipAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null);
        
        /// <summary>
        /// 清理日志
        /// </summary>
        /// <param name="beforeTime">清理此时间之前的日志</param>
        void ClearLogs(DateTime? beforeTime = null);
        
        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>日志统计数据</returns>
        SimpleLogStatistics GetLogStatistics();
        
        /// <summary>
        /// 获取日志健康状态
        /// </summary>
        /// <returns>健康状态检查结果</returns>
        SimpleLogHealthStatus GetHealthStatus();

        /// <summary>
        /// 获取最后一次健康检查结果
        /// </summary>
        /// <returns>最后一次健康检查结果</returns>
        SimpleLogHealthStatus GetLastHealthStatus();
        
        /// <summary>
        /// 重置统计数据
        /// </summary>
        void ResetStatistics();
        
        /// <summary>
        /// 保存当前统计数据
        /// </summary>
        /// <returns>保存任务</returns>
        Task SaveStatisticsAsync();
        
        /// <summary>
        /// 获取历史统计数据
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>历史统计快照</returns>
        Task<LogStatisticsSnapshot[]> GetHistoricalStatisticsAsync(int count = 10);
        
        /// <summary>
        /// 获取指定日期的统计数据
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>统计快照</returns>
        Task<LogStatisticsSnapshot> GetDailyStatisticsAsync(DateTime date);
    }
}