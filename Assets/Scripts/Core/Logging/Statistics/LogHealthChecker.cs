using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Logging.Statistics
{
    /// <summary>
    /// 日志健康状态级别
    /// </summary>
    public enum LogHealthLevel
    {
        /// <summary>健康</summary>
        Healthy,
        /// <summary>警告</summary>
        Warning,
        /// <summary>严重</summary>
        Critical,
        /// <summary>故障</summary>
        Failed
    }
    
    /// <summary>
    /// 日志健康状态检查结果
    /// </summary>
    [Serializable]
    public class LogHealthStatus
    {
        [SerializeField] private LogHealthLevel level;
        [SerializeField] private string message;
        [SerializeField] private string recommendation;
        [SerializeField] private DateTime checkTime;
        [SerializeField] private Dictionary<string, object> metrics;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="level">健康级别</param>
        /// <param name="message">状态消息</param>
        /// <param name="recommendation">建议</param>
        public LogHealthStatus(LogHealthLevel level, string message, string recommendation = "")
        {
            this.level = level;
            this.message = message;
            this.recommendation = recommendation;
            this.checkTime = DateTime.Now;
            this.metrics = new Dictionary<string, object>();
        }
        
        /// <summary>健康级别</summary>
        public LogHealthLevel Level => level;
        
        /// <summary>状态消息</summary>
        public string Message => message;
        
        /// <summary>建议</summary>
        public string Recommendation => recommendation;
        
        /// <summary>检查时间</summary>
        public DateTime CheckTime => checkTime;
        
        /// <summary>相关指标</summary>
        public IReadOnlyDictionary<string, object> Metrics => metrics;
        
        /// <summary>
        /// 添加指标数据
        /// </summary>
        /// <param name="key">指标名称</param>
        /// <param name="value">指标值</param>
        public void AddMetric(string key, object value)
        {
            metrics[key] = value;
        }
        
        public override string ToString()
        {
            return $"[{level}] {message}";
        }
    }
    
    /// <summary>
    /// 日志健康状态检查器
    /// 负责监控日志系统的健康状态并提供诊断建议
    /// </summary>
    public class LogHealthChecker
    {
        [Header("健康检查阈值")]
        [SerializeField] private double maxErrorRate = 0.1; // 最大错误率 10%
        [SerializeField] private double maxWriteFailureRate = 0.05; // 最大写入失败率 5%
        [SerializeField] private double maxAverageWriteTime = 100; // 最大平均写入时间 100ms
        [SerializeField] private long maxMemoryUsage = 50 * 1024 * 1024; // 最大内存使用 50MB
        [SerializeField] private int maxQueueSize = 1000; // 最大队列大小
        [SerializeField] private double minLogsPerSecond = 0.1; // 最小日志频率
        [SerializeField] private double maxLogsPerSecond = 1000; // 最大日志频率
        
        private readonly LogStatistics _statistics;
        private DateTime _lastHealthCheck;
        private LogHealthStatus _lastHealthStatus;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statistics">日志统计对象</param>
        public LogHealthChecker(LogStatistics statistics)
        {
            _statistics = statistics ?? throw new ArgumentNullException(nameof(statistics));
            _lastHealthCheck = DateTime.MinValue;
        }
        
        /// <summary>
        /// 最后一次健康检查状态
        /// </summary>
        public LogHealthStatus LastHealthStatus => _lastHealthStatus;
        
        /// <summary>
        /// 最后一次健康检查时间
        /// </summary>
        public DateTime LastHealthCheck => _lastHealthCheck;
        
        /// <summary>
        /// 执行完整的健康状态检查
        /// </summary>
        /// <returns>健康状态结果</returns>
        public LogHealthStatus CheckHealth()
        {
            _lastHealthCheck = DateTime.Now;
            
            var checks = new List<LogHealthStatus>
            {
                CheckErrorRate(),
                CheckWritePerformance(),
                CheckMemoryUsage(),
                CheckQueueStatus(),
                CheckLogFrequency(),
                CheckSystemUptime()
            };
            
            // 找出最严重的问题
            LogHealthLevel overallLevel = LogHealthLevel.Healthy;
            var criticalIssues = new List<string>();
            var warnings = new List<string>();
            var recommendations = new List<string>();
            
            foreach (var check in checks)
            {
                if (check.Level > overallLevel)
                {
                    overallLevel = check.Level;
                }
                
                switch (check.Level)
                {
                    case LogHealthLevel.Critical:
                    case LogHealthLevel.Failed:
                        criticalIssues.Add(check.Message);
                        if (!string.IsNullOrEmpty(check.Recommendation))
                            recommendations.Add(check.Recommendation);
                        break;
                    case LogHealthLevel.Warning:
                        warnings.Add(check.Message);
                        if (!string.IsNullOrEmpty(check.Recommendation))
                            recommendations.Add(check.Recommendation);
                        break;
                }
            }
            
            // 生成综合状态消息
            string message = GenerateOverallMessage(overallLevel, criticalIssues, warnings);
            string recommendation = string.Join("; ", recommendations);
            
            _lastHealthStatus = new LogHealthStatus(overallLevel, message, recommendation);
            
            // 添加综合指标
            _lastHealthStatus.AddMetric("总日志数", _statistics.TotalLogCount);
            _lastHealthStatus.AddMetric("错误率", _statistics.GetErrorRate());
            _lastHealthStatus.AddMetric("写入成功率", _statistics.GetWriteSuccessRate());
            _lastHealthStatus.AddMetric("平均写入时间", _statistics.AverageWriteTime);
            _lastHealthStatus.AddMetric("内存使用", _statistics.CurrentMemoryUsage);
            _lastHealthStatus.AddMetric("队列大小", _statistics.QueueSize);
            _lastHealthStatus.AddMetric("运行时间", _statistics.Uptime.ToString());
            
            return _lastHealthStatus;
        }
        
        /// <summary>
        /// 检查错误率
        /// </summary>
        private LogHealthStatus CheckErrorRate()
        {
            double errorRate = _statistics.GetErrorRate();
            
            if (errorRate > maxErrorRate)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Critical,
                    $"错误率过高: {errorRate:P2} (阈值: {maxErrorRate:P2})",
                    "检查应用程序逻辑，减少错误日志的产生"
                );
            }
            else if (errorRate > maxErrorRate * 0.7)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"错误率偏高: {errorRate:P2}",
                    "监控错误趋势，考虑优化代码质量"
                );
            }
            
            return new LogHealthStatus(LogHealthLevel.Healthy, "错误率正常");
        }
        
        /// <summary>
        /// 检查写入性能
        /// </summary>
        private LogHealthStatus CheckWritePerformance()
        {
            double writeSuccessRate = _statistics.GetWriteSuccessRate();
            double avgWriteTime = _statistics.AverageWriteTime;
            
            if (writeSuccessRate < (1.0 - maxWriteFailureRate))
            {
                return new LogHealthStatus(
                    LogHealthLevel.Critical,
                    $"写入失败率过高: {(1.0 - writeSuccessRate):P2}",
                    "检查磁盘空间和文件权限，考虑启用错误恢复机制"
                );
            }
            
            if (avgWriteTime > maxAverageWriteTime)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"写入性能较慢: {avgWriteTime:F2}ms (阈值: {maxAverageWriteTime}ms)",
                    "考虑优化磁盘I/O或增加缓冲区大小"
                );
            }
            
            return new LogHealthStatus(LogHealthLevel.Healthy, "写入性能正常");
        }
        
        /// <summary>
        /// 检查内存使用情况
        /// </summary>
        private LogHealthStatus CheckMemoryUsage()
        {
            long currentMemory = _statistics.CurrentMemoryUsage;
            long peakMemory = _statistics.PeakMemoryUsage;
            
            if (currentMemory > maxMemoryUsage)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Critical,
                    $"内存使用过高: {currentMemory / (1024 * 1024)}MB (阈值: {maxMemoryUsage / (1024 * 1024)}MB)",
                    "考虑减少缓冲区大小或启用更积极的日志轮转"
                );
            }
            else if (currentMemory > maxMemoryUsage * 0.8)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"内存使用偏高: {currentMemory / (1024 * 1024)}MB",
                    "监控内存趋势，考虑优化内存使用"
                );
            }
            
            return new LogHealthStatus(LogHealthLevel.Healthy, "内存使用正常");
        }
        
        /// <summary>
        /// 检查队列状态
        /// </summary>
        private LogHealthStatus CheckQueueStatus()
        {
            int queueSize = _statistics.QueueSize;
            
            if (queueSize > maxQueueSize)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Critical,
                    $"日志队列积压严重: {queueSize} (阈值: {maxQueueSize})",
                    "增加写入线程数量或优化写入性能"
                );
            }
            else if (queueSize > maxQueueSize * 0.7)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"日志队列积压: {queueSize}",
                    "监控队列趋势，考虑优化处理速度"
                );
            }
            
            return new LogHealthStatus(LogHealthLevel.Healthy, "队列状态正常");
        }
        
        /// <summary>
        /// 检查日志频率
        /// </summary>
        private LogHealthStatus CheckLogFrequency()
        {
            double logsPerSecond = _statistics.GetLogsPerSecond();
            
            if (logsPerSecond > maxLogsPerSecond)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"日志频率过高: {logsPerSecond:F2}/秒 (阈值: {maxLogsPerSecond}/秒)",
                    "考虑提高日志级别或减少冗余日志"
                );
            }
            else if (logsPerSecond < minLogsPerSecond && _statistics.Uptime.TotalMinutes > 5)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"日志频率过低: {logsPerSecond:F2}/秒",
                    "检查日志系统是否正常工作"
                );
            }
            
            return new LogHealthStatus(LogHealthLevel.Healthy, "日志频率正常");
        }
        
        /// <summary>
        /// 检查系统运行时间
        /// </summary>
        private LogHealthStatus CheckSystemUptime()
        {
            var uptime = _statistics.Uptime;
            
            if (uptime.TotalDays > 30)
            {
                return new LogHealthStatus(
                    LogHealthLevel.Warning,
                    $"系统运行时间较长: {uptime.TotalDays:F1}天",
                    "考虑定期重启以清理资源"
                );
            }
            
            return new LogHealthStatus(LogHealthLevel.Healthy, "系统运行时间正常");
        }
        
        /// <summary>
        /// 生成综合状态消息
        /// </summary>
        private string GenerateOverallMessage(LogHealthLevel level, List<string> criticalIssues, List<string> warnings)
        {
            switch (level)
            {
                case LogHealthLevel.Healthy:
                    return "日志系统运行正常";
                    
                case LogHealthLevel.Warning:
                    return $"日志系统存在 {warnings.Count} 个警告: {string.Join(", ", warnings)}";
                    
                case LogHealthLevel.Critical:
                case LogHealthLevel.Failed:
                    return $"日志系统存在 {criticalIssues.Count} 个严重问题: {string.Join(", ", criticalIssues)}";
                    
                default:
                    return "未知状态";
            }
        }
        
        /// <summary>
        /// 配置健康检查阈值
        /// </summary>
        /// <param name="errorRate">最大错误率</param>
        /// <param name="writeFailureRate">最大写入失败率</param>
        /// <param name="avgWriteTime">最大平均写入时间</param>
        /// <param name="memoryUsage">最大内存使用</param>
        /// <param name="queueSize">最大队列大小</param>
        public void ConfigureThresholds(double? errorRate = null, double? writeFailureRate = null, 
            double? avgWriteTime = null, long? memoryUsage = null, int? queueSize = null)
        {
            if (errorRate.HasValue) maxErrorRate = errorRate.Value;
            if (writeFailureRate.HasValue) maxWriteFailureRate = writeFailureRate.Value;
            if (avgWriteTime.HasValue) maxAverageWriteTime = avgWriteTime.Value;
            if (memoryUsage.HasValue) maxMemoryUsage = memoryUsage.Value;
            if (queueSize.HasValue) maxQueueSize = queueSize.Value;
        }
    }
}