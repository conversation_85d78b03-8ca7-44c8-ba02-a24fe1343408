using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using UnityEngine;

namespace DigitalHuman.Core.Logging.Statistics
{
    /// <summary>
    /// 日志统计数据收集器
    /// 负责收集和管理日志系统的各种统计信息
    /// </summary>
    [Serializable]
    public class LogStatistics
    {
        [Header("基础统计")]
        [SerializeField] private long totalLogCount;
        [SerializeField] private Dictionary<LogLevel, long> logCountByLevel;
        [SerializeField] private Dictionary<string, long> logCountByModule;
        
        [Header("性能统计")]
        [SerializeField] private double averageWriteTime;
        [SerializeField] private long totalWriteTime;
        [SerializeField] private long writeOperationCount;
        [SerializeField] private long failedWriteCount;
        
        [Header("文件统计")]
        [SerializeField] private long totalFilesCreated;
        [SerializeField] private long totalBytesWritten;
        [SerializeField] private long currentFileSize;
        [SerializeField] private int rotationCount;
        
        [Header("内存统计")]
        [SerializeField] private long peakMemoryUsage;
        [SerializeField] private long currentMemoryUsage;
        [SerializeField] private int queueSize;
        [SerializeField] private int poolSize;
        
        [Header("时间统计")]
        [SerializeField] private DateTime startTime;
        [SerializeField] private DateTime lastLogTime;
        [SerializeField] private TimeSpan uptime;
        
        // 线程安全的计数器
        private readonly ConcurrentDictionary<LogLevel, long> _levelCounters;
        private readonly ConcurrentDictionary<string, long> _moduleCounters;
        private readonly object _lockObject = new object();
        
        /// <summary>
        /// 构造函数，初始化统计数据
        /// </summary>
        public LogStatistics()
        {
            startTime = DateTime.Now;
            logCountByLevel = new Dictionary<LogLevel, long>();
            logCountByModule = new Dictionary<string, long>();
            _levelCounters = new ConcurrentDictionary<LogLevel, long>();
            _moduleCounters = new ConcurrentDictionary<string, long>();
            
            // 初始化所有日志级别的计数器
            foreach (LogLevel level in Enum.GetValues(typeof(LogLevel)))
            {
                logCountByLevel[level] = 0;
                _levelCounters[level] = 0;
            }
        }
        
        #region 公共属性
        
        /// <summary>
        /// 总日志数量
        /// </summary>
        public long TotalLogCount => totalLogCount;
        
        /// <summary>
        /// 按级别统计的日志数量
        /// </summary>
        public IReadOnlyDictionary<LogLevel, long> LogCountByLevel => logCountByLevel;
        
        /// <summary>
        /// 按模块统计的日志数量
        /// </summary>
        public IReadOnlyDictionary<string, long> LogCountByModule => logCountByModule;
        
        /// <summary>
        /// 平均写入时间（毫秒）
        /// </summary>
        public double AverageWriteTime => averageWriteTime;
        
        /// <summary>
        /// 写入失败次数
        /// </summary>
        public long FailedWriteCount => failedWriteCount;
        
        /// <summary>
        /// 总文件数量
        /// </summary>
        public long TotalFilesCreated => totalFilesCreated;
        
        /// <summary>
        /// 总写入字节数
        /// </summary>
        public long TotalBytesWritten => totalBytesWritten;
        
        /// <summary>
        /// 轮转次数
        /// </summary>
        public int RotationCount => rotationCount;
        
        /// <summary>
        /// 峰值内存使用量
        /// </summary>
        public long PeakMemoryUsage => peakMemoryUsage;
        
        /// <summary>
        /// 当前内存使用量
        /// </summary>
        public long CurrentMemoryUsage => currentMemoryUsage;
        
        /// <summary>
        /// 队列大小
        /// </summary>
        public int QueueSize => queueSize;
        
        /// <summary>
        /// 对象池大小
        /// </summary>
        public int PoolSize => poolSize;
        
        /// <summary>
        /// 系统启动时间
        /// </summary>
        public DateTime StartTime => startTime;
        
        /// <summary>
        /// 最后一次日志时间
        /// </summary>
        public DateTime LastLogTime => lastLogTime;
        
        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan Uptime => DateTime.Now - startTime;
        
        #endregion
        
        #region 统计更新方法
        
        /// <summary>
        /// 记录新的日志条目
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="module">模块名称</param>
        public void RecordLogEntry(LogLevel level, string module)
        {
            lock (_lockObject)
            {
                totalLogCount++;
                lastLogTime = DateTime.Now;
                
                // 更新级别统计
                _levelCounters.AddOrUpdate(level, 1, (key, value) => value + 1);
                logCountByLevel[level] = _levelCounters[level];
                
                // 更新模块统计
                if (!string.IsNullOrEmpty(module))
                {
                    _moduleCounters.AddOrUpdate(module, 1, (key, value) => value + 1);
                    logCountByModule[module] = _moduleCounters[module];
                }
            }
        }
        
        /// <summary>
        /// 记录写入操作性能
        /// </summary>
        /// <param name="writeTimeMs">写入时间（毫秒）</param>
        /// <param name="success">是否成功</param>
        public void RecordWriteOperation(double writeTimeMs, bool success)
        {
            lock (_lockObject)
            {
                if (success)
                {
                    writeOperationCount++;
                    totalWriteTime += (long)writeTimeMs;
                    averageWriteTime = (double)totalWriteTime / writeOperationCount;
                }
                else
                {
                    failedWriteCount++;
                }
            }
        }
        
        /// <summary>
        /// 记录文件操作
        /// </summary>
        /// <param name="bytesWritten">写入字节数</param>
        /// <param name="newFile">是否创建了新文件</param>
        public void RecordFileOperation(long bytesWritten, bool newFile = false)
        {
            lock (_lockObject)
            {
                totalBytesWritten += bytesWritten;
                currentFileSize += bytesWritten;
                
                if (newFile)
                {
                    totalFilesCreated++;
                    currentFileSize = 0;
                }
            }
        }
        
        /// <summary>
        /// 记录文件轮转
        /// </summary>
        public void RecordRotation()
        {
            lock (_lockObject)
            {
                rotationCount++;
                currentFileSize = 0;
            }
        }
        
        /// <summary>
        /// 更新内存使用统计
        /// </summary>
        /// <param name="currentUsage">当前内存使用量</param>
        /// <param name="queueCount">队列大小</param>
        /// <param name="poolCount">对象池大小</param>
        public void UpdateMemoryUsage(long currentUsage, int queueCount, int poolCount)
        {
            lock (_lockObject)
            {
                currentMemoryUsage = currentUsage;
                queueSize = queueCount;
                poolSize = poolCount;
                
                if (currentUsage > peakMemoryUsage)
                {
                    peakMemoryUsage = currentUsage;
                }
            }
        }
        
        #endregion
        
        #region 统计查询方法
        
        /// <summary>
        /// 获取指定级别的日志数量
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>日志数量</returns>
        public long GetLogCount(LogLevel level)
        {
            return _levelCounters.GetValueOrDefault(level, 0);
        }
        
        /// <summary>
        /// 获取指定模块的日志数量
        /// </summary>
        /// <param name="module">模块名称</param>
        /// <returns>日志数量</returns>
        public long GetModuleLogCount(string module)
        {
            return _moduleCounters.GetValueOrDefault(module, 0);
        }
        
        /// <summary>
        /// 获取错误率（错误和致命错误的比例）
        /// </summary>
        /// <returns>错误率（0-1之间）</returns>
        public double GetErrorRate()
        {
            if (totalLogCount == 0) return 0;
            
            long errorCount = GetLogCount(LogLevel.Error) + GetLogCount(LogLevel.Fatal);
            return (double)errorCount / totalLogCount;
        }
        
        /// <summary>
        /// 获取写入成功率
        /// </summary>
        /// <returns>成功率（0-1之间）</returns>
        public double GetWriteSuccessRate()
        {
            long totalOperations = writeOperationCount + failedWriteCount;
            if (totalOperations == 0) return 1.0;
            
            return (double)writeOperationCount / totalOperations;
        }
        
        /// <summary>
        /// 获取每秒日志数量
        /// </summary>
        /// <returns>每秒日志数量</returns>
        public double GetLogsPerSecond()
        {
            var uptime = Uptime.TotalSeconds;
            if (uptime <= 0) return 0;
            
            return totalLogCount / uptime;
        }
        
        #endregion
        
        #region 重置和清理
        
        /// <summary>
        /// 重置所有统计数据
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                totalLogCount = 0;
                totalWriteTime = 0;
                writeOperationCount = 0;
                failedWriteCount = 0;
                totalFilesCreated = 0;
                totalBytesWritten = 0;
                currentFileSize = 0;
                rotationCount = 0;
                peakMemoryUsage = 0;
                currentMemoryUsage = 0;
                queueSize = 0;
                poolSize = 0;
                averageWriteTime = 0;
                
                startTime = DateTime.Now;
                lastLogTime = DateTime.MinValue;
                
                foreach (var level in Enum.GetValues(typeof(LogLevel)))
                {
                    logCountByLevel[(LogLevel)level] = 0;
                    _levelCounters[(LogLevel)level] = 0;
                }
                
                logCountByModule.Clear();
                _moduleCounters.Clear();
            }
        }
        
        #endregion
        
        /// <summary>
        /// 获取统计摘要信息
        /// </summary>
        /// <returns>统计摘要字符串</returns>
        public override string ToString()
        {
            return $"日志统计 - 总数: {totalLogCount}, 错误率: {GetErrorRate():P2}, " +
                   $"运行时间: {Uptime:hh\\:mm\\:ss}, 每秒日志: {GetLogsPerSecond():F2}";
        }
    }
}