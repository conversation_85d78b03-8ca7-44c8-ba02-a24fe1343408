using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志系统配置
    /// </summary>
    [Serializable]
    public class LogConfiguration
    {
        /// <summary>
        /// 是否启用日志功能
        /// </summary>
        public bool IsLoggingEnabled { get; set; } = true;
        
        /// <summary>
        /// 全局最小日志级别
        /// </summary>
        public LogLevel GlobalMinLevel { get; set; } = LogLevel.Info;
        
        /// <summary>
        /// 模块级别的日志级别配置
        /// </summary>
        public Dictionary<string, LogLevel> ModuleLevels { get; set; } = new Dictionary<string, LogLevel>();
        
        /// <summary>
        /// 日志写入器配置列表
        /// </summary>
        public List<LogWriterConfiguration> Writers { get; set; } = new List<LogWriterConfiguration>();
        
        /// <summary>
        /// 日志轮转配置
        /// </summary>
        public LogRotationConfiguration Rotation { get; set; } = new LogRotationConfiguration();
        
        /// <summary>
        /// 性能配置
        /// </summary>
        public PerformanceConfiguration Performance { get; set; } = new PerformanceConfiguration();
        
        /// <summary>
        /// 日志导出配置
        /// </summary>
        public LogExportConfiguration Export { get; set; } = new LogExportConfiguration();
        
        /// <summary>
        /// 获取模块的日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>日志级别</returns>
        public LogLevel GetModuleLevel(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
            {
                return GlobalMinLevel;
            }
            
            // 支持层次结构，如 Core.Audio 可以匹配 Core.Audio.* 的配置
            string currentModule = moduleName;
            while (!string.IsNullOrEmpty(currentModule))
            {
                if (ModuleLevels.ContainsKey(currentModule))
                {
                    return ModuleLevels[currentModule];
                }
                
                // 向上查找父模块
                int lastDotIndex = currentModule.LastIndexOf('.');
                if (lastDotIndex > 0)
                {
                    currentModule = currentModule.Substring(0, lastDotIndex);
                }
                else
                {
                    break;
                }
            }
            
            return GlobalMinLevel;
        }
        
        /// <summary>
        /// 设置模块日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="level">日志级别</param>
        public void SetModuleLevel(string moduleName, LogLevel level)
        {
            if (string.IsNullOrEmpty(moduleName))
            {
                GlobalMinLevel = level;
            }
            else
            {
                ModuleLevels[moduleName] = level;
            }
        }
        
        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefaults()
        {
            IsLoggingEnabled = true;
            GlobalMinLevel = LogLevel.Info;
            ModuleLevels.Clear();
            Writers.Clear();
            Rotation = new LogRotationConfiguration();
            Performance = new PerformanceConfiguration();
            Export = new LogExportConfiguration();
            
            // 添加默认的文件写入器配置
            Writers.Add(new LogWriterConfiguration
            {
                Type = "FileLogWriter",
                Name = "MainFileWriter",
                IsEnabled = true,
                MinLevel = LogLevel.Info,
                Configuration = new Dictionary<string, object>
                {
                    ["filePath"] = "Logs/app.log",
                    ["maxFileSize"] = "10MB",
                    ["maxFiles"] = 30,
                    ["formatter"] = "JsonLogFormatter",
                    ["bufferSize"] = 1000,
                    ["flushInterval"] = "5s"
                }
            });
        }
    }
    
    /// <summary>
    /// 日志写入器配置
    /// </summary>
    [Serializable]
    public class LogWriterConfiguration
    {
        /// <summary>
        /// 写入器类型
        /// </summary>
        public string Type { get; set; }
        
        /// <summary>
        /// 写入器名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        public LogLevel MinLevel { get; set; } = LogLevel.Info;
        
        /// <summary>
        /// 具体配置参数
        /// </summary>
        public Dictionary<string, object> Configuration { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetConfigValue<T>(string key, T defaultValue = default(T))
        {
            if (Configuration == null || !Configuration.ContainsKey(key))
            {
                return defaultValue;
            }
            
            try
            {
                return (T)Configuration[key];
            }
            catch
            {
                return defaultValue;
            }
        }
        
        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetConfigValue(string key, object value)
        {
            if (Configuration == null)
            {
                Configuration = new Dictionary<string, object>();
            }
            Configuration[key] = value;
        }
    }
    
    /// <summary>
    /// 日志轮转配置
    /// </summary>
    [Serializable]
    public class LogRotationConfiguration
    {
        /// <summary>
        /// 轮转策略
        /// </summary>
        public string Strategy { get; set; } = "SizeAndCount";
        
        /// <summary>
        /// 最大文件大小
        /// </summary>
        public string MaxFileSize { get; set; } = "10MB";
        
        /// <summary>
        /// 最大文件数量
        /// </summary>
        public int MaxFileCount { get; set; } = 30;
        
        /// <summary>
        /// 是否归档旧文件
        /// </summary>
        public bool ArchiveOldFiles { get; set; } = true;
        
        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool CompressionEnabled { get; set; } = true;
        
        /// <summary>
        /// 获取最大文件大小（字节）
        /// </summary>
        /// <returns>字节数</returns>
        public long GetMaxFileSizeBytes()
        {
            return ParseSizeString(MaxFileSize);
        }
        
        /// <summary>
        /// 解析大小字符串为字节数
        /// </summary>
        /// <param name="sizeString">大小字符串，如 "10MB"</param>
        /// <returns>字节数</returns>
        private long ParseSizeString(string sizeString)
        {
            if (string.IsNullOrEmpty(sizeString))
            {
                return 10 * 1024 * 1024; // 默认10MB
            }
            
            sizeString = sizeString.ToUpper().Trim();
            
            long multiplier = 1;
            if (sizeString.EndsWith("KB"))
            {
                multiplier = 1024;
                sizeString = sizeString.Substring(0, sizeString.Length - 2);
            }
            else if (sizeString.EndsWith("MB"))
            {
                multiplier = 1024 * 1024;
                sizeString = sizeString.Substring(0, sizeString.Length - 2);
            }
            else if (sizeString.EndsWith("GB"))
            {
                multiplier = 1024 * 1024 * 1024;
                sizeString = sizeString.Substring(0, sizeString.Length - 2);
            }
            
            if (long.TryParse(sizeString.Trim(), out long size))
            {
                return size * multiplier;
            }
            
            return 10 * 1024 * 1024; // 默认10MB
        }
    }
    
    /// <summary>
    /// 性能配置
    /// </summary>
    [Serializable]
    public class PerformanceConfiguration
    {
        /// <summary>
        /// 是否启用异步写入
        /// </summary>
        public bool AsyncWriting { get; set; } = true;
        
        /// <summary>
        /// 缓冲区大小
        /// </summary>
        public int BufferSize { get; set; } = 1000;
        
        /// <summary>
        /// 刷新间隔
        /// </summary>
        public string FlushInterval { get; set; } = "5s";
        
        /// <summary>
        /// 最大内存使用量
        /// </summary>
        public string MaxMemoryUsage { get; set; } = "50MB";
        
        /// <summary>
        /// 获取刷新间隔（毫秒）
        /// </summary>
        /// <returns>毫秒数</returns>
        public int GetFlushIntervalMs()
        {
            return ParseTimeString(FlushInterval);
        }
        
        /// <summary>
        /// 获取最大内存使用量（字节）
        /// </summary>
        /// <returns>字节数</returns>
        public long GetMaxMemoryUsageBytes()
        {
            return ParseSizeString(MaxMemoryUsage);
        }
        
        /// <summary>
        /// 解析时间字符串为毫秒数
        /// </summary>
        /// <param name="timeString">时间字符串，如 "5s"</param>
        /// <returns>毫秒数</returns>
        private int ParseTimeString(string timeString)
        {
            if (string.IsNullOrEmpty(timeString))
            {
                return 5000; // 默认5秒
            }
            
            timeString = timeString.ToLower().Trim();
            
            int multiplier = 1000; // 默认秒
            if (timeString.EndsWith("ms"))
            {
                multiplier = 1;
                timeString = timeString.Substring(0, timeString.Length - 2);
            }
            else if (timeString.EndsWith("s"))
            {
                multiplier = 1000;
                timeString = timeString.Substring(0, timeString.Length - 1);
            }
            else if (timeString.EndsWith("m"))
            {
                multiplier = 60 * 1000;
                timeString = timeString.Substring(0, timeString.Length - 1);
            }
            
            if (int.TryParse(timeString.Trim(), out int time))
            {
                return time * multiplier;
            }
            
            return 5000; // 默认5秒
        }
        
        /// <summary>
        /// 解析大小字符串为字节数
        /// </summary>
        /// <param name="sizeString">大小字符串</param>
        /// <returns>字节数</returns>
        private long ParseSizeString(string sizeString)
        {
            if (string.IsNullOrEmpty(sizeString))
            {
                return 50 * 1024 * 1024; // 默认50MB
            }
            
            sizeString = sizeString.ToUpper().Trim();
            
            long multiplier = 1;
            if (sizeString.EndsWith("KB"))
            {
                multiplier = 1024;
                sizeString = sizeString.Substring(0, sizeString.Length - 2);
            }
            else if (sizeString.EndsWith("MB"))
            {
                multiplier = 1024 * 1024;
                sizeString = sizeString.Substring(0, sizeString.Length - 2);
            }
            else if (sizeString.EndsWith("GB"))
            {
                multiplier = 1024 * 1024 * 1024;
                sizeString = sizeString.Substring(0, sizeString.Length - 2);
            }
            
            if (long.TryParse(sizeString.Trim(), out long size))
            {
                return size * multiplier;
            }
            
            return 50 * 1024 * 1024; // 默认50MB
        }
    }
    
    /// <summary>
    /// 日志导出配置
    /// </summary>
    [Serializable]
    public class LogExportConfiguration
    {
        /// <summary>
        /// 导出目录
        /// </summary>
        public string ExportDirectory { get; set; } = "LogExports";
        
        /// <summary>
        /// 是否包含系统信息
        /// </summary>
        public bool IncludeSystemInfo { get; set; } = true;
        
        /// <summary>
        /// 是否压缩导出文件
        /// </summary>
        public bool CompressExports { get; set; } = true;
        
        /// <summary>
        /// 最大导出文件大小（MB）
        /// </summary>
        public int MaxExportSizeMB { get; set; } = 100;
        
        /// <summary>
        /// 导出格式
        /// </summary>
        public string ExportFormat { get; set; } = "json";
    }
    
    /// <summary>
    /// 日志统计信息
    /// </summary>
    [Serializable]
    public class LogStatistics
    {
        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 总日志数量
        /// </summary>
        public long TotalLogCount { get; set; }
        
        /// <summary>
        /// 按级别统计的日志数量
        /// </summary>
        public Dictionary<LogLevel, long> LogCountByLevel { get; set; } = new Dictionary<LogLevel, long>();
        
        /// <summary>
        /// 按模块统计的日志数量
        /// </summary>
        public Dictionary<string, long> LogCountByModule { get; set; } = new Dictionary<string, long>();
        
        /// <summary>
        /// 总文件大小（字节）
        /// </summary>
        public long TotalFileSizeBytes { get; set; }
        
        /// <summary>
        /// 活跃日志文件数量
        /// </summary>
        public int ActiveLogFiles { get; set; }
        
        /// <summary>
        /// 最后日志时间
        /// </summary>
        public DateTime LastLogTime { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LogStatistics()
        {
            StartTime = DateTime.Now;
            LogCountByLevel = new Dictionary<LogLevel, long>();
            LogCountByModule = new Dictionary<string, long>();
        }
        
        /// <summary>
        /// 增加日志计数
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="module">模块名称</param>
        public void IncrementLogCount(LogLevel level, string module)
        {
            TotalLogCount++;
            LastLogTime = DateTime.Now;
            
            // 按级别统计
            if (LogCountByLevel.ContainsKey(level))
            {
                LogCountByLevel[level]++;
            }
            else
            {
                LogCountByLevel[level] = 1;
            }
            
            // 按模块统计
            if (!string.IsNullOrEmpty(module))
            {
                if (LogCountByModule.ContainsKey(module))
                {
                    LogCountByModule[module]++;
                }
                else
                {
                    LogCountByModule[module] = 1;
                }
            }
        }
    }
}