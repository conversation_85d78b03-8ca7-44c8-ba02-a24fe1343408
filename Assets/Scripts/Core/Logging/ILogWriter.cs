using System;
using System.Threading.Tasks;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志写入器接口
    /// </summary>
    public interface ILogWriter : IDisposable
    {
        /// <summary>
        /// 写入器名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        LogLevel MinLevel { get; set; }
        
        /// <summary>
        /// 异步写入日志条目
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>写入任务</returns>
        Task WriteAsync(LogEntry entry);
        
        /// <summary>
        /// 刷新缓冲区
        /// </summary>
        void Flush();
        
        /// <summary>
        /// 配置写入器
        /// </summary>
        /// <param name="config">配置对象</param>
        void Configure(LogWriterConfiguration config);
    }
}