using System;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志事件基类
    /// </summary>
    public class LogEvent : EventBase
    {
        /// <summary>
        /// 日志条目
        /// </summary>
        public LogEntry Entry { get; set; }
        
        /// <summary>
        /// 写入器名称
        /// </summary>
        public string WriterName { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <param name="writerName">写入器名称</param>
        public LogEvent(LogEntry entry, string writerName = null)
        {
            Entry = entry;
            WriterName = writerName;
        }
    }
    
    /// <summary>
    /// 日志错误事件
    /// </summary>
    public class LogErrorEvent : EventBase
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 错误上下文
        /// </summary>
        public string Context { get; set; }
        
        /// <summary>
        /// 写入器名称
        /// </summary>
        public string WriterName { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="exception">异常信息</param>
        /// <param name="context">错误上下文</param>
        /// <param name="writerName">写入器名称</param>
        public LogErrorEvent(Exception exception, string context, string writerName = null)
        {
            Exception = exception;
            Context = context;
            WriterName = writerName;
        }
    }
    
    /// <summary>
    /// 日志导出完成事件
    /// </summary>
    public class LogExportCompletedEvent : EventBase
    {
        /// <summary>
        /// 导出文件路径
        /// </summary>
        public string ExportPath { get; set; }
        
        /// <summary>
        /// 导出请求参数
        /// </summary>
        public LogExportRequest Request { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="exportPath">导出文件路径</param>
        /// <param name="request">导出请求</param>
        /// <param name="success">是否成功</param>
        /// <param name="errorMessage">错误消息</param>
        public LogExportCompletedEvent(string exportPath, LogExportRequest request, bool success, string errorMessage = null)
        {
            ExportPath = exportPath;
            Request = request;
            Success = success;
            ErrorMessage = errorMessage;
        }
    }
    
    /// <summary>
    /// 日志状态变化事件
    /// </summary>
    public class LoggingStateChangedEvent : EventBase
    {
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// 变化原因
        /// </summary>
        public string Reason { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isEnabled">是否启用</param>
        /// <param name="reason">变化原因</param>
        public LoggingStateChangedEvent(bool isEnabled, string reason)
        {
            IsEnabled = isEnabled;
            Reason = reason;
        }
    }
    
    /// <summary>
    /// 日志轮转事件
    /// </summary>
    public class LogRotationEvent : EventBase
    {
        /// <summary>
        /// 旧文件路径
        /// </summary>
        public string OldFilePath { get; set; }
        
        /// <summary>
        /// 新文件路径
        /// </summary>
        public string NewFilePath { get; set; }
        
        /// <summary>
        /// 轮转原因
        /// </summary>
        public string Reason { get; set; }
        
        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldFilePath">旧文件路径</param>
        /// <param name="newFilePath">新文件路径</param>
        /// <param name="reason">轮转原因</param>
        /// <param name="fileSize">文件大小</param>
        public LogRotationEvent(string oldFilePath, string newFilePath, string reason, long fileSize)
        {
            OldFilePath = oldFilePath;
            NewFilePath = newFilePath;
            Reason = reason;
            FileSize = fileSize;
        }
    }
    
    /// <summary>
    /// 日志导出请求
    /// </summary>
    [Serializable]
    public class LogExportRequest
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        public LogLevel? MinLevel { get; set; }
        
        /// <summary>
        /// 模块列表
        /// </summary>
        public string[] Modules { get; set; }
        
        /// <summary>
        /// 导出格式
        /// </summary>
        public string Format { get; set; } = "json";
        
        /// <summary>
        /// 是否包含系统信息
        /// </summary>
        public bool IncludeSystemInfo { get; set; } = true;
        
        /// <summary>
        /// 是否压缩
        /// </summary>
        public bool Compress { get; set; } = true;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LogExportRequest()
        {
            Modules = new string[0];
        }
    }
}