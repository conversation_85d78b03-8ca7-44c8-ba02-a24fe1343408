using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 简化的日志统计类，用于MVP版本
    /// </summary>
    public class SimpleLogStatistics
    {
        private long _totalLogs = 0;
        private long _errorCount = 0;
        private long _warningCount = 0;
        private long _infoCount = 0;
        private long _debugCount = 0;
        private long _traceCount = 0;
        private DateTime _startTime = DateTime.Now;
        
        /// <summary>
        /// 总日志数量
        /// </summary>
        public long TotalLogs => _totalLogs;

        /// <summary>
        /// 总日志数量（别名）
        /// </summary>
        public long TotalLogCount => _totalLogs;
        
        /// <summary>
        /// 错误数量
        /// </summary>
        public long ErrorCount => _errorCount;
        
        /// <summary>
        /// 警告数量
        /// </summary>
        public long WarningCount => _warningCount;
        
        /// <summary>
        /// 信息数量
        /// </summary>
        public long InfoCount => _infoCount;
        
        /// <summary>
        /// 调试数量
        /// </summary>
        public long DebugCount => _debugCount;
        
        /// <summary>
        /// 跟踪数量
        /// </summary>
        public long TraceCount => _traceCount;
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime => _startTime;
        
        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan Uptime => DateTime.Now - _startTime;
        
        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="level">日志级别</param>
        public void RecordLog(LogLevel level)
        {
            _totalLogs++;
            LastLogTime = DateTime.Now;

            switch (level)
            {
                case LogLevel.Trace:
                    _traceCount++;
                    break;
                case LogLevel.Debug:
                    _debugCount++;
                    break;
                case LogLevel.Info:
                    _infoCount++;
                    break;
                case LogLevel.Warn:
                    _warningCount++;
                    break;
                case LogLevel.Error:
                    _errorCount++;
                    break;
                case LogLevel.Fatal:
                    _errorCount++; // 致命错误也算作错误
                    break;
            }
        }
        
        /// <summary>
        /// 重置统计
        /// </summary>
        public void Reset()
        {
            _totalLogs = 0;
            _errorCount = 0;
            _warningCount = 0;
            _infoCount = 0;
            _debugCount = 0;
            _traceCount = 0;
            _startTime = DateTime.Now;
        }
        
        /// <summary>
        /// 获取错误率
        /// </summary>
        /// <returns>错误率（0-1之间）</returns>
        public double GetErrorRate()
        {
            if (_totalLogs == 0) return 0;
            return (double)_errorCount / _totalLogs;
        }
        
        /// <summary>
        /// 获取每秒日志数
        /// </summary>
        /// <returns>每秒日志数</returns>
        public double GetLogsPerSecond()
        {
            var uptime = Uptime.TotalSeconds;
            if (uptime <= 0) return 0;
            return _totalLogs / uptime;
        }
        
        /// <summary>
        /// 获取指定级别的日志数量
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>日志数量</returns>
        public long GetLogCount(LogLevel level)
        {
            switch (level)
            {
                case LogLevel.Trace:
                    return _traceCount;
                case LogLevel.Debug:
                    return _debugCount;
                case LogLevel.Info:
                    return _infoCount;
                case LogLevel.Warn:
                    return _warningCount;
                case LogLevel.Error:
                case LogLevel.Fatal:
                    return _errorCount;
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 获取模块日志数量（简化版本，返回总数）
        /// </summary>
        /// <param name="module">模块名</param>
        /// <returns>日志数量</returns>
        public long GetModuleLogCount(string module)
        {
            // 简化版本，返回总数
            return _totalLogs;
        }

        /// <summary>
        /// 按级别统计的日志数量字典
        /// </summary>
        public Dictionary<LogLevel, long> LogCountByLevel
        {
            get
            {
                return new Dictionary<LogLevel, long>
                {
                    { LogLevel.Trace, _traceCount },
                    { LogLevel.Debug, _debugCount },
                    { LogLevel.Info, _infoCount },
                    { LogLevel.Warn, _warningCount },
                    { LogLevel.Error, _errorCount }
                };
            }
        }

        /// <summary>
        /// 按模块统计的日志数量字典（简化版本）
        /// </summary>
        public Dictionary<string, long> LogCountByModule
        {
            get
            {
                return new Dictionary<string, long>
                {
                    { "Total", _totalLogs }
                };
            }
        }

        /// <summary>
        /// 最后日志时间
        /// </summary>
        public DateTime LastLogTime { get; private set; } = DateTime.Now;

        /// <summary>
        /// 记录写入操作（简化版本）
        /// </summary>
        /// <param name="duration">持续时间</param>
        /// <param name="success">是否成功</param>
        public void RecordWriteOperation(TimeSpan duration, bool success)
        {
            // 简化版本，不做具体统计
        }

        /// <summary>
        /// 更新内存使用量（简化版本）
        /// </summary>
        /// <param name="memoryUsage">内存使用量</param>
        public void UpdateMemoryUsage(long memoryUsage)
        {
            // 简化版本，不做具体统计
        }

        /// <summary>
        /// 获取统计摘要
        /// </summary>
        /// <returns>统计摘要字符串</returns>
        public override string ToString()
        {
            return $"总计: {_totalLogs}, 错误: {_errorCount}, 警告: {_warningCount}, " +
                   $"信息: {_infoCount}, 调试: {_debugCount}, 跟踪: {_traceCount}, " +
                   $"运行时间: {Uptime:hh\\:mm\\:ss}";
        }
    }
}
