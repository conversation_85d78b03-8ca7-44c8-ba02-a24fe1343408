using System;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 模块化日志记录器实现
    /// </summary>
    public class Logger : ILogger
    {
        private readonly ILogManager _logManager;
        private LogLevel _minLevel;
        
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; private set; }
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        public LogLevel MinLevel
        {
            get => _minLevel;
            set => _minLevel = value;
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="logManager">日志管理器</param>
        public Logger(string moduleName, ILogManager logManager)
        {
            ModuleName = moduleName ?? throw new ArgumentNullException(nameof(moduleName));
            _logManager = logManager ?? throw new ArgumentNullException(nameof(logManager));
            
            // 从配置中获取初始的最小级别
            _minLevel = LogLevel.Info; // 默认值，实际值将从配置中获取
        }
        
        /// <summary>
        /// 记录跟踪级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Trace(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Trace))
            {
                _logManager.Log(LogLevel.Trace, message, ModuleName, exception);
            }
        }
        
        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Debug(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Debug))
            {
                _logManager.Log(LogLevel.Debug, message, ModuleName, exception);
            }
        }
        
        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Info(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Info))
            {
                _logManager.Log(LogLevel.Info, message, ModuleName, exception);
            }
        }
        
        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Warn(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Warn))
            {
                _logManager.Log(LogLevel.Warn, message, ModuleName, exception);
            }
        }

        /// <summary>
        /// 记录警告级别日志（Warning方法的别名）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Warning(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Warn))
            {
                _logManager.Log(LogLevel.Warn, message, ModuleName, exception);
            }
        }
        
        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Error(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Error))
            {
                _logManager.Log(LogLevel.Error, message, ModuleName, exception);
            }
        }
        
        /// <summary>
        /// 记录致命错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Fatal(string message, Exception exception = null)
        {
            if (IsEnabled(LogLevel.Fatal))
            {
                _logManager.Log(LogLevel.Fatal, message, ModuleName, exception);
            }
        }
        
        /// <summary>
        /// 检查指定级别的日志是否启用
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>是否启用</returns>
        public bool IsEnabled(LogLevel level)
        {
            if (!_logManager.IsLoggingEnabled)
            {
                return false;
            }
            
            return level.IsEnabled(_minLevel);
        }
        
        /// <summary>
        /// 更新最小日志级别（从配置中获取）
        /// </summary>
        /// <param name="level">新的最小级别</param>
        internal void UpdateMinLevel(LogLevel level)
        {
            _minLevel = level;
        }
        
        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Logger[{ModuleName}] MinLevel={_minLevel}";
        }
    }
}