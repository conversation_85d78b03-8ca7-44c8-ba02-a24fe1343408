namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 跟踪级别 - 最详细的日志信息
        /// </summary>
        Trace = 0,
        
        /// <summary>
        /// 调试级别 - 调试信息
        /// </summary>
        Debug = 1,
        
        /// <summary>
        /// 信息级别 - 一般信息
        /// </summary>
        Info = 2,
        
        /// <summary>
        /// 警告级别 - 警告信息
        /// </summary>
        Warn = 3,

        /// <summary>
        /// 警告级别 - 警告信息（Warning别名）
        /// </summary>
        Warning = 3,
        
        /// <summary>
        /// 错误级别 - 错误信息
        /// </summary>
        Error = 4,
        
        /// <summary>
        /// 致命错误级别 - 致命错误信息
        /// </summary>
        Fatal = 5
    }
    
    /// <summary>
    /// 日志级别扩展方法
    /// </summary>
    public static class LogLevelExtensions
    {
        /// <summary>
        /// 获取日志级别的字符串表示
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>字符串表示</returns>
        public static string ToShortString(this LogLevel level)
        {
            return level switch
            {
                LogLevel.Trace => "TRACE",
                LogLevel.Debug => "DEBUG",
                LogLevel.Info => "INFO",
                LogLevel.Warn => "WARN",
                LogLevel.Error => "ERROR",
                LogLevel.Fatal => "FATAL",
                _ => "UNKNOWN"
            };
        }
        
        /// <summary>
        /// 从字符串解析日志级别
        /// </summary>
        /// <param name="levelString">级别字符串</param>
        /// <param name="defaultLevel">默认级别</param>
        /// <returns>日志级别</returns>
        public static LogLevel ParseLogLevel(string levelString, LogLevel defaultLevel = LogLevel.Info)
        {
            if (string.IsNullOrEmpty(levelString))
            {
                return defaultLevel;
            }
            
            return levelString.ToUpper() switch
            {
                "TRACE" => LogLevel.Trace,
                "DEBUG" => LogLevel.Debug,
                "INFO" => LogLevel.Info,
                "WARN" or "WARNING" => LogLevel.Warn,
                "ERROR" => LogLevel.Error,
                "FATAL" => LogLevel.Fatal,
                _ => defaultLevel
            };
        }
        
        /// <summary>
        /// 检查级别是否启用
        /// </summary>
        /// <param name="currentLevel">当前级别</param>
        /// <param name="minLevel">最小级别</param>
        /// <returns>是否启用</returns>
        public static bool IsEnabled(this LogLevel currentLevel, LogLevel minLevel)
        {
            return currentLevel >= minLevel;
        }
    }
}