using System;

namespace DigitalHuman.Core.Logging
{
    /// <summary>
    /// 模块化日志记录器接口
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        string ModuleName { get; }
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        LogLevel MinLevel { get; set; }
        
        /// <summary>
        /// 记录跟踪级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Trace(string message, Exception exception = null);
        
        /// <summary>
        /// 记录调试级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Debug(string message, Exception exception = null);
        
        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Info(string message, Exception exception = null);
        
        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Warn(string message, Exception exception = null);

        /// <summary>
        /// 记录警告级别日志（Warning方法的别名）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Warning(string message, Exception exception = null);
        
        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Error(string message, Exception exception = null);
        
        /// <summary>
        /// 记录致命错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        void Fatal(string message, Exception exception = null);
        
        /// <summary>
        /// 检查指定级别的日志是否启用
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>是否启用</returns>
        bool IsEnabled(LogLevel level);
    }
}