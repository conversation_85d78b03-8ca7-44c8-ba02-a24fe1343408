using System;
using UnityEngine;

namespace DigitalHuman.Core
{
    /// <summary>
    /// 管理器基类，提供通用的管理器功能实现
    /// </summary>
    public abstract class ManagerBase : MonoBehaviour, IManager
    {
        [SerializeField] private bool _isInitialized = false;
        
        /// <summary>
        /// 管理器是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        
        /// <summary>
        /// 管理器初始化完成事件
        /// </summary>
        public event Action OnInitialized;
        
        /// <summary>
        /// 管理器清理完成事件
        /// </summary>
        public event Action OnCleanup;
        
        /// <summary>
        /// Unity Awake 生命周期
        /// </summary>
        protected virtual void Awake()
        {
            if (!_isInitialized)
            {
                Initialize();
            }
        }
        
        /// <summary>
        /// Unity OnDestroy 生命周期
        /// </summary>
        protected virtual void OnDestroy()
        {
            if (_isInitialized)
            {
                Cleanup();
            }
        }
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        public virtual void Initialize()
        {
            if (_isInitialized)
            {
                Debug.LogWarning($"{GetType().Name} 已经初始化，跳过重复初始化");
                return;
            }
            
            try
            {
                OnInitializeInternal();
                _isInitialized = true;
                OnInitialized?.Invoke();
                Debug.Log($"{GetType().Name} 初始化完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"{GetType().Name} 初始化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 清理管理器资源
        /// </summary>
        public virtual void Cleanup()
        {
            if (!_isInitialized)
            {
                return;
            }
            
            try
            {
                OnCleanupInternal();
                _isInitialized = false;
                OnCleanup?.Invoke();
                Debug.Log($"{GetType().Name} 清理完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"{GetType().Name} 清理失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 子类实现具体的初始化逻辑
        /// </summary>
        protected abstract void OnInitializeInternal();
        
        /// <summary>
        /// 子类实现具体的清理逻辑
        /// </summary>
        protected abstract void OnCleanupInternal();
    }
}