using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DigitalHuman.MVP;
using DigitalHuman.Core.Authentication;
using DigitalHuman.Core.DataSync;
using DigitalHuman.Core.Logging;
using System.Text;
using System;
using AuthLogger = DigitalHuman.Core.Logging.ILogger;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 测试仪表板 - 提供可视化界面测试所有功能
    /// </summary>
    public class TestDashboard : MonoBehaviour
    {
        [Header("UI面板")]
        public GameObject mainPanel;
        public GameObject authPanel;
        public GameObject syncPanel;
        public GameObject logPanel;
        
        [Header("状态显示")]
        public TextMeshProUGUI systemStatusText;
        public TextMeshProUGUI authStatusText;
        public TextMeshProUGUI syncStatusText;
        public TextMeshProUGUI logStatusText;
        
        [Header("认证测试")]
        public TMP_InputField usernameInput;
        public TMP_InputField passwordInput;
        public Button loginButton;
        public Button logoutButton;
        public Button validateSessionButton;
        public Button refreshTokenButton;
        
        [Header("数据同步测试")]
        public Button startSyncButton;
        public Button stopSyncButton;
        public Button clearCacheButton;
        public Slider syncProgressSlider;
        public TextMeshProUGUI syncProgressText;
        
        [Header("日志测试")]
        public Button logInfoButton;
        public Button logWarningButton;
        public Button logErrorButton;
        public Button clearLogsButton;
        public TMP_InputField logMessageInput;
        public ScrollRect logScrollView;
        public TextMeshProUGUI logDisplayText;
        
        [Header("系统控制")]
        public Button restartSystemButton;
        public Button showSystemInfoButton;
        public Toggle debugModeToggle;
        
        // 私有字段
        private EnhancedMVPManager mvpManager;
        private IAuthenticationManager authManager;
        private IDataSyncManager syncManager;
        private AuthLogger logger;
        private StringBuilder logBuffer;
        
        void Start()
        {
            InitializeDashboard();
            SetupEventHandlers();
            StartStatusUpdates();
        }
        
        /// <summary>
        /// 初始化仪表板
        /// </summary>
        private void InitializeDashboard()
        {
            // 获取管理器实例
            mvpManager = FindObjectOfType<EnhancedMVPManager>();
            if (mvpManager == null)
            {
                mvpManager = FindObjectOfType<MinimalMVPManager>() as EnhancedMVPManager;
            }
            
            try
            {
                authManager = AuthenticationManager.Instance as IAuthenticationManager;
                syncManager = DataSyncManager.Instance as IDataSyncManager;
                
                var logManager = LogManager.Instance;
                logger = logManager?.GetLogger("TestDashboard");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[TestDashboard] 初始化失败: {ex.Message}");
            }
            
            // 初始化日志缓冲区
            logBuffer = new StringBuilder();
            
            // 设置默认值
            if (usernameInput != null) usernameInput.text = "demo_user";
            if (passwordInput != null) passwordInput.text = "demo_pass";
            if (logMessageInput != null) logMessageInput.text = "测试日志消息";
            
            logger?.Info("测试仪表板初始化完成");
            AddLogMessage("🎯 测试仪表板已启动");
        }
        
        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 认证按钮事件
            if (loginButton != null) loginButton.onClick.AddListener(OnLoginClicked);
            if (logoutButton != null) logoutButton.onClick.AddListener(OnLogoutClicked);
            if (validateSessionButton != null) validateSessionButton.onClick.AddListener(OnValidateSessionClicked);
            if (refreshTokenButton != null) refreshTokenButton.onClick.AddListener(OnRefreshTokenClicked);
            
            // 数据同步按钮事件
            if (startSyncButton != null) startSyncButton.onClick.AddListener(OnStartSyncClicked);
            if (stopSyncButton != null) stopSyncButton.onClick.AddListener(OnStopSyncClicked);
            if (clearCacheButton != null) clearCacheButton.onClick.AddListener(OnClearCacheClicked);
            
            // 日志按钮事件
            if (logInfoButton != null) logInfoButton.onClick.AddListener(() => OnLogTestClicked("Info"));
            if (logWarningButton != null) logWarningButton.onClick.AddListener(() => OnLogTestClicked("Warning"));
            if (logErrorButton != null) logErrorButton.onClick.AddListener(() => OnLogTestClicked("Error"));
            if (clearLogsButton != null) clearLogsButton.onClick.AddListener(OnClearLogsClicked);
            
            // 系统控制按钮事件
            if (restartSystemButton != null) restartSystemButton.onClick.AddListener(OnRestartSystemClicked);
            if (showSystemInfoButton != null) showSystemInfoButton.onClick.AddListener(OnShowSystemInfoClicked);
            if (debugModeToggle != null) debugModeToggle.onValueChanged.AddListener(OnDebugModeToggled);
            
            // 订阅认证事件
            if (authManager != null)
            {
                authManager.OnUserLoggedIn += OnUserLoggedIn;
                authManager.OnUserLoggedOut += OnUserLoggedOut;
            }
            
            // 订阅数据同步事件
            if (syncManager != null)
            {
                syncManager.OnSyncStatusChanged += OnSyncStatusChanged;
                syncManager.OnSyncProgressUpdated += OnSyncProgressUpdated;
            }
        }
        
        /// <summary>
        /// 开始状态更新
        /// </summary>
        private void StartStatusUpdates()
        {
            InvokeRepeating(nameof(UpdateAllStatus), 0f, 2f);
        }
        
        /// <summary>
        /// 更新所有状态显示
        /// </summary>
        private void UpdateAllStatus()
        {
            UpdateSystemStatus();
            UpdateAuthStatus();
            UpdateSyncStatus();
            UpdateLogStatus();
            UpdateButtonStates();
        }
        
        /// <summary>
        /// 更新系统状态
        /// </summary>
        private void UpdateSystemStatus()
        {
            if (systemStatusText == null) return;
            
            var status = new StringBuilder();
            status.AppendLine("=== 系统状态 ===");
            status.AppendLine($"时间: {DateTime.Now:HH:mm:ss}");
            
            if (mvpManager != null)
            {
                status.AppendLine($"MVP管理器: ✅ 运行中");
                status.AppendLine($"版本: {mvpManager.AppVersion}");
                status.AppendLine($"调试模式: {(mvpManager.EnableDebugMode ? "开启" : "关闭")}");
                status.AppendLine($"帧率: {mvpManager.TargetFrameRate}");
            }
            else
            {
                status.AppendLine("MVP管理器: ❌ 未找到");
            }
            
            systemStatusText.text = status.ToString();
        }
        
        /// <summary>
        /// 更新认证状态
        /// </summary>
        private void UpdateAuthStatus()
        {
            if (authStatusText == null) return;
            
            var status = new StringBuilder();
            status.AppendLine("=== 认证状态 ===");
            
            if (authManager != null)
            {
                status.AppendLine($"认证管理器: ✅ 可用");
                status.AppendLine($"登录状态: {(authManager.IsLoggedIn ? "✅ 已登录" : "❌ 未登录")}");
                
                if (authManager.IsLoggedIn && authManager.CurrentUser != null)
                {
                    status.AppendLine($"用户: {authManager.CurrentUser.DisplayName}");
                    status.AppendLine($"用户名: {authManager.CurrentUser.Username}");
                    status.AppendLine($"邮箱: {authManager.CurrentUser.Email}");
                }
            }
            else
            {
                status.AppendLine("认证管理器: ❌ 不可用");
            }
            
            authStatusText.text = status.ToString();
        }
        
        /// <summary>
        /// 更新同步状态
        /// </summary>
        private void UpdateSyncStatus()
        {
            if (syncStatusText == null) return;
            
            var status = new StringBuilder();
            status.AppendLine("=== 数据同步状态 ===");
            
            if (syncManager != null)
            {
                status.AppendLine($"同步管理器: ✅ 可用");
                status.AppendLine($"同步状态: {syncManager.CurrentStatus}");
                status.AppendLine($"正在同步: {(syncManager.IsSyncing ? "是" : "否")}");
                
                if (syncManager.LastSyncTime.HasValue)
                {
                    status.AppendLine($"最后同步: {syncManager.LastSyncTime.Value:HH:mm:ss}");
                }
                else
                {
                    status.AppendLine("最后同步: 从未同步");
                }
            }
            else
            {
                status.AppendLine("同步管理器: ❌ 不可用");
            }
            
            syncStatusText.text = status.ToString();
        }
        
        /// <summary>
        /// 更新日志状态
        /// </summary>
        private void UpdateLogStatus()
        {
            if (logStatusText == null) return;
            
            var status = new StringBuilder();
            status.AppendLine("=== 日志状态 ===");
            
            try
            {
                var logManager = LogManager.Instance;
                if (logManager != null)
                {
                    status.AppendLine($"日志管理器: ✅ 可用");
                    status.AppendLine($"日志级别: Info");
                    status.AppendLine($"日志缓冲区: {logBuffer.Length} 字符");
                }
                else
                {
                    status.AppendLine("日志管理器: ❌ 不可用");
                }
            }
            catch (Exception ex)
            {
                status.AppendLine($"日志管理器: ❌ 错误 - {ex.Message}");
            }
            
            logStatusText.text = status.ToString();
        }
        
        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            bool isLoggedIn = authManager?.IsLoggedIn ?? false;
            bool isSyncing = syncManager?.IsSyncing ?? false;
            
            // 认证按钮状态
            if (loginButton != null) loginButton.interactable = !isLoggedIn && authManager != null;
            if (logoutButton != null) logoutButton.interactable = isLoggedIn && authManager != null;
            if (validateSessionButton != null) validateSessionButton.interactable = authManager != null;
            if (refreshTokenButton != null) refreshTokenButton.interactable = isLoggedIn && authManager != null;
            
            // 同步按钮状态
            if (startSyncButton != null) startSyncButton.interactable = !isSyncing && syncManager != null;
            if (stopSyncButton != null) stopSyncButton.interactable = isSyncing && syncManager != null;
            if (clearCacheButton != null) clearCacheButton.interactable = !isSyncing && syncManager != null;
            
            // 调试模式切换
            if (debugModeToggle != null && mvpManager != null)
            {
                debugModeToggle.isOn = mvpManager.EnableDebugMode;
            }
        }

        #region 认证测试事件处理

        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private async void OnLoginClicked()
        {
            if (authManager == null) return;

            string username = usernameInput?.text ?? "demo_user";
            string password = passwordInput?.text ?? "demo_pass";

            AddLogMessage($"🔐 尝试登录用户: {username}");

            try
            {
                var result = await authManager.LoginAsync(username, password);

                if (result.IsSuccess)
                {
                    AddLogMessage($"✅ 登录成功: {result.UserInfo.DisplayName}");
                    logger?.Info($"用户登录成功: {username}");
                }
                else
                {
                    AddLogMessage($"❌ 登录失败: {result.ErrorMessage}");
                    logger?.Warning($"用户登录失败: {username} - {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 登录异常: {ex.Message}");
                logger?.Error($"登录异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 登出按钮点击事件
        /// </summary>
        private async void OnLogoutClicked()
        {
            if (authManager == null) return;

            AddLogMessage("🚪 执行登出操作");

            try
            {
                var result = await authManager.LogoutAsync();

                if (result)
                {
                    AddLogMessage("✅ 登出成功");
                    logger?.Info("用户登出成功");
                }
                else
                {
                    AddLogMessage("❌ 登出失败");
                    logger?.Warning("用户登出失败");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 登出异常: {ex.Message}");
                logger?.Error($"登出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证会话按钮点击事件
        /// </summary>
        private async void OnValidateSessionClicked()
        {
            if (authManager == null) return;

            AddLogMessage("🔍 验证会话状态");

            try
            {
                var result = await authManager.ValidateSessionAsync();

                if (result)
                {
                    AddLogMessage("✅ 会话验证成功");
                    logger?.Info("会话验证成功");
                }
                else
                {
                    AddLogMessage("❌ 会话验证失败");
                    logger?.Warning("会话验证失败");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 会话验证异常: {ex.Message}");
                logger?.Error($"会话验证异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新令牌按钮点击事件
        /// </summary>
        private async void OnRefreshTokenClicked()
        {
            if (authManager == null) return;

            AddLogMessage("🔄 刷新认证令牌");

            try
            {
                var result = await authManager.RefreshTokenAsync();

                if (result)
                {
                    AddLogMessage("✅ 令牌刷新成功");
                    logger?.Info("令牌刷新成功");
                }
                else
                {
                    AddLogMessage("❌ 令牌刷新失败");
                    logger?.Warning("令牌刷新失败");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 令牌刷新异常: {ex.Message}");
                logger?.Error($"令牌刷新异常: {ex.Message}");
            }
        }

        #endregion

        #region 数据同步测试事件处理

        /// <summary>
        /// 开始同步按钮点击事件
        /// </summary>
        private async void OnStartSyncClicked()
        {
            if (syncManager == null) return;

            AddLogMessage("🔄 开始数据同步");

            try
            {
                var result = await syncManager.StartSyncAsync(DigitalHuman.Core.DataSync.Models.SyncType.Full);

                if (result.IsSuccess)
                {
                    AddLogMessage($"✅ 数据同步完成，耗时: {result.Duration.TotalSeconds:F2}秒");
                    logger?.Info($"数据同步完成，耗时: {result.Duration.TotalSeconds:F2}秒");
                }
                else
                {
                    AddLogMessage($"❌ 数据同步失败: {result.ErrorMessage}");
                    logger?.Warning($"数据同步失败: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 数据同步异常: {ex.Message}");
                logger?.Error($"数据同步异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止同步按钮点击事件
        /// </summary>
        private async void OnStopSyncClicked()
        {
            if (syncManager == null) return;

            AddLogMessage("⏹️ 停止数据同步");

            try
            {
                await syncManager.StopSyncAsync();
                AddLogMessage("✅ 数据同步已停止");
                logger?.Info("数据同步已停止");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 停止同步异常: {ex.Message}");
                logger?.Error($"停止同步异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理缓存按钮点击事件
        /// </summary>
        private async void OnClearCacheClicked()
        {
            if (syncManager == null) return;

            AddLogMessage("🗑️ 清理同步缓存");

            try
            {
                await syncManager.ClearSyncCacheAsync();
                AddLogMessage("✅ 同步缓存已清理");
                logger?.Info("同步缓存已清理");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 清理缓存异常: {ex.Message}");
                logger?.Error($"清理缓存异常: {ex.Message}");
            }
        }

        #endregion

        #region 日志测试事件处理

        /// <summary>
        /// 日志测试按钮点击事件
        /// </summary>
        private void OnLogTestClicked(string level)
        {
            string message = logMessageInput?.text ?? "测试日志消息";
            string fullMessage = $"[{level}] {message} - {DateTime.Now:HH:mm:ss}";

            try
            {
                switch (level)
                {
                    case "Info":
                        logger?.Info(message);
                        Debug.Log($"[TestDashboard] {fullMessage}");
                        AddLogMessage($"ℹ️ {fullMessage}");
                        break;
                    case "Warning":
                        logger?.Warning(message);
                        Debug.LogWarning($"[TestDashboard] {fullMessage}");
                        AddLogMessage($"⚠️ {fullMessage}");
                        break;
                    case "Error":
                        logger?.Error(message);
                        Debug.LogError($"[TestDashboard] {fullMessage}");
                        AddLogMessage($"❌ {fullMessage}");
                        break;
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 日志记录异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理日志按钮点击事件
        /// </summary>
        private void OnClearLogsClicked()
        {
            logBuffer.Clear();
            if (logDisplayText != null)
            {
                logDisplayText.text = "";
            }
            AddLogMessage("🗑️ 日志已清理");
        }

        #endregion

        #region 系统控制事件处理

        /// <summary>
        /// 重启系统按钮点击事件
        /// </summary>
        private void OnRestartSystemClicked()
        {
            AddLogMessage("🔄 重启MVP系统");

            try
            {
                if (mvpManager != null)
                {
                    mvpManager.RestartMVPSystem();
                    AddLogMessage("✅ MVP系统重启完成");
                    logger?.Info("MVP系统重启完成");
                }
                else
                {
                    AddLogMessage("❌ MVP管理器不可用");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 系统重启异常: {ex.Message}");
                logger?.Error($"系统重启异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示系统信息按钮点击事件
        /// </summary>
        private void OnShowSystemInfoClicked()
        {
            AddLogMessage("📋 显示系统信息");

            try
            {
                if (mvpManager != null)
                {
                    string systemInfo;
                    if (mvpManager is EnhancedMVPManager enhancedMVP)
                    {
                        systemInfo = enhancedMVP.GetEnhancedSystemStatus();
                    }
                    else
                    {
                        systemInfo = mvpManager.GetSystemStatus();
                    }

                    // 将系统信息添加到日志显示
                    var lines = systemInfo.Split('\n');
                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            AddLogMessage($"📋 {line.Trim()}");
                        }
                    }

                    logger?.Info("系统信息已显示");
                }
                else
                {
                    AddLogMessage("❌ MVP管理器不可用");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 显示系统信息异常: {ex.Message}");
                logger?.Error($"显示系统信息异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 调试模式切换事件
        /// </summary>
        private void OnDebugModeToggled(bool enabled)
        {
            try
            {
                if (mvpManager != null)
                {
                    mvpManager.EnableDebugMode = enabled;
                    AddLogMessage($"🔧 调试模式: {(enabled ? "开启" : "关闭")}");
                    logger?.Info($"调试模式: {(enabled ? "开启" : "关闭")}");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 调试模式切换异常: {ex.Message}");
                logger?.Error($"调试模式切换异常: {ex.Message}");
            }
        }

        #endregion

        #region 事件订阅处理

        /// <summary>
        /// 用户登录事件处理
        /// </summary>
        private void OnUserLoggedIn(UserInfo userInfo)
        {
            AddLogMessage($"🎉 用户登录事件: {userInfo.DisplayName}");
        }

        /// <summary>
        /// 用户登出事件处理
        /// </summary>
        private void OnUserLoggedOut()
        {
            AddLogMessage("👋 用户登出事件");
        }

        /// <summary>
        /// 同步状态变化事件处理
        /// </summary>
        private void OnSyncStatusChanged(DigitalHuman.Core.DataSync.Models.SyncStatus status)
        {
            AddLogMessage($"🔄 同步状态变化: {status}");
        }

        /// <summary>
        /// 同步进度更新事件处理
        /// </summary>
        private void OnSyncProgressUpdated(DigitalHuman.Core.DataSync.Models.SyncProgress progress)
        {
            if (syncProgressSlider != null)
            {
                syncProgressSlider.value = progress.ProgressPercentage / 100f;
            }

            if (syncProgressText != null)
            {
                syncProgressText.text = $"{progress.ProgressPercentage:F1}% - {progress.CurrentStep}";
            }

            AddLogMessage($"📊 同步进度: {progress.ProgressPercentage:F1}% - {progress.CurrentStep}");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 添加日志消息到显示区域
        /// </summary>
        private void AddLogMessage(string message)
        {
            if (logDisplayText == null) return;

            // 添加时间戳
            string timestampedMessage = $"[{DateTime.Now:HH:mm:ss}] {message}";

            // 添加到缓冲区
            logBuffer.AppendLine(timestampedMessage);

            // 限制日志行数（保留最近100行）
            var lines = logBuffer.ToString().Split('\n');
            if (lines.Length > 100)
            {
                logBuffer.Clear();
                for (int i = lines.Length - 100; i < lines.Length; i++)
                {
                    if (!string.IsNullOrEmpty(lines[i]))
                    {
                        logBuffer.AppendLine(lines[i]);
                    }
                }
            }

            // 更新显示
            logDisplayText.text = logBuffer.ToString();

            // 自动滚动到底部
            if (logScrollView != null)
            {
                Canvas.ForceUpdateCanvases();
                logScrollView.verticalNormalizedPosition = 0f;
            }
        }

        #endregion

        /// <summary>
        /// 组件销毁时的清理
        /// </summary>
        void OnDestroy()
        {
            // 取消事件订阅
            if (authManager != null)
            {
                authManager.OnUserLoggedIn -= OnUserLoggedIn;
                authManager.OnUserLoggedOut -= OnUserLoggedOut;
            }

            if (syncManager != null)
            {
                syncManager.OnSyncStatusChanged -= OnSyncStatusChanged;
                syncManager.OnSyncProgressUpdated -= OnSyncProgressUpdated;
            }

            // 停止定时更新
            CancelInvoke();

            logger?.Info("测试仪表板销毁");
        }
    }
}
