using UnityEngine;
using UnityEngine.UIElements;
using DigitalHuman.Core.Authentication;
using System;
using System.Collections;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 主界面管理器 - 负责主界面的导航、状态管理、用户认证集成和响应式布局
    /// </summary>
    public class MainUIManager : MonoBehaviour
    {
        [Header("UI文档")]
        [SerializeField] private UIDocument uiDocument;

        [Header("页面管理")]
        [SerializeField] private string currentPage = "welcome";

        // UI元素引用
        private VisualElement root;
        private VisualElement welcomePage;
        private VisualElement chatPage;
        private VisualElement settingsPage;

        // 导航按钮
        private Button navHome;
        private Button navChat;
        private Button navSettings;

        // 快速操作按钮
        private Button startChatButton;
        private Button voiceSettingsButton;
        private Button avatarSettingsButton;

        // 状态指示器
        private Label systemStatusLabel;
        private Label renderModeLabel;
        private Label voiceEngineLabel;
        private Label aiServiceLabel;
        private Label networkStatusText;
        private Label activationStatusText;

        // 认证相关元素
        private Button loginButton;
        private VisualElement userInfo;
        private VisualElement loginDialog;
        private VisualElement activationDialog;

        // 状态属性
        public bool IsOnline { get; private set; } = true;
        public bool IsActivated { get; private set; } = true;
        public string CurrentRenderMode { get; private set; } = "3D模型";
        public bool IsVoiceEngineConnected { get; private set; } = true;
        public bool IsAIServiceConnected { get; private set; } = true;
        public bool IsUserLoggedIn { get; private set; } = false;
        public UserInfo CurrentUser { get; private set; } = null;

        // 认证管理器
        private AuthenticationManager authManager;

        // 事件
        public event Action<string> OnPageChanged;
        public event Action OnStartChat;
        public event Action OnOpenVoiceSettings;
        public event Action OnOpenAvatarSettings;
        public event Action OnOpenSettings;
        public event Action<string> OnActivationCodeSubmitted;
        public event Action<bool> OnMVPModeToggled;
        public event Action<string, string> OnLoginRequested;
        public event Action OnLogoutRequested;

        void Awake()
        {
            // 获取UIDocument组件
            if (uiDocument == null)
            {
                uiDocument = GetComponent<UIDocument>();
            }

            if (uiDocument == null)
            {
                Debug.LogError("[MainUIManager] 未找到UIDocument组件");
                return;
            }

            // 初始化UI
            InitializeUI();
        }

        void Start()
        {
            // 初始化认证系统
            InitializeAuthentication();

            // 设置事件处理器
            SetupEventHandlers();

            // 显示默认页面
            ShowPage(currentPage);

            // 开始状态更新协程
            StartCoroutine(UpdateTimeDisplay());
            StartCoroutine(UpdateSystemStats());

            Debug.Log("[MainUIManager] 主界面管理器初始化完成");
        }

        /// <summary>
        /// 初始化UI元素引用
        /// </summary>
        private void InitializeUI()
        {
            try
            {
                root = uiDocument.rootVisualElement;

                // 获取页面容器
                welcomePage = root.Q<VisualElement>("welcome-page");
                chatPage = root.Q<VisualElement>("chat-page");
                settingsPage = root.Q<VisualElement>("settings-page");

                // 获取导航按钮
                navHome = root.Q<Button>("nav-home");
                navChat = root.Q<Button>("nav-chat");
                navSettings = root.Q<Button>("nav-settings");

                // 获取快速操作按钮
                startChatButton = root.Q<Button>("start-chat");
                voiceSettingsButton = root.Q<Button>("voice-settings");
                avatarSettingsButton = root.Q<Button>("avatar-settings");

                // 获取状态标签
                systemStatusLabel = root.Q<Label>("system-status");
                renderModeLabel = root.Q<Label>("render-mode");
                voiceEngineLabel = root.Q<Label>("voice-engine");
                aiServiceLabel = root.Q<Label>("ai-service");
                networkStatusText = root.Q<Label>("network-status-text");
                activationStatusText = root.Q<Label>("activation-status-text");

                // 获取认证相关元素
                loginButton = root.Q<Button>("login-button");
                userInfo = root.Q<VisualElement>("user-info");
                loginDialog = root.Q<VisualElement>("login-dialog");
                activationDialog = root.Q<VisualElement>("activation-dialog");

                // 更新初始状态
                UpdateStatusIndicators();
                UpdateSystemInfo();

                Debug.Log("[MainUIManager] UI元素初始化完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] UI初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化认证系统集成
        /// </summary>
        private void InitializeAuthentication()
        {
            try
            {
                // 尝试获取认证管理器
                authManager = FindObjectOfType<AuthenticationManager>();
                
                if (authManager != null)
                {
                    // 订阅认证事件
                    authManager.OnUserLoggedIn += OnUserLoggedIn;
                    authManager.OnUserLoggedOut += OnUserLoggedOut;
                    
                    // 检查当前状态
                    IsUserLoggedIn = authManager.IsLoggedIn;
                    CurrentUser = authManager.CurrentUser;
                    
                    UpdateAuthenticationUI();
                    Debug.Log("[MainUIManager] 认证系统集成完成");
                }
                else
                {
                    Debug.LogWarning("[MainUIManager] 未找到认证管理器");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 认证系统初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // 导航按钮事件
                navHome?.RegisterCallback<ClickEvent>(evt => ShowPage("welcome"));
                navChat?.RegisterCallback<ClickEvent>(evt => ShowPage("chat"));
                navSettings?.RegisterCallback<ClickEvent>(evt => ShowPage("settings"));

                // 快速操作按钮事件
                startChatButton?.RegisterCallback<ClickEvent>(evt => {
                    OnStartChat?.Invoke();
                    ShowPage("chat");
                });
                
                voiceSettingsButton?.RegisterCallback<ClickEvent>(evt => OnOpenVoiceSettings?.Invoke());
                avatarSettingsButton?.RegisterCallback<ClickEvent>(evt => OnOpenAvatarSettings?.Invoke());

                // 认证按钮事件
                loginButton?.RegisterCallback<ClickEvent>(evt => ShowLoginDialog());

                Debug.Log("[MainUIManager] 事件处理器设置完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 事件处理器设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示指定页面
        /// </summary>
        public void ShowPage(string pageName)
        {
            try
            {
                // 隐藏所有页面
                welcomePage?.AddToClassList("hidden");
                chatPage?.AddToClassList("hidden");
                settingsPage?.AddToClassList("hidden");

                // 移除所有导航按钮的激活状态
                navHome?.RemoveFromClassList("active");
                navChat?.RemoveFromClassList("active");
                navSettings?.RemoveFromClassList("active");

                // 显示目标页面并激活对应按钮
                switch (pageName.ToLower())
                {
                    case "welcome":
                    case "home":
                        welcomePage?.RemoveFromClassList("hidden");
                        navHome?.AddToClassList("active");
                        currentPage = "welcome";
                        break;
                    case "chat":
                        chatPage?.RemoveFromClassList("hidden");
                        navChat?.AddToClassList("active");
                        currentPage = "chat";
                        break;
                    case "settings":
                        settingsPage?.RemoveFromClassList("hidden");
                        navSettings?.AddToClassList("active");
                        currentPage = "settings";
                        break;
                    default:
                        Debug.LogWarning($"[MainUIManager] 未知页面: {pageName}");
                        return;
                }

                OnPageChanged?.Invoke(currentPage);
                Debug.Log($"[MainUIManager] 切换到页面: {currentPage}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 页面切换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新网络状态
        /// </summary>
        public void UpdateNetworkStatus(bool isOnline)
        {
            IsOnline = isOnline;
            UpdateStatusIndicators();
        }

        /// <summary>
        /// 更新激活状态
        /// </summary>
        public void UpdateActivationStatus(bool isActivated)
        {
            IsActivated = isActivated;
            UpdateStatusIndicators();
        }

        /// <summary>
        /// 更新渲染模式
        /// </summary>
        public void UpdateRenderMode(string mode)
        {
            CurrentRenderMode = mode;
            UpdateSystemInfo();
        }

        /// <summary>
        /// 更新语音引擎状态
        /// </summary>
        public void UpdateVoiceEngineStatus(bool isConnected)
        {
            IsVoiceEngineConnected = isConnected;
            UpdateSystemInfo();
        }

        /// <summary>
        /// 更新AI服务状态
        /// </summary>
        public void UpdateAIServiceStatus(bool isConnected)
        {
            IsAIServiceConnected = isConnected;
            UpdateSystemInfo();
        }

        /// <summary>
        /// 显示登录对话框
        /// </summary>
        public void ShowLoginDialog()
        {
            loginDialog?.RemoveFromClassList("hidden");
        }

        /// <summary>
        /// 隐藏登录对话框
        /// </summary>
        public void HideLoginDialog()
        {
            loginDialog?.AddToClassList("hidden");
        }

        /// <summary>
        /// 显示激活对话框
        /// </summary>
        public void ShowActivationDialog()
        {
            activationDialog?.RemoveFromClassList("hidden");
        }

        /// <summary>
        /// 隐藏激活对话框
        /// </summary>
        public void HideActivationDialog()
        {
            activationDialog?.AddToClassList("hidden");
        }

        /// <summary>
        /// 更新状态指示器
        /// </summary>
        private void UpdateStatusIndicators()
        {
            try
            {
                if (networkStatusText != null)
                {
                    networkStatusText.text = IsOnline ? "正常" : "离线";
                    networkStatusText.RemoveFromClassList("status-normal");
                    networkStatusText.RemoveFromClassList("status-error");
                    networkStatusText.AddToClassList(IsOnline ? "status-normal" : "status-error");
                }

                if (activationStatusText != null)
                {
                    activationStatusText.text = IsActivated ? "已激活" : "未激活";
                    activationStatusText.RemoveFromClassList("status-normal");
                    activationStatusText.RemoveFromClassList("status-warning");
                    activationStatusText.AddToClassList(IsActivated ? "status-normal" : "status-warning");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 状态指示器更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新系统信息
        /// </summary>
        private void UpdateSystemInfo()
        {
            try
            {
                if (systemStatusLabel != null)
                {
                    systemStatusLabel.text = "正常运行";
                }

                if (renderModeLabel != null)
                {
                    renderModeLabel.text = CurrentRenderMode;
                }

                if (voiceEngineLabel != null)
                {
                    voiceEngineLabel.text = IsVoiceEngineConnected ? "已连接" : "未连接";
                }

                if (aiServiceLabel != null)
                {
                    aiServiceLabel.text = IsAIServiceConnected ? "已连接" : "未连接";
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 系统信息更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新认证UI
        /// </summary>
        private void UpdateAuthenticationUI()
        {
            try
            {
                if (IsUserLoggedIn && CurrentUser != null)
                {
                    // 显示用户信息，隐藏登录按钮
                    loginButton?.AddToClassList("hidden");
                    userInfo?.RemoveFromClassList("hidden");
                }
                else
                {
                    // 显示登录按钮，隐藏用户信息
                    loginButton?.RemoveFromClassList("hidden");
                    userInfo?.AddToClassList("hidden");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 认证UI更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 用户登录事件处理
        /// </summary>
        private void OnUserLoggedIn(UserInfo user)
        {
            IsUserLoggedIn = true;
            CurrentUser = user;
            UpdateAuthenticationUI();
            Debug.Log($"[MainUIManager] 用户已登录: {user?.DisplayName}");
        }

        /// <summary>
        /// 用户登出事件处理
        /// </summary>
        private void OnUserLoggedOut()
        {
            IsUserLoggedIn = false;
            CurrentUser = null;
            UpdateAuthenticationUI();
            Debug.Log("[MainUIManager] 用户已登出");
        }

        /// <summary>
        /// 更新时间显示协程
        /// </summary>
        private IEnumerator UpdateTimeDisplay()
        {
            while (true)
            {
                yield return new WaitForSeconds(1f);
                // 这里可以更新时间显示
            }
        }

        /// <summary>
        /// 更新系统统计协程
        /// </summary>
        private IEnumerator UpdateSystemStats()
        {
            while (true)
            {
                yield return new WaitForSeconds(2f);
                // 这里可以更新FPS、内存等统计信息
            }
        }

        /// <summary>
        /// 生成设备ID
        /// </summary>
        private void GenerateDeviceId()
        {
            try
            {
                string deviceId = SystemInfo.deviceUniqueIdentifier;
                if (string.IsNullOrEmpty(deviceId))
                {
                    deviceId = Guid.NewGuid().ToString();
                }

                var deviceIdLabel = root?.Q<Label>("device-id");
                if (deviceIdLabel != null)
                {
                    deviceIdLabel.text = deviceId.Substring(0, 8).ToUpper();
                }

                Debug.Log($"[MainUIManager] 设备ID: {deviceId}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 生成设备ID失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理激活码提交
        /// </summary>
        private void HandleActivation()
        {
            try
            {
                var activationInput = root?.Q<TextField>("activation-input");
                if (activationInput != null)
                {
                    string activationCode = activationInput.value;
                    if (!string.IsNullOrEmpty(activationCode))
                    {
                        OnActivationCodeSubmitted?.Invoke(activationCode);
                        Debug.Log($"[MainUIManager] 激活码已提交: {activationCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 处理激活失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示帮助对话框
        /// </summary>
        private void ShowHelpDialog()
        {
            try
            {
                var helpDialog = root?.Q<VisualElement>("help-dialog");
                helpDialog?.RemoveFromClassList("hidden");
                Debug.Log("[MainUIManager] 显示帮助对话框");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 显示帮助对话框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 响应式布局适配
        /// </summary>
        private void AdaptToScreenSize()
        {
            try
            {
                int screenWidth = Screen.width;
                int screenHeight = Screen.height;

                // 大屏幕适配 (≥1920x1080)
                if (screenWidth >= 1920 && screenHeight >= 1080)
                {
                    root?.AddToClassList("large-screen");
                    root?.RemoveFromClassList("small-screen");
                    Debug.Log("[MainUIManager] 应用大屏幕布局");
                }
                else
                {
                    root?.AddToClassList("small-screen");
                    root?.RemoveFromClassList("large-screen");
                    Debug.Log("[MainUIManager] 应用小屏幕布局");
                }

                // 竖屏适配
                if (screenHeight > screenWidth)
                {
                    root?.AddToClassList("portrait");
                    root?.RemoveFromClassList("landscape");
                }
                else
                {
                    root?.AddToClassList("landscape");
                    root?.RemoveFromClassList("portrait");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 屏幕适配失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取系统状态信息
        /// </summary>
        public string GetSystemStatus()
        {
            try
            {
                var status = new System.Text.StringBuilder();
                status.AppendLine("=== 系统状态 ===");
                status.AppendLine($"网络状态: {(IsOnline ? "在线" : "离线")}");
                status.AppendLine($"激活状态: {(IsActivated ? "已激活" : "未激活")}");
                status.AppendLine($"渲染模式: {CurrentRenderMode}");
                status.AppendLine($"语音引擎: {(IsVoiceEngineConnected ? "已连接" : "未连接")}");
                status.AppendLine($"AI服务: {(IsAIServiceConnected ? "已连接" : "未连接")}");
                status.AppendLine($"用户状态: {(IsUserLoggedIn ? $"已登录 ({CurrentUser?.DisplayName})" : "未登录")}");
                status.AppendLine($"当前页面: {currentPage}");
                status.AppendLine($"屏幕分辨率: {Screen.width}x{Screen.height}");
                status.AppendLine($"Unity版本: {Application.unityVersion}");
                status.AppendLine($"平台: {Application.platform}");

                return status.ToString();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MainUIManager] 获取系统状态失败: {ex.Message}");
                return "系统状态获取失败";
            }
        }

        void OnDestroy()
        {
            // 清理事件订阅
            if (authManager != null)
            {
                authManager.OnUserLoggedIn -= OnUserLoggedIn;
                authManager.OnUserLoggedOut -= OnUserLoggedOut;
            }
        }
    }
}
