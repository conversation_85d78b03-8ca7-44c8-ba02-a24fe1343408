using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DigitalHuman.MVP;
using DigitalHuman.Core.Authentication;
using DigitalHuman.Core.DataSync;
using DigitalHuman.Core.Logging;
using System.Text;
using System;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 运行时测试UI - 在运行时动态创建简单的测试界面
    /// </summary>
    public class RuntimeTestUI : MonoBehaviour
    {
        private Canvas canvas;
        private GameObject mainPanel;
        private TextMeshProUGUI statusText;
        private Button loginButton;
        private Button syncButton;
        private Button logButton;
        
        private EnhancedMVPManager mvpManager;
        private IAuthenticationManager authManager;
        private IDataSyncManager syncManager;
        private StringBuilder logBuffer;
        
        void Start()
        {
            // 延迟创建UI，确保其他系统已初始化
            Invoke(nameof(CreateTestUI), 1f);
        }
        
        /// <summary>
        /// 创建测试UI
        /// </summary>
        private void CreateTestUI()
        {
            try
            {
                Debug.Log("[RuntimeTestUI] 开始创建测试UI");
                
                // 获取管理器实例
                InitializeManagers();
                
                // 创建Canvas
                CreateCanvas();
                
                // 创建主面板
                CreateMainPanel();
                
                // 创建UI元素
                CreateStatusText();
                CreateButtons();
                
                // 初始化日志缓冲区
                logBuffer = new StringBuilder();
                
                // 开始状态更新
                InvokeRepeating(nameof(UpdateStatus), 0f, 2f);
                
                Debug.Log("[RuntimeTestUI] 测试UI创建完成");
                AddLogMessage("🎯 运行时测试UI已启动");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[RuntimeTestUI] 创建UI失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        private void InitializeManagers()
        {
            mvpManager = FindObjectOfType<EnhancedMVPManager>();
            if (mvpManager == null)
            {
                mvpManager = FindObjectOfType<MinimalMVPManager>() as EnhancedMVPManager;
            }
            
            try
            {
                authManager = AuthenticationManager.Instance as IAuthenticationManager;
                syncManager = DataSyncManager.Instance as IDataSyncManager;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"[RuntimeTestUI] 管理器初始化警告: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 创建Canvas
        /// </summary>
        private void CreateCanvas()
        {
            // 查找现有Canvas
            canvas = FindObjectOfType<Canvas>();
            
            if (canvas == null)
            {
                // 创建新的Canvas
                GameObject canvasObject = new GameObject("RuntimeTestCanvas");
                canvas = canvasObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 100; // 确保在最上层
                
                canvasObject.AddComponent<CanvasScaler>();
                canvasObject.AddComponent<GraphicRaycaster>();
                
                // 添加EventSystem
                if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
                {
                    GameObject eventSystemObject = new GameObject("EventSystem");
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.EventSystem>();
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                }
                
                Debug.Log("[RuntimeTestUI] 创建了新的Canvas");
            }
            else
            {
                Debug.Log("[RuntimeTestUI] 使用现有Canvas");
            }
        }
        
        /// <summary>
        /// 创建主面板
        /// </summary>
        private void CreateMainPanel()
        {
            mainPanel = new GameObject("RuntimeTestPanel");
            mainPanel.transform.SetParent(canvas.transform, false);
            
            RectTransform rect = mainPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0, 0);
            rect.anchorMax = new Vector2(1, 1);
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;
            
            Image image = mainPanel.AddComponent<Image>();
            image.color = new Color(0.1f, 0.1f, 0.1f, 0.8f);
        }
        
        /// <summary>
        /// 创建状态文本
        /// </summary>
        private void CreateStatusText()
        {
            GameObject statusObject = new GameObject("StatusText");
            statusObject.transform.SetParent(mainPanel.transform, false);
            
            RectTransform rect = statusObject.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0, 0.5f);
            rect.anchorMax = new Vector2(1, 1);
            rect.offsetMin = new Vector2(20, 20);
            rect.offsetMax = new Vector2(-20, -20);
            
            statusText = statusObject.AddComponent<TextMeshProUGUI>();
            statusText.text = "数字人管理系统 - 运行时测试界面\n\n状态加载中...";
            statusText.fontSize = 16;
            statusText.color = Color.white;
            statusText.alignment = TextAlignmentOptions.TopLeft;
        }
        
        /// <summary>
        /// 创建按钮
        /// </summary>
        private void CreateButtons()
        {
            // 创建按钮容器
            GameObject buttonContainer = new GameObject("ButtonContainer");
            buttonContainer.transform.SetParent(mainPanel.transform, false);
            
            RectTransform containerRect = buttonContainer.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0, 0);
            containerRect.anchorMax = new Vector2(1, 0.5f);
            containerRect.offsetMin = new Vector2(20, 20);
            containerRect.offsetMax = new Vector2(-20, -20);
            
            HorizontalLayoutGroup layout = buttonContainer.AddComponent<HorizontalLayoutGroup>();
            layout.spacing = 20;
            layout.childControlWidth = true;
            layout.childForceExpandWidth = true;
            
            // 创建登录按钮
            loginButton = CreateButton(buttonContainer.transform, "登录测试", Color.green);
            loginButton.onClick.AddListener(OnLoginClicked);
            
            // 创建同步按钮
            syncButton = CreateButton(buttonContainer.transform, "同步测试", Color.blue);
            syncButton.onClick.AddListener(OnSyncClicked);
            
            // 创建日志按钮
            logButton = CreateButton(buttonContainer.transform, "日志测试", Color.yellow);
            logButton.onClick.AddListener(OnLogClicked);
        }
        
        /// <summary>
        /// 创建按钮
        /// </summary>
        private Button CreateButton(Transform parent, string text, Color color)
        {
            GameObject buttonObject = new GameObject($"Button_{text}");
            buttonObject.transform.SetParent(parent, false);
            
            RectTransform rect = buttonObject.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(150, 50);
            
            Image image = buttonObject.AddComponent<Image>();
            image.color = color;
            
            Button button = buttonObject.AddComponent<Button>();
            
            // 创建按钮文本
            GameObject textObject = new GameObject("Text");
            textObject.transform.SetParent(buttonObject.transform, false);
            
            RectTransform textRect = textObject.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI textComponent = textObject.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 14;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.Center;
            
            return button;
        }
        
        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus()
        {
            if (statusText == null) return;
            
            var status = new StringBuilder();
            status.AppendLine("=== 数字人管理系统 - 运行时测试界面 ===");
            status.AppendLine($"时间: {DateTime.Now:HH:mm:ss}");
            status.AppendLine();
            
            // MVP管理器状态
            if (mvpManager != null)
            {
                status.AppendLine("✅ MVP管理器: 运行中");
                status.AppendLine($"   版本: {mvpManager.AppVersion}");
                status.AppendLine($"   调试模式: {(mvpManager.EnableDebugMode ? "开启" : "关闭")}");
            }
            else
            {
                status.AppendLine("❌ MVP管理器: 未找到");
            }
            
            // 认证状态
            if (authManager != null)
            {
                status.AppendLine("✅ 认证管理器: 可用");
                status.AppendLine($"   登录状态: {(authManager.IsLoggedIn ? "已登录" : "未登录")}");
                if (authManager.IsLoggedIn && authManager.CurrentUser != null)
                {
                    status.AppendLine($"   用户: {authManager.CurrentUser.DisplayName}");
                }
            }
            else
            {
                status.AppendLine("❌ 认证管理器: 不可用");
            }
            
            // 同步状态
            if (syncManager != null)
            {
                status.AppendLine("✅ 同步管理器: 可用");
                status.AppendLine($"   状态: {syncManager.CurrentStatus}");
                status.AppendLine($"   正在同步: {(syncManager.IsSyncing ? "是" : "否")}");
            }
            else
            {
                status.AppendLine("❌ 同步管理器: 不可用");
            }
            
            // 日志信息
            status.AppendLine();
            status.AppendLine("=== 最近日志 ===");
            if (logBuffer != null && logBuffer.Length > 0)
            {
                var lines = logBuffer.ToString().Split('\n');
                int startIndex = Mathf.Max(0, lines.Length - 5);
                for (int i = startIndex; i < lines.Length; i++)
                {
                    if (!string.IsNullOrEmpty(lines[i]))
                    {
                        status.AppendLine(lines[i]);
                    }
                }
            }
            else
            {
                status.AppendLine("暂无日志");
            }
            
            statusText.text = status.ToString();
        }
        
        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private async void OnLoginClicked()
        {
            AddLogMessage("🔐 点击登录测试");
            
            if (authManager != null)
            {
                try
                {
                    var result = await authManager.LoginAsync("demo_user", "demo_pass");
                    if (result.IsSuccess)
                    {
                        AddLogMessage($"✅ 登录成功: {result.UserInfo.DisplayName}");
                    }
                    else
                    {
                        AddLogMessage($"❌ 登录失败: {result.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    AddLogMessage($"❌ 登录异常: {ex.Message}");
                }
            }
            else
            {
                AddLogMessage("❌ 认证管理器不可用");
            }
        }
        
        /// <summary>
        /// 同步按钮点击事件
        /// </summary>
        private async void OnSyncClicked()
        {
            AddLogMessage("🔄 点击同步测试");
            
            if (syncManager != null)
            {
                try
                {
                    var result = await syncManager.StartSyncAsync(DigitalHuman.Core.DataSync.Models.SyncType.Full);
                    if (result.IsSuccess)
                    {
                        AddLogMessage($"✅ 同步完成，耗时: {result.Duration.TotalSeconds:F2}秒");
                    }
                    else
                    {
                        AddLogMessage($"❌ 同步失败: {result.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    AddLogMessage($"❌ 同步异常: {ex.Message}");
                }
            }
            else
            {
                AddLogMessage("❌ 同步管理器不可用");
            }
        }
        
        /// <summary>
        /// 日志按钮点击事件
        /// </summary>
        private void OnLogClicked()
        {
            AddLogMessage("📝 点击日志测试");
            
            try
            {
                var logManager = LogManager.Instance;
                if (logManager != null)
                {
                    var logger = logManager.GetLogger("RuntimeTestUI");
                    logger?.Info("这是一条测试日志消息");
                    AddLogMessage("✅ 日志记录成功");
                }
                else
                {
                    AddLogMessage("❌ 日志管理器不可用");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ 日志异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 添加日志消息
        /// </summary>
        private void AddLogMessage(string message)
        {
            if (logBuffer == null) logBuffer = new StringBuilder();
            
            string timestampedMessage = $"[{DateTime.Now:HH:mm:ss}] {message}";
            logBuffer.AppendLine(timestampedMessage);
            
            // 限制日志行数
            var lines = logBuffer.ToString().Split('\n');
            if (lines.Length > 20)
            {
                logBuffer.Clear();
                for (int i = lines.Length - 20; i < lines.Length; i++)
                {
                    if (!string.IsNullOrEmpty(lines[i]))
                    {
                        logBuffer.AppendLine(lines[i]);
                    }
                }
            }
            
            Debug.Log($"[RuntimeTestUI] {timestampedMessage}");
        }
        
        void OnDestroy()
        {
            CancelInvoke();
        }
    }
}
