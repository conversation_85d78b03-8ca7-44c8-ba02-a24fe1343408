using UnityEngine;
using UnityEngine.UI;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 强制UI显示 - 最简单直接的UI创建方法
    /// </summary>
    public class ForceUI : MonoBehaviour
    {
        void Awake()
        {
            Debug.Log("[ForceUI] Awake - 强制创建UI");
            CreateForceUI();
        }
        
        void Start()
        {
            Debug.Log("[ForceUI] Start - 再次确保UI存在");
            if (GameObject.Find("ForceCanvas") == null)
            {
                CreateForceUI();
            }
        }
        
        void CreateForceUI()
        {
            try
            {
                Debug.Log("[ForceUI] 开始创建强制UI");
                
                // 删除现有的Canvas
                var existingCanvas = FindObjectsOfType<Canvas>();
                foreach (var existingCanvasItem in existingCanvas)
                {
                    if (existingCanvasItem.name.Contains("Force") || existingCanvasItem.name.Contains("Test"))
                    {
                        DestroyImmediate(existingCanvasItem.gameObject);
                    }
                }
                
                // 创建Canvas
                GameObject canvasObject = new GameObject("ForceCanvas");
                Canvas canvas = canvasObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 10000; // 最高优先级
                
                CanvasScaler scaler = canvasObject.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                canvasObject.AddComponent<GraphicRaycaster>();
                
                // 创建EventSystem
                if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
                {
                    GameObject eventSystemObject = new GameObject("EventSystem");
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.EventSystem>();
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                }
                
                // 创建全屏背景
                GameObject backgroundObject = new GameObject("ForceBackground");
                backgroundObject.transform.SetParent(canvasObject.transform, false);
                
                RectTransform bgRect = backgroundObject.AddComponent<RectTransform>();
                bgRect.anchorMin = Vector2.zero;
                bgRect.anchorMax = Vector2.one;
                bgRect.offsetMin = Vector2.zero;
                bgRect.offsetMax = Vector2.zero;
                
                Image bgImage = backgroundObject.AddComponent<Image>();
                bgImage.color = new Color(1, 0, 1, 1); // 紫红色背景，更明显
                
                // 创建大文本
                GameObject textObject = new GameObject("ForceText");
                textObject.transform.SetParent(backgroundObject.transform, false);
                
                RectTransform textRect = textObject.AddComponent<RectTransform>();
                textRect.anchorMin = Vector2.zero;
                textRect.anchorMax = Vector2.one;
                textRect.offsetMin = new Vector2(100, 100);
                textRect.offsetMax = new Vector2(-100, -100);
                
                Text text = textObject.AddComponent<Text>();
                text.text = "🎯 数字人管理系统\n\n✅ 强制UI已显示！\n\n如果您能看到这个紫红色界面\n说明UI系统终于工作了！\n\n点击下方按钮测试交互";
                text.fontSize = 48;
                text.color = Color.white;
                text.alignment = TextAnchor.MiddleCenter;
                text.fontStyle = FontStyle.Bold;
                text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
                
                // 创建大按钮
                GameObject buttonObject = new GameObject("ForceButton");
                buttonObject.transform.SetParent(backgroundObject.transform, false);
                
                RectTransform buttonRect = buttonObject.AddComponent<RectTransform>();
                buttonRect.anchoredPosition = new Vector2(0, -300);
                buttonRect.sizeDelta = new Vector2(400, 100);
                
                Image buttonImage = buttonObject.AddComponent<Image>();
                buttonImage.color = Color.yellow;
                
                Button button = buttonObject.AddComponent<Button>();
                button.onClick.AddListener(() => {
                    Debug.Log("[ForceUI] 按钮被点击！");
                    text.text = "🎉 成功！\n\n按钮点击工作正常！\n\nUI系统已经正常运行！";
                    text.color = Color.green;
                    bgImage.color = Random.ColorHSV();
                });
                
                // 按钮文本
                GameObject buttonTextObject = new GameObject("ForceButtonText");
                buttonTextObject.transform.SetParent(buttonObject.transform, false);
                
                RectTransform buttonTextRect = buttonTextObject.AddComponent<RectTransform>();
                buttonTextRect.anchorMin = Vector2.zero;
                buttonTextRect.anchorMax = Vector2.one;
                buttonTextRect.offsetMin = Vector2.zero;
                buttonTextRect.offsetMax = Vector2.zero;
                
                Text buttonText = buttonTextObject.AddComponent<Text>();
                buttonText.text = "点击测试";
                buttonText.fontSize = 36;
                buttonText.color = Color.black;
                buttonText.alignment = TextAnchor.MiddleCenter;
                buttonText.fontStyle = FontStyle.Bold;
                buttonText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
                
                // 确保不被销毁
                DontDestroyOnLoad(canvasObject);
                
                // 强制刷新
                Canvas.ForceUpdateCanvases();
                
                Debug.Log("[ForceUI] 强制UI创建完成！");
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ForceUI] 创建UI失败: {ex.Message}");
                Debug.LogError($"[ForceUI] 堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        void Update()
        {
            // 每帧检查UI是否存在
            if (Time.frameCount % 60 == 0) // 每秒检查一次
            {
                if (GameObject.Find("ForceCanvas") == null)
                {
                    Debug.LogWarning("[ForceUI] Canvas丢失，重新创建");
                    CreateForceUI();
                }
            }
        }
        
        void OnGUI()
        {
            // 作为备用，使用OnGUI绘制
            GUI.color = Color.red;
            GUI.Box(new Rect(0, 0, Screen.width, Screen.height), "");
            
            GUI.color = Color.white;
            GUIStyle style = new GUIStyle(GUI.skin.label);
            style.fontSize = 48;
            style.alignment = TextAnchor.MiddleCenter;
            style.fontStyle = FontStyle.Bold;
            
            GUI.Label(new Rect(0, 0, Screen.width, Screen.height), 
                "🎯 数字人管理系统\n\nOnGUI备用界面\n\n如果您能看到这个红色界面\n说明至少OnGUI工作正常", style);
            
            if (GUI.Button(new Rect(Screen.width/2 - 200, Screen.height/2 + 100, 400, 80), "OnGUI测试按钮"))
            {
                Debug.Log("[ForceUI] OnGUI按钮被点击！");
            }
        }
    }
}
