using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 简单测试UI - 确保能够显示的最基本界面
    /// </summary>
    public class SimpleTestUI : MonoBehaviour
    {
        void Start()
        {
            // 立即创建UI，不延迟
            CreateSimpleUI();
        }
        
        void CreateSimpleUI()
        {
            Debug.Log("[SimpleTestUI] 开始创建简单测试UI");
            
            try
            {
                // 创建Canvas
                GameObject canvasObject = new GameObject("TestCanvas");
                Canvas canvas = canvasObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 1000; // 确保在最上层
                
                CanvasScaler scaler = canvasObject.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                canvasObject.AddComponent<GraphicRaycaster>();
                
                // 创建EventSystem
                if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
                {
                    GameObject eventSystemObject = new GameObject("EventSystem");
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.EventSystem>();
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                }
                
                // 创建背景面板
                GameObject panelObject = new GameObject("TestPanel");
                panelObject.transform.SetParent(canvasObject.transform, false);
                
                RectTransform panelRect = panelObject.AddComponent<RectTransform>();
                panelRect.anchorMin = Vector2.zero;
                panelRect.anchorMax = Vector2.one;
                panelRect.offsetMin = Vector2.zero;
                panelRect.offsetMax = Vector2.zero;
                
                Image panelImage = panelObject.AddComponent<Image>();
                panelImage.color = new Color(0, 0, 0, 0.8f); // 半透明黑色背景
                
                // 创建标题文本
                GameObject titleObject = new GameObject("TitleText");
                titleObject.transform.SetParent(panelObject.transform, false);
                
                RectTransform titleRect = titleObject.AddComponent<RectTransform>();
                titleRect.anchorMin = new Vector2(0, 0.8f);
                titleRect.anchorMax = new Vector2(1, 1);
                titleRect.offsetMin = new Vector2(50, 0);
                titleRect.offsetMax = new Vector2(-50, 0);
                
                TextMeshProUGUI titleText = titleObject.AddComponent<TextMeshProUGUI>();
                titleText.text = "🎯 数字人管理系统 - 测试界面";
                titleText.fontSize = 36;
                titleText.color = Color.white;
                titleText.alignment = TextAlignmentOptions.Center;
                titleText.fontStyle = FontStyles.Bold;
                
                // 创建状态文本
                GameObject statusObject = new GameObject("StatusText");
                statusObject.transform.SetParent(panelObject.transform, false);
                
                RectTransform statusRect = statusObject.AddComponent<RectTransform>();
                statusRect.anchorMin = new Vector2(0, 0.4f);
                statusRect.anchorMax = new Vector2(1, 0.8f);
                statusRect.offsetMin = new Vector2(50, 0);
                statusRect.offsetMax = new Vector2(-50, 0);
                
                TextMeshProUGUI statusText = statusObject.AddComponent<TextMeshProUGUI>();
                statusText.text = GetSystemStatus();
                statusText.fontSize = 18;
                statusText.color = Color.white;
                statusText.alignment = TextAlignmentOptions.TopLeft;
                
                // 创建测试按钮
                CreateTestButton(panelObject.transform, "登录测试", new Vector2(-200, -100), Color.green, OnLoginTest);
                CreateTestButton(panelObject.transform, "同步测试", new Vector2(0, -100), Color.blue, OnSyncTest);
                CreateTestButton(panelObject.transform, "日志测试", new Vector2(200, -100), Color.yellow, OnLogTest);
                
                // 创建说明文本
                GameObject infoObject = new GameObject("InfoText");
                infoObject.transform.SetParent(panelObject.transform, false);
                
                RectTransform infoRect = infoObject.AddComponent<RectTransform>();
                infoRect.anchorMin = new Vector2(0, 0);
                infoRect.anchorMax = new Vector2(1, 0.3f);
                infoRect.offsetMin = new Vector2(50, 50);
                infoRect.offsetMax = new Vector2(-50, 0);
                
                TextMeshProUGUI infoText = infoObject.AddComponent<TextMeshProUGUI>();
                infoText.text = "✅ 测试界面已成功创建！\n\n点击上方按钮测试各项功能。\n\n如果您能看到这个界面，说明UI系统工作正常。";
                infoText.fontSize = 16;
                infoText.color = Color.cyan;
                infoText.alignment = TextAlignmentOptions.TopLeft;
                
                // 定时更新状态
                InvokeRepeating(nameof(UpdateStatus), 2f, 2f);
                
                Debug.Log("[SimpleTestUI] 简单测试UI创建完成");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[SimpleTestUI] 创建UI失败: {ex.Message}");
            }
        }
        
        void CreateTestButton(Transform parent, string text, Vector2 position, Color color, System.Action onClick)
        {
            GameObject buttonObject = new GameObject($"Button_{text}");
            buttonObject.transform.SetParent(parent, false);
            
            RectTransform buttonRect = buttonObject.AddComponent<RectTransform>();
            buttonRect.anchoredPosition = position;
            buttonRect.sizeDelta = new Vector2(150, 50);
            
            Image buttonImage = buttonObject.AddComponent<Image>();
            buttonImage.color = color;
            
            Button button = buttonObject.AddComponent<Button>();
            button.onClick.AddListener(() => onClick?.Invoke());
            
            // 按钮文本
            GameObject textObject = new GameObject("Text");
            textObject.transform.SetParent(buttonObject.transform, false);
            
            RectTransform textRect = textObject.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI buttonText = textObject.AddComponent<TextMeshProUGUI>();
            buttonText.text = text;
            buttonText.fontSize = 14;
            buttonText.color = Color.white;
            buttonText.alignment = TextAlignmentOptions.Center;
            buttonText.fontStyle = FontStyles.Bold;
        }
        
        string GetSystemStatus()
        {
            var status = new System.Text.StringBuilder();
            status.AppendLine("=== 系统状态 ===");
            status.AppendLine($"时间: {System.DateTime.Now:HH:mm:ss}");
            status.AppendLine($"Unity版本: {Application.unityVersion}");
            status.AppendLine($"平台: {Application.platform}");
            status.AppendLine($"分辨率: {Screen.width}x{Screen.height}");
            status.AppendLine();
            
            // 检查管理器
            var mvpManager = FindObjectOfType<DigitalHuman.MVP.EnhancedMVPManager>();
            if (mvpManager != null)
            {
                status.AppendLine("✅ MVP管理器: 已找到");
                status.AppendLine($"   版本: {mvpManager.AppVersion}");
                status.AppendLine($"   调试模式: {mvpManager.EnableDebugMode}");
            }
            else
            {
                status.AppendLine("❌ MVP管理器: 未找到");
            }
            
            return status.ToString();
        }
        
        void UpdateStatus()
        {
            var statusText = GameObject.Find("StatusText")?.GetComponent<TextMeshProUGUI>();
            if (statusText != null)
            {
                statusText.text = GetSystemStatus();
            }
        }
        
        void OnLoginTest()
        {
            Debug.Log("[SimpleTestUI] 登录测试按钮被点击");
            ShowMessage("🔐 登录测试功能已触发！");
        }
        
        void OnSyncTest()
        {
            Debug.Log("[SimpleTestUI] 同步测试按钮被点击");
            ShowMessage("🔄 同步测试功能已触发！");
        }
        
        void OnLogTest()
        {
            Debug.Log("[SimpleTestUI] 日志测试按钮被点击");
            ShowMessage("📝 日志测试功能已触发！");
        }
        
        void ShowMessage(string message)
        {
            Debug.Log($"[SimpleTestUI] {message}");
            
            // 在界面上显示消息
            var infoText = GameObject.Find("InfoText")?.GetComponent<TextMeshProUGUI>();
            if (infoText != null)
            {
                infoText.text = $"{message}\n\n时间: {System.DateTime.Now:HH:mm:ss}\n\n✅ 按钮功能正常工作！";
                infoText.color = Color.green;
            }
        }
        
        void OnDestroy()
        {
            CancelInvoke();
        }
    }
}
