using UnityEngine;
using UnityEngine.UI;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 调试测试UI - 最简单的可见界面
    /// </summary>
    public class DebugTestUI : MonoBehaviour
    {
        void Start()
        {
            Debug.Log("[DebugTestUI] 开始创建调试UI");
            CreateDebugUI();
        }
        
        void CreateDebugUI()
        {
            try
            {
                Debug.Log("[DebugTestUI] 步骤1: 创建Canvas");
                
                // 创建Canvas
                GameObject canvasObject = new GameObject("DebugCanvas");
                Canvas canvas = canvasObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 9999; // 最高层级
                
                CanvasScaler scaler = canvasObject.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                canvasObject.AddComponent<GraphicRaycaster>();
                
                Debug.Log("[DebugTestUI] 步骤2: 创建EventSystem");
                
                // 创建EventSystem
                if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
                {
                    GameObject eventSystemObject = new GameObject("EventSystem");
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.EventSystem>();
                    eventSystemObject.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                }
                
                Debug.Log("[DebugTestUI] 步骤3: 创建红色背景");
                
                // 创建一个大红色背景，确保可见
                GameObject backgroundObject = new GameObject("RedBackground");
                backgroundObject.transform.SetParent(canvasObject.transform, false);
                
                RectTransform bgRect = backgroundObject.AddComponent<RectTransform>();
                bgRect.anchorMin = Vector2.zero;
                bgRect.anchorMax = Vector2.one;
                bgRect.offsetMin = Vector2.zero;
                bgRect.offsetMax = Vector2.zero;
                
                Image bgImage = backgroundObject.AddComponent<Image>();
                bgImage.color = Color.red; // 明显的红色背景
                
                Debug.Log("[DebugTestUI] 步骤4: 创建白色文本");
                
                // 创建大白字
                GameObject textObject = new GameObject("BigWhiteText");
                textObject.transform.SetParent(backgroundObject.transform, false);
                
                RectTransform textRect = textObject.AddComponent<RectTransform>();
                textRect.anchorMin = Vector2.zero;
                textRect.anchorMax = Vector2.one;
                textRect.offsetMin = Vector2.zero;
                textRect.offsetMax = Vector2.zero;
                
                Text text = textObject.AddComponent<Text>();
                text.text = "🎯 数字人管理系统\n测试界面已启动！\n\n如果您能看到这个红色界面\n说明UI系统工作正常";
                text.fontSize = 48;
                text.color = Color.white;
                text.alignment = TextAnchor.MiddleCenter;
                text.fontStyle = FontStyle.Bold;
                
                // 使用Unity默认字体
                text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
                
                Debug.Log("[DebugTestUI] 步骤5: 创建测试按钮");
                
                // 创建一个大按钮
                GameObject buttonObject = new GameObject("TestButton");
                buttonObject.transform.SetParent(backgroundObject.transform, false);
                
                RectTransform buttonRect = buttonObject.AddComponent<RectTransform>();
                buttonRect.anchoredPosition = new Vector2(0, -200);
                buttonRect.sizeDelta = new Vector2(300, 80);
                
                Image buttonImage = buttonObject.AddComponent<Image>();
                buttonImage.color = Color.green;
                
                Button button = buttonObject.AddComponent<Button>();
                button.onClick.AddListener(OnTestButtonClicked);
                
                // 按钮文本
                GameObject buttonTextObject = new GameObject("ButtonText");
                buttonTextObject.transform.SetParent(buttonObject.transform, false);
                
                RectTransform buttonTextRect = buttonTextObject.AddComponent<RectTransform>();
                buttonTextRect.anchorMin = Vector2.zero;
                buttonTextRect.anchorMax = Vector2.one;
                buttonTextRect.offsetMin = Vector2.zero;
                buttonTextRect.offsetMax = Vector2.zero;
                
                Text buttonText = buttonTextObject.AddComponent<Text>();
                buttonText.text = "点击测试";
                buttonText.fontSize = 32;
                buttonText.color = Color.white;
                buttonText.alignment = TextAnchor.MiddleCenter;
                buttonText.fontStyle = FontStyle.Bold;
                buttonText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
                
                Debug.Log("[DebugTestUI] 调试UI创建完成！");
                
                // 确保不被销毁
                DontDestroyOnLoad(canvasObject);
                
                // 强制刷新
                Canvas.ForceUpdateCanvases();
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[DebugTestUI] 创建UI失败: {ex.Message}");
                Debug.LogError($"[DebugTestUI] 堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        void OnTestButtonClicked()
        {
            Debug.Log("[DebugTestUI] 测试按钮被点击！");
            
            // 改变背景颜色表示按钮工作
            var background = GameObject.Find("RedBackground")?.GetComponent<Image>();
            if (background != null)
            {
                background.color = Random.ColorHSV();
            }
            
            // 更新文本
            var text = GameObject.Find("BigWhiteText")?.GetComponent<Text>();
            if (text != null)
            {
                text.text = $"✅ 按钮点击成功！\n时间: {System.DateTime.Now:HH:mm:ss}\n\nUI系统工作正常！";
            }
        }
        
        void Update()
        {
            // 每帧检查UI是否存在
            if (GameObject.Find("DebugCanvas") == null)
            {
                Debug.LogWarning("[DebugTestUI] Canvas丢失，重新创建");
                CreateDebugUI();
            }
        }
    }
}
