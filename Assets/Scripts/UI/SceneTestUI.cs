using UnityEngine;
using UnityEngine.UI;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 场景测试UI - 直接在场景中显示的UI组件
    /// </summary>
    public class SceneTestUI : MonoBehaviour
    {
        [Header("UI组件")]
        public Canvas canvas;
        public Text statusText;
        public Button testButton;
        
        void Awake()
        {
            Debug.Log("[SceneTestUI] Awake - 场景UI组件已加载");
            
            // 确保Canvas在最上层
            if (canvas != null)
            {
                canvas.sortingOrder = 9999;
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            }
        }
        
        void Start()
        {
            Debug.Log("[SceneTestUI] Start - 开始初始化场景UI");
            
            // 初始化UI
            InitializeUI();
            
            // 设置按钮事件
            if (testButton != null)
            {
                testButton.onClick.AddListener(OnTestButtonClicked);
            }
            
            Debug.Log("[SceneTestUI] 场景UI初始化完成");
        }
        
        void InitializeUI()
        {
            if (statusText != null)
            {
                statusText.text = "🎯 数字人管理系统\n场景测试界面\n\n✅ 如果您能看到这个界面\n说明UI系统工作正常！\n\n点击下方按钮测试功能";
                statusText.fontSize = 24;
                statusText.color = Color.white;
            }
            
            // 强制刷新Canvas
            if (canvas != null)
            {
                Canvas.ForceUpdateCanvases();
            }
        }
        
        void OnTestButtonClicked()
        {
            Debug.Log("[SceneTestUI] 测试按钮被点击！");
            
            if (statusText != null)
            {
                statusText.text = $"✅ 按钮点击成功！\n时间: {System.DateTime.Now:HH:mm:ss}\n\n🎉 UI系统工作正常！\n\n系统信息:\n- Unity版本: {Application.unityVersion}\n- 平台: {Application.platform}\n- 分辨率: {Screen.width}x{Screen.height}";
                statusText.color = Color.green;
            }
            
            // 改变背景颜色
            var background = GetComponentInChildren<Image>();
            if (background != null)
            {
                background.color = Random.ColorHSV();
            }
        }
        
        void Update()
        {
            // 每帧确保UI可见
            if (canvas != null && !canvas.enabled)
            {
                canvas.enabled = true;
                Debug.Log("[SceneTestUI] 重新启用Canvas");
            }
        }
        
        void OnEnable()
        {
            Debug.Log("[SceneTestUI] OnEnable - UI组件已启用");
        }
        
        void OnDisable()
        {
            Debug.Log("[SceneTestUI] OnDisable - UI组件已禁用");
        }
    }
}
