using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DigitalHuman.MVP;
using DigitalHuman.Core.Authentication;
using DigitalHuman.Core.Logging;
using System.Text;
using AuthLogger = DigitalHuman.Core.Logging.ILogger;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 简单状态UI - 显示系统运行状态和功能演示
    /// 为MVP系统提供可视化界面
    /// </summary>
    public class SimpleStatusUI : MonoBehaviour
    {
        [Header("UI组件引用")]
        /// <summary>
        /// 系统状态显示文本
        /// </summary>
        public TextMeshProUGUI systemStatusText;
        
        /// <summary>
        /// 认证状态显示文本
        /// </summary>
        public TextMeshProUGUI authStatusText;
        
        /// <summary>
        /// 日志显示文本
        /// </summary>
        public TextMeshProUGUI logDisplayText;
        
        /// <summary>
        /// 登录按钮
        /// </summary>
        public Button loginButton;
        
        /// <summary>
        /// 登出按钮
        /// </summary>
        public Button logoutButton;
        
        /// <summary>
        /// 刷新状态按钮
        /// </summary>
        public Button refreshButton;

        [Header("配置参数")]
        /// <summary>
        /// 状态更新间隔（秒）
        /// </summary>
        public float statusUpdateInterval = 2f;
        
        /// <summary>
        /// 最大日志显示行数
        /// </summary>
        public int maxLogLines = 10;
        
        /// <summary>
        /// 是否自动滚动日志
        /// </summary>
        public bool autoScrollLog = true;

        // 私有字段
        private EnhancedMVPManager mvpManager;
        private IAuthenticationManager authManager;
        private AuthLogger logger;
        private StringBuilder logBuffer;
        private float lastUpdateTime;

        /// <summary>
        /// 初始化UI组件
        /// </summary>
        void Start()
        {
            InitializeUI();
            SetupEventHandlers();
            StartStatusUpdates();
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 查找MVP管理器
            mvpManager = FindObjectOfType<EnhancedMVPManager>();
            if (mvpManager == null)
            {
                mvpManager = FindObjectOfType<MinimalMVPManager>() as EnhancedMVPManager;
            }

            // 获取认证管理器
            try
            {
                authManager = AuthenticationManager.Instance as IAuthenticationManager;
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[SimpleStatusUI] 无法获取认证管理器: {ex.Message}");
            }

            // 获取日志记录器
            try
            {
                var logManager = LogManager.Instance;
                logger = logManager?.GetLogger("StatusUI");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[SimpleStatusUI] 无法获取日志记录器: {ex.Message}");
            }

            // 初始化日志缓冲区
            logBuffer = new StringBuilder();

            // 设置初始UI状态
            UpdateUIComponents();
            
            logger?.Info("简单状态UI初始化完成");
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 设置按钮事件
            if (loginButton != null)
            {
                loginButton.onClick.AddListener(OnLoginButtonClicked);
            }

            if (logoutButton != null)
            {
                logoutButton.onClick.AddListener(OnLogoutButtonClicked);
            }

            if (refreshButton != null)
            {
                refreshButton.onClick.AddListener(OnRefreshButtonClicked);
            }

            // 订阅认证事件
            if (authManager != null)
            {
                authManager.OnUserLoggedIn += OnUserLoggedIn;
                authManager.OnUserLoggedOut += OnUserLoggedOut;
            }

            // 订阅Unity日志事件
            Application.logMessageReceived += OnLogMessageReceived;
        }

        /// <summary>
        /// 开始状态更新
        /// </summary>
        private void StartStatusUpdates()
        {
            lastUpdateTime = Time.time;
            InvokeRepeating(nameof(UpdateStatus), 0f, statusUpdateInterval);
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus()
        {
            UpdateUIComponents();
            lastUpdateTime = Time.time;
        }

        /// <summary>
        /// 更新UI组件
        /// </summary>
        private void UpdateUIComponents()
        {
            // 更新系统状态
            UpdateSystemStatus();
            
            // 更新认证状态
            UpdateAuthStatus();
            
            // 更新按钮状态
            UpdateButtonStates();
        }

        /// <summary>
        /// 更新系统状态显示
        /// </summary>
        private void UpdateSystemStatus()
        {
            if (systemStatusText == null) return;

            var statusBuilder = new StringBuilder();
            statusBuilder.AppendLine("=== 数字人管理系统状态 ===");
            statusBuilder.AppendLine($"时间: {System.DateTime.Now:HH:mm:ss}");
            statusBuilder.AppendLine();

            if (mvpManager != null)
            {
                if (mvpManager is EnhancedMVPManager enhancedMVP)
                {
                    statusBuilder.AppendLine(enhancedMVP.GetEnhancedSystemStatus());
                }
                else
                {
                    statusBuilder.AppendLine(mvpManager.GetSystemStatus());
                }
            }
            else
            {
                statusBuilder.AppendLine("❌ MVP管理器未找到");
            }

            systemStatusText.text = statusBuilder.ToString();
        }

        /// <summary>
        /// 更新认证状态显示
        /// </summary>
        private void UpdateAuthStatus()
        {
            if (authStatusText == null) return;

            var authBuilder = new StringBuilder();
            authBuilder.AppendLine("=== 认证状态 ===");

            if (authManager != null)
            {
                if (authManager.IsLoggedIn)
                {
                    authBuilder.AppendLine("✅ 已登录");
                    if (authManager.CurrentUser != null)
                    {
                        authBuilder.AppendLine($"用户: {authManager.CurrentUser.DisplayName}");
                        authBuilder.AppendLine($"用户名: {authManager.CurrentUser.Username}");
                        authBuilder.AppendLine($"邮箱: {authManager.CurrentUser.Email}");
                    }
                }
                else
                {
                    authBuilder.AppendLine("❌ 未登录");
                }
            }
            else
            {
                authBuilder.AppendLine("⚠️ 认证系统不可用");
            }

            authStatusText.text = authBuilder.ToString();
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            bool isLoggedIn = authManager?.IsLoggedIn ?? false;

            if (loginButton != null)
            {
                loginButton.interactable = !isLoggedIn && authManager != null;
            }

            if (logoutButton != null)
            {
                logoutButton.interactable = isLoggedIn && authManager != null;
            }

            if (refreshButton != null)
            {
                refreshButton.interactable = true;
            }
        }

        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        private async void OnLoginButtonClicked()
        {
            if (authManager == null) return;

            try
            {
                logger?.Info("用户点击登录按钮");
                
                // 使用演示账户登录
                var result = await authManager.LoginAsync("demo_user", "demo_pass");
                
                if (result.IsSuccess)
                {
                    AddLogMessage($"✅ 登录成功: {result.UserInfo.DisplayName}");
                }
                else
                {
                    AddLogMessage($"❌ 登录失败: {result.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                AddLogMessage($"❌ 登录异常: {ex.Message}");
                logger?.Error($"登录异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 登出按钮点击事件
        /// </summary>
        private async void OnLogoutButtonClicked()
        {
            if (authManager == null) return;

            try
            {
                logger?.Info("用户点击登出按钮");
                
                var result = await authManager.LogoutAsync();
                
                if (result)
                {
                    AddLogMessage("✅ 登出成功");
                }
                else
                {
                    AddLogMessage($"❌ 登出失败");
                }
            }
            catch (System.Exception ex)
            {
                AddLogMessage($"❌ 登出异常: {ex.Message}");
                logger?.Error($"登出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void OnRefreshButtonClicked()
        {
            logger?.Info("用户点击刷新按钮");
            UpdateUIComponents();
            AddLogMessage("🔄 状态已刷新");
        }

        /// <summary>
        /// 用户登录事件处理
        /// </summary>
        private void OnUserLoggedIn(UserInfo userInfo)
        {
            AddLogMessage($"🎉 用户登录: {userInfo.DisplayName}");
            UpdateUIComponents();
        }

        /// <summary>
        /// 用户登出事件处理
        /// </summary>
        private void OnUserLoggedOut()
        {
            AddLogMessage("👋 用户已登出");
            UpdateUIComponents();
        }

        /// <summary>
        /// Unity日志消息处理
        /// </summary>
        private void OnLogMessageReceived(string logString, string stackTrace, LogType type)
        {
            // 只显示来自数字人系统的日志
            if (logString.Contains("[EnhancedMVP]") || logString.Contains("[MinimalMVP]") || 
                logString.Contains("[SimpleStatusUI]"))
            {
                string prefix = type switch
                {
                    LogType.Error => "❌",
                    LogType.Warning => "⚠️",
                    LogType.Log => "ℹ️",
                    _ => "📝"
                };
                
                AddLogMessage($"{prefix} {logString}");
            }
        }

        /// <summary>
        /// 添加日志消息
        /// </summary>
        private void AddLogMessage(string message)
        {
            if (logDisplayText == null) return;

            // 添加时间戳
            string timestampedMessage = $"[{System.DateTime.Now:HH:mm:ss}] {message}";
            
            // 添加到缓冲区
            logBuffer.AppendLine(timestampedMessage);
            
            // 限制日志行数
            var lines = logBuffer.ToString().Split('\n');
            if (lines.Length > maxLogLines)
            {
                logBuffer.Clear();
                for (int i = lines.Length - maxLogLines; i < lines.Length; i++)
                {
                    if (!string.IsNullOrEmpty(lines[i]))
                    {
                        logBuffer.AppendLine(lines[i]);
                    }
                }
            }
            
            // 更新显示
            logDisplayText.text = logBuffer.ToString();
        }

        /// <summary>
        /// 组件销毁时的清理
        /// </summary>
        void OnDestroy()
        {
            // 取消事件订阅
            if (authManager != null)
            {
                authManager.OnUserLoggedIn -= OnUserLoggedIn;
                authManager.OnUserLoggedOut -= OnUserLoggedOut;
            }

            Application.logMessageReceived -= OnLogMessageReceived;
            
            // 停止定时更新
            CancelInvoke();
            
            logger?.Info("简单状态UI销毁");
        }
    }
}
