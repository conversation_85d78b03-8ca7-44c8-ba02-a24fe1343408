using UnityEngine;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 立即UI - 在任何GameObject上都能工作的UI组件
    /// </summary>
    public class ImmediateUI : MonoBehaviour
    {
        private bool uiCreated = false;
        
        void Awake()
        {
            Debug.Log("[ImmediateUI] Awake - 立即创建UI");
            CreateImmediateUI();
        }
        
        void Start()
        {
            Debug.Log("[ImmediateUI] Start - 确保UI存在");
            if (!uiCreated)
            {
                CreateImmediateUI();
            }
        }
        
        void CreateImmediateUI()
        {
            if (uiCreated) return;
            
            try
            {
                Debug.Log("[ImmediateUI] 开始创建立即UI");
                uiCreated = true;
                
                // 直接使用OnGUI，最可靠的方法
                Debug.Log("[ImmediateUI] 立即UI组件已激活，将使用OnGUI显示");
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ImmediateUI] 创建UI失败: {ex.Message}");
            }
        }
        
        void OnGUI()
        {
            // 使用OnGUI绘制，这是最可靠的UI方法
            try
            {
                // 设置背景
                GUI.color = Color.magenta;
                GUI.Box(new Rect(0, 0, Screen.width, Screen.height), "");
                
                // 设置文本样式
                GUI.color = Color.white;
                GUIStyle titleStyle = new GUIStyle(GUI.skin.label);
                titleStyle.fontSize = 48;
                titleStyle.alignment = TextAnchor.MiddleCenter;
                titleStyle.fontStyle = FontStyle.Bold;
                
                // 显示标题
                GUI.Label(new Rect(0, Screen.height * 0.2f, Screen.width, 100), 
                    "🎯 数字人管理系统", titleStyle);
                
                // 显示状态
                GUIStyle statusStyle = new GUIStyle(GUI.skin.label);
                statusStyle.fontSize = 24;
                statusStyle.alignment = TextAnchor.MiddleCenter;
                
                GUI.Label(new Rect(0, Screen.height * 0.4f, Screen.width, 200), 
                    "✅ OnGUI界面已显示！\n\n如果您能看到这个紫红色界面\n说明UI系统终于工作了！\n\n点击下方按钮测试交互", statusStyle);
                
                // 创建按钮
                GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
                buttonStyle.fontSize = 32;
                
                if (GUI.Button(new Rect(Screen.width/2 - 200, Screen.height * 0.7f, 400, 80), "点击测试", buttonStyle))
                {
                    Debug.Log("[ImmediateUI] OnGUI按钮被点击！");
                    
                    // 显示系统信息
                    string systemInfo = $"✅ 按钮点击成功！\n\n" +
                                      $"时间: {System.DateTime.Now:HH:mm:ss}\n" +
                                      $"Unity版本: {Application.unityVersion}\n" +
                                      $"平台: {Application.platform}\n" +
                                      $"分辨率: {Screen.width}x{Screen.height}\n\n" +
                                      $"🎉 UI系统工作正常！";
                    
                    // 更新显示内容
                    statusText = systemInfo;
                }
                
                // 显示系统信息
                if (!string.IsNullOrEmpty(statusText))
                {
                    GUI.Label(new Rect(50, Screen.height * 0.4f, Screen.width - 100, 300), 
                        statusText, statusStyle);
                }
                
                // 显示调试信息
                GUIStyle debugStyle = new GUIStyle(GUI.skin.label);
                debugStyle.fontSize = 16;
                debugStyle.alignment = TextAnchor.UpperLeft;
                
                string debugInfo = $"调试信息:\n" +
                                 $"GameObject: {gameObject.name}\n" +
                                 $"组件激活: {enabled}\n" +
                                 $"帧数: {Time.frameCount}\n" +
                                 $"时间: {Time.time:F1}s";
                
                GUI.Label(new Rect(10, 10, 300, 100), debugInfo, debugStyle);
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ImmediateUI] OnGUI错误: {ex.Message}");
            }
        }
        
        private string statusText = "";
        
        void Update()
        {
            // 每秒输出一次调试信息
            if (Time.frameCount % 60 == 0)
            {
                Debug.Log($"[ImmediateUI] 运行中 - 帧数: {Time.frameCount}, 时间: {Time.time:F1}s");
            }
        }
    }
}
