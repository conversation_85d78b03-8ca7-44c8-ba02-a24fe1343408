#!/bin/bash

echo "=== 验证信息显示系统实现测试 ==="
echo "日期: $(date)"
echo ""

# 检查主要文件是否存在
echo "检查核心文件..."
files=(
    "Assets/Scripts/UI/InfoDisplayManager.cs"
    "Assets/Tests/Editor/InfoDisplayManagerTests.cs"
)

missing_files=0
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_files=$((missing_files + 1))
    fi
done

echo ""
echo "文件检查完成: $missing_files 个文件缺失"

# 检查代码质量
echo ""
echo "检查代码质量..."

# 检查InfoDisplayManager.cs
if [ -f "Assets/Scripts/UI/InfoDisplayManager.cs" ]; then
    echo "分析 InfoDisplayManager.cs:"
    
    # 检查类定义
    if grep -q "public class InfoDisplayManager" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ 类定义正确"
    else
        echo "  ✗ 类定义有问题"
    fi
    
    # 检查关键方法
    methods=("UpdateCurrentDateTime" "UpdateMockWeatherData" "UpdateCurrentSystemInfo" "UpdateTTSPlaybackInfo" "ForceUpdateAll")
    for method in "${methods[@]}"; do
        if grep -q "$method" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ 方法 $method 存在"
        else
            echo "  ✗ 方法 $method 缺失"
        fi
    done
    
    # 检查数据结构
    structures=("DateTimeInfo" "WeatherInfo" "SystemInfo" "TTSPlaybackInfo" "TextSegment")
    for struct in "${structures[@]}"; do
        if grep -q "class $struct\|struct $struct" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ 数据结构 $struct 存在"
        else
            echo "  ✗ 数据结构 $struct 缺失"
        fi
    done
    
    # 检查事件定义
    events=("OnDateTimeUpdated" "OnWeatherUpdated" "OnSystemInfoUpdated" "OnTTSInfoUpdated")
    for event in "${events[@]}"; do
        if grep -q "public event.*$event" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ 事件 $event 存在"
        else
            echo "  ✗ 事件 $event 缺失"
        fi
    done
    
    # 检查协程
    coroutines=("UpdateTimeInfo" "UpdateWeatherInfo" "UpdateSystemInfo" "UpdateTTSHighlight")
    for coroutine in "${coroutines[@]}"; do
        if grep -q "IEnumerator $coroutine" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ 协程 $coroutine 存在"
        else
            echo "  ✗ 协程 $coroutine 缺失"
        fi
    done
fi

# 检查测试文件
echo ""
echo "检查测试文件..."

if [ -f "Assets/Tests/Editor/InfoDisplayManagerTests.cs" ]; then
    test_count=$(grep -c "\[Test\]" "Assets/Tests/Editor/InfoDisplayManagerTests.cs")
    unity_test_count=$(grep -c "\[UnityTest\]" "Assets/Tests/Editor/InfoDisplayManagerTests.cs")
    total_tests=$((test_count + unity_test_count))
    echo "  InfoDisplayManagerTests.cs: $test_count 个测试方法, $unity_test_count 个Unity测试方法 (总计: $total_tests)"
    
    # 检查关键测试
    key_tests=("GetCurrentDateTimeInfo" "GetCurrentWeatherInfo" "GetCurrentSystemInfo" "UpdateTTSPlaybackInfo" "SetCity" "ForceUpdateAll")
    for test in "${key_tests[@]}"; do
        if grep -q "$test.*Test\|$test.*Should" "Assets/Tests/Editor/InfoDisplayManagerTests.cs"; then
            echo "    ✓ $test 测试存在"
        else
            echo "    ✗ $test 测试缺失"
        fi
    done
    
    # 检查数据验证测试
    validation_tests=("WeatherTemperature" "WeatherHumidity" "TTSProgress" "TTSVolume")
    for test in "${validation_tests[@]}"; do
        if grep -q "$test.*Should" "Assets/Tests/Editor/InfoDisplayManagerTests.cs"; then
            echo "    ✓ $test 验证测试存在"
        else
            echo "    ✗ $test 验证测试缺失"
        fi
    done
fi

# 检查功能完整性
echo ""
echo "检查功能完整性..."

if [ -f "Assets/Scripts/UI/InfoDisplayManager.cs" ]; then
    # 检查日期时间功能
    if grep -q "GetChineseWeekday\|GetChineseDate\|GetLunarDate" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ 日期时间本地化功能"
    else
        echo "  ✗ 日期时间本地化功能缺失"
    fi
    
    # 检查天气功能
    if grep -q "FetchWeatherFromAPI\|UpdateMockWeatherData\|GetAirQualityDescription" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ 天气信息功能"
    else
        echo "  ✗ 天气信息功能缺失"
    fi
    
    # 检查系统监控功能
    if grep -q "memoryUsage\|frameRate\|cpuUsage\|networkLatency" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ 系统监控功能"
    else
        echo "  ✗ 系统监控功能缺失"
    fi
    
    # 检查TTS功能
    if grep -q "UpdateTTSTextHighlight\|FormatTime\|TextSegment" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ TTS播放状态和文字高亮功能"
    else
        echo "  ✗ TTS播放状态和文字高亮功能缺失"
    fi
    
    # 检查UI更新功能
    if grep -q "UpdateDateTimeUI\|UpdateWeatherUI\|UpdateSystemInfoUI\|UpdateTTSUI" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ UI更新功能"
    else
        echo "  ✗ UI更新功能缺失"
    fi
    
    # 检查协程管理
    if grep -q "StartUpdateCoroutines\|StopCoroutine" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
        echo "  ✓ 协程生命周期管理"
    else
        echo "  ✗ 协程生命周期管理缺失"
    fi
fi

# 检查数据结构完整性
echo ""
echo "检查数据结构完整性..."

if [ -f "Assets/Scripts/UI/InfoDisplayManager.cs" ]; then
    # 检查DateTimeInfo结构
    datetime_fields=("currentDateTime" "formattedDate" "formattedTime" "weekdayName" "chineseDate" "lunarDate")
    for field in "${datetime_fields[@]}"; do
        if grep -q "$field" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ DateTimeInfo.$field 字段存在"
        else
            echo "  ✗ DateTimeInfo.$field 字段缺失"
        fi
    done
    
    # 检查WeatherInfo结构
    weather_fields=("temperature" "humidity" "windSpeed" "airQuality" "uvIndex")
    for field in "${weather_fields[@]}"; do
        if grep -q "$field" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ WeatherInfo.$field 字段存在"
        else
            echo "  ✗ WeatherInfo.$field 字段缺失"
        fi
    done
    
    # 检查TTSPlaybackInfo结构
    tts_fields=("isPlaying" "isPaused" "currentText" "progress" "volume" "segments")
    for field in "${tts_fields[@]}"; do
        if grep -q "$field" "Assets/Scripts/UI/InfoDisplayManager.cs"; then
            echo "  ✓ TTSPlaybackInfo.$field 字段存在"
        else
            echo "  ✗ TTSPlaybackInfo.$field 字段缺失"
        fi
    done
fi

echo ""
echo "=== 验证完成 ==="

# 生成报告
echo ""
echo "=== 实现总结 ==="
echo "1. 日期时间信息显示"
echo "   - 实时时间更新"
echo "   - 中文日期格式化"
echo "   - 星期名称本地化"
echo "   - 农历日期支持"
echo "   - 多种时间格式"
echo ""
echo "2. 天气信息显示"
echo "   - 当前天气状况"
echo "   - 温度和体感温度"
echo "   - 湿度和风速"
echo "   - 空气质量指数"
echo "   - UV指数显示"
echo "   - 天气API集成支持"
echo "   - 小时和日预报"
echo ""
echo "3. 系统信息监控"
echo "   - CPU使用率监控"
echo "   - 内存使用情况"
echo "   - GPU使用率"
echo "   - 实时帧率显示"
echo "   - 网络延迟检测"
echo "   - 系统版本信息"
echo ""
echo "4. TTS播放状态"
echo "   - 播放进度显示"
echo "   - 文字高亮同步"
echo "   - 播放时间格式化"
echo "   - 音量和语速显示"
echo "   - 文字分段高亮"
echo "   - 播放控制状态"
echo ""
echo "5. 技术特性"
echo "   - 协程异步更新"
echo "   - 事件驱动架构"
echo "   - UI元素动态绑定"
echo "   - 数据验证和范围检查"
echo "   - 生命周期管理"
echo "   - 错误处理机制"
echo ""

if [ $missing_files -eq 0 ]; then
    echo "✅ 任务8.3实现完成: 信息显示系统"
    exit 0
else
    echo "❌ 实现不完整: $missing_files 个文件缺失"
    exit 1
fi