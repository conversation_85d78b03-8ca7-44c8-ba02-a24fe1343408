<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/AuthDemo.uss?fileID=7433441132597879392&amp;guid=PLACEHOLDER&amp;type=3#AuthDemo" />
    
    <!-- 认证演示根容器 -->
    <ui:VisualElement name="auth-demo-root" class="auth-demo-root">
        
        <!-- 顶部标题栏 -->
        <ui:VisualElement name="demo-header" class="demo-header">
            <ui:Label text="数字人认证功能演示" name="demo-title" class="demo-title" />
            <ui:VisualElement name="auth-status-area" class="auth-status-area">
                <ui:VisualElement name="auth-status" class="status-indicator">
                    <ui:VisualElement name="auth-icon" class="status-icon auth-not-logged-in" />
                    <ui:Label text="未登录" name="auth-text" class="status-text" />
                </ui:VisualElement>
                <ui:VisualElement name="user-info" class="user-info" style="display: none;">
                    <ui:Label text="用户名" name="user-display-name" class="user-name" />
                    <ui:Button text="登出" name="logout-button" class="logout-button" />
                </ui:VisualElement>
                <ui:Button text="登录" name="login-button" class="login-button" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 主要内容区域 -->
        <ui:VisualElement name="demo-content" class="demo-content">
            <ui:VisualElement name="demo-info" class="demo-info">
                <ui:Label text="认证功能特性" class="section-title" />
                <ui:VisualElement name="feature-list" class="feature-list">
                    <ui:Label text="✅ 用户登录/登出" class="feature-item" />
                    <ui:Label text="✅ 实时状态显示" class="feature-item" />
                    <ui:Label text="✅ 表单验证" class="feature-item" />
                    <ui:Label text="✅ 错误处理" class="feature-item" />
                    <ui:Label text="✅ 键盘快捷键" class="feature-item" />
                    <ui:Label text="✅ 异步操作" class="feature-item" />
                </ui:VisualElement>
            </ui:VisualElement>
            
            <ui:VisualElement name="demo-instructions" class="demo-instructions">
                <ui:Label text="使用说明" class="section-title" />
                <ui:VisualElement name="instruction-list" class="instruction-list">
                    <ui:Label text="1. 点击右上角'登录'按钮" class="instruction-item" />
                    <ui:Label text="2. 输入测试凭据: admin / admin123" class="instruction-item" />
                    <ui:Label text="3. 观察状态指示器变化" class="instruction-item" />
                    <ui:Label text="4. 测试登出功能" class="instruction-item" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        
    </ui:VisualElement>
    
    <!-- 登录对话框 -->
    <ui:VisualElement name="login-dialog" class="modal-overlay" style="display: none;">
        <ui:VisualElement name="login-window" class="modal-window">
            <ui:VisualElement name="login-header" class="modal-header">
                <ui:Label text="用户登录" class="modal-title" />
                <ui:Button text="×" name="login-close" class="close-button" />
            </ui:VisualElement>
            
            <ui:VisualElement name="login-content" class="modal-content">
                <ui:Label text="请输入您的登录凭据" class="login-description" />
                
                <ui:VisualElement name="login-form" class="login-form">
                    <ui:VisualElement name="form-field" class="form-field">
                        <ui:Label text="用户名" class="field-label" />
                        <ui:TextField placeholder-text="请输入用户名" name="username-input" class="form-input" />
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="form-field" class="form-field">
                        <ui:Label text="密码" class="field-label" />
                        <ui:TextField placeholder-text="请输入密码" name="password-input" class="form-input" password="true" />
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="login-status" class="login-status" style="display: none;">
                        <ui:VisualElement name="status-icon" class="status-icon" />
                        <ui:Label text="" name="login-status-text" class="status-message" />
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="login-actions" class="login-actions">
                        <ui:Button text="登录" name="login-submit" class="login-button primary" />
                        <ui:Button text="取消" name="login-cancel" class="login-button secondary" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:VisualElement name="login-help" class="login-help">
                    <ui:Label text="测试账户: admin / admin123" class="help-text" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
    
</ui:UXML>