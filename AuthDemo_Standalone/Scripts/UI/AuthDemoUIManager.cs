using UnityEngine;
using UnityEngine.UIElements;
using System;
using DigitalHuman.Core.Authentication;

namespace DigitalHuman.UI
{
    /// <summary>
    /// 认证演示UI管理器 - 简化版本，专注于认证功能展示
    /// </summary>
    public class AuthDemoUIManager : MonoBehaviour
    {
        [Header("UI文档")]
        [SerializeField] private UIDocument uiDocument;
        
        // UI元素引用
        private VisualElement rootElement;
        private VisualElement authIcon;
        private Label authText;
        private Button loginButton;
        private Button logoutButton;
        private VisualElement userInfo;
        private Label userDisplayName;
        private VisualElement loginDialog;
        private TextField usernameInput;
        private TextField passwordInput;
        private Button loginSubmit;
        private Button loginCancel;
        private Button loginClose;
        private VisualElement loginStatus;
        private Label loginStatusText;
        
        // 状态属性
        public bool IsUserLoggedIn { get; private set; } = false;
        public UserInfo CurrentUser { get; private set; } = null;
        
        // 认证管理器引用
        private AuthenticationManager authManager;
        
        private void Awake()
        {
            if (uiDocument == null)
                uiDocument = GetComponent<UIDocument>();
        }
        
        private void Start()
        {
            InitializeUI();
            InitializeAuthentication();
            SetupEventHandlers();
        }
        
        /// <summary>
        /// 初始化UI元素引用
        /// </summary>
        private void InitializeUI()
        {
            rootElement = uiDocument.rootVisualElement;
            
            // 获取认证相关UI元素
            authIcon = rootElement.Q<VisualElement>("auth-icon");
            authText = rootElement.Q<Label>("auth-text");
            loginButton = rootElement.Q<Button>("login-button");
            logoutButton = rootElement.Q<Button>("logout-button");
            userInfo = rootElement.Q<VisualElement>("user-info");
            userDisplayName = rootElement.Q<Label>("user-display-name");
            loginDialog = rootElement.Q<VisualElement>("login-dialog");
            usernameInput = rootElement.Q<TextField>("username-input");
            passwordInput = rootElement.Q<TextField>("password-input");
            loginSubmit = rootElement.Q<Button>("login-submit");
            loginCancel = rootElement.Q<Button>("login-cancel");
            loginClose = rootElement.Q<Button>("login-close");
            loginStatus = rootElement.Q<VisualElement>("login-status");
            loginStatusText = rootElement.Q<Label>("login-status-text");
            
            // 设置初始状态
            UpdateAuthenticationUI();
        }
        
        /// <summary>
        /// 初始化认证管理器
        /// </summary>
        private void InitializeAuthentication()
        {
            // 获取认证管理器实例
            authManager = AuthenticationManager.Instance;
            
            if (authManager != null)
            {
                // 订阅认证事件
                authManager.OnUserLoggedIn += OnUserLoggedIn;
                authManager.OnUserLoggedOut += OnUserLoggedOut;
                authManager.OnAuthenticationStatusChanged += OnAuthenticationStatusChanged;
                
                // 检查当前登录状态
                IsUserLoggedIn = authManager.IsLoggedIn;
                CurrentUser = authManager.CurrentUser;
                
                Debug.Log("认证管理器初始化完成");
            }
            else
            {
                Debug.LogWarning("无法获取认证管理器实例");
            }
        }
        
        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 认证相关事件
            loginButton?.RegisterCallback<ClickEvent>(evt => ShowLoginDialog());
            logoutButton?.RegisterCallback<ClickEvent>(evt => HandleLogout());
            loginSubmit?.RegisterCallback<ClickEvent>(evt => HandleLogin());
            loginCancel?.RegisterCallback<ClickEvent>(evt => HideLoginDialog());
            loginClose?.RegisterCallback<ClickEvent>(evt => HideLoginDialog());
            
            // 登录表单键盘事件
            usernameInput?.RegisterCallback<KeyDownEvent>(evt => {
                if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
                {
                    passwordInput?.Focus();
                }
            });
            
            passwordInput?.RegisterCallback<KeyDownEvent>(evt => {
                if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
                {
                    HandleLogin();
                }
            });
        }
        
        /// <summary>
        /// 显示登录对话框
        /// </summary>
        public void ShowLoginDialog()
        {
            if (loginDialog != null)
            {
                loginDialog.style.display = DisplayStyle.Flex;
                usernameInput?.Focus();
                ClearLoginForm();
                HideLoginStatus();
            }
        }
        
        /// <summary>
        /// 隐藏登录对话框
        /// </summary>
        public void HideLoginDialog()
        {
            if (loginDialog != null)
            {
                loginDialog.style.display = DisplayStyle.None;
                ClearLoginForm();
                HideLoginStatus();
            }
        }
        
        /// <summary>
        /// 处理登录操作
        /// </summary>
        private async void HandleLogin()
        {
            string username = usernameInput?.value ?? "";
            string password = passwordInput?.value ?? "";
            
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                ShowLoginStatus("请输入用户名和密码", false);
                return;
            }
            
            ShowLoginStatus("正在登录...", true);
            SetLoginFormEnabled(false);
            
            try
            {
                if (authManager != null)
                {
                    var result = await authManager.LoginAsync(username, password);
                    
                    if (result.IsSuccess)
                    {
                        ShowLoginStatus("登录成功", true);
                        await System.Threading.Tasks.Task.Delay(1000);
                        HideLoginDialog();
                    }
                    else
                    {
                        ShowLoginStatus(result.ErrorMessage ?? "登录失败", false);
                    }
                }
                else
                {
                    ShowLoginStatus("认证服务不可用", false);
                }
            }
            catch (Exception ex)
            {
                ShowLoginStatus($"登录异常: {ex.Message}", false);
                Debug.LogError($"登录异常: {ex}");
            }
            finally
            {
                SetLoginFormEnabled(true);
            }
        }
        
        /// <summary>
        /// 处理登出操作
        /// </summary>
        private async void HandleLogout()
        {
            try
            {
                if (authManager != null)
                {
                    await authManager.LogoutAsync();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"登出异常: {ex}");
            }
        }
        
        /// <summary>
        /// 用户登录成功事件处理
        /// </summary>
        private void OnUserLoggedIn(UserInfo userInfo)
        {
            IsUserLoggedIn = true;
            CurrentUser = userInfo;
            UpdateAuthenticationUI();
            Debug.Log($"用户 {userInfo.Username} 登录成功");
        }
        
        /// <summary>
        /// 用户登出事件处理
        /// </summary>
        private void OnUserLoggedOut()
        {
            IsUserLoggedIn = false;
            CurrentUser = null;
            UpdateAuthenticationUI();
            Debug.Log("用户已登出");
        }
        
        /// <summary>
        /// 认证状态变化事件处理
        /// </summary>
        private void OnAuthenticationStatusChanged(AuthenticationStatus status)
        {
            Debug.Log($"认证状态变化: {status.GetDescription()}");
            UpdateAuthenticationUI();
        }
        
        /// <summary>
        /// 更新认证相关UI
        /// </summary>
        private void UpdateAuthenticationUI()
        {
            if (IsUserLoggedIn && CurrentUser != null)
            {
                // 显示用户信息，隐藏登录按钮
                if (loginButton != null) loginButton.style.display = DisplayStyle.None;
                if (userInfo != null) userInfo.style.display = DisplayStyle.Flex;
                
                if (userDisplayName != null)
                {
                    userDisplayName.text = CurrentUser.DisplayName ?? CurrentUser.Username;
                }
                
                // 更新认证状态指示器
                if (authIcon != null && authText != null)
                {
                    authIcon.RemoveFromClassList("auth-not-logged-in");
                    authIcon.AddToClassList("auth-logged-in");
                    authText.text = "已登录";
                }
            }
            else
            {
                // 显示登录按钮，隐藏用户信息
                if (loginButton != null) loginButton.style.display = DisplayStyle.Flex;
                if (userInfo != null) userInfo.style.display = DisplayStyle.None;
                
                // 更新认证状态指示器
                if (authIcon != null && authText != null)
                {
                    authIcon.RemoveFromClassList("auth-logged-in");
                    authIcon.AddToClassList("auth-not-logged-in");
                    authText.text = "未登录";
                }
            }
        }
        
        /// <summary>
        /// 显示登录状态消息
        /// </summary>
        private void ShowLoginStatus(string message, bool isProcessing)
        {
            if (loginStatus != null && loginStatusText != null)
            {
                loginStatus.style.display = DisplayStyle.Flex;
                loginStatusText.text = message;
                
                loginStatus.RemoveFromClassList("status-success");
                loginStatus.RemoveFromClassList("status-error");
                loginStatus.RemoveFromClassList("status-processing");
                
                if (isProcessing)
                {
                    loginStatus.AddToClassList("status-processing");
                }
                else if (message.Contains("成功"))
                {
                    loginStatus.AddToClassList("status-success");
                }
                else
                {
                    loginStatus.AddToClassList("status-error");
                }
            }
        }
        
        /// <summary>
        /// 隐藏登录状态消息
        /// </summary>
        private void HideLoginStatus()
        {
            if (loginStatus != null)
            {
                loginStatus.style.display = DisplayStyle.None;
            }
        }
        
        /// <summary>
        /// 清空登录表单
        /// </summary>
        private void ClearLoginForm()
        {
            if (usernameInput != null) usernameInput.value = "";
            if (passwordInput != null) passwordInput.value = "";
        }
        
        /// <summary>
        /// 设置登录表单启用状态
        /// </summary>
        private void SetLoginFormEnabled(bool enabled)
        {
            if (usernameInput != null) usernameInput.SetEnabled(enabled);
            if (passwordInput != null) passwordInput.SetEnabled(enabled);
            if (loginSubmit != null) loginSubmit.SetEnabled(enabled);
        }
        
        private void OnDestroy()
        {
            // 清理认证事件订阅
            if (authManager != null)
            {
                authManager.OnUserLoggedIn -= OnUserLoggedIn;
                authManager.OnUserLoggedOut -= OnUserLoggedOut;
                authManager.OnAuthenticationStatusChanged -= OnAuthenticationStatusChanged;
            }
        }
    }
}