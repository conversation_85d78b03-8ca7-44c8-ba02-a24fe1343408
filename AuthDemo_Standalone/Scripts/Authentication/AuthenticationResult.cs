using System;

namespace DigitalHuman.Core.Authentication
{
    /// <summary>
    /// 认证结果数据模型
    /// </summary>
    [Serializable]
    public class AuthenticationResult
    {
        /// <summary>
        /// 认证是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
        
        /// <summary>
        /// 用户信息
        /// </summary>
        public UserInfo UserInfo { get; set; }
        
        /// <summary>
        /// 认证令牌
        /// </summary>
        public string Token { get; set; }
        
        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string RefreshToken { get; set; }
        
        /// <summary>
        /// 令牌过期时间
        /// </summary>
        public DateTime TokenExpiresAt { get; set; }
        
        /// <summary>
        /// 认证时间
        /// </summary>
        public DateTime AuthenticatedAt { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public AuthenticationResult()
        {
            AuthenticatedAt = DateTime.Now;
        }
        
        /// <summary>
        /// 创建成功的认证结果
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <param name="token">认证令牌</param>
        /// <param name="refreshToken">刷新令牌</param>
        /// <param name="expiresAt">过期时间</param>
        /// <returns>认证结果</returns>
        public static AuthenticationResult Success(UserInfo userInfo, string token, string refreshToken = null, DateTime? expiresAt = null)
        {
            return new AuthenticationResult
            {
                IsSuccess = true,
                UserInfo = userInfo,
                Token = token,
                RefreshToken = refreshToken,
                TokenExpiresAt = expiresAt ?? DateTime.Now.AddHours(24)
            };
        }
        
        /// <summary>
        /// 创建失败的认证结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="errorCode">错误代码</param>
        /// <returns>认证结果</returns>
        public static AuthenticationResult Failure(string errorMessage, string errorCode = null)
        {
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }
        
        /// <summary>
        /// 检查令牌是否已过期
        /// </summary>
        /// <returns>是否已过期</returns>
        public bool IsTokenExpired()
        {
            return DateTime.Now >= TokenExpiresAt;
        }
    }
}