using System;

namespace DigitalHuman.Core.Authentication
{
    /// <summary>
    /// 认证状态枚举
    /// </summary>
    public enum AuthenticationStatus
    {
        /// <summary>
        /// 未认证
        /// </summary>
        NotAuthenticated = 0,
        
        /// <summary>
        /// 认证中
        /// </summary>
        Authenticating = 1,
        
        /// <summary>
        /// 已认证
        /// </summary>
        Authenticated = 2,
        
        /// <summary>
        /// 认证失败
        /// </summary>
        AuthenticationFailed = 3,
        
        /// <summary>
        /// 令牌过期
        /// </summary>
        TokenExpired = 4,
        
        /// <summary>
        /// 会话无效
        /// </summary>
        SessionInvalid = 5,
        
        /// <summary>
        /// 刷新令牌中
        /// </summary>
        RefreshingToken = 6,
        
        /// <summary>
        /// 登出中
        /// </summary>
        LoggingOut = 7
    }
    
    /// <summary>
    /// 认证状态扩展方法
    /// </summary>
    public static class AuthenticationStatusExtensions
    {
        /// <summary>
        /// 检查是否为已认证状态
        /// </summary>
        /// <param name="status">认证状态</param>
        /// <returns>是否已认证</returns>
        public static bool IsAuthenticated(this AuthenticationStatus status)
        {
            return status == AuthenticationStatus.Authenticated;
        }
        
        /// <summary>
        /// 检查是否为处理中状态
        /// </summary>
        /// <param name="status">认证状态</param>
        /// <returns>是否处理中</returns>
        public static bool IsProcessing(this AuthenticationStatus status)
        {
            return status == AuthenticationStatus.Authenticating ||
                   status == AuthenticationStatus.RefreshingToken ||
                   status == AuthenticationStatus.LoggingOut;
        }
        
        /// <summary>
        /// 检查是否为错误状态
        /// </summary>
        /// <param name="status">认证状态</param>
        /// <returns>是否为错误状态</returns>
        public static bool IsError(this AuthenticationStatus status)
        {
            return status == AuthenticationStatus.AuthenticationFailed ||
                   status == AuthenticationStatus.TokenExpired ||
                   status == AuthenticationStatus.SessionInvalid;
        }
        
        /// <summary>
        /// 获取状态的中文描述
        /// </summary>
        /// <param name="status">认证状态</param>
        /// <returns>中文描述</returns>
        public static string GetDescription(this AuthenticationStatus status)
        {
            return status switch
            {
                AuthenticationStatus.NotAuthenticated => "未认证",
                AuthenticationStatus.Authenticating => "认证中",
                AuthenticationStatus.Authenticated => "已认证",
                AuthenticationStatus.AuthenticationFailed => "认证失败",
                AuthenticationStatus.TokenExpired => "令牌过期",
                AuthenticationStatus.SessionInvalid => "会话无效",
                AuthenticationStatus.RefreshingToken => "刷新令牌中",
                AuthenticationStatus.LoggingOut => "登出中",
                _ => "未知状态"
            };
        }
    }
}