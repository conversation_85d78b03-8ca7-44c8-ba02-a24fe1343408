using System;
using System.Threading.Tasks;
using UnityEngine;

namespace DigitalHuman.Core.Authentication
{
    public class SimpleAuthManager : MonoBehaviour
    {
        public static SimpleAuthManager Instance { get; private set; }
        
        public bool IsLoggedIn { get; private set; } = false;
        public UserInfo CurrentUser { get; private set; } = null;
        
        public event Action<UserInfo> OnUserLoggedIn;
        public event Action OnUserLoggedOut;
        public event Action<AuthenticationStatus> OnAuthenticationStatusChanged;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        public async Task<AuthenticationResult> LoginAsync(string username, string password)
        {
            // 模拟网络延迟
            await Task.Delay(1000);
            
            if (username == "admin" && password == "admin123")
            {
                var userInfo = new UserInfo
                {
                    UserId = "user_001",
                    Username = username,
                    Email = "<EMAIL>",
                    DisplayName = "管理员"
                };
                
                CurrentUser = userInfo;
                IsLoggedIn = true;
                
                OnUserLoggedIn?.Invoke(userInfo);
                OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.Authenticated);
                
                return AuthenticationResult.Success(userInfo, "demo_token");
            }
            else
            {
                OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.AuthenticationFailed);
                return AuthenticationResult.Failure("用户名或密码错误");
            }
        }
        
        public async Task<bool> LogoutAsync()
        {
            await Task.Delay(100);
            
            CurrentUser = null;
            IsLoggedIn = false;
            
            OnUserLoggedOut?.Invoke();
            OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.NotAuthenticated);
            
            return true;
        }
    }
}
