using System;
using System.Threading.Tasks;

namespace DigitalHuman.Core.Authentication
{
    /// <summary>
    /// 用户认证管理器接口
    /// </summary>
    public interface IAuthenticationManager
    {
        /// <summary>
        /// 当前用户是否已登录
        /// </summary>
        bool IsLoggedIn { get; }
        
        /// <summary>
        /// 当前用户信息
        /// </summary>
        UserInfo CurrentUser { get; }
        
        /// <summary>
        /// 用户登录事件
        /// </summary>
        event Action<UserInfo> OnUserLoggedIn;
        
        /// <summary>
        /// 用户登出事件
        /// </summary>
        event Action OnUserLoggedOut;
        
        /// <summary>
        /// 认证状态变化事件
        /// </summary>
        event Action<AuthenticationStatus> OnAuthenticationStatusChanged;
        
        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录结果</returns>
        Task<AuthenticationResult> LoginAsync(string username, string password);
        
        /// <summary>
        /// 用户登出
        /// </summary>
        /// <returns>登出结果</returns>
        Task<bool> LogoutAsync();
        
        /// <summary>
        /// 刷新认证令牌
        /// </summary>
        /// <returns>刷新结果</returns>
        Task<bool> RefreshTokenAsync();
        
        /// <summary>
        /// 验证当前会话是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        Task<bool> ValidateSessionAsync();
        
        /// <summary>
        /// 获取认证令牌
        /// </summary>
        /// <returns>认证令牌</returns>
        string GetAuthToken();
        
        /// <summary>
        /// 清除认证信息
        /// </summary>
        void ClearAuthenticationData();
    }
}