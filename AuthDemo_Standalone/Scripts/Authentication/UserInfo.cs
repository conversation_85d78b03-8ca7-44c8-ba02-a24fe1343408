using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Authentication
{
    /// <summary>
    /// 用户信息数据模型
    /// </summary>
    [Serializable]
    public class UserInfo
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// 用户邮箱
        /// </summary>
        public string Email { get; set; }
        
        /// <summary>
        /// 用户显示名称
        /// </summary>
        public string DisplayName { get; set; }
        
        /// <summary>
        /// 用户角色
        /// </summary>
        public List<string> Roles { get; set; }
        
        /// <summary>
        /// 用户权限
        /// </summary>
        public List<string> Permissions { get; set; }
        
        /// <summary>
        /// 账户创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime LastLoginAt { get; set; }
        
        /// <summary>
        /// 用户配置文件
        /// </summary>
        public Dictionary<string, object> Profile { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public UserInfo()
        {
            Roles = new List<string>();
            Permissions = new List<string>();
            Profile = new Dictionary<string, object>();
        }
        
        /// <summary>
        /// 检查用户是否具有指定角色
        /// </summary>
        /// <param name="role">角色名称</param>
        /// <returns>是否具有角色</returns>
        public bool HasRole(string role)
        {
            return Roles != null && Roles.Contains(role);
        }
        
        /// <summary>
        /// 检查用户是否具有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否具有权限</returns>
        public bool HasPermission(string permission)
        {
            return Permissions != null && Permissions.Contains(permission);
        }
        
        /// <summary>
        /// 获取用户配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetProfileValue<T>(string key, T defaultValue = default(T))
        {
            if (Profile != null && Profile.ContainsKey(key))
            {
                try
                {
                    return (T)Profile[key];
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }
        
        /// <summary>
        /// 设置用户配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetProfileValue(string key, object value)
        {
            if (Profile == null)
            {
                Profile = new Dictionary<string, object>();
            }
            Profile[key] = value;
        }
    }
}