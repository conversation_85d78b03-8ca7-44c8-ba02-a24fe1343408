# 数字人认证功能演示

## 概述
这是一个独立的认证功能演示，展示了完整的用户登录/登出流程。

## 文件结构
```
AuthDemo_Standalone/
├── Scripts/
│   ├── Authentication/
│   │   ├── SimpleAuthManager.cs      # 简化认证管理器
│   │   ├── UserInfo.cs              # 用户信息模型
│   │   ├── AuthenticationResult.cs  # 认证结果模型
│   │   └── AuthenticationStatus.cs  # 认证状态枚举
│   └── UI/
│       └── AuthDemoUIManager.cs     # 认证演示UI管理器
├── UI/
│   ├── AuthDemo.uxml               # UI布局文件
│   └── AuthDemo.uss                # UI样式文件
└── README.md                       # 说明文档
```

## 功能特性
- ✅ 用户登录/登出
- ✅ 实时状态显示
- ✅ 表单验证
- ✅ 错误处理
- ✅ 键盘快捷键
- ✅ 异步操作

## 测试账户
- 用户名: admin
- 密码: admin123

## 使用方法
1. 将所有文件导入Unity项目
2. 创建新场景
3. 添加UI Document组件并设置AuthDemo.uxml
4. 添加AuthDemoUIManager组件
5. 添加SimpleAuthManager组件
6. 运行场景进行测试

## 测试流程
1. 观察右上角红色"未登录"状态
2. 点击"登录"按钮
3. 输入测试凭据
4. 观察状态变为绿色"已登录"
5. 测试登出功能
