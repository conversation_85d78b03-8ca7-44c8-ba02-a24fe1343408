// 将此脚本放在Assets/Editor/目录下，然后在Unity中执行
using UnityEngine;
using UnityEditor;

public class ExportAuthDemo
{
    [MenuItem("AuthDemo/Export Unity Package")]
    public static void ExportPackage()
    {
        string[] assetPaths = {
            "Assets/AuthDemo_Standalone"
        };
        
        AssetDatabase.ExportPackage(assetPaths, "AuthDemo.unitypackage", 
            ExportPackageOptions.Recurse | ExportPackageOptions.IncludeDependencies);
        
        Debug.Log("认证演示包已导出: AuthDemo.unitypackage");
    }
}
