// 简单的编译测试脚本，只测试认证相关代码
using UnityEngine;
using UnityEditor;
using System.IO;

public class CompileAuthOnly
{
    [MenuItem("Tools/Compile Auth Only")]
    public static void CompileAuthentication()
    {
        Debug.Log("开始编译认证相关代码...");
        
        // 检查关键文件是否存在
        string[] authFiles = {
            "Assets/Scripts/Core/Authentication/AuthenticationManager.cs",
            "Assets/Scripts/Core/Authentication/IAuthenticationManager.cs", 
            "Assets/Scripts/Core/Authentication/Models/UserInfo.cs",
            "Assets/Scripts/Core/Authentication/Models/AuthenticationResult.cs",
            "Assets/Scripts/Core/Authentication/Models/AuthenticationStatus.cs",
            "Assets/Scripts/UI/MainUIManager.cs"
        };
        
        bool allFilesExist = true;
        foreach (string file in authFiles)
        {
            if (!File.Exists(file))
            {
                Debug.LogError($"文件不存在: {file}");
                allFilesExist = false;
            }
            else
            {
                Debug.Log($"✓ {file}");
            }
        }
        
        if (allFilesExist)
        {
            Debug.Log("所有认证相关文件都存在");
            AssetDatabase.Refresh();
            Debug.Log("资源数据库已刷新");
        }
        else
        {
            Debug.LogError("部分文件缺失，无法继续编译");
        }
    }
}