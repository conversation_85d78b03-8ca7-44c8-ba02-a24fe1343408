#!/bin/bash

echo "=== Development分支重构 - 创建可构建的认证应用 ==="
echo ""

# 设置变量
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
BUILD_PATH="./Builds/RefactoredAuthApp"
APP_NAME="DigitalHuman_Authentication"
LOG_FILE="refactor_build.log"

echo "1. 分析当前项目状态..."

# 检查当前分支
current_branch=$(git branch | grep '^\*' | sed 's/^\* //')
echo "当前分支: $current_branch"

if [ "$current_branch" != "development" ]; then
    echo "❌ 请确保在development分支上运行此脚本"
    exit 1
fi

echo "✅ 在development分支上，可以安全进行重构"

echo ""
echo "2. 创建重构备份..."

# 创建重构前的备份
REFACTOR_BACKUP_DIR="./refactor_backup_$(date +%s)"
mkdir -p "$REFACTOR_BACKUP_DIR"

# 备份关键的有问题的文件，但不删除它们
problematic_dirs=(
    "Assets/Scripts/Core/Database"
    "Assets/Scripts/Core/Logging/Writers"
    "Assets/Scripts/Core/Audio"
    "Assets/Scripts/Examples"
    "Assets/Scripts/Core/Camera"
    "Assets/Scripts/Core/Hotword"
    "Assets/Scripts/Core/DataSync"
    "Assets/Scripts/Core/Licensing"
    "Assets/Scripts/Core/Rendering"
    "Assets/Scripts/Core/Network"
    "Assets/Tests.disabled"
)

echo "备份有问题的目录..."
for dir in "${problematic_dirs[@]}"; do
    if [ -d "$dir" ]; then
        cp -r "$dir" "$REFACTOR_BACKUP_DIR/"
        echo "  备份: $dir"
    fi
done

echo "✅ 重构备份完成"

echo ""
echo "3. 修复核心依赖问题..."

# 修复AuthenticationManager中的ILogger冲突
echo "修复AuthenticationManager..."
if [ -f "Assets/Scripts/Core/Authentication/AuthenticationManager.cs" ]; then
    # 确保使用完全限定的ILogger类型
    sed -i '' 's/private ILogger _logger;/private DigitalHuman.Core.Logging.ILogger _logger;/g' "Assets/Scripts/Core/Authentication/AuthenticationManager.cs"
    echo "  ✓ 修复ILogger冲突"
fi

# 修复MainUIManager中可能的依赖问题
echo "检查MainUIManager..."
if [ -f "Assets/Scripts/UI/MainUIManager.cs" ]; then
    echo "  ✓ MainUIManager存在"
fi

echo ""
echo "4. 创建简化的构建脚本..."

# 创建专门的重构构建脚本
cat > "Assets/Scripts/Editor/RefactorBuildScript.cs" << 'EOF'
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.SceneManagement;
using System.IO;

namespace DigitalHuman.Editor
{
    public class RefactorBuildScript
    {
        public static void BuildRefactoredApp()
        {
            Debug.Log("[Refactor] 开始构建重构后的认证应用...");
            
            try
            {
                // 创建重构后的场景
                CreateRefactoredScene();
                
                // 构建设置
                string buildPath = Path.Combine(Application.dataPath, "../Builds/RefactoredAuthApp/DigitalHuman_Authentication.app");
                string[] scenes = { "Assets/Scenes/RefactoredAuthScene.unity" };
                
                // 确保构建目录存在
                string buildDir = Path.GetDirectoryName(buildPath);
                if (!Directory.Exists(buildDir))
                {
                    Directory.CreateDirectory(buildDir);
                }
                
                // 配置播放器设置
                PlayerSettings.productName = "数字人认证系统";
                PlayerSettings.companyName = "DigitalHuman Team";
                PlayerSettings.bundleVersion = "1.0.0-Refactored";
                PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.auth.refactored");
                
                // 分辨率和显示设置
                PlayerSettings.defaultIsNativeResolution = false;
                PlayerSettings.runInBackground = true;
                PlayerSettings.defaultScreenWidth = 1280;
                PlayerSettings.defaultScreenHeight = 720;
                PlayerSettings.resizableWindow = true;
                PlayerSettings.fullScreenMode = FullScreenMode.Windowed;
                
                // 优化设置 - 禁用代码剥离避免问题
                PlayerSettings.stripEngineCode = false;
                PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Disabled);
                
                // 构建选项
                BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = buildPath,
                    target = BuildTarget.StandaloneOSX,
                    options = BuildOptions.Development
                };
                
                // 执行构建
                var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                
                if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    Debug.Log($"[Refactor] 构建成功！位置: {buildPath}");
                    Debug.Log($"[Refactor] 构建大小: {report.summary.totalSize} bytes");
                    EditorUtility.RevealInFinder(buildPath);
                }
                else
                {
                    Debug.LogError($"[Refactor] 构建失败: {report.summary.result}");
                    
                    // 显示详细错误
                    foreach (var step in report.steps)
                    {
                        foreach (var message in step.messages)
                        {
                            if (message.type == UnityEngine.LogType.Error)
                            {
                                Debug.LogError($"[Refactor] 错误: {message.content}");
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Refactor] 构建异常: {ex.Message}");
            }
        }
        
        private static void CreateRefactoredScene()
        {
            Debug.Log("[Refactor] 创建重构后的认证场景...");
            
            // 创建新场景
            var scene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
            
            // 创建主摄像机
            var cameraGO = new GameObject("Main Camera");
            var camera = cameraGO.AddComponent<Camera>();
            camera.backgroundColor = new Color(0.125f, 0.125f, 0.125f, 1f);
            camera.clearFlags = CameraClearFlags.SolidColor;
            cameraGO.tag = "MainCamera";
            cameraGO.transform.position = new Vector3(0, 1, -10);
            
            // 创建UI根对象
            var uiRootGO = new GameObject("UI Root");
            var uiDocument = uiRootGO.AddComponent<UIDocument>();
            
            // 设置UI文档 - 优先使用主UI
            string uiPath = "Assets/UI/Main/MainUI.uxml";
            var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uiPath);
            if (visualTreeAsset != null)
            {
                uiDocument.visualTreeAsset = visualTreeAsset;
                Debug.Log($"[Refactor] 主UI文档设置成功: {uiPath}");
            }
            else
            {
                Debug.LogWarning("[Refactor] 无法加载主UI文档");
            }
            
            // 添加主UI管理器
            try
            {
                var mainUIManager = uiRootGO.AddComponent<DigitalHuman.UI.MainUIManager>();
                Debug.Log("[Refactor] MainUIManager添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Refactor] 无法添加MainUIManager: {ex.Message}");
            }
            
            // 创建认证管理器对象
            var authManagerGO = new GameObject("Authentication Manager");
            try
            {
                var authManager = authManagerGO.AddComponent<DigitalHuman.Core.Authentication.AuthenticationManager>();
                Debug.Log("[Refactor] AuthenticationManager添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Refactor] 无法添加AuthenticationManager: {ex.Message}");
            }
            
            // 添加事件系统
            var eventSystemGO = new GameObject("Event System");
            try
            {
                var eventSystem = eventSystemGO.AddComponent<DigitalHuman.Core.EventSystem>();
                Debug.Log("[Refactor] EventSystem添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Refactor] 无法添加EventSystem: {ex.Message}");
            }
            
            // 添加日志管理器
            var logManagerGO = new GameObject("Log Manager");
            try
            {
                var logManager = logManagerGO.AddComponent<DigitalHuman.Core.Logging.LogManager>();
                Debug.Log("[Refactor] LogManager添加成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Refactor] 无法添加LogManager: {ex.Message}");
            }
            
            // 保存场景
            string scenePath = "Assets/Scenes/RefactoredAuthScene.unity";
            EditorSceneManager.SaveScene(scene, scenePath);
            
            Debug.Log($"[Refactor] 重构后的认证场景已创建: {scenePath}");
        }
    }
}
EOF

echo "✅ 重构构建脚本已创建"

echo ""
echo "5. 尝试编译检查..."

# 先尝试快速编译检查
echo "检查项目编译状态..."
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "." \
    -logFile "compile_check.log" \
    -executeMethod UnityEditor.EditorApplication.Exit

COMPILE_CHECK_EXIT_CODE=$?

if [ $COMPILE_CHECK_EXIT_CODE -eq 0 ]; then
    echo "✅ 项目编译检查通过"
    
    echo ""
    echo "6. 开始构建重构后的应用..."
    
    # 执行构建
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "." \
        -buildTarget StandaloneOSX \
        -logFile "$LOG_FILE" \
        -executeMethod RefactorBuildScript.BuildRefactoredApp
    
    BUILD_EXIT_CODE=$?
    
    echo ""
    if [ $BUILD_EXIT_CODE -eq 0 ]; then
        echo "🎉 重构后的认证应用构建成功！"
        
        if [ -d "$BUILD_PATH/$APP_NAME.app" ]; then
            echo ""
            echo "📱 应用信息:"
            echo "   - 名称: 数字人认证系统 (重构版)"
            echo "   - 路径: $BUILD_PATH/$APP_NAME.app"
            
            APP_SIZE=$(du -sh "$BUILD_PATH/$APP_NAME.app" | cut -f1)
            echo "   - 大小: $APP_SIZE"
            
            echo ""
            echo "🧪 认证功能测试:"
            echo "   1. 双击应用启动"
            echo "   2. 观察右上角认证状态指示器"
            echo "   3. 点击'登录'按钮"
            echo "   4. 输入测试凭据: admin / admin123"
            echo "   5. 验证登录状态变化（红色→绿色）"
            echo "   6. 查看用户名显示"
            echo "   7. 测试登出功能"
            
            echo ""
            echo "🚀 启动应用:"
            echo "   open '$BUILD_PATH/$APP_NAME.app'"
            
            # 询问是否立即运行
            read -p "是否立即启动重构后的应用？(y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                echo "🚀 启动重构后的认证应用..."
                open "$BUILD_PATH/$APP_NAME.app"
                echo ""
                echo "✨ 应用已启动！"
                echo ""
                echo "🎯 这是重构后的单一.app文件！"
                echo "   - 修复了编译问题"
                echo "   - 保留了完整的认证功能"
                echo "   - 代码结构更加清晰"
            fi
            
            echo ""
            echo "📁 最终应用文件位置:"
            echo "   $BUILD_PATH/$APP_NAME.app"
            echo ""
            echo "🎊 重构成功！您现在拥有一个可工作的认证应用！"
            
        else
            echo "❌ 构建文件未找到"
        fi
    else
        echo "❌ 构建失败，退出代码: $BUILD_EXIT_CODE"
        echo ""
        echo "📋 查看构建日志:"
        echo "   cat $LOG_FILE"
        
        # 显示最后的错误信息
        if [ -f "$LOG_FILE" ]; then
            echo ""
            echo "最近的错误信息:"
            tail -20 "$LOG_FILE" | grep -i error || echo "未找到明显错误"
        fi
    fi
else
    echo "❌ 项目编译检查失败"
    echo ""
    echo "需要进一步修复编译问题..."
    echo "查看编译日志: cat compile_check.log"
    
    # 显示编译错误
    if [ -f "compile_check.log" ]; then
        echo ""
        echo "编译错误摘要:"
        grep -i "error CS" compile_check.log | head -10 || echo "未找到CS编译错误"
    fi
fi

echo ""
echo "7. 重构总结..."
echo ""
echo "✅ 已完成的工作:"
echo "   - 创建了development分支"
echo "   - 备份了当前实现"
echo "   - 修复了已知的依赖问题"
echo "   - 创建了重构构建脚本"
echo ""
echo "📁 备份位置:"
echo "   - Git标签: auth-implementation-backup"
echo "   - 文件备份: $REFACTOR_BACKUP_DIR"
echo ""
echo "🔄 下一步建议:"
if [ $COMPILE_CHECK_EXIT_CODE -eq 0 ] && [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo "   - 重构成功！可以继续开发新功能"
    echo "   - 考虑逐步恢复其他功能模块"
else
    echo "   - 需要进一步修复编译问题"
    echo "   - 可以考虑更激进的重构方案"
fi

echo ""
echo "=== 重构完成 ==="