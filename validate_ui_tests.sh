#!/bin/bash

echo "=== 验证用户界面和信息显示系统测试 ==="
echo "日期: $(date)"
echo ""

# 检查主要文件是否存在
echo "检查核心文件..."
files=(
    "Assets/Scripts/UI/MainUIManager.cs"
    "Assets/Scripts/UI/DisplayAdapter.cs"
    "Assets/Tests/Editor/MainUIManagerTests.cs"
    "Assets/Tests/Editor/DisplayAdapterTests.cs"
    "Assets/UI/Main/MainUI.uxml"
    "Assets/UI/Main/MainUI.uss"
)

missing_files=0
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_files=$((missing_files + 1))
    fi
done

echo ""
echo "文件检查完成: $missing_files 个文件缺失"

# 检查代码质量
echo ""
echo "检查代码质量..."

# 检查MainUIManager.cs
if [ -f "Assets/Scripts/UI/MainUIManager.cs" ]; then
    echo "分析 MainUIManager.cs:"
    
    # 检查类定义
    if grep -q "public class MainUIManager" "Assets/Scripts/UI/MainUIManager.cs"; then
        echo "  ✓ 类定义正确"
    else
        echo "  ✗ 类定义有问题"
    fi
    
    # 检查关键方法
    methods=("ShowPage" "UpdateNetworkStatus" "UpdateActivationStatus" "AdaptToDisplay")
    for method in "${methods[@]}"; do
        if grep -q "public.*$method" "Assets/Scripts/UI/MainUIManager.cs"; then
            echo "  ✓ 方法 $method 存在"
        else
            echo "  ✗ 方法 $method 缺失"
        fi
    done
    
    # 检查事件定义
    events=("OnPageChanged" "OnStartChat" "OnOpenVoiceSettings")
    for event in "${events[@]}"; do
        if grep -q "public event.*$event" "Assets/Scripts/UI/MainUIManager.cs"; then
            echo "  ✓ 事件 $event 存在"
        else
            echo "  ✗ 事件 $event 缺失"
        fi
    done
fi

# 检查DisplayAdapter.cs
if [ -f "Assets/Scripts/UI/DisplayAdapter.cs" ]; then
    echo ""
    echo "分析 DisplayAdapter.cs:"
    
    # 检查类定义
    if grep -q "public class DisplayAdapter" "Assets/Scripts/UI/DisplayAdapter.cs"; then
        echo "  ✓ 类定义正确"
    else
        echo "  ✗ 类定义有问题"
    fi
    
    # 检查关键方法
    methods=("DetectAndAdaptDisplay" "SetUIScale" "GetRecommendedUIScale")
    for method in "${methods[@]}"; do
        if grep -q "public.*$method" "Assets/Scripts/UI/DisplayAdapter.cs"; then
            echo "  ✓ 方法 $method 存在"
        else
            echo "  ✗ 方法 $method 缺失"
        fi
    done
    
    # 检查DisplayInfo结构
    if grep -q "struct DisplayInfo" "Assets/Scripts/UI/DisplayAdapter.cs"; then
        echo "  ✓ DisplayInfo 结构存在"
    else
        echo "  ✗ DisplayInfo 结构缺失"
    fi
fi

# 检查测试文件
echo ""
echo "检查测试文件..."

if [ -f "Assets/Tests/Editor/MainUIManagerTests.cs" ]; then
    test_count=$(grep -c "\[Test\]" "Assets/Tests/Editor/MainUIManagerTests.cs")
    echo "  MainUIManagerTests.cs: $test_count 个测试方法"
fi

if [ -f "Assets/Tests/Editor/DisplayAdapterTests.cs" ]; then
    test_count=$(grep -c "\[Test\]" "Assets/Tests/Editor/DisplayAdapterTests.cs")
    echo "  DisplayAdapterTests.cs: $test_count 个测试方法"
fi

# 检查UI文件
echo ""
echo "检查UI文件..."

if [ -f "Assets/UI/Main/MainUI.uxml" ]; then
    # 检查关键UI元素
    ui_elements=("main-root" "top-navigation" "main-content" "welcome-page" "activation-dialog")
    for element in "${ui_elements[@]}"; do
        if grep -q "name=\"$element\"" "Assets/UI/Main/MainUI.uxml"; then
            echo "  ✓ UI元素 $element 存在"
        else
            echo "  ✗ UI元素 $element 缺失"
        fi
    done
fi

if [ -f "Assets/UI/Main/MainUI.uss" ]; then
    # 检查关键样式类
    css_classes=("main-root" "nav-button" "action-button" "modal-overlay")
    for class in "${css_classes[@]}"; do
        if grep -q "\.$class" "Assets/UI/Main/MainUI.uss"; then
            echo "  ✓ CSS类 $class 存在"
        else
            echo "  ✗ CSS类 $class 缺失"
        fi
    done
fi

echo ""
echo "=== 验证完成 ==="

# 生成报告
echo ""
echo "=== 实现总结 ==="
echo "1. 主界面管理器 (MainUIManager)"
echo "   - 页面导航和切换"
echo "   - 状态指示器管理"
echo "   - 系统信息显示"
echo "   - 激活对话框"
echo "   - 响应式布局支持"
echo ""
echo "2. 显示适配器 (DisplayAdapter)"
echo "   - 自动检测显示设备"
echo "   - UI缩放适配"
echo "   - 大屏和竖屏优化"
echo "   - 质量设置调整"
echo ""
echo "3. UI界面设计"
echo "   - 现代化深色主题"
echo "   - 响应式布局"
echo "   - 多设备适配"
echo "   - 激活对话框"
echo ""
echo "4. 测试覆盖"
echo "   - 单元测试"
echo "   - 功能测试"
echo "   - 边界条件测试"
echo ""

if [ $missing_files -eq 0 ]; then
    echo "✅ 任务8.1实现完成: 主界面和导航系统"
    exit 0
else
    echo "❌ 实现不完整: $missing_files 个文件缺失"
    exit 1
fi