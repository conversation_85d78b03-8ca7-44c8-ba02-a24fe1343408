# Unity项目相关
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
[Ll]ogs/
[Uu]ser[Ss]ettings/

# 自动生成的文件
*.pidb
*.booproj
*.tmp
*.user
*.userprefs
*.unityproj
*.DS_Store

# Unity编辑器相关
.vs/
ExportedObj/
.consulo/
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db

# Unity3D生成的meta文件
*.pidb.meta
*.pdb.meta
*.mdb.meta

# Unity3D生成的可执行文件
*.unitypackage
*.app

# Crashlytics生成的符号
crashlytics-build.properties

# 本地配置文件
/[Aa]ssets/StreamingAssets/Config/
/[Aa]ssets/StreamingAssets/Logs/
/[Aa]ssets/StreamingAssets/Data/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
/Logs/
/AuditLogs/

# 缓存文件
/Cache/
/[Tt]emp/
*.cache

# 敏感配置文件
appsettings.local.json
secrets.json
*.key
*.pem
*.p12

# 资源缓存
/[Aa]ssets/Resources/Cache/
/[Aa]ssets/AddressableAssetsData/*/addressables_content_state.bin

# 平台特定文件
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# macOS
.DS_Store
.AppleDouble
.LSOverride

# IDE相关
# Visual Studio
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# Rider
.idea/
*.sln.iml

# VSCode
.vscode/

# 第三方插件
/[Aa]ssets/Plugins/Android/*.aar
/[Aa]ssets/Plugins/iOS/

# 音频缓存
/AudioCache/
*.wav.cache
*.mp3.cache

# 视频缓存
/VideoCache/
*.mp4.cache

# AI模型缓存
/ModelCache/
*.model

# 备份文件
*.backup
*.bak
*~

# 操作系统文件
*.swp
*.swo
*~
.directory

# 网络测试文件
network_test_*
api_test_*

# 性能测试结果
/PerformanceTests/Results/
*.profiler

# 安全测试文件
security_test_*
penetration_test_*

# 构建输出
/Output/
/Distribution/
/Builds/
*.zip
*.rar
*.7z

# 构建日志
build_*.log
build_test.log
build_mvp.log

# 构建临时文件
BuildReport/
BuildCache/

# 用户生成内容
/UserData/
/UserProfiles/
/ChatHistory/

# 临时文件
*.tmp.*
temp_*
/tmp/ 

# .kiro
# .kiro/

# cursor
.cursor/

# vscode 文件

# 文档文件夹（开发文档，不纳入版本控制）
# docs/
