#!/bin/bash

# 摄像头交互和情感响应系统验证脚本

echo "=== 摄像头交互和情感响应系统验证 ==="
echo "验证时间: $(date)"
echo ""

# 检查核心实现文件
echo "1. 检查核心实现文件..."
files=(
    "Assets/Scripts/Core/Camera/CameraManager.cs"
    "Assets/Scripts/Core/Camera/FaceDetector.cs"
    "Assets/Scripts/Core/Network/EmotionApiClient.cs"
    "Assets/Scripts/Core/Rendering/EmotionResponseController.cs"
    "Assets/Scripts/Core/Camera/ICameraManager.cs"
    "Assets/Scripts/Core/Camera/IFaceDetector.cs"
    "Assets/Scripts/Core/Network/IEmotionApiClient.cs"
    "Assets/Scripts/Core/Rendering/IEmotionResponseController.cs"
)

missing_files=()
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_files+=("$file")
    fi
done

# 检查测试文件
echo ""
echo "2. 检查测试文件..."
test_files=(
    "Assets/Tests/Editor/CameraManagerTests.cs"
    "Assets/Tests/Editor/FaceDetectorTests.cs"
    "Assets/Tests/Editor/EmotionApiClientTests.cs"
    "Assets/Tests/Editor/EmotionResponseControllerTests.cs"
)

missing_test_files=()
for file in "${test_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_test_files+=("$file")
    fi
done

# 检查模型文件
echo ""
echo "3. 检查数据模型文件..."
model_files=(
    "Assets/Scripts/Core/Camera/Models/CameraModels.cs"
    "Assets/Scripts/Core/Network/Models/EmotionModels.cs"
    "Assets/Scripts/Core/Rendering/Models/RenderingModels.cs"
)

missing_model_files=()
for file in "${model_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_model_files+=("$file")
    fi
done

# 检查示例文件
echo ""
echo "4. 检查示例文件..."
example_files=(
    "Assets/Scripts/Examples/CameraSystemExample.cs"
    "Assets/Scripts/Examples/EmotionInteractionExample.cs"
)

missing_example_files=()
for file in "${example_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_example_files+=("$file")
    fi
done

# 检查关键功能实现
echo ""
echo "5. 检查关键功能实现..."

# 检查摄像头管理器功能
echo "5.1 摄像头管理器功能:"
if grep -q "RequestCameraPermission" Assets/Scripts/Core/Camera/CameraManager.cs 2>/dev/null; then
    echo "✓ 摄像头权限请求"
else
    echo "✗ 摄像头权限请求 (未实现)"
fi

if grep -q "StartCapture" Assets/Scripts/Core/Camera/CameraManager.cs 2>/dev/null; then
    echo "✓ 视频捕获功能"
else
    echo "✗ 视频捕获功能 (未实现)"
fi

if grep -q "DetectCameraDevices" Assets/Scripts/Core/Camera/CameraManager.cs 2>/dev/null; then
    echo "✓ 摄像头设备检测"
else
    echo "✗ 摄像头设备检测 (未实现)"
fi

# 检查面部检测器功能
echo ""
echo "5.2 面部检测器功能:"
if grep -q "ProcessFrame" Assets/Scripts/Core/Camera/FaceDetector.cs 2>/dev/null; then
    echo "✓ 视频帧处理"
else
    echo "✗ 视频帧处理 (未实现)"
fi

if grep -q "DetectFacesInFrame" Assets/Scripts/Core/Camera/FaceDetector.cs 2>/dev/null; then
    echo "✓ 面部检测算法"
else
    echo "✗ 面部检测算法 (未实现)"
fi

if grep -q "UpdateFaceTracking" Assets/Scripts/Core/Camera/FaceDetector.cs 2>/dev/null; then
    echo "✓ 面部跟踪功能"
else
    echo "✗ 面部跟踪功能 (未实现)"
fi

# 检查情感分析API客户端功能
echo ""
echo "5.3 情感分析API客户端功能:"
if grep -q "AnalyzeEmotionAsync" Assets/Scripts/Core/Network/EmotionApiClient.cs 2>/dev/null; then
    echo "✓ 异步情感分析"
else
    echo "✗ 异步情感分析 (未实现)"
fi

if grep -q "TestConnectionAsync" Assets/Scripts/Core/Network/EmotionApiClient.cs 2>/dev/null; then
    echo "✓ 连接测试功能"
else
    echo "✗ 连接测试功能 (未实现)"
fi

if grep -q "emotionCache" Assets/Scripts/Core/Network/EmotionApiClient.cs 2>/dev/null; then
    echo "✓ 结果缓存机制"
else
    echo "✗ 结果缓存机制 (未实现)"
fi

# 检查情感响应控制器功能
echo ""
echo "5.4 情感响应控制器功能:"
if grep -q "ProcessEmotion" Assets/Scripts/Core/Rendering/EmotionResponseController.cs 2>/dev/null; then
    echo "✓ 情感数据处理"
else
    echo "✗ 情感数据处理 (未实现)"
fi

if grep -q "SetEmotion" Assets/Scripts/Core/Rendering/EmotionResponseController.cs 2>/dev/null; then
    echo "✓ 情感状态设置"
else
    echo "✗ 情感状态设置 (未实现)"
fi

if grep -q "PlayEmotionAnimation" Assets/Scripts/Core/Rendering/EmotionResponseController.cs 2>/dev/null; then
    echo "✓ 情感动画播放"
else
    echo "✗ 情感动画播放 (未实现)"
fi

# 检查需求覆盖情况
echo ""
echo "6. 检查需求覆盖情况..."

# 需求 4.1, 4.2, 4.6 - 摄像头视频捕获
echo "6.1 需求 4.1, 4.2, 4.6 (摄像头视频捕获):"
if grep -q "RequestCameraPermission\|StartCapture\|DetectCameraDevices" Assets/Scripts/Core/Camera/CameraManager.cs 2>/dev/null; then
    echo "✓ 摄像头权限请求和视频流捕获已实现"
else
    echo "✗ 摄像头权限请求和视频流捕获未完全实现"
fi

# 需求 4.3, 4.4, 4.5 - 面部检测和跟踪
echo ""
echo "6.2 需求 4.3, 4.4, 4.5 (面部检测和跟踪):"
if grep -q "DetectFacesInFrame\|UpdateFaceTracking\|CheckUserPresence" Assets/Scripts/Core/Camera/FaceDetector.cs 2>/dev/null; then
    echo "✓ 面部检测和跟踪功能已实现"
else
    echo "✗ 面部检测和跟踪功能未完全实现"
fi

# 需求 19.1, 19.2, 19.5, 19.6 - 情感分析服务
echo ""
echo "6.3 需求 19.1, 19.2, 19.5, 19.6 (情感分析服务):"
if grep -q "AnalyzeEmotionAsync\|EmotionAnalysisConfig\|enabled" Assets/Scripts/Core/Network/EmotionApiClient.cs 2>/dev/null; then
    echo "✓ 情感分析服务集成已实现"
else
    echo "✗ 情感分析服务集成未完全实现"
fi

# 需求 19.2, 19.3, 19.4 - 情感响应动画
echo ""
echo "6.4 需求 19.2, 19.3, 19.4 (情感响应动画):"
if grep -q "ProcessEmotion\|SetEmotion\|PlayEmotionAnimation" Assets/Scripts/Core/Rendering/EmotionResponseController.cs 2>/dev/null; then
    echo "✓ 情感响应动画控制已实现"
else
    echo "✗ 情感响应动画控制未完全实现"
fi

# 生成总结报告
echo ""
echo "=== 验证总结 ==="
total_files=$((${#files[@]} + ${#test_files[@]} + ${#model_files[@]} + ${#example_files[@]}))
missing_total=$((${#missing_files[@]} + ${#missing_test_files[@]} + ${#missing_model_files[@]} + ${#missing_example_files[@]}))
present_files=$((total_files - missing_total))

echo "文件完整性: $present_files/$total_files 个文件存在"

if [ ${#missing_files[@]} -eq 0 ] && [ ${#missing_test_files[@]} -eq 0 ] && [ ${#missing_model_files[@]} -eq 0 ] && [ ${#missing_example_files[@]} -eq 0 ]; then
    echo "✓ 所有必需文件都已存在"
    echo "✓ 摄像头交互和情感响应系统实现完整"
    echo ""
    echo "子任务状态:"
    echo "✓ 6.1 实现摄像头视频捕获 - 已完成"
    echo "✓ 6.2 实现面部检测和跟踪 - 已完成"
    echo "✓ 6.3 集成情感分析服务 - 已完成"
    echo "✓ 6.4 实现情感响应动画 - 已完成"
    echo ""
    echo "任务 6 摄像头交互和情感响应系统 - 实现完成 ✓"
else
    echo "✗ 存在缺失文件，需要补充实现"
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo "缺失核心文件: ${missing_files[*]}"
    fi
    if [ ${#missing_test_files[@]} -gt 0 ]; then
        echo "缺失测试文件: ${missing_test_files[*]}"
    fi
    if [ ${#missing_model_files[@]} -gt 0 ]; then
        echo "缺失模型文件: ${missing_model_files[*]}"
    fi
    if [ ${#missing_example_files[@]} -gt 0 ]; then
        echo "缺失示例文件: ${missing_example_files[*]}"
    fi
fi

echo ""
echo "验证完成时间: $(date)"