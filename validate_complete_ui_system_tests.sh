#!/bin/bash

# 完整UI系统测试验证脚本
# 验证所有UI相关组件的测试覆盖率和质量

echo "=== 完整UI系统测试验证 ==="
echo "验证时间: $(date)"
echo ""

# UI组件列表
declare -a ui_components=(
    "MainUIManager:Assets/Scripts/UI/MainUIManager.cs:Assets/Tests/Editor/MainUIManagerTests.cs"
    "ChatUIManager:Assets/Scripts/UI/ChatUIManager.cs:Assets/Tests/Editor/ChatUIManagerTests.cs"
    "InfoDisplayManager:Assets/Scripts/UI/InfoDisplayManager.cs:Assets/Tests/Editor/InfoDisplayManagerTests.cs"
    "MediaContentManager:Assets/Scripts/UI/MediaContentManager.cs:Assets/Tests/Editor/MediaContentManagerTests.cs"
    "DisplayAdapter:Assets/Scripts/UI/DisplayAdapter.cs:Assets/Tests/Editor/DisplayAdapterTests.cs"
    "SettingsUIManager:Assets/Scripts/UI/Settings/SettingsUIManager.cs:Assets/Tests/Editor/SettingsUIManagerTests.cs"
)

total_components=${#ui_components[@]}
passed_components=0
total_test_methods=0

echo "检查 $total_components 个UI组件的测试覆盖..."
echo ""

# 检查每个组件
for component_info in "${ui_components[@]}"; do
    IFS=':' read -r component_name source_file test_file <<< "$component_info"
    
    echo "检查组件: $component_name"
    echo "├─ 源文件: $source_file"
    echo "├─ 测试文件: $test_file"
    
    # 检查源文件是否存在
    if [ -f "$source_file" ]; then
        echo "├─ ✓ 源文件存在"
    else
        echo "├─ ✗ 源文件不存在"
        continue
    fi
    
    # 检查测试文件是否存在
    if [ -f "$test_file" ]; then
        echo "├─ ✓ 测试文件存在"
    else
        echo "├─ ✗ 测试文件不存在"
        continue
    fi
    
    # 统计测试方法数量
    test_methods=$(grep -c "\[Test\]" "$test_file" 2>/dev/null || echo "0")
    echo "├─ 测试方法数量: $test_methods"
    total_test_methods=$((total_test_methods + test_methods))
    
    # 检查Assert数量
    assert_count=$(grep -c "Assert\." "$test_file" 2>/dev/null || echo "0")
    echo "├─ Assert断言数量: $assert_count"
    
    # 检查中文注释
    chinese_comments=$(grep -c "/// .*" "$test_file" 2>/dev/null || echo "0")
    echo "├─ 中文注释数量: $chinese_comments"
    
    # 评估覆盖率
    if [ $test_methods -ge 10 ]; then
        echo "└─ ✓ 覆盖率评估: 良好"
        passed_components=$((passed_components + 1))
    elif [ $test_methods -ge 5 ]; then
        echo "└─ ⚠ 覆盖率评估: 一般"
        passed_components=$((passed_components + 1))
    else
        echo "└─ ✗ 覆盖率评估: 需要改进"
    fi
    
    echo ""
done

# 检查UI相关的UXML和USS文件
echo "检查UI资源文件..."
uxml_files=$(find Assets/UI -name "*.uxml" 2>/dev/null | wc -l)
uss_files=$(find Assets/UI -name "*.uss" 2>/dev/null | wc -l)
echo "├─ UXML文件数量: $uxml_files"
echo "├─ USS文件数量: $uss_files"

# 检查UI资源文件是否存在
ui_resources=(
    "Assets/UI/Main/MainUI.uxml"
    "Assets/UI/Main/MainUI.uss"
    "Assets/UI/Chat/ChatUI.uxml"
    "Assets/UI/Chat/ChatUI.uss"
    "Assets/UI/Settings/SettingsUI.uxml"
    "Assets/UI/Settings/SettingsUI.uss"
)

missing_resources=0
for resource in "${ui_resources[@]}"; do
    if [ -f "$resource" ]; then
        echo "├─ ✓ $resource"
    else
        echo "├─ ✗ $resource (缺失)"
        missing_resources=$((missing_resources + 1))
    fi
done
echo ""

# 检查测试运行器集成
echo "检查测试运行器集成..."
if grep -q "RunAllUITests" Assets/Tests/Editor/TestRunner.cs; then
    echo "✓ UI系统综合测试已集成"
else
    echo "✗ UI系统综合测试未集成"
fi

ui_test_methods=(
    "RunMainUIManagerTests"
    "RunChatUIManagerTests"
    "RunInfoDisplayManagerTests"
    "RunMediaContentManagerTests"
    "RunDisplayAdapterTests"
    "RunSettingsUIManagerTests"
)

integrated_tests=0
for test_method in "${ui_test_methods[@]}"; do
    if grep -q "$test_method" Assets/Tests/Editor/TestRunner.cs; then
        echo "├─ ✓ $test_method"
        integrated_tests=$((integrated_tests + 1))
    else
        echo "├─ ✗ $test_method (未集成)"
    fi
done
echo ""

# 检查快速验证集成
echo "检查快速验证集成..."
ui_validation_methods=(
    "ValidateMainUIManagerBasicFunctionality"
    "ValidateChatUIManagerBasicFunctionality"
    "ValidateInfoDisplayManagerBasicFunctionality"
    "ValidateMediaContentManagerBasicFunctionality"
    "ValidateDisplayAdapterBasicFunctionality"
)

integrated_validations=0
for validation_method in "${ui_validation_methods[@]}"; do
    if grep -q "$validation_method" Assets/Tests/Editor/QuickValidationTests.cs; then
        echo "├─ ✓ $validation_method"
        integrated_validations=$((integrated_validations + 1))
    else
        echo "├─ ⚠ $validation_method (可选)"
    fi
done
echo ""

# 生成总体报告
echo "=== 总体统计 ==="
echo "UI组件总数: $total_components"
echo "测试覆盖组件数: $passed_components"
echo "总测试方法数: $total_test_methods"
echo "UI资源文件缺失: $missing_resources"
echo "测试运行器集成: $integrated_tests/${#ui_test_methods[@]}"
echo "快速验证集成: $integrated_validations/${#ui_validation_methods[@]}"
echo ""

# 计算覆盖率百分比
coverage_percentage=$((passed_components * 100 / total_components))
echo "UI系统测试覆盖率: $coverage_percentage%"

# 评估整体质量
if [ $coverage_percentage -ge 90 ] && [ $missing_resources -eq 0 ] && [ $integrated_tests -ge 4 ]; then
    echo "整体评估: 优秀 ✅"
    quality_score="A"
elif [ $coverage_percentage -ge 80 ] && [ $missing_resources -le 2 ] && [ $integrated_tests -ge 3 ]; then
    echo "整体评估: 良好 ✅"
    quality_score="B"
elif [ $coverage_percentage -ge 60 ] && [ $integrated_tests -ge 2 ]; then
    echo "整体评估: 一般 ⚠️"
    quality_score="C"
else
    echo "整体评估: 需要改进 ❌"
    quality_score="D"
fi

echo ""
echo "=== 详细建议 ==="

if [ $missing_resources -gt 0 ]; then
    echo "🔧 需要创建缺失的UI资源文件"
fi

if [ $integrated_tests -lt ${#ui_test_methods[@]} ]; then
    echo "🔧 需要完善测试运行器集成"
fi

if [ $total_test_methods -lt 50 ]; then
    echo "🔧 建议增加更多测试用例以提高覆盖率"
fi

echo "🎯 建议运行以下测试验证："
echo "   1. Unity编辑器: DigitalHuman/Tests/Run All UI Tests"
echo "   2. 快速验证: DigitalHuman/Tests/Run All Quick Validation Tests"
echo "   3. 单独验证: ./validate_main_ui_manager_tests.sh"

echo ""
echo "=== 验证完成 ==="
echo "质量评分: $quality_score"
echo "推荐下一步: 运行实际测试验证功能正确性"