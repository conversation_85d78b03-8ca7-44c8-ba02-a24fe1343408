#!/bin/bash

echo "🔍 验证Mac开发环境..."

# 检查Unity Hub
if command -v /Applications/Unity\ Hub.app/Contents/MacOS/Unity\ Hub &> /dev/null; then
    echo "✅ Unity Hub 已安装"
else
    echo "❌ Unity Hub 未找到"
    echo "   请从 https://unity.com/download 下载安装"
fi

# 检查Unity编辑器
if ls /Applications/Unity/Hub/Editor/*/Unity.app &> /dev/null; then
    echo "✅ Unity编辑器已安装"
    ls /Applications/Unity/Hub/Editor/*/Unity.app | head -1
else
    echo "❌ Unity编辑器未找到"
    echo "   请通过Unity Hub安装Unity 2022.3 LTS"
fi

# 检查.NET
if command -v dotnet &> /dev/null; then
    echo "✅ .NET 已安装: $(dotnet --version)"
else
    echo "⚠️  .NET 未安装 (Unity自带，但建议安装)"
    echo "   运行: brew install dotnet"
fi

# 检查项目文件
if [ -f "Assets/Scripts/Core/Configuration/ConfigurationManager.cs" ]; then
    echo "✅ 项目文件结构正确"
else
    echo "❌ 项目文件不完整"
fi

# 检查Git
if command -v git &> /dev/null; then
    echo "✅ Git 已安装: $(git --version)"
else
    echo "❌ Git 未安装"
fi

echo ""
echo "🎯 下一步操作："
echo "1. 打开Unity Hub"
echo "2. 点击 Projects > Open"
echo "3. 选择当前项目文件夹"
echo "4. 等待Unity导入完成"
echo "5. 运行 Window > General > Test Runner 验证功能"

echo ""
echo "📱 测试设置界面："
echo "1. 打开 MainScene"
echo "2. 添加 TestSceneManager 组件"
echo "3. 播放场景并按 F1 键"