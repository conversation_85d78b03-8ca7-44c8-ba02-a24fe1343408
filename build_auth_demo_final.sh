#!/bin/bash

echo "=== 构建认证功能演示应用 ==="
echo ""

# 设置变量
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
BUILD_PATH="./Builds/AuthDemo"
APP_NAME="DigitalHuman_AuthDemo"
LOG_FILE="build_auth_demo.log"

echo "1. 准备构建环境..."

# 检查Unity
if [ ! -f "$UNITY_PATH" ]; then
    echo "❌ Unity未找到"
    exit 1
fi

# 创建构建目录
mkdir -p "$BUILD_PATH"
echo "✅ 构建目录准备完成"

echo ""
echo "2. 检查认证相关文件..."

# 检查必要文件
required_files=(
    "Assets/Scripts/UI/AuthDemoUIManager.cs"
    "Assets/Scripts/Core/Authentication/AuthenticationManager.cs"
    "Assets/Scripts/Core/Authentication/IAuthenticationManager.cs"
    "Assets/Scripts/Core/Authentication/Models/UserInfo.cs"
    "Assets/Scripts/Core/Authentication/Models/AuthenticationResult.cs"
    "Assets/Scripts/Core/Authentication/Models/AuthenticationStatus.cs"
    "Assets/UI/AuthDemo.uxml"
    "Assets/UI/AuthDemo.uss"
)

all_files_exist=true
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file 缺失"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = false ]; then
    echo "❌ 部分必要文件缺失，无法继续构建"
    exit 1
fi

echo ""
echo "3. 创建认证演示构建脚本..."

# 创建专门的构建脚本
cat > "Assets/Scripts/Editor/AuthDemoBuildScript.cs" << 'EOF'
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.SceneManagement;
using System.IO;

namespace DigitalHuman.Editor
{
    public class AuthDemoBuildScript
    {
        public static void BuildAuthDemo()
        {
            Debug.Log("[AuthDemo] 开始构建认证演示应用...");
            
            try
            {
                // 创建临时场景
                CreateAuthDemoScene();
                
                // 构建设置
                string buildPath = Path.Combine(Application.dataPath, "../Builds/AuthDemo/DigitalHuman_AuthDemo.app");
                string[] scenes = { "Assets/Scenes/TempAuthDemoScene.unity" };
                
                // 确保构建目录存在
                string buildDir = Path.GetDirectoryName(buildPath);
                if (!Directory.Exists(buildDir))
                {
                    Directory.CreateDirectory(buildDir);
                }
                
                // 配置播放器设置
                PlayerSettings.productName = "数字人认证演示";
                PlayerSettings.companyName = "DigitalHuman";
                PlayerSettings.bundleVersion = "1.0.0-AuthDemo";
                PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.authdemo");
                
                // 构建选项
                BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = buildPath,
                    target = BuildTarget.StandaloneOSX,
                    options = BuildOptions.Development
                };
                
                // 执行构建
                var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                
                if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    Debug.Log($"[AuthDemo] 构建成功！位置: {buildPath}");
                    Debug.Log($"[AuthDemo] 构建大小: {report.summary.totalSize} bytes");
                    EditorUtility.RevealInFinder(buildPath);
                }
                else
                {
                    Debug.LogError($"[AuthDemo] 构建失败: {report.summary.result}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AuthDemo] 构建异常: {ex.Message}");
            }
        }
        
        private static void CreateAuthDemoScene()
        {
            Debug.Log("[AuthDemo] 创建临时演示场景...");
            
            // 创建新场景
            var scene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
            
            // 创建主摄像机
            var cameraGO = new GameObject("Main Camera");
            var camera = cameraGO.AddComponent<Camera>();
            camera.backgroundColor = Color.black;
            camera.clearFlags = CameraClearFlags.SolidColor;
            cameraGO.tag = "MainCamera";
            
            // 创建UI根对象
            var uiRootGO = new GameObject("UI Root");
            var uiDocument = uiRootGO.AddComponent<UIDocument>();
            
            // 设置UI文档
            string uiPath = "Assets/UI/AuthDemo.uxml";
            var visualTreeAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(uiPath);
            if (visualTreeAsset != null)
            {
                uiDocument.visualTreeAsset = visualTreeAsset;
                Debug.Log("[AuthDemo] UI文档设置成功");
            }
            else
            {
                Debug.LogWarning("[AuthDemo] 无法加载UI文档");
            }
            
            // 添加认证演示管理器
            var authDemoManager = uiRootGO.AddComponent<DigitalHuman.UI.AuthDemoUIManager>();
            
            // 创建认证管理器对象
            var authManagerGO = new GameObject("Authentication Manager");
            var authManager = authManagerGO.AddComponent<DigitalHuman.Core.Authentication.AuthenticationManager>();
            
            // 保存临时场景
            string scenePath = "Assets/Scenes/TempAuthDemoScene.unity";
            EditorSceneManager.SaveScene(scene, scenePath);
            
            Debug.Log($"[AuthDemo] 临时场景已创建: {scenePath}");
        }
    }
}
EOF

echo "✅ 认证演示构建脚本已创建"

echo ""
echo "4. 开始Unity构建..."

# 执行构建
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "." \
    -buildTarget StandaloneOSX \
    -logFile "$LOG_FILE" \
    -executeMethod AuthDemoBuildScript.BuildAuthDemo

BUILD_EXIT_CODE=$?

echo ""
if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo "🎉 构建成功完成！"
    
    if [ -d "$BUILD_PATH/$APP_NAME.app" ]; then
        echo ""
        echo "📱 应用信息:"
        echo "   - 名称: 数字人认证演示"
        echo "   - 路径: $BUILD_PATH/$APP_NAME.app"
        
        APP_SIZE=$(du -sh "$BUILD_PATH/$APP_NAME.app" | cut -f1)
        echo "   - 大小: $APP_SIZE"
        
        echo ""
        echo "🧪 认证功能测试指南:"
        echo "   1. 双击应用图标启动"
        echo "   2. 观察右上角红色'未登录'状态"
        echo "   3. 点击'登录'按钮"
        echo "   4. 输入测试凭据: admin / admin123"
        echo "   5. 观察状态变为绿色'已登录'"
        echo "   6. 查看用户名显示"
        echo "   7. 点击'登出'测试登出功能"
        
        echo ""
        echo "🚀 启动应用:"
        echo "   open '$BUILD_PATH/$APP_NAME.app'"
        
        # 询问是否立即运行
        read -p "是否立即启动应用进行测试？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🚀 启动认证演示应用..."
            open "$BUILD_PATH/$APP_NAME.app"
            echo ""
            echo "✨ 应用已启动！请按照测试指南进行认证功能测试。"
        fi
    else
        echo "❌ 构建文件未找到，请检查构建日志"
    fi
else
    echo "❌ 构建失败，退出代码: $BUILD_EXIT_CODE"
    echo ""
    echo "📋 查看构建日志:"
    echo "   cat $LOG_FILE"
    
    # 显示最后的错误信息
    if [ -f "$LOG_FILE" ]; then
        echo ""
        echo "最近的错误信息:"
        tail -10 "$LOG_FILE" | grep -i error || echo "未找到明显错误"
    fi
fi

echo ""
echo "=== 认证演示构建完成 ==="