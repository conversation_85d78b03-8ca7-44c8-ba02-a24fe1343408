#!/bin/bash

echo "=== 创建独立认证演示应用 ==="
echo ""

# 创建独立演示目录
DEMO_DIR="./AuthDemo_Standalone"
echo "1. 创建独立演示目录..."
mkdir -p "$DEMO_DIR"

# 复制必要的认证文件
echo "2. 复制认证相关文件..."
mkdir -p "$DEMO_DIR/Scripts/Authentication"
mkdir -p "$DEMO_DIR/Scripts/UI"
mkdir -p "$DEMO_DIR/UI"

# 复制认证核心文件
cp "Assets/Scripts/Core/Authentication/IAuthenticationManager.cs" "$DEMO_DIR/Scripts/Authentication/" 2>/dev/null || echo "跳过IAuthenticationManager.cs"
cp "Assets/Scripts/Core/Authentication/Models/UserInfo.cs" "$DEMO_DIR/Scripts/Authentication/" 2>/dev/null || echo "跳过UserInfo.cs"
cp "Assets/Scripts/Core/Authentication/Models/AuthenticationResult.cs" "$DEMO_DIR/Scripts/Authentication/" 2>/dev/null || echo "跳过AuthenticationResult.cs"
cp "Assets/Scripts/Core/Authentication/Models/AuthenticationStatus.cs" "$DEMO_DIR/Scripts/Authentication/" 2>/dev/null || echo "跳过AuthenticationStatus.cs"

# 复制UI文件
cp "Assets/Scripts/UI/AuthDemoUIManager.cs" "$DEMO_DIR/Scripts/UI/" 2>/dev/null || echo "跳过AuthDemoUIManager.cs"
cp "Assets/UI/AuthDemo.uxml" "$DEMO_DIR/UI/" 2>/dev/null || echo "跳过AuthDemo.uxml"
cp "Assets/UI/AuthDemo.uss" "$DEMO_DIR/UI/" 2>/dev/null || echo "跳过AuthDemo.uss"

echo "✅ 文件复制完成"

# 创建简化的认证管理器
echo "3. 创建简化认证管理器..."
cat > "$DEMO_DIR/Scripts/Authentication/SimpleAuthManager.cs" << 'EOF'
using System;
using System.Threading.Tasks;
using UnityEngine;

namespace DigitalHuman.Core.Authentication
{
    public class SimpleAuthManager : MonoBehaviour
    {
        public static SimpleAuthManager Instance { get; private set; }
        
        public bool IsLoggedIn { get; private set; } = false;
        public UserInfo CurrentUser { get; private set; } = null;
        
        public event Action<UserInfo> OnUserLoggedIn;
        public event Action OnUserLoggedOut;
        public event Action<AuthenticationStatus> OnAuthenticationStatusChanged;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        public async Task<AuthenticationResult> LoginAsync(string username, string password)
        {
            // 模拟网络延迟
            await Task.Delay(1000);
            
            if (username == "admin" && password == "admin123")
            {
                var userInfo = new UserInfo
                {
                    UserId = "user_001",
                    Username = username,
                    Email = "<EMAIL>",
                    DisplayName = "管理员"
                };
                
                CurrentUser = userInfo;
                IsLoggedIn = true;
                
                OnUserLoggedIn?.Invoke(userInfo);
                OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.Authenticated);
                
                return AuthenticationResult.Success(userInfo, "demo_token");
            }
            else
            {
                OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.AuthenticationFailed);
                return AuthenticationResult.Failure("用户名或密码错误");
            }
        }
        
        public async Task<bool> LogoutAsync()
        {
            await Task.Delay(100);
            
            CurrentUser = null;
            IsLoggedIn = false;
            
            OnUserLoggedOut?.Invoke();
            OnAuthenticationStatusChanged?.Invoke(AuthenticationStatus.NotAuthenticated);
            
            return true;
        }
    }
}
EOF

echo "✅ 简化认证管理器创建完成"

# 创建演示说明文件
echo "4. 创建演示说明..."
cat > "$DEMO_DIR/README.md" << 'EOF'
# 数字人认证功能演示

## 概述
这是一个独立的认证功能演示，展示了完整的用户登录/登出流程。

## 文件结构
```
AuthDemo_Standalone/
├── Scripts/
│   ├── Authentication/
│   │   ├── SimpleAuthManager.cs      # 简化认证管理器
│   │   ├── UserInfo.cs              # 用户信息模型
│   │   ├── AuthenticationResult.cs  # 认证结果模型
│   │   └── AuthenticationStatus.cs  # 认证状态枚举
│   └── UI/
│       └── AuthDemoUIManager.cs     # 认证演示UI管理器
├── UI/
│   ├── AuthDemo.uxml               # UI布局文件
│   └── AuthDemo.uss                # UI样式文件
└── README.md                       # 说明文档
```

## 功能特性
- ✅ 用户登录/登出
- ✅ 实时状态显示
- ✅ 表单验证
- ✅ 错误处理
- ✅ 键盘快捷键
- ✅ 异步操作

## 测试账户
- 用户名: admin
- 密码: admin123

## 使用方法
1. 将所有文件导入Unity项目
2. 创建新场景
3. 添加UI Document组件并设置AuthDemo.uxml
4. 添加AuthDemoUIManager组件
5. 添加SimpleAuthManager组件
6. 运行场景进行测试

## 测试流程
1. 观察右上角红色"未登录"状态
2. 点击"登录"按钮
3. 输入测试凭据
4. 观察状态变为绿色"已登录"
5. 测试登出功能
EOF

echo "✅ 演示说明创建完成"

# 创建Unity包导出脚本
echo "5. 创建Unity包导出脚本..."
cat > "$DEMO_DIR/export_unity_package.cs" << 'EOF'
// 将此脚本放在Assets/Editor/目录下，然后在Unity中执行
using UnityEngine;
using UnityEditor;

public class ExportAuthDemo
{
    [MenuItem("AuthDemo/Export Unity Package")]
    public static void ExportPackage()
    {
        string[] assetPaths = {
            "Assets/AuthDemo_Standalone"
        };
        
        AssetDatabase.ExportPackage(assetPaths, "AuthDemo.unitypackage", 
            ExportPackageOptions.Recurse | ExportPackageOptions.IncludeDependencies);
        
        Debug.Log("认证演示包已导出: AuthDemo.unitypackage");
    }
}
EOF

echo "✅ Unity包导出脚本创建完成"

# 显示完成信息
echo ""
echo "🎉 独立认证演示创建完成！"
echo ""
echo "📁 演示位置: $DEMO_DIR"
echo ""
echo "📋 包含文件:"
ls -la "$DEMO_DIR"
echo ""
echo "📂 子目录:"
find "$DEMO_DIR" -type d
echo ""
echo "📄 所有文件:"
find "$DEMO_DIR" -type f
echo ""
echo "🚀 使用方法:"
echo "1. 将 $DEMO_DIR 目录复制到Unity项目的Assets目录下"
echo "2. 在Unity中创建新场景"
echo "3. 按照README.md中的说明设置场景"
echo "4. 运行场景测试认证功能"
echo ""
echo "🧪 测试凭据:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "=== 独立演示创建完成 ==="