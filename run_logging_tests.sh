#!/bin/bash

# 日志系统单元测试运行脚本
# 用于在 macOS 环境下运行日志系统的完整测试套件

echo "=== 日志系统单元测试运行脚本 ==="
echo "开始时间: $(date)"
echo ""

# 检查 Unity 是否安装
if ! command -v /Applications/Unity/Hub/Editor/*/Unity.app/Contents/MacOS/Unity &> /dev/null; then
    echo "❌ 错误: 未找到 Unity 编辑器"
    echo "请确保 Unity 已正确安装"
    exit 1
fi

# 获取项目路径
PROJECT_PATH=$(pwd)
echo "📁 项目路径: $PROJECT_PATH"

# 检查必要的测试文件是否存在
TEST_FILES=(
    "Assets/Scripts/Core/Logging/Tests/LogManagerTests.cs"
    "Assets/Scripts/Core/Logging/Tests/FileLogWriterTests.cs"
    "Assets/Scripts/Core/Logging/Tests/LogRotationTests.cs"
    "Assets/Scripts/Core/Logging/Tests/LogConfigurationTests.cs"
    "Assets/Scripts/Core/Logging/Tests/LogStatisticsTests.cs"
    "Assets/Scripts/Core/Logging/Tests/LoggingTestSuite.cs"
)

echo "🔍 检查测试文件..."
for file in "${TEST_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        exit 1
    fi
done

echo ""
echo "📋 可用的测试选项:"
echo "  1. 运行所有测试 (推荐)"
echo "  2. 运行 LogManager 核心功能测试"
echo "  3. 运行 FileLogWriter 异步写入测试"
echo "  4. 运行日志轮转功能测试"
echo "  5. 运行配置管理集成测试"
echo "  6. 运行日志统计功能测试"
echo "  7. 生成测试报告"
echo "  8. 清理测试文件"
echo ""

# 读取用户选择
read -p "请选择要运行的测试 (1-8): " choice

case $choice in
    1)
        echo "🚀 运行所有测试..."
        echo "注意: 这将在 Unity 编辑器中运行完整的测试套件"
        echo "请在 Unity 编辑器中找到 LoggingTestSuite 组件并运行测试"
        ;;
    2)
        echo "🧪 运行 LogManager 核心功能测试..."
        echo "请在 Unity 编辑器中找到 LogManagerTests 组件并运行测试"
        ;;
    3)
        echo "📝 运行 FileLogWriter 异步写入测试..."
        echo "请在 Unity 编辑器中找到 FileLogWriterTests 组件并运行测试"
        ;;
    4)
        echo "🔄 运行日志轮转功能测试..."
        echo "请在 Unity 编辑器中找到 LogRotationTests 组件并运行测试"
        ;;
    5)
        echo "⚙️ 运行配置管理集成测试..."
        echo "请在 Unity 编辑器中找到 LogConfigurationTests 组件并运行测试"
        ;;
    6)
        echo "📊 运行日志统计功能测试..."
        echo "请在 Unity 编辑器中找到 LogStatisticsTests 组件并运行测试"
        ;;
    7)
        echo "📄 生成测试报告..."
        echo "测试报告将在运行测试后自动生成"
        echo "请查看 Unity Console 中的详细输出"
        ;;
    8)
        echo "🧹 清理测试文件..."
        
        # 清理可能的测试输出文件
        TEST_OUTPUT_DIRS=(
            "$(pwd)/Library/Logs"
            "$HOME/Library/Logs/Unity"
            "$(pwd)/Logs"
            "/tmp/LoggingTests"
        )
        
        for dir in "${TEST_OUTPUT_DIRS[@]}"; do
            if [ -d "$dir" ]; then
                echo "  清理目录: $dir"
                find "$dir" -name "*Test*" -type f -delete 2>/dev/null || true
            fi
        done
        
        # 清理 Unity 生成的临时文件
        if [ -d "$(pwd)/Library/ScriptAssemblies" ]; then
            echo "  清理 Unity 脚本程序集缓存..."
            rm -rf "$(pwd)/Library/ScriptAssemblies"
        fi
        
        echo "  ✅ 测试文件清理完成"
        ;;
    *)
        echo "❌ 无效选择，请输入 1-8 之间的数字"
        exit 1
        ;;
esac

echo ""
echo "💡 使用提示:"
echo "  - 所有测试都需要在 Unity 编辑器中运行"
echo "  - 测试结果将显示在 Unity Console 中"
echo "  - 建议在运行测试前备份重要数据"
echo "  - 测试过程中会创建临时文件，测试完成后会自动清理"
echo ""

echo "📚 测试文档位置:"
echo "  - 测试代码: Assets/Scripts/Core/Logging/Tests/"
echo "  - 测试文档: docs/logging/"
echo "  - 任务规范: .kiro/specs/logging-system/tasks.md"
echo ""

echo "🔧 故障排除:"
echo "  - 如果测试失败，请检查 Unity Console 中的错误信息"
echo "  - 确保所有依赖的管理器组件已正确初始化"
echo "  - 检查文件权限，确保测试可以创建临时文件"
echo "  - 如果遇到编译错误，请重新导入项目"
echo ""

echo "结束时间: $(date)"
echo "=== 测试脚本执行完成 ==="