#!/bin/bash

# 数字人管理系统 - 最小MVP自动化构建脚本
# 作者：DigitalHuman Team
# 版本：1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Unity是否安装
check_unity() {
    log_info "检查Unity安装..."
    
    # macOS上的Unity路径
    UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.*/Unity.app/Contents/MacOS/Unity"
    
    # 查找Unity可执行文件
    UNITY_EXEC=$(find /Applications/Unity/Hub/Editor -name "Unity" -type f -path "*/MacOS/Unity" 2>/dev/null | head -1)
    
    if [ -z "$UNITY_EXEC" ]; then
        # 尝试默认路径
        if [ -f "/Applications/Unity/Unity.app/Contents/MacOS/Unity" ]; then
            UNITY_EXEC="/Applications/Unity/Unity.app/Contents/MacOS/Unity"
        else
            log_error "未找到Unity安装，请确保Unity 2022.3 LTS已正确安装"
            exit 1
        fi
    fi
    
    log_success "找到Unity: $UNITY_EXEC"
}

# 检查项目结构
check_project() {
    log_info "检查项目结构..."
    
    # 检查必要文件
    if [ ! -f "Assets/Scripts/Editor/BuildScript.cs" ]; then
        log_error "未找到构建脚本: Assets/Scripts/Editor/BuildScript.cs"
        exit 1
    fi
    
    if [ ! -f "Assets/Scenes/MinimalMVPScene.unity" ]; then
        log_error "未找到MVP场景: Assets/Scenes/MinimalMVPScene.unity"
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 清理构建目录
clean_build() {
    log_info "清理构建目录..."
    
    if [ -d "Builds" ]; then
        rm -rf Builds
        log_success "已清理构建目录"
    else
        log_info "构建目录不存在，跳过清理"
    fi
}

# 快速编译检查
quick_build_test() {
    log_info "执行快速编译检查..."
    
    "$UNITY_EXEC" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -executeMethod DigitalHuman.Editor.BuildScript.QuickBuildTest \
        -logFile build_test.log
    
    if [ $? -eq 0 ]; then
        log_success "快速编译检查通过"
    else
        log_error "快速编译检查失败，请查看 build_test.log"
        cat build_test.log
        exit 1
    fi
}

# 执行构建
build_mvp() {
    log_info "开始构建最小MVP版本..."
    
    "$UNITY_EXEC" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -executeMethod DigitalHuman.Editor.BuildScript.BuildMinimalMVP \
        -logFile build_mvp.log
    
    if [ $? -eq 0 ]; then
        log_success "MVP构建完成"
    else
        log_error "MVP构建失败，请查看 build_mvp.log"
        cat build_mvp.log
        exit 1
    fi
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    BUILD_PATH="Builds/MinimalMVP/DigitalHuman_MVP.app"
    
    if [ -d "$BUILD_PATH" ]; then
        BUILD_SIZE=$(du -sh "$BUILD_PATH" | cut -f1)
        log_success "构建成功！"
        log_info "构建路径: $BUILD_PATH"
        log_info "构建大小: $BUILD_SIZE"
        
        # 在Finder中显示构建结果
        open -R "$BUILD_PATH"
        
        return 0
    else
        log_error "构建失败：未找到构建文件 $BUILD_PATH"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "数字人管理系统 - 最小MVP构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --clean    构建前清理构建目录"
    echo "  -t, --test     仅执行快速编译测试"
    echo "  -v, --verbose  显示详细日志"
    echo ""
    echo "示例:"
    echo "  $0              # 标准构建"
    echo "  $0 --clean      # 清理后构建"
    echo "  $0 --test       # 仅测试编译"
    echo ""
}

# 主函数
main() {
    local clean_flag=false
    local test_only=false
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_flag=true
                shift
                ;;
            -t|--test)
                test_only=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示构建信息
    echo "========================================"
    echo "  数字人管理系统 - 最小MVP构建脚本"
    echo "========================================"
    echo "构建时间: $(date)"
    echo "构建平台: macOS"
    echo "Unity版本: 2022.3 LTS"
    echo "========================================"
    echo ""
    
    # 执行构建流程
    check_unity
    check_project
    
    if [ "$clean_flag" = true ]; then
        clean_build
    fi
    
    quick_build_test
    
    if [ "$test_only" = true ]; then
        log_success "编译测试完成，跳过实际构建"
        exit 0
    fi
    
    build_mvp
    
    if verify_build; then
        echo ""
        echo "========================================"
        log_success "🎉 构建流程完成！"
        echo "========================================"
        echo ""
        echo "下一步："
        echo "1. 测试构建的应用程序"
        echo "2. 配置API服务端点"
        echo "3. 部署到目标环境"
        echo ""
    else
        echo ""
        echo "========================================"
        log_error "❌ 构建流程失败！"
        echo "========================================"
        echo ""
        echo "故障排除："
        echo "1. 检查Unity版本是否为2022.3 LTS"
        echo "2. 查看构建日志文件"
        echo "3. 确保项目无编译错误"
        echo ""
        exit 1
    fi
}

# 脚本入口
main "$@"