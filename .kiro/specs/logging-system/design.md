# 日志管理系统设计文档

## 概述

日志管理系统是一个高度模块化、解耦的核心基础设施组件，采用依赖注入和接口抽象设计，支持多级别日志记录、自动轮转、性能优化和AI友好的日志格式。系统集成到现有的管理器架构中，通过事件系统实现松耦合通信，并与配置管理系统协同工作。

## 架构

### 核心架构原则

1. **接口抽象**: 通过ILogManager、ILogWriter、ILogFormatter等接口实现解耦
2. **依赖注入**: 使用构造函数注入和工厂模式管理依赖关系
3. **单例模式**: LogManager采用SingletonManager模式确保全局唯一性
4. **事件驱动**: 通过EventSystem发布日志事件，实现模块间解耦通信
5. **插件化**: 支持自定义LogWriter、LogFormatter和LogFilter的扩展

### 系统层次结构

```
LogManager (SingletonManager)
├── ILogWriter (接口层)
│   ├── FileLogWriter (文件写入器)
│   ├── ConsoleLogWriter (控制台写入器)
│   └── RemoteLogWriter (远程日志写入器)
├── ILogFormatter (格式化器接口)
│   ├── JsonLogFormatter (JSON格式化器)
│   ├── PlainTextLogFormatter (纯文本格式化器)
│   └── StructuredLogFormatter (结构化格式化器)
├── ILogFilter (过滤器接口)
│   ├── LevelLogFilter (级别过滤器)
│   ├── ModuleLogFilter (模块过滤器)
│   └── TimeLogFilter (时间过滤器)
└── LogRotationManager (日志轮转管理器)
    ├── SizeBasedRotation (基于大小的轮转)
    ├── TimeBasedRotation (基于时间的轮转)
    └── CountBasedRotation (基于数量的轮转)
```

## 组件和接口

### 1. 核心接口定义

#### ILogManager
```csharp
public interface ILogManager : IManager
{
    bool IsLoggingEnabled { get; set; }
    
    void Log(LogLevel level, string message, string module = null, Exception exception = null);
    void LogTrace(string message, string module = null);
    void LogDebug(string message, string module = null);
    void LogInfo(string message, string module = null);
    void LogWarn(string message, string module = null);
    void LogError(string message, string module = null, Exception exception = null);
    void LogFatal(string message, string module = null, Exception exception = null);
    
    ILogger GetLogger(string moduleName);
    void SetLogLevel(LogLevel level, string module = null);
    void AddLogWriter(ILogWriter writer);
    void RemoveLogWriter(ILogWriter writer);
    void FlushAll();
    
    // 日志导出功能
    Task<string> ExportLogsAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null);
    Task<string> ExportLogsToZipAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null);
    void ClearLogs(DateTime? beforeTime = null);
    LogStatistics GetLogStatistics();
}
```

#### ILogger (模块化日志记录器)
```csharp
public interface ILogger
{
    string ModuleName { get; }
    LogLevel MinLevel { get; set; }
    
    void Trace(string message, Exception exception = null);
    void Debug(string message, Exception exception = null);
    void Info(string message, Exception exception = null);
    void Warn(string message, Exception exception = null);
    void Error(string message, Exception exception = null);
    void Fatal(string message, Exception exception = null);
    
    bool IsEnabled(LogLevel level);
}
```

#### ILogWriter
```csharp
public interface ILogWriter : IDisposable
{
    string Name { get; }
    bool IsEnabled { get; set; }
    LogLevel MinLevel { get; set; }
    
    Task WriteAsync(LogEntry entry);
    void Flush();
    void Configure(LogWriterConfiguration config);
}
```

#### ILogFormatter
```csharp
public interface ILogFormatter
{
    string Format(LogEntry entry);
    bool SupportsStructuredData { get; }
}
```

### 2. 数据模型

#### LogEntry
```csharp
public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public LogLevel Level { get; set; }
    public string Message { get; set; }
    public string Module { get; set; }
    public string ThreadId { get; set; }
    public Exception Exception { get; set; }
    public Dictionary<string, object> Properties { get; set; }
    public string CorrelationId { get; set; }
}
```

#### LogLevel
```csharp
public enum LogLevel
{
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warn = 3,
    Error = 4,
    Fatal = 5
}
```

#### LogConfiguration
```csharp
public class LogConfiguration
{
    public bool IsLoggingEnabled { get; set; } = true;
    public LogLevel GlobalMinLevel { get; set; } = LogLevel.Info;
    public Dictionary<string, LogLevel> ModuleLevels { get; set; }
    public List<LogWriterConfiguration> Writers { get; set; }
    public LogRotationConfiguration Rotation { get; set; }
    public PerformanceConfiguration Performance { get; set; }
    public LogExportConfiguration Export { get; set; }
}

public class LogExportConfiguration
{
    public string ExportDirectory { get; set; } = "LogExports";
    public bool IncludeSystemInfo { get; set; } = true;
    public bool CompressExports { get; set; } = true;
    public int MaxExportSizeMB { get; set; } = 100;
    public string ExportFormat { get; set; } = "json"; // json, txt, csv
}

public class LogStatistics
{
    public DateTime StartTime { get; set; }
    public long TotalLogCount { get; set; }
    public Dictionary<LogLevel, long> LogCountByLevel { get; set; }
    public Dictionary<string, long> LogCountByModule { get; set; }
    public long TotalFileSizeBytes { get; set; }
    public int ActiveLogFiles { get; set; }
    public DateTime LastLogTime { get; set; }
}
```

### 3. 核心组件实现

#### LogManager
- 继承自SingletonManager<LogManager>
- 实现ILogManager接口
- 管理多个ILogWriter实例
- 提供模块化的ILogger实例
- 集成配置管理和事件系统

#### FileLogWriter
- 实现ILogWriter接口
- 支持异步写入和缓冲
- 集成日志轮转功能
- 支持文件锁定和并发写入

#### LogRotationManager
- 监控日志文件大小和数量
- 执行自动轮转策略
- 清理过期日志文件
- 支持压缩归档

## 数据模型

### 日志条目结构
```json
{
  "timestamp": "2024-07-19T21:30:45.123Z",
  "level": "INFO",
  "module": "Core.Audio",
  "thread": "MainThread",
  "message": "音频设备初始化完成",
  "correlationId": "req-12345",
  "properties": {
    "deviceName": "Default Audio Device",
    "sampleRate": 44100
  },
  "exception": null
}
```

### 配置文件结构
```json
{
  "isLoggingEnabled": true,
  "globalMinLevel": "Info",
  "moduleLevels": {
    "Core.Audio": "Debug",
    "Core.Network": "Info",
    "Core.Rendering": "Warn"
  },
  "writers": [
    {
      "type": "FileLogWriter",
      "name": "MainFileWriter",
      "enabled": true,
      "minLevel": "Info",
      "configuration": {
        "filePath": "Logs/app.log",
        "maxFileSize": "10MB",
        "maxFiles": 30,
        "formatter": "JsonLogFormatter",
        "bufferSize": 1000,
        "flushInterval": "5s"
      }
    }
  ],
  "rotation": {
    "strategy": "SizeAndCount",
    "maxFileSize": "10MB",
    "maxFileCount": 30,
    "archiveOldFiles": true,
    "compressionEnabled": true
  },
  "performance": {
    "asyncWriting": true,
    "bufferSize": 1000,
    "flushInterval": "5s",
    "maxMemoryUsage": "50MB"
  },
  "export": {
    "exportDirectory": "LogExports",
    "includeSystemInfo": true,
    "compressExports": true,
    "maxExportSizeMB": 100,
    "exportFormat": "json"
  }
}
```

## 错误处理

### 错误处理策略

1. **写入失败处理**
   - 重试机制：指数退避算法
   - 降级策略：切换到备用写入器
   - 错误日志：记录到系统事件日志

2. **磁盘空间不足**
   - 自动清理最旧的日志文件
   - 压缩现有日志文件
   - 发送磁盘空间警告事件

3. **配置错误处理**
   - 使用默认配置作为后备
   - 验证配置完整性
   - 发送配置错误事件

4. **性能降级**
   - 内存使用超限时停止缓冲
   - CPU使用过高时降低日志级别
   - 磁盘IO过载时延长刷新间隔

### 异常恢复机制

```csharp
public class LogErrorRecoveryStrategy
{
    public async Task<bool> HandleWriteFailure(ILogWriter writer, LogEntry entry, Exception exception)
    {
        // 1. 记录错误到系统日志
        // 2. 尝试重新初始化写入器
        // 3. 如果失败，切换到备用写入器
        // 4. 发送错误恢复事件
    }
}
```

## 测试策略

### 单元测试

1. **LogManager测试**
   - 日志级别过滤测试
   - 模块化日志记录测试
   - 写入器管理测试
   - 配置动态更新测试

2. **LogWriter测试**
   - 异步写入性能测试
   - 文件轮转功能测试
   - 错误恢复机制测试
   - 并发写入安全测试

3. **LogFormatter测试**
   - JSON格式化正确性测试
   - 特殊字符处理测试
   - 性能基准测试
   - 结构化数据支持测试

### 集成测试

1. **系统集成测试**
   - 与现有管理器系统集成测试
   - 事件系统交互测试
   - 配置管理系统集成测试
   - 多线程环境稳定性测试

2. **性能测试**
   - 高并发日志写入测试
   - 内存使用量监控测试
   - 磁盘IO性能测试
   - 日志轮转性能测试

### 端到端测试

1. **完整流程测试**
   - 从日志记录到文件轮转的完整流程
   - 配置热更新功能测试
   - 错误恢复和降级测试
   - AI工具日志分析兼容性测试

2. **压力测试**
   - 长时间运行稳定性测试
   - 大量日志数据处理测试
   - 资源限制环境测试
   - 异常情况恢复测试

## 性能优化

### 异步处理架构

1. **生产者-消费者模式**
   - 使用Channel<LogEntry>实现无锁队列
   - 批量处理日志条目
   - 背压控制机制

2. **内存管理**
   - 对象池复用LogEntry实例
   - 字符串缓存减少GC压力
   - 内存映射文件提高IO性能

3. **IO优化**
   - 缓冲写入减少系统调用
   - 异步文件操作
   - 预分配文件空间

### 性能监控

```csharp
public class LogPerformanceMetrics
{
    public long TotalLogsWritten { get; set; }
    public double AverageWriteTime { get; set; }
    public long MemoryUsage { get; set; }
    public int QueueDepth { get; set; }
    public double ThroughputPerSecond { get; set; }
}
```

## 扩展点设计

### 自定义写入器

```csharp
public class DatabaseLogWriter : ILogWriter
{
    public async Task WriteAsync(LogEntry entry)
    {
        // 实现数据库写入逻辑
    }
}
```

### 自定义格式化器

```csharp
public class CustomLogFormatter : ILogFormatter
{
    public string Format(LogEntry entry)
    {
        // 实现自定义格式化逻辑
    }
}
```

### 插件注册机制

```csharp
public static class LogManagerExtensions
{
    public static void RegisterCustomWriter<T>(this ILogManager logManager) where T : ILogWriter, new()
    {
        // 注册自定义写入器
    }
}
```

## 日志导出功能

### 导出功能设计

1. **导出接口**
   ```csharp
   public interface ILogExporter
   {
       Task<string> ExportAsync(LogExportRequest request);
       Task<string> ExportToZipAsync(LogExportRequest request);
       void CleanupExports(TimeSpan maxAge);
   }
   
   public class LogExportRequest
   {
       public DateTime? StartTime { get; set; }
       public DateTime? EndTime { get; set; }
       public LogLevel? MinLevel { get; set; }
       public List<string> Modules { get; set; }
       public string Format { get; set; } = "json";
       public bool IncludeSystemInfo { get; set; } = true;
       public bool Compress { get; set; } = true;
   }
   ```

2. **导出文件结构**
   ```
   LogExport_20240719_213045.zip
   ├── system_info.json          # 系统信息
   ├── export_metadata.json      # 导出元数据
   ├── logs/
   │   ├── app_20240719.log      # 主应用日志
   │   ├── error_20240719.log    # 错误日志
   │   └── debug_20240719.log    # 调试日志
   └── statistics.json           # 日志统计信息
   ```

3. **系统信息收集**
   ```csharp
   public class SystemInfo
   {
       public string ApplicationVersion { get; set; }
       public string UnityVersion { get; set; }
       public string OperatingSystem { get; set; }
       public string DeviceModel { get; set; }
       public DateTime ExportTime { get; set; }
       public TimeSpan ApplicationUptime { get; set; }
       public Dictionary<string, string> EnvironmentVariables { get; set; }
   }
   ```

### 用户界面集成

1. **设置面板集成**
   - 在SettingsUI中添加日志管理面板
   - 提供日志开关控制
   - 显示日志统计信息
   - 提供导出功能按钮

2. **导出流程**
   - 用户点击"导出日志"按钮
   - 显示导出选项对话框（时间范围、日志级别等）
   - 后台执行导出任务
   - 完成后提供下载链接或保存位置

## 与现有系统集成

### 事件系统集成

```csharp
public class LogEvent : EventBase
{
    public LogEntry Entry { get; set; }
    public string WriterName { get; set; }
}

public class LogErrorEvent : EventBase
{
    public Exception Exception { get; set; }
    public string Context { get; set; }
}

public class LogExportCompletedEvent : EventBase
{
    public string ExportPath { get; set; }
    public LogExportRequest Request { get; set; }
    public bool Success { get; set; }
    public string ErrorMessage { get; set; }
}

public class LoggingStateChangedEvent : EventBase
{
    public bool IsEnabled { get; set; }
    public string Reason { get; set; }
}
```

### 配置管理集成

- 扩展ConfigurationType枚举添加Logging类型
- 集成到ConfigurationManager的加载和保存流程
- 支持配置热更新和验证
- 日志开关状态持久化

### 管理器生命周期集成

- LogManager在系统启动时优先初始化
- 与其他管理器的依赖关系管理
- 优雅关闭和资源清理
- 支持运行时开关控制

### UI设置面板集成

```csharp
public class LogSettingsPanel : SettingsPanelBase
{
    public Toggle loggingEnabledToggle;
    public Dropdown logLevelDropdown;
    public Button exportLogsButton;
    public Label logStatisticsLabel;
    public Button clearLogsButton;
    
    protected override void OnInitialize()
    {
        // 初始化日志设置面板
        // 绑定事件处理器
        // 更新UI状态
    }
}