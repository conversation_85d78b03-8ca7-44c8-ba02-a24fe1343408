{"enabled": true, "name": "Test Coverage Generator 测试覆盖生成器", "description": "监控源文件更改以识别新的或者修改的函数，检查测试覆盖率，生成确实的测试用例，运行测试，病更新覆盖率报告。Monitors source file changes to identify new/modified functions, check test coverage, generate missing tests, run tests, and update coverage reports", "version": "1", "when": {"type": "fileEdited", "patterns": ["Assets/Scripts/**/*.cs"]}, "then": {"type": "askAgent", "prompt": "A source file has been modified. Please:\n\n1. 分析修改的文件，识别新增或修改的函数和方法\n2. 检查 Assets/Tests/Editor/ 目录中是否存在对应的测试文件和测试用例\n3. 对于缺少测试覆盖的新代码，生成相应的单元测试用例\n4. 确保测试用例符合项目的测试规范和架构原则\n5. 运行测试验证其通过性\n6. 更新测试覆盖率报告\n\n请确保：\n- 测试覆盖率达到80%以上\n- 所有测试用例使用中文注释\n- 遵循模块化设计和依赖注入原则\n- 测试文件放置在正确的 Assets/Tests/Editor/ 目录下"}}