{"enabled": true, "name": "Security Vulnerability Scanner安全漏洞扫描器", "description": "自动检查已更改文件的潜在安全问题，包括API密钥、凭据、私钥和其他敏感数据暴露Automatically reviews changed files for potential security issues including API keys, credentials, private keys, and other sensitive data exposure", "version": "1", "when": {"type": "fileEdited", "patterns": ["Assets/Scripts/**/*.cs", "*.json", "*.config", "*.xml", "*.yaml", "*.yml", "*.env", "*.properties", "ProjectSettings/**/*.asset", "Packages/manifest.json"]}, "then": {"type": "askAgent", "prompt": "Please review the changed files for potential security issues. Specifically look for:\n\n1. API keys, tokens, or credentials in source code\n2. Private keys or sensitive credentials \n3. Encryption keys or certificates\n4. Authentication tokens or session IDs\n5. Passwords or secrets in configuration files\n6. IP addresses containing sensitive data\n7. Hardcoded internal URLs\n8. Database connection credentials\n\nFor each security issue found:\n1. Highlight the specific security risk and its location\n2. Suggest a secure alternative approach (environment variables, secure storage, etc.)\n3. Recommend security best practices for the specific type of credential/data\n\nFocus on C# scripts, configuration files, Unity asset files, and package manifests. Pay special attention to files in Core/Configuration, Core/Network, and Settings directories as they are likely to contain sensitive configuration data."}}