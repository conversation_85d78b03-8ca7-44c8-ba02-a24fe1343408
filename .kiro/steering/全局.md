---
inclusion: always
---
# 通用规范

- 一直使用中文回复我。
- 代码中的注释使用中文。
- 开发要使用模块化开发，注意低耦合开发，能分开的函数尽量最小化。
- 修改代码的时候一定注意不要修改开始正常的代码，尤其是已经验证过正常的功能。
- 保持项目整体的结构和代码不改变，保留以前的功能，不修改任何的文字内容和样式，然后沿用现在的设计风格和样式。我们需要执行指定的任务，当你修改代码时候一定要专注于问题相关的内容，非必要不要修改其他已经验证正确的功能逻辑和 UI 样式
- 不要为了测试就直接删除已经完成的代码，这种极度危险和不合理的操作。

# 开发规范与要求

## 开发环境

如果没有特别说明的话，我们是在 Mac M4 电脑上进行的开发，注意使用 Mac 的终端命令

## 架构原则

- **模块化设计**: 严格遵循低耦合、高内聚原则，每个模块职责单一明确
- **函数最小化**: 将复杂逻辑拆分为最小粒度的纯函数，提高代码可测试性和复用性
- **依赖注入**: 优先使用依赖注入模式，避免硬编码依赖关系

## 代码风格

- **语言要求**: 所有回复、代码注释、文档必须使用中文
- **命名规范**:
  - 变量和函数使用驼峰命名法
  - 常量使用大写下划线分隔
  - 类名使用帕斯卡命名法
- **注释标准**: 每个函数必须包含中文注释说明用途、参数和返回值

## 测试要求

- **测试驱动**: 新功能开发前必须先编写测试用例
- **覆盖率要求**: 单元测试覆盖率不低于 80%
- **测试通过**: 所有测试必须通过后才能进行下一步开发
- **测试类型**: 包含单元测试、集成测试和端到端测试

## 开发流程

1. 分析需求并拆分为最小功能单元
2. 编写测试用例
3. 实现功能代码
4. 运行测试验证
5. 代码审查和重构优化

## git commit 规范

要符合通用的 git commit 规范，包括以下内容：

- 使用 feat 或 fix 或 refactor 或 style 或 test 或 chore 或 docs 或 build 或 ci 或 perf 或 revert 或 release 或 workflow 作为开头
- 保证每个 commit 有详细的描述，至少 20 字，清晰的描述做的修改。
