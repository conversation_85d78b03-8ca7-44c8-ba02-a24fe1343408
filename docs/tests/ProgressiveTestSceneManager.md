# ProgressiveTestSceneManager 渐进式测试场景管理器文档

## 概述

`ProgressiveTestSceneManager` 是数字人管理系统的渐进式测试场景管理器，负责管理整个测试场景的运行、协调各个测试组件的执行，并提供统一的GUI界面来控制测试流程。该组件遵循渐进式开发原则，支持模块化测试和增量功能验证。

## 命名空间

```csharp
DigitalHuman.Tests
```

## 类定义

### ProgressiveTestSceneManager

渐进式测试场景管理器主类，继承自 `MonoBehaviour`，提供完整的测试场景管理功能。

#### 继承关系

```csharp
public class ProgressiveTestSceneManager : MonoBehaviour
```

## 公共属性

### 场景配置属性

#### SceneTitle

```csharp
[Header("场景配置")]
public string SceneTitle = "数字人管理系统 - 渐进式功能测试";
```

**描述：** 测试场景的标题，显示在GUI界面和日志中

**默认值：** "数字人管理系统 - 渐进式功能测试"

#### Version

```csharp
public string Version = "v1.2.0-Progressive";
```

**描述：** 当前测试场景的版本信息

**默认值：** "v1.2.0-Progressive"

#### ShowWelcomeMessage

```csharp
public bool ShowWelcomeMessage = true;
```

**描述：** 是否在场景启动时显示欢迎信息

**默认值：** `true`

### 测试组件属性

#### simpleTest

```csharp
[Header("测试组件")]
public SimpleProgressiveTest simpleTest;
```

**描述：** 简单渐进式测试组件的引用

**类型：** `SimpleProgressiveTest`

#### minimalLogTest

```csharp
public MinimalLogTest minimalLogTest;
```

**描述：** 最小日志测试组件的引用

**类型：** `MinimalLogTest`

## 私有字段

### sceneInitialized

```csharp
private bool sceneInitialized = false;
```

**描述：** 标记场景是否已经初始化

### startTime

```csharp
private float startTime;
```

**描述：** 场景启动时间，用于计算运行时长

## 公共方法

### Start

Unity生命周期方法，在场景启动时调用。

```csharp
void Start()
```

**功能：**
- 记录场景启动时间
- 调用场景初始化方法

**使用示例：**
```csharp
// Unity自动调用，无需手动调用
// 场景加载时会自动执行初始化流程
```

### RunAllTests

运行所有已配置的测试组件。

```csharp
public void RunAllTests()
```

**功能：**
- 按顺序执行所有测试组件
- 输出测试启动日志
- 处理测试组件为空的情况

**使用示例：**
```csharp
// 通过代码调用
var sceneManager = FindObjectOfType<ProgressiveTestSceneManager>();
sceneManager.RunAllTests();

// 或通过GUI界面点击"运行所有测试"按钮
```

### GetSceneStatus

获取当前场景的详细状态信息。

```csharp
public string GetSceneStatus()
```

**返回值：** `string` - 包含场景状态的格式化字符串

**返回信息包括：**
- 场景标题和版本
- 运行时间
- 初始化状态
- 测试组件加载状态

**使用示例：**
```csharp
var sceneManager = FindObjectOfType<ProgressiveTestSceneManager>();
string status = sceneManager.GetSceneStatus();
Debug.Log(status);

// 输出示例：
// === 场景状态 ===
// 场景标题: 数字人管理系统 - 渐进式功能测试
// 版本: v1.2.0-Progressive
// 运行时间: 45.2秒
// 初始化状态: 已初始化
// 简单测试: 已加载
// 最小日志测试: 已加载
```

## 私有方法

### InitializeTestScene

初始化测试场景的核心方法。

```csharp
private void InitializeTestScene()
```

**功能：**
- 防止重复初始化
- 输出场景信息和系统信息
- 显示欢迎信息（如果启用）
- 自动查找测试组件
- 标记初始化完成

**输出信息包括：**
- 场景标题和版本
- 启动时间
- Unity版本和运行平台
- 初始化状态

### ShowWelcome

显示欢迎信息和功能介绍。

```csharp
private void ShowWelcome()
```

**功能：**
- 显示测试场景用途说明
- 列出支持的功能模块
- 提供使用指导

**显示内容：**
- 基础架构系统
- 日志管理系统
- 用户认证系统
- 数据同步系统
- 许可管理系统

### FindTestComponents

自动查找场景中的测试组件。

```csharp
private void FindTestComponents()
```

**功能：**
- 自动查找 `SimpleProgressiveTest` 组件
- 自动查找 `MinimalLogTest` 组件
- 输出组件发现状态日志
- 更新组件引用

## Unity生命周期方法

### OnGUI

Unity GUI渲染方法，提供测试控制界面。

```csharp
void OnGUI()
```

**GUI界面包括：**

#### 顶部标题栏
- 场景标题显示
- 版本和运行时间显示
- "运行所有测试"按钮

#### 底部控制面板
- "显示场景状态"按钮
- "清除控制台"按钮
- "重新初始化"按钮
- 组件状态指示器

**界面布局：**
```
┌─────────────────────────────────────────────────────────┐
│ 数字人管理系统 - 渐进式功能测试    [运行所有测试]        │
│ v1.2.0-Progressive - 运行时间: 45.2秒                   │
└─────────────────────────────────────────────────────────┘
│                                                         │
│                    主要内容区域                          │
│                                                         │
┌─────────────────────────────────────────────────────────┐
│ [显示场景状态] [清除控制台] [重新初始化]    组件状态:    │
│                                           简单测试: ✅   │
│                                           日志测试: ✅   │
└─────────────────────────────────────────────────────────┘
```

### OnApplicationQuit

应用程序退出时的清理方法。

```csharp
void OnApplicationQuit()
```

**功能：**
- 输出退出信息
- 显示总运行时间
- 执行清理操作

## 使用指南

### 基本设置

1. **创建测试场景**
```csharp
// 在Unity中创建新场景
// 添加空的GameObject
// 附加ProgressiveTestSceneManager组件
```

2. **配置测试组件**
```csharp
// 在Inspector中设置场景配置
SceneTitle = "我的测试场景";
Version = "v1.0.0";
ShowWelcomeMessage = true;

// 拖拽测试组件到对应字段
simpleTest = GetComponent<SimpleProgressiveTest>();
minimalLogTest = GetComponent<MinimalLogTest>();
```

### 运行测试

#### 通过GUI界面
```csharp
// 运行场景后，使用GUI界面控制：
// 1. 点击"运行所有测试"执行全部测试
// 2. 点击"显示场景状态"查看当前状态
// 3. 点击"重新初始化"重置场景
```

#### 通过代码调用
```csharp
// 获取场景管理器实例
var sceneManager = FindObjectOfType<ProgressiveTestSceneManager>();

// 运行所有测试
sceneManager.RunAllTests();

// 获取场景状态
string status = sceneManager.GetSceneStatus();
Debug.Log(status);
```

### 添加新的测试组件

1. **创建测试组件类**
```csharp
public class MyCustomTest : MonoBehaviour
{
    public void RunMyTest()
    {
        Debug.Log("执行自定义测试");
    }
}
```

2. **在场景管理器中添加引用**
```csharp
[Header("测试组件")]
public SimpleProgressiveTest simpleTest;
public MinimalLogTest minimalLogTest;
public MyCustomTest myCustomTest; // 新增
```

3. **更新查找和运行逻辑**
```csharp
private void FindTestComponents()
{
    // 现有代码...
    
    if (myCustomTest == null)
    {
        myCustomTest = FindObjectOfType<MyCustomTest>();
        if (myCustomTest != null)
        {
            Debug.Log("✅ 找到自定义测试组件");
        }
    }
}

public void RunAllTests()
{
    Debug.Log("🚀 开始运行所有测试");
    
    // 现有代码...
    
    if (myCustomTest != null)
    {
        Debug.Log("启动自定义测试...");
        myCustomTest.RunMyTest();
    }
}
```

## 最佳实践

### 1. 场景组织
```csharp
// 推荐的场景层次结构
TestScene
├── ProgressiveTestSceneManager (主管理器)
├── TestComponents (测试组件容器)
│   ├── SimpleProgressiveTest
│   ├── MinimalLogTest
│   └── CustomTests...
└── UI (可选的UI元素)
```

### 2. 测试组件设计
```csharp
// 测试组件应该遵循统一接口
public interface IProgressiveTest
{
    void RunTest();
    bool IsTestComplete();
    string GetTestStatus();
}

public class MyTest : MonoBehaviour, IProgressiveTest
{
    public void RunTest() { /* 实现测试逻辑 */ }
    public bool IsTestComplete() { /* 返回完成状态 */ }
    public string GetTestStatus() { /* 返回状态信息 */ }
}
```

### 3. 日志规范
```csharp
// 使用统一的日志格式
Debug.Log("✅ 成功信息");
Debug.LogWarning("⚠️ 警告信息");
Debug.LogError("❌ 错误信息");
Debug.Log("🚀 开始操作");
Debug.Log("📋 信息列表");
Debug.Log("🧪 测试相关");
```

### 4. 状态管理
```csharp
// 使用枚举管理场景状态
public enum SceneState
{
    Uninitialized,
    Initializing,
    Ready,
    Testing,
    Completed,
    Error
}

private SceneState currentState = SceneState.Uninitialized;
```

## 扩展开发

### 自定义GUI主题
```csharp
// 创建自定义GUI样式
private GUIStyle titleStyle;
private GUIStyle buttonStyle;

void Start()
{
    InitializeGUIStyles();
}

private void InitializeGUIStyles()
{
    titleStyle = new GUIStyle(GUI.skin.label);
    titleStyle.fontSize = 16;
    titleStyle.fontStyle = FontStyle.Bold;
    
    buttonStyle = new GUIStyle(GUI.skin.button);
    buttonStyle.fontSize = 12;
}
```

### 测试结果收集
```csharp
// 添加测试结果收集功能
public class TestResult
{
    public string TestName { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; }
    public float Duration { get; set; }
}

private List<TestResult> testResults = new List<TestResult>();

public void RecordTestResult(TestResult result)
{
    testResults.Add(result);
    Debug.Log($"测试结果记录: {result.TestName} - {(result.Success ? "成功" : "失败")}");
}
```

### 配置文件支持
```csharp
// 支持从配置文件加载设置
[System.Serializable]
public class TestSceneConfig
{
    public string sceneTitle;
    public string version;
    public bool showWelcomeMessage;
    public string[] enabledTests;
}

private void LoadConfiguration()
{
    string configPath = Path.Combine(Application.streamingAssetsPath, "test_config.json");
    if (File.Exists(configPath))
    {
        string json = File.ReadAllText(configPath);
        TestSceneConfig config = JsonUtility.FromJson<TestSceneConfig>(json);
        ApplyConfiguration(config);
    }
}
```

## 故障排除

### 常见问题

1. **测试组件未找到**
```csharp
// 检查组件是否正确附加到GameObject
if (simpleTest == null)
{
    Debug.LogWarning("⚠️ SimpleProgressiveTest组件未找到，请检查场景配置");
}
```

2. **GUI界面显示异常**
```csharp
// 确保OnGUI方法中的布局计算正确
void OnGUI()
{
    // 检查屏幕尺寸
    if (Screen.width < 800 || Screen.height < 600)
    {
        GUILayout.Label("屏幕尺寸过小，建议使用更大的窗口");
        return;
    }
    
    // 正常GUI渲染...
}
```

3. **测试执行顺序问题**
```csharp
// 使用协程控制测试执行顺序
public void RunAllTestsSequentially()
{
    StartCoroutine(RunTestsCoroutine());
}

private IEnumerator RunTestsCoroutine()
{
    if (simpleTest != null)
    {
        simpleTest.RunProgressiveTest();
        yield return new WaitForSeconds(1f);
    }
    
    if (minimalLogTest != null)
    {
        minimalLogTest.RunMinimalLogTest();
        yield return new WaitForSeconds(1f);
    }
}
```

## 性能考虑

### GUI优化
```csharp
// 避免在OnGUI中进行复杂计算
private string cachedStatus;
private float lastStatusUpdate;

void OnGUI()
{
    // 缓存状态信息，避免频繁计算
    if (Time.time - lastStatusUpdate > 1f)
    {
        cachedStatus = GetSceneStatus();
        lastStatusUpdate = Time.time;
    }
    
    // 使用缓存的状态信息
    GUILayout.Label(cachedStatus);
}
```

### 内存管理
```csharp
// 及时清理不需要的引用
void OnDestroy()
{
    simpleTest = null;
    minimalLogTest = null;
    testResults?.Clear();
}
```

## 相关文档

- [SimpleProgressiveTest 简单渐进式测试文档](SimpleProgressiveTest.md)
- [MinimalLogTest 最小日志测试文档](MinimalLogTest.md)
- [ProgressiveTestManager 渐进式测试管理器文档](ProgressiveTestManager.md)
- [测试系统总体架构](README.md)
- [Unity测试环境设置](../unity-test-setup.md)

## 版本历史

- **v1.0**: 基础场景管理功能
- **v1.1**: 添加GUI界面和组件自动发现
- **v1.2**: 增强状态管理和错误处理
- **v1.2.0-Progressive**: 当前版本，支持渐进式测试流程