# ProgressiveTestManager 渐进式测试管理器文档

## 概述

`ProgressiveTestManager` 是专门为渐进式开发设计的测试管理器，遵循项目的渐进式开发原则，提供逐步测试和验证新功能的能力。该组件支持自动初始化、详细日志记录和可视化控制面板，确保新功能能够安全地集成到现有系统中。

## 命名空间

```csharp
DigitalHuman.Tests
```

## 类定义

### ProgressiveTestManager

渐进式测试管理器主类，继承自 `MonoBehaviour`，提供完整的渐进式测试功能。

#### 继承关系

```csharp
public class ProgressiveTestManager : MonoBehaviour
```

#### 公共属性

##### AutoInitializeOnStart

```csharp
[Header("测试配置")]
public bool AutoInitializeOnStart = true;
```

**描述：** 是否在启动时自动初始化测试系统

**默认值：** `true`

**功能：**
- 控制组件是否在 `Start()` 方法中自动执行初始化
- 设置为 `false` 时需要手动调用 `InitializeProgressiveTest()` 方法

##### ShowDetailedLogs

```csharp
public bool ShowDetailedLogs = true;
```

**描述：** 是否显示详细的测试日志

**默认值：** `true`

**功能：**
- 控制测试过程中是否输出详细的调试信息
- 包括各种级别的日志消息（Info、Debug、Warning）

#### 私有字段

- `logger` (ILogger): 日志记录器实例，用于记录测试过程中的信息
- `isInitialized` (bool): 初始化状态标志，防止重复初始化

#### 公共方法

##### InitializeProgressiveTest

初始化渐进式测试系统的主要方法。

```csharp
public void InitializeProgressiveTest()
```

**功能：**
- 检查是否已经初始化，避免重复初始化
- 按步骤初始化日志系统
- 测试基础功能
- 设置初始化完成标志

**执行步骤：**
1. 检查初始化状态
2. 初始化日志系统
3. 测试基础功能
4. 标记初始化完成

**使用示例：**
```csharp
var testManager = gameObject.AddComponent<ProgressiveTestManager>();
testManager.AutoInitializeOnStart = false; // 禁用自动初始化
testManager.InitializeProgressiveTest(); // 手动初始化
```

##### TestNewFeatures

测试新功能模块的方法。

```csharp
public void TestNewFeatures()
```

**功能：**
- 验证系统是否已初始化
- 逐步测试新开发的功能模块
- 当前包含单例管理器测试

**前置条件：** 必须先调用 `InitializeProgressiveTest()` 完成初始化

**使用示例：**
```csharp
// 确保系统已初始化
if (testManager.isInitialized)
{
    testManager.TestNewFeatures();
}
```

##### GetSystemStatus

获取当前系统状态信息。

```csharp
public string GetSystemStatus()
```

**返回值：** `string` - 包含系统状态详细信息的字符串

**返回信息包括：**
- 初始化状态
- 日志系统状态
- Unity版本信息
- 运行平台信息
- 当前时间

**使用示例：**
```csharp
string status = testManager.GetSystemStatus();
Debug.Log(status);
```

#### 私有方法

##### InitializeLoggingSystem

初始化日志系统的内部方法。

```csharp
private void InitializeLoggingSystem()
```

**功能：**
- 获取 `LogManager` 单例实例
- 创建模块化日志记录器
- 测试日志功能（如果启用详细日志）
- 验证日志系统正常工作

**测试内容：**
- 日志管理器实例获取
- 模块化日志记录器创建
- 各级别日志消息输出测试

##### TestBasicFunctionality

测试Unity基础功能的内部方法。

```csharp
private void TestBasicFunctionality()
```

**功能：**
- 验证Unity环境信息
- 测试GameObject创建和销毁
- 确保基础Unity功能正常

**测试内容：**
- Unity版本信息
- 运行平台信息
- 系统时间获取
- GameObject生命周期管理

##### TestSingletonManager

测试单例管理器功能的内部方法。

```csharp
private void TestSingletonManager()
```

**功能：**
- 验证单例模式实现
- 测试多次获取实例的一致性
- 确保单例管理器正常工作

**测试逻辑：**
```csharp
var logManager1 = LogManager.Instance;
var logManager2 = LogManager.Instance;
// 验证两个引用指向同一个实例
```

#### Unity生命周期方法

##### Start

Unity组件启动方法。

```csharp
void Start()
```

**功能：**
- 检查 `AutoInitializeOnStart` 设置
- 自动执行初始化（如果启用）

##### OnGUI

Unity GUI渲染方法，提供可视化控制面板。

```csharp
void OnGUI()
```

**功能：**
- 显示测试系统状态
- 提供交互式控制按钮
- 显示使用说明

**控制面板功能：**
- **初始化测试系统**: 手动触发初始化
- **测试新功能模块**: 执行新功能测试
- **显示系统状态**: 输出详细状态信息
- **测试日志功能**: 验证日志系统工作
- **清除控制台**: 输出分隔线便于查看

##### OnApplicationQuit

应用程序退出时的清理方法。

```csharp
void OnApplicationQuit()
```

**功能：**
- 记录退出日志
- 执行必要的清理操作

## 使用指南

### 基本使用

#### 1. 添加组件到场景

```csharp
// 在场景中创建GameObject并添加组件
var testObject = new GameObject("ProgressiveTestManager");
var testManager = testObject.AddComponent<ProgressiveTestManager>();
```

#### 2. 配置测试参数

```csharp
// 配置自动初始化
testManager.AutoInitializeOnStart = true;

// 配置详细日志
testManager.ShowDetailedLogs = true;
```

#### 3. 手动控制测试流程

```csharp
// 禁用自动初始化，手动控制
testManager.AutoInitializeOnStart = false;

// 手动初始化
testManager.InitializeProgressiveTest();

// 测试新功能
testManager.TestNewFeatures();

// 获取状态信息
string status = testManager.GetSystemStatus();
Debug.Log(status);
```

### 高级使用

#### 1. 集成到现有测试流程

```csharp
public class CustomTestRunner : MonoBehaviour
{
    private ProgressiveTestManager progressiveTest;
    
    void Start()
    {
        // 创建渐进式测试管理器
        progressiveTest = gameObject.AddComponent<ProgressiveTestManager>();
        progressiveTest.AutoInitializeOnStart = false;
        
        // 执行自定义测试流程
        StartCoroutine(RunCustomTestSequence());
    }
    
    IEnumerator RunCustomTestSequence()
    {
        // 步骤1: 初始化基础系统
        progressiveTest.InitializeProgressiveTest();
        yield return new WaitForSeconds(1f);
        
        // 步骤2: 测试新功能
        progressiveTest.TestNewFeatures();
        yield return new WaitForSeconds(1f);
        
        // 步骤3: 验证系统状态
        string status = progressiveTest.GetSystemStatus();
        Debug.Log($"测试完成状态: {status}");
    }
}
```

#### 2. 扩展新功能测试

```csharp
// 可以通过继承扩展测试功能
public class ExtendedProgressiveTestManager : ProgressiveTestManager
{
    /// <summary>
    /// 测试自定义功能模块
    /// </summary>
    public void TestCustomFeatures()
    {
        Debug.Log("=== 开始测试自定义功能 ===");
        
        try
        {
            // 添加自定义功能测试逻辑
            TestCustomModule1();
            TestCustomModule2();
            
            Debug.Log("=== 自定义功能测试完成 ===");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"自定义功能测试失败: {ex.Message}");
        }
    }
    
    private void TestCustomModule1()
    {
        // 自定义模块1测试逻辑
        Debug.Log("✓ 自定义模块1测试通过");
    }
    
    private void TestCustomModule2()
    {
        // 自定义模块2测试逻辑
        Debug.Log("✓ 自定义模块2测试通过");
    }
}
```

### 在Editor中使用

#### 1. Inspector配置

在Unity Inspector中可以直接配置：
- `Auto Initialize On Start`: 勾选启用自动初始化
- `Show Detailed Logs`: 勾选显示详细日志

#### 2. 运行时控制面板

运行游戏后，屏幕左上角会显示控制面板，包含：
- 系统状态显示
- 交互式控制按钮
- 使用说明

#### 3. 控制台输出

测试过程中会在Unity控制台输出详细信息：
```
=== 开始渐进式测试初始化 ===
步骤1: 初始化日志系统
✓ 日志系统初始化成功
步骤2: 测试基础功能
✓ Unity版本: 2022.3.0f1
✓ 平台: WindowsEditor
✓ 当前时间: 2025-01-24 14:30:22
✓ GameObject创建成功
✓ GameObject销毁成功
=== 渐进式测试初始化完成 ===
```

## 最佳实践

### 1. 渐进式开发集成

```csharp
// 在开发新功能时，先添加到测试管理器
public class NewFeatureTestManager : ProgressiveTestManager
{
    protected override void TestNewFeatures()
    {
        base.TestNewFeatures(); // 先执行基础测试
        
        // 然后测试新功能
        TestNewFeature1();
        TestNewFeature2();
    }
    
    private void TestNewFeature1()
    {
        Debug.Log("测试新功能1...");
        // 新功能1的测试逻辑
    }
}
```

### 2. 错误处理和恢复

```csharp
public void SafeTestExecution()
{
    try
    {
        InitializeProgressiveTest();
        TestNewFeatures();
    }
    catch (System.Exception ex)
    {
        Debug.LogError($"测试执行失败: {ex.Message}");
        
        // 尝试恢复或提供降级方案
        RecoverFromTestFailure();
    }
}

private void RecoverFromTestFailure()
{
    Debug.Log("尝试从测试失败中恢复...");
    // 实现恢复逻辑
}
```

### 3. 性能监控

```csharp
public void MonitorTestPerformance()
{
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();
    
    InitializeProgressiveTest();
    
    stopwatch.Stop();
    Debug.Log($"初始化耗时: {stopwatch.ElapsedMilliseconds}ms");
    
    if (stopwatch.ElapsedMilliseconds > 1000)
    {
        Debug.LogWarning("初始化时间过长，可能存在性能问题");
    }
}
```

## 注意事项

### 1. 使用限制

- 该组件主要用于开发和测试阶段
- 不建议在生产环境中保留
- GUI控制面板仅在开发模式下显示

### 2. 性能考虑

- 测试过程中会创建临时GameObject
- 详细日志会增加控制台输出
- 建议在发布版本中禁用或移除

### 3. 依赖关系

- 依赖于 `DigitalHuman.Core.Logging` 命名空间
- 需要 `LogManager` 单例正常工作
- 使用Unity的MonoBehaviour生命周期

### 4. 线程安全

- 所有操作都在主线程中执行
- 不涉及多线程操作
- 适合Unity的单线程模型

## 扩展开发

### 添加新的测试模块

```csharp
// 在TestNewFeatures方法中添加新的测试
private void TestNewFeatures()
{
    if (!isInitialized)
    {
        Debug.LogWarning("请先初始化渐进式测试");
        return;
    }
    
    Debug.Log("=== 开始测试新功能模块 ===");
    
    try
    {
        TestSingletonManager();
        TestNewModule1(); // 新增测试模块
        TestNewModule2(); // 新增测试模块
        
        Debug.Log("=== 新功能模块测试完成 ===");
    }
    catch (System.Exception ex)
    {
        Debug.LogError($"测试新功能时发生异常: {ex.Message}");
    }
}
```

### 自定义GUI界面

```csharp
// 扩展OnGUI方法添加自定义控件
void OnGUI()
{
    // 调用基础GUI
    base.OnGUI();
    
    // 添加自定义控件
    GUILayout.BeginArea(new Rect(370, 10, 200, 300));
    GUILayout.Label("自定义测试控制", GUI.skin.box);
    
    if (GUILayout.Button("自定义测试1"))
    {
        // 执行自定义测试
    }
    
    GUILayout.EndArea();
}
```

## 相关文档

- [渐进式开发指南](.kiro/steering/渐进式开发.md)
- [日志系统文档](../logging/LogManager.md)
- [单例管理器文档](../core/SingletonManager.md)
- [测试系统总体架构](README.md)

## 版本历史

- **v1.0**: 初始版本，提供基础的渐进式测试功能
  - 自动初始化支持
  - 日志系统集成测试
  - 基础功能验证
  - 可视化控制面板
  - 单例管理器测试