# MinimalLogTest 最小化日志测试文档

## 概述

`MinimalLogTest` 是一个轻量级的日志系统测试组件，专门用于验证日志系统的基础功能。它采用渐进式开发的理念，只测试核心功能，避免复杂的集成测试，确保日志系统的基本可用性。

## 命名空间

```csharp
DigitalHuman.Tests
```

## 类定义

### MinimalLogTest

最小化日志测试主类，继承自 `MonoBehaviour`，提供简单直观的日志系统功能验证。

#### 继承关系

```csharp
public class MinimalLogTest : MonoBehaviour
```

#### 私有字段

- `testCompleted` (bool): 测试是否完成的标志
- `testResult` (string): 测试结果描述

#### 生命周期方法

##### Start

Unity 生命周期方法，在组件启动时自动执行测试。

```csharp
void Start()
```

**功能：**
- 输出测试开始日志
- 自动调用 `RunMinimalLogTest()` 方法

**使用场景：**
- 将组件添加到 GameObject 后自动执行测试
- 适合快速验证日志系统状态

#### 公共方法

##### RunMinimalLogTest

执行最小化日志测试的核心方法。

```csharp
public void RunMinimalLogTest()
```

**功能：**
- 测试 Unity 基础日志功能
- 验证自定义日志管理器的可用性
- 测试模块化日志记录器功能
- 记录测试结果和状态

**测试流程：**
1. **Unity 基础日志测试**
   - `Debug.Log()` - 普通信息日志
   - `Debug.LogWarning()` - 警告日志
   - `Debug.LogError()` - 错误日志

2. **自定义日志系统测试**
   - 获取 `LogManager.Instance`
   - 创建模块化日志记录器
   - 测试不同级别的日志记录

3. **结果评估**
   - "测试成功" - 所有功能正常
   - "部分成功" - 部分功能可用
   - "测试失败" - 出现异常错误

**使用示例：**
```csharp
// 手动执行测试
var testComponent = gameObject.GetComponent<MinimalLogTest>();
testComponent.RunMinimalLogTest();

// 或者通过 GUI 按钮触发
```

**异常处理：**
- 捕获日志管理器获取异常
- 捕获日志记录器创建异常
- 捕获日志记录操作异常
- 所有异常都会被记录但不会中断测试

#### GUI 方法

##### OnGUI

Unity GUI 渲染方法，提供可视化的测试界面。

```csharp
void OnGUI()
```

**界面元素：**
- **测试状态显示**
  - 当前测试结果
  - 测试完成状态

- **交互按钮**
  - "重新运行测试" - 重新执行测试流程
  - "输出系统信息" - 显示系统环境信息

- **系统信息输出**
  - Unity 版本信息
  - 运行平台信息
  - 当前时间戳

**界面布局：**
```
┌─────────────────────────┐
│    最小化日志测试        │
├─────────────────────────┤
│ 测试结果: [状态]         │
│ 测试完成: [是/否]        │
├─────────────────────────┤
│ [重新运行测试]           │
│ [输出系统信息]           │
└─────────────────────────┘
```

## 测试覆盖范围

### 1. Unity 基础日志系统
- `Debug.Log()` 功能验证
- `Debug.LogWarning()` 功能验证
- `Debug.LogError()` 功能验证

### 2. 自定义日志管理器
- `LogManager.Instance` 单例获取
- 日志管理器初始化状态检查
- 基本可用性验证

### 3. 模块化日志记录器
- `GetLogger()` 方法功能
- 不同日志级别记录
  - `Info()` 信息日志
  - `Debug()` 调试日志
  - `Warn()` 警告日志
  - `Error()` 错误日志

### 4. 异常处理机制
- 日志系统初始化异常
- 日志记录操作异常
- 测试流程异常恢复

## 使用指南

### 基本使用

1. **添加到场景**
```csharp
// 在 Unity Editor 中
// 1. 创建空的 GameObject
// 2. 添加 MinimalLogTest 组件
// 3. 运行场景，测试会自动开始
```

2. **代码中使用**
```csharp
// 动态添加测试组件
var testObject = new GameObject("LogTest");
var testComponent = testObject.AddComponent<MinimalLogTest>();

// 手动执行测试
testComponent.RunMinimalLogTest();
```

### 集成到测试流程

```csharp
public class TestManager : MonoBehaviour
{
    void Start()
    {
        // 作为测试套件的一部分
        RunBasicTests();
    }
    
    void RunBasicTests()
    {
        // 1. 运行最小化日志测试
        var logTest = gameObject.AddComponent<MinimalLogTest>();
        
        // 2. 等待测试完成后继续其他测试
        StartCoroutine(WaitForLogTestCompletion(logTest));
    }
    
    IEnumerator WaitForLogTestCompletion(MinimalLogTest logTest)
    {
        while (!logTest.testCompleted)
        {
            yield return new WaitForSeconds(0.1f);
        }
        
        Debug.Log("日志测试完成，继续其他测试...");
        // 继续其他测试流程
    }
}
```

### 自定义测试扩展

```csharp
public class ExtendedLogTest : MinimalLogTest
{
    /// <summary>
    /// 扩展测试功能
    /// </summary>
    public void RunExtendedTest()
    {
        // 先运行基础测试
        RunMinimalLogTest();
        
        // 添加额外的测试逻辑
        TestLogLevels();
        TestLogFormatters();
        TestLogWriters();
    }
    
    private void TestLogLevels()
    {
        var logger = DigitalHuman.Core.Logging.LogManager.Instance?.GetLogger("ExtendedTest");
        if (logger != null)
        {
            // 测试所有日志级别
            logger.Trace("跟踪日志测试");
            logger.Debug("调试日志测试");
            logger.Info("信息日志测试");
            logger.Warn("警告日志测试");
            logger.Error("错误日志测试");
            logger.Fatal("致命错误日志测试");
        }
    }
}
```

## 测试结果解释

### 测试状态

- **"未开始"** - 测试尚未执行
- **"测试中..."** - 测试正在进行
- **"测试成功"** - 所有功能正常工作
- **"部分成功"** - 部分功能可用，但存在问题
- **"测试失败"** - 测试过程中出现严重错误

### 成功标准

测试被认为成功需要满足：
1. Unity 基础日志功能正常
2. LogManager 单例获取成功
3. 模块化日志记录器创建成功
4. 各级别日志记录正常执行
5. 无未处理异常抛出

### 故障排除

#### 常见问题

1. **"日志管理器获取失败"**
   - 检查 LogManager 是否正确初始化
   - 确认单例模式实现正确
   - 验证命名空间引用

2. **"日志记录器获取失败"**
   - 检查 GetLogger 方法实现
   - 确认模块名称参数有效
   - 验证日志记录器工厂

3. **日志记录异常**
   - 检查日志级别配置
   - 确认日志写入器状态
   - 验证日志格式化器

#### 调试建议

```csharp
// 启用详细日志输出
void OnGUI()
{
    // 添加调试信息显示
    if (GUILayout.Button("显示调试信息"))
    {
        var logManager = DigitalHuman.Core.Logging.LogManager.Instance;
        Debug.Log($"LogManager 状态: {(logManager != null ? "已初始化" : "未初始化")}");
        
        if (logManager != null)
        {
            Debug.Log($"日志功能启用: {logManager.IsLoggingEnabled}");
            
            var logger = logManager.GetLogger("DebugTest");
            Debug.Log($"Logger 状态: {(logger != null ? "正常" : "异常")}");
        }
    }
}
```

## 最佳实践

### 1. 渐进式测试
- 从最基础的功能开始测试
- 逐步增加测试复杂度
- 确保每个层级都稳定后再继续

### 2. 异常处理
- 所有测试操作都应包含异常处理
- 异常不应中断整个测试流程
- 记录详细的错误信息用于调试

### 3. 状态管理
- 明确的测试状态标识
- 可重复执行的测试流程
- 清晰的结果反馈

### 4. 用户体验
- 提供直观的 GUI 界面
- 实时显示测试进度
- 支持手动重新测试

## 注意事项

1. **接口方法使用**
   - 使用标准的 `logger.Warn()` 方法记录警告日志
   - 确保与 ILogger 接口定义保持一致

2. **测试范围限制**
   - 这是最小化测试，不包含复杂的集成测试
   - 不测试日志持久化、轮转等高级功能
   - 主要验证基本可用性

3. **依赖关系**
   - 依赖于 LogManager 的正确实现
   - 需要 Unity 环境支持
   - 要求正确的命名空间引用

## 相关文档

- [日志系统总体架构](../logging/README.md)
- [LogManager 日志管理器文档](../logging/LogManager.md)
- [测试系统概览](README.md)
- [CoreFeaturesTest 核心功能测试](CoreFeaturesTest.md)
- [ProgressiveTestManager 渐进式测试管理器](ProgressiveTestManager.md)

## 版本历史

- **v1.0**: 初始版本，提供基础日志系统测试功能
- 支持 Unity 基础日志和自定义日志系统测试
- 包含简单的 GUI 界面和状态管理