# CoreFeaturesTest 核心功能测试文档

## 概述

`CoreFeaturesTest` 是专门用于测试新开发核心功能的测试组件。它提供了一个完整的测试框架，用于验证日志系统、基础架构、单例管理器和配置系统等核心功能的正确性。该组件支持自动化测试和手动测试，并提供了直观的GUI界面用于测试控制和结果查看。

## 命名空间

```csharp
DigitalHuman.Tests
```

## 类定义

### CoreFeaturesTest

核心功能测试主类，继承自 `MonoBehaviour`，提供完整的核心功能测试套件。

#### 公共属性

##### AutoRunOnStart

```csharp
[Header("测试配置")]
public bool AutoRunOnStart = true;
```

**描述：** 是否在启动时自动运行测试

**默认值：** `true`

**用途：** 控制组件启动时是否自动执行测试，适用于自动化测试场景

##### TestDelaySeconds

```csharp
public float TestDelaySeconds = 2f;
```

**描述：** 测试延迟时间（秒）

**默认值：** `2.0f`

**用途：** 设置启动后延迟多长时间开始执行测试，确保系统完全初始化

#### 私有字段

- `logger` (ILogger): 日志记录器实例
- `testRunning` (bool): 测试运行状态标志
- `testResults` (string): 测试结果累积字符串

#### 生命周期方法

##### Start

```csharp
void Start()
```

**功能：**
- 初始化测试组件
- 根据 `AutoRunOnStart` 设置决定是否自动启动测试
- 输出初始化日志

**使用示例：**
```csharp
// 在Unity中添加组件后，Start方法会自动调用
// 如果AutoRunOnStart为true，会在TestDelaySeconds秒后自动开始测试
```

#### 核心测试方法

##### RunCoreFeatureTestsAsync

```csharp
public async Task RunCoreFeatureTestsAsync()
```

**功能：**
- 执行完整的核心功能测试套件
- 包含日志系统、基础架构、单例管理器和配置系统测试
- 提供异常处理和测试状态管理
- 异步执行，不阻塞主线程

**测试流程：**
1. 检查测试运行状态，防止重复执行
2. 初始化测试环境和结果记录
3. 依次执行各项测试
4. 记录测试结果和异常信息
5. 清理测试状态

**使用示例：**
```csharp
// 手动启动完整测试
var coreTest = GetComponent<CoreFeaturesTest>();
await coreTest.RunCoreFeatureTestsAsync();

// 在按钮点击事件中使用
if (GUILayout.Button("运行核心功能测试"))
{
    _ = RunCoreFeatureTestsAsync();
}
```

##### TestLoggingSystemAsync

```csharp
private async Task TestLoggingSystemAsync()
```

**功能：**
- 测试日志系统的完整功能
- 验证日志管理器和日志记录器的获取
- 测试各种日志级别的记录
- 验证模块化日志功能

**测试项目：**
- 日志管理器单例获取
- 日志记录器创建和获取
- Debug、Info、Warning、Error级别日志记录
- 模块化日志记录器功能

**使用示例：**
```csharp
// 单独测试日志系统
await TestLoggingSystemAsync();
```

##### TestBaseArchitectureAsync

```csharp
private async Task TestBaseArchitectureAsync()
```

**功能：**
- 测试Unity基础架构功能
- 验证GameObject创建和销毁
- 测试协程功能
- 检查Unity环境信息

**测试项目：**
- Unity版本和平台信息
- GameObject生命周期管理
- 协程执行机制
- 基础Unity API功能

**使用示例：**
```csharp
// 单独测试基础架构
await TestBaseArchitectureAsync();
```

##### TestSingletonManagerAsync

```csharp
private async Task TestSingletonManagerAsync()
```

**功能：**
- 测试单例管理器模式的正确性
- 验证单例实例的唯一性
- 测试多次获取实例的一致性

**测试项目：**
- 单例模式实现验证
- 实例唯一性检查
- 多次获取一致性测试

**使用示例：**
```csharp
// 单独测试单例管理器
await TestSingletonManagerAsync();
```

##### TestConfigurationSystemAsync

```csharp
private async Task TestConfigurationSystemAsync()
```

**功能：**
- 测试配置系统基础功能
- 验证PlayerPrefs读写操作
- 测试JSON序列化功能

**测试项目：**
- PlayerPrefs存储和读取
- JSON序列化和反序列化
- 配置数据持久化

**使用示例：**
```csharp
// 单独测试配置系统
await TestConfigurationSystemAsync();
```

#### 辅助方法

##### RunTestAfterDelay

```csharp
private System.Collections.IEnumerator RunTestAfterDelay()
```

**功能：**
- 延迟执行测试的协程方法
- 等待指定时间后启动测试
- 确保系统完全初始化后再开始测试

##### TestCoroutine

```csharp
private System.Collections.IEnumerator TestCoroutine()
```

**功能：**
- 测试协程功能的辅助方法
- 验证协程的正常执行
- 记录协程测试结果

##### AddTestResult

```csharp
private void AddTestResult(string result)
```

**参数：**
- `result` (string): 测试结果描述

**功能：**
- 添加测试结果到累积字符串
- 同时输出到Unity控制台
- 通过日志系统记录结果

**使用示例：**
```csharp
AddTestResult("✅ 测试项目通过");
AddTestResult("❌ 测试项目失败");
```

##### GetTestResultsSummary

```csharp
public string GetTestResultsSummary()
```

**返回值：** `string` - 测试结果摘要

**功能：**
- 获取完整的测试结果摘要
- 返回所有测试结果的累积字符串
- 用于外部查询测试状态

**使用示例：**
```csharp
var summary = coreTest.GetTestResultsSummary();
Debug.Log($"测试摘要: {summary}");
```

#### GUI界面

##### OnGUI

```csharp
void OnGUI()
```

**功能：**
- 提供可视化的测试控制界面
- 显示测试状态和结果
- 提供手动测试控制按钮
- 实时显示测试进度

**界面元素：**
- 测试状态显示
- 各种测试控制按钮
- 测试结果实时显示
- 结果清理功能

**界面布局：**
```
┌─────────────────────────────┐
│     核心功能测试面板         │
├─────────────────────────────┤
│ 测试运行中: 是/否           │
│ 日志系统: 正常/未初始化     │
├─────────────────────────────┤
│ [运行核心功能测试]          │
│ [测试日志系统]              │
│ [测试基础架构]              │
│ [显示测试结果]              │
│ [清除测试结果]              │
├─────────────────────────────┤
│ 最近测试结果:               │
│ ✅ 日志管理器获取成功       │
│ ✅ 日志记录器获取成功       │
│ ✅ 日志级别测试完成         │
│ ...                         │
└─────────────────────────────┘
```

## 使用指南

### 基本使用

#### 1. 添加到场景

```csharp
// 在Unity中创建空GameObject
var testObject = new GameObject("CoreFeaturesTest");

// 添加测试组件
var coreTest = testObject.AddComponent<CoreFeaturesTest>();

// 配置测试参数
coreTest.AutoRunOnStart = true;
coreTest.TestDelaySeconds = 3.0f;
```

#### 2. 自动化测试

```csharp
// 启用自动运行
coreTest.AutoRunOnStart = true;

// 设置延迟时间
coreTest.TestDelaySeconds = 2.0f;

// 启动场景后会自动执行测试
```

#### 3. 手动测试

```csharp
// 禁用自动运行
coreTest.AutoRunOnStart = false;

// 手动启动完整测试
await coreTest.RunCoreFeatureTestsAsync();

// 或使用GUI界面按钮进行测试
```

### 高级使用

#### 1. 集成到测试套件

```csharp
public class TestSuite : MonoBehaviour
{
    private CoreFeaturesTest coreTest;
    
    void Start()
    {
        coreTest = GetComponent<CoreFeaturesTest>();
        StartCoroutine(RunTestSequence());
    }
    
    IEnumerator RunTestSequence()
    {
        // 等待系统初始化
        yield return new WaitForSeconds(1.0f);
        
        // 运行核心功能测试
        yield return StartCoroutine(RunCoreTestsCoroutine());
        
        // 运行其他测试...
    }
    
    IEnumerator RunCoreTestsCoroutine()
    {
        var testTask = coreTest.RunCoreFeatureTestsAsync();
        yield return new WaitUntil(() => testTask.IsCompleted);
        
        // 检查测试结果
        var results = coreTest.GetTestResultsSummary();
        ProcessTestResults(results);
    }
}
```

#### 2. 自定义测试扩展

```csharp
public class ExtendedCoreTest : CoreFeaturesTest
{
    /// <summary>
    /// 扩展测试方法
    /// </summary>
    protected async Task TestCustomFeatureAsync()
    {
        AddTestResult("--- 测试自定义功能 ---");
        
        try
        {
            // 自定义测试逻辑
            await CustomTestLogic();
            AddTestResult("✅ 自定义功能测试通过");
        }
        catch (System.Exception ex)
        {
            AddTestResult($"❌ 自定义功能测试失败: {ex.Message}");
        }
    }
    
    private async Task CustomTestLogic()
    {
        // 实现自定义测试逻辑
        await Task.Delay(100);
    }
}
```

#### 3. 测试结果处理

```csharp
public class TestResultProcessor : MonoBehaviour
{
    public void ProcessTestResults(CoreFeaturesTest coreTest)
    {
        var results = coreTest.GetTestResultsSummary();
        
        // 分析测试结果
        var lines = results.Split('\n');
        int passCount = 0;
        int failCount = 0;
        
        foreach (var line in lines)
        {
            if (line.Contains("✅"))
                passCount++;
            else if (line.Contains("❌"))
                failCount++;
        }
        
        // 生成测试报告
        var report = $"测试报告:\n通过: {passCount}\n失败: {failCount}";
        Debug.Log(report);
        
        // 保存测试结果
        SaveTestResults(results);
    }
    
    private void SaveTestResults(string results)
    {
        var timestamp = System.DateTime.Now.ToString("yyyyMMdd_HHmmss");
        var filename = $"CoreTestResults_{timestamp}.txt";
        System.IO.File.WriteAllText(filename, results);
    }
}
```

## 测试项目详解

### 1. 日志系统测试

**测试目标：** 验证日志系统的完整功能

**测试步骤：**
1. 获取日志管理器单例实例
2. 创建模块化日志记录器
3. 测试各种日志级别记录
4. 验证模块化日志功能

**预期结果：**
- 日志管理器成功获取
- 日志记录器正常创建
- 各级别日志正常记录
- 模块化功能正常工作

### 2. 基础架构测试

**测试目标：** 验证Unity基础架构功能

**测试步骤：**
1. 检查Unity环境信息
2. 测试GameObject生命周期
3. 验证协程功能
4. 检查基础API可用性

**预期结果：**
- Unity环境信息正确
- GameObject创建销毁正常
- 协程执行正常
- 基础API功能可用

### 3. 单例管理器测试

**测试目标：** 验证单例模式实现

**测试步骤：**
1. 多次获取单例实例
2. 比较实例引用一致性
3. 验证单例唯一性

**预期结果：**
- 所有获取的实例引用相同
- 单例模式正确实现
- 实例唯一性得到保证

### 4. 配置系统测试

**测试目标：** 验证配置系统基础功能

**测试步骤：**
1. 测试PlayerPrefs读写
2. 验证JSON序列化
3. 检查数据持久化

**预期结果：**
- PlayerPrefs读写正常
- JSON序列化功能正常
- 数据持久化机制可用

## 最佳实践

### 1. 测试环境准备

```csharp
// 确保测试环境干净
void PrepareTestEnvironment()
{
    // 清理之前的测试数据
    PlayerPrefs.DeleteAll();
    
    // 重置测试状态
    testResults = "";
    testRunning = false;
    
    // 确保日志系统可用
    if (LogManager.Instance == null)
    {
        Debug.LogError("日志系统未初始化");
    }
}
```

### 2. 异常处理

```csharp
// 在每个测试方法中使用try-catch
private async Task TestWithExceptionHandling()
{
    try
    {
        // 测试逻辑
        await SomeTestOperation();
        AddTestResult("✅ 测试通过");
    }
    catch (System.Exception ex)
    {
        AddTestResult($"❌ 测试失败: {ex.Message}");
        Debug.LogException(ex);
    }
}
```

### 3. 测试隔离

```csharp
// 确保测试之间相互独立
private async Task IsolatedTest()
{
    // 保存当前状态
    var originalState = SaveCurrentState();
    
    try
    {
        // 执行测试
        await RunTest();
    }
    finally
    {
        // 恢复状态
        RestoreState(originalState);
    }
}
```

### 4. 结果验证

```csharp
// 使用断言验证测试结果
private void AssertTestResult(bool condition, string message)
{
    if (condition)
    {
        AddTestResult($"✅ {message}");
    }
    else
    {
        AddTestResult($"❌ {message}");
        throw new System.Exception($"断言失败: {message}");
    }
}
```

## 故障排除

### 常见问题

1. **测试无法启动**
   - 检查AutoRunOnStart设置
   - 确认TestDelaySeconds配置
   - 验证组件是否正确添加

2. **日志系统测试失败**
   - 确认LogManager是否正确初始化
   - 检查日志系统依赖项
   - 验证单例模式实现

3. **GUI界面不显示**
   - 确认OnGUI方法正常调用
   - 检查屏幕分辨率和布局
   - 验证GUI样式设置

4. **异步测试异常**
   - 检查async/await使用
   - 确认异常处理机制
   - 验证任务完成状态

### 调试技巧

```csharp
// 启用详细日志
private void EnableVerboseLogging()
{
    if (logger != null)
    {
        logger.Debug("详细测试日志已启用");
    }
}

// 添加测试断点
private void AddTestBreakpoint(string testName)
{
    Debug.Log($"测试断点: {testName}");
    // 在此处设置断点进行调试
}
```

## 扩展开发

### 添加新测试

```csharp
// 添加新的测试方法
private async Task TestNewFeatureAsync()
{
    AddTestResult("--- 测试新功能 ---");
    
    try
    {
        // 新功能测试逻辑
        await NewFeatureTestLogic();
        AddTestResult("✅ 新功能测试完成");
    }
    catch (System.Exception ex)
    {
        AddTestResult($"❌ 新功能测试异常: {ex.Message}");
    }
}

// 在RunCoreFeatureTestsAsync中调用
public async Task RunCoreFeatureTestsAsync()
{
    // ... 现有测试 ...
    
    // 添加新测试
    await TestNewFeatureAsync();
    
    // ... 其余代码 ...
}
```

### 自定义测试结果格式

```csharp
// 自定义结果格式化
private string FormatTestResult(string testName, bool passed, string details = "")
{
    var icon = passed ? "✅" : "❌";
    var status = passed ? "通过" : "失败";
    var timestamp = System.DateTime.Now.ToString("HH:mm:ss");
    
    return $"[{timestamp}] {icon} {testName}: {status} {details}";
}
```

## 依赖项

- `UnityEngine`: Unity核心功能
- `System.Threading.Tasks`: 异步操作支持
- `DigitalHuman.Core.Logging`: 日志系统集成

## 相关文档

- [ProgressiveTestManager 渐进式测试管理器文档](ProgressiveTestManager.md)
- [ComprehensiveIntegrationTest 综合集成测试文档](ComprehensiveIntegrationTest.md)
- [LogManager 日志管理器文档](../logging/LogManager.md)
- [SingletonManager 单例管理器文档](../core/SingletonManager.md)
- [测试系统总体架构](README.md)