# Mac环境安装指南

在Mac上测试这个Unity项目，你需要安装以下环境：

## 🔧 必需环境

### 1. Unity编辑器
**推荐版本：Unity 2022.3 LTS 或更新版本**

**安装方式：**
```bash
# 方式1: 通过Unity Hub（推荐）
# 1. 下载Unity Hub: https://unity.com/download
# 2. 通过Hub安装Unity 2022.3 LTS

# 方式2: 通过Homebrew
brew install --cask unity-hub
```

**为什么需要这个版本：**
- 我们使用了UI Toolkit，需要Unity 2021.3+
- 使用了较新的C# 语法特性
- 项目配置基于较新的Unity版本

### 2. .NET环境
Unity会自带，但确保系统有：
```bash
# 检查是否已安装
dotnet --version

# 如果没有，通过Homebrew安装
brew install dotnet
```

### 3. 必需的Unity模块
在Unity Hub中安装时，确保勾选：
- ✅ **Mac Build Support (Mono)**
- ✅ **Universal Windows Platform Build Support** (如果需要Windows构建)
- ✅ **Documentation** (可选，但推荐)

## 📋 详细安装步骤

### 步骤1: 安装Unity Hub
```bash
# 下载并安装Unity Hub
open https://unity.com/download

# 或使用Homebrew
brew install --cask unity-hub
```

### 步骤2: 通过Unity Hub安装Unity编辑器
1. 打开Unity Hub
2. 点击"Installs"标签
3. 点击"Install Editor"
4. 选择"Unity 2022.3 LTS"（推荐）或更新版本
5. 在模块选择中勾选：
   - Mac Build Support (Mono)
   - Documentation

### 步骤3: 打开项目
1. 在Unity Hub中点击"Projects"
2. 点击"Open"
3. 选择项目根目录（包含Assets文件夹的目录）
4. Unity会自动导入项目

## 🧪 测试步骤

### 1. 验证项目导入
打开Unity后，检查：
- Console窗口没有严重错误
- Project窗口显示完整的文件结构
- Assets/Scripts下的所有脚本都能正常编译

### 2. 运行单元测试
```
Window > General > Test Runner
```
- 切换到"EditMode"标签
- 点击"Run All"运行所有测试
- 确保配置管理相关测试通过

### 3. 测试设置界面
1. 打开MainScene: `Assets/Scenes/MainScene.unity`
2. 创建测试设置：
   - 在Hierarchy中创建空GameObject
   - 重命名为"TestManager"
   - 添加`TestSceneManager`组件
   - 创建UI Document组件并分配UXML文件

### 4. 运行场景测试
1. 点击Play按钮
2. 按F1键打开设置界面
3. 测试各个设置面板的功能

## 🔍 故障排除

### 常见问题1: 编译错误
```bash
# 如果遇到编译错误，尝试：
# 1. 删除Library文件夹
rm -rf Library/

# 2. 重新打开Unity项目
```

### 常见问题2: UI Toolkit相关错误
确保Unity版本 >= 2021.3，UI Toolkit在较老版本中不完整

### 常见问题3: 缺少依赖
检查`Packages/manifest.json`，确保包含：
```json
{
  "dependencies": {
    "com.unity.ui": "1.0.0-preview.18",
    "com.unity.ui.builder": "1.0.0-preview.18"
  }
}
```

## 💡 推荐的开发环境

### IDE选择
1. **Visual Studio for Mac** (免费)
2. **JetBrains Rider** (付费，功能强大)
3. **Visual Studio Code** (免费，需要C#扩展)

### 安装VS Code + C#扩展
```bash
brew install --cask visual-studio-code

# 在VS Code中安装扩展：
# - C# for Visual Studio Code
# - Unity Code Snippets
```

## 🚀 快速验证

运行项目根目录下的验证脚本：
```bash
./verify_environment.sh
```

或手动检查：
```bash
# 检查Unity Hub
ls /Applications/Unity\ Hub.app

# 检查Unity编辑器
ls /Applications/Unity/Hub/Editor/

# 检查.NET
dotnet --version
```

## 📊 总结

**最小环境要求：**
- ✅ **Unity 2022.3 LTS** (约4GB)
- ✅ **Unity Hub** (约200MB)
- ✅ **macOS 10.15+** (系统要求)

**推荐环境：**
- ✅ **Visual Studio Code** + C#扩展
- ✅ **.NET SDK** (用于命令行工具)
- ✅ **Git** (版本控制)

**验证步骤：**
```bash
# 运行环境检查脚本
./verify_environment.sh

# 或手动检查Unity安装
ls /Applications/Unity/Hub/Editor/
```

安装完成后，你就可以：
1. 🧪 运行所有单元测试验证核心功能
2. 🎨 查看和测试设置界面UI
3. ⚙️ 测试配置管理、加密存储等功能
4. 🔧 进行进一步的开发和调试

整个安装过程大约需要30分钟到1小时，主要时间花在下载Unity编辑器上。

## 📝 项目功能状态

### ✅ 已完成功能
- **配置管理核心功能** - 100%完成，可测试
- **数据加密存储** - 100%完成，可测试  
- **配置验证** - 100%完成，可测试
- **序列化工具** - 100%完成，可测试
- **单元测试** - 100%完成，可在Unity中运行

### ⚠️ 需要Unity环境测试
- **设置界面UI** - 90%完成，需Unity环境测试

### 🎯 测试重点
1. 配置管理器的加密存储功能
2. 各种配置数据模型的序列化
3. 设置界面的多标签页切换
4. 配置验证和错误处理
5. 配置导入导出功能