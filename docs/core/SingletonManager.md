# SingletonManager 单例管理器基类文档

## 概述

`SingletonManager<T>` 是数字人管理系统的核心基础设施，提供线程安全的单例模式实现。它是一个泛型抽象基类，继承自 `MonoBehaviour`，为系统中的各种管理器提供统一的单例模式支持。

## 命名空间

```csharp
DigitalHuman.Core.Base
```

## 类定义

### SingletonManager<T>

线程安全的单例管理器基类，确保每个管理器类型在整个应用程序生命周期中只有一个实例。

#### 类签名

```csharp
public abstract class SingletonManager<T> : MonoBehaviour where T : SingletonManager<T>
```

**泛型约束：**
- `T` 必须继承自 `SingletonManager<T>`
- 使用 CRTP（奇异递归模板模式）确保类型安全

#### 核心特性

- **线程安全**: 使用双重检查锁定模式确保多线程环境下的安全性
- **自动创建**: 首次访问时自动创建实例
- **场景持久化**: 实例在场景切换时不会被销毁
- **重复实例保护**: 自动检测和销毁重复实例
- **应用退出保护**: 防止在应用退出时创建新实例

### 属性

#### Instance (静态属性)

```csharp
public static T Instance { get; }
```

**描述：** 获取管理器的单例实例

**返回值：** `T` - 管理器实例，如果应用程序正在退出则返回 null

**线程安全：** 是

**实现细节：**
- 使用双重检查锁定模式确保线程安全
- 首次访问时自动创建实例
- 如果场景中不存在实例，会自动创建新的 GameObject 并添加组件
- 自动调用 `DontDestroyOnLoad` 确保实例持久化

**使用示例：**
```csharp
// 获取日志管理器实例
var logManager = LogManager.Instance;
if (logManager != null)
{
    logManager.LogInfo("系统启动", "System");
}

// 获取配置管理器实例
var configManager = ConfigurationManager.Instance;
if (configManager != null && configManager.IsInitialized)
{
    var config = configManager.LoadConfiguration<LogConfiguration>(ConfigurationType.Logging);
}
```

#### IsInitialized

```csharp
public bool IsInitialized { get; private set; }
```

**描述：** 指示管理器是否已完成初始化

**返回值：** `bool` - 如果管理器已初始化返回 true，否则返回 false

**使用示例：**
```csharp
var manager = SomeManager.Instance;
if (manager != null && manager.IsInitialized)
{
    // 管理器已准备就绪，可以安全使用
    manager.DoSomething();
}
else
{
    Debug.LogWarning("管理器尚未初始化");
}
```

### 方法

#### InitializeManager (受保护的虚方法)

```csharp
protected virtual void InitializeManager()
```

**描述：** 初始化管理器的核心方法，子类应重写此方法实现具体的初始化逻辑

**访问级别：** Protected Virtual

**调用时机：**
- 实例首次创建时自动调用
- 在 `Awake` 生命周期中调用
- 确保只初始化一次

**异常处理：**
- 如果初始化失败，会记录错误日志并重新抛出异常
- 初始化失败时 `IsInitialized` 会被设置为 false

**子类实现示例：**
```csharp
public class LogManager : SingletonManager<LogManager>
{
    protected override void InitializeManager()
    {
        if (IsInitialized)
        {
            return;
        }
        
        try
        {
            Debug.Log("[LogManager] 开始初始化");
            
            // 执行具体的初始化逻辑
            LoadConfiguration();
            InitializeWriters();
            InitializeExporter();
            
            IsInitialized = true;
            Debug.Log("[LogManager] 初始化完成");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[LogManager] 初始化失败: {ex.Message}");
            IsInitialized = false;
            throw;
        }
    }
}
```

### Unity 生命周期方法

#### Awake (受保护的虚方法)

```csharp
protected virtual void Awake()
```

**描述：** Unity 的 Awake 生命周期方法，处理单例实例的创建和重复检测

**功能：**
- 设置单例实例引用
- 调用 `DontDestroyOnLoad` 确保对象持久化
- 检测并销毁重复实例
- 调用 `InitializeManager` 进行初始化

**重复实例处理：**
- 如果检测到重复实例，会记录警告日志并销毁多余的实例
- 确保整个应用程序中只有一个实例存在

#### OnApplicationQuit (受保护的虚方法)

```csharp
protected virtual void OnApplicationQuit()
```

**描述：** Unity 的应用退出生命周期方法，设置应用退出标志

**功能：**
- 设置 `_applicationIsQuitting` 标志为 true
- 防止在应用退出过程中创建新的单例实例

#### OnDestroy (受保护的虚方法)

```csharp
protected virtual void OnDestroy()
```

**描述：** Unity 的销毁生命周期方法，清理单例实例引用

**功能：**
- 线程安全地清理静态实例引用
- 确保实例被正确释放

## 使用指南

### 创建自定义管理器

```csharp
using DigitalHuman.Core.Base;

/// <summary>
/// 音频管理器示例
/// </summary>
public class AudioManager : SingletonManager<AudioManager>
{
    [Header("音频配置")]
    [SerializeField] private float masterVolume = 1.0f;
    [SerializeField] private AudioSource musicSource;
    [SerializeField] private AudioSource sfxSource;
    
    /// <summary>
    /// 初始化音频管理器
    /// </summary>
    protected override void InitializeManager()
    {
        if (IsInitialized)
        {
            return;
        }
        
        try
        {
            Debug.Log("[AudioManager] 开始初始化音频系统");
            
            // 初始化音频源
            InitializeAudioSources();
            
            // 加载音频配置
            LoadAudioConfiguration();
            
            // 设置音量
            SetMasterVolume(masterVolume);
            
            IsInitialized = true;
            Debug.Log("[AudioManager] 音频系统初始化完成");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[AudioManager] 初始化失败: {ex.Message}");
            IsInitialized = false;
            throw;
        }
    }
    
    /// <summary>
    /// 播放背景音乐
    /// </summary>
    /// <param name="clip">音频剪辑</param>
    public void PlayMusic(AudioClip clip)
    {
        if (!IsInitialized)
        {
            Debug.LogWarning("[AudioManager] 管理器尚未初始化");
            return;
        }
        
        if (musicSource != null && clip != null)
        {
            musicSource.clip = clip;
            musicSource.Play();
        }
    }
    
    /// <summary>
    /// 设置主音量
    /// </summary>
    /// <param name="volume">音量值 (0-1)</param>
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        
        if (musicSource != null)
            musicSource.volume = masterVolume;
        
        if (sfxSource != null)
            sfxSource.volume = masterVolume;
    }
    
    private void InitializeAudioSources()
    {
        // 创建音频源组件
        if (musicSource == null)
        {
            musicSource = gameObject.AddComponent<AudioSource>();
            musicSource.loop = true;
            musicSource.playOnAwake = false;
        }
        
        if (sfxSource == null)
        {
            sfxSource = gameObject.AddComponent<AudioSource>();
            sfxSource.loop = false;
            sfxSource.playOnAwake = false;
        }
    }
    
    private void LoadAudioConfiguration()
    {
        // 从配置系统加载音频设置
        var configManager = ConfigurationManager.Instance;
        if (configManager != null && configManager.IsInitialized)
        {
            // 加载音频配置逻辑
        }
    }
}
```

### 使用管理器

```csharp
public class GameController : MonoBehaviour
{
    void Start()
    {
        // 获取音频管理器实例
        var audioManager = AudioManager.Instance;
        if (audioManager != null && audioManager.IsInitialized)
        {
            // 播放背景音乐
            AudioClip bgMusic = Resources.Load<AudioClip>("Music/Background");
            audioManager.PlayMusic(bgMusic);
            
            // 设置音量
            audioManager.SetMasterVolume(0.8f);
        }
        
        // 获取日志管理器实例
        var logManager = LogManager.Instance;
        if (logManager != null)
        {
            logManager.LogInfo("游戏控制器启动完成", "GameController");
        }
    }
}
```

### 依赖管理器的初始化顺序

```csharp
public class SystemInitializer : MonoBehaviour
{
    void Start()
    {
        StartCoroutine(InitializeSystemsInOrder());
    }
    
    private IEnumerator InitializeSystemsInOrder()
    {
        // 1. 首先初始化配置管理器
        var configManager = ConfigurationManager.Instance;
        yield return new WaitUntil(() => configManager != null && configManager.IsInitialized);
        
        // 2. 然后初始化日志管理器
        var logManager = LogManager.Instance;
        yield return new WaitUntil(() => logManager != null && logManager.IsInitialized);
        
        // 3. 最后初始化其他管理器
        var audioManager = AudioManager.Instance;
        yield return new WaitUntil(() => audioManager != null && audioManager.IsInitialized);
        
        Debug.Log("所有系统管理器初始化完成");
    }
}
```

## 最佳实践

### 1. 初始化检查

```csharp
public void SomeMethod()
{
    var manager = SomeManager.Instance;
    if (manager == null || !manager.IsInitialized)
    {
        Debug.LogWarning("管理器未准备就绪");
        return;
    }
    
    // 安全使用管理器
    manager.DoSomething();
}
```

### 2. 异常处理

```csharp
protected override void InitializeManager()
{
    if (IsInitialized)
    {
        return;
    }
    
    try
    {
        Debug.Log($"[{GetType().Name}] 开始初始化");
        
        // 初始化逻辑
        PerformInitialization();
        
        IsInitialized = true;
        Debug.Log($"[{GetType().Name}] 初始化完成");
    }
    catch (Exception ex)
    {
        Debug.LogError($"[{GetType().Name}] 初始化失败: {ex.Message}");
        IsInitialized = false;
        
        // 可以选择重新抛出异常或进行恢复处理
        throw;
    }
}
```

### 3. 资源清理

```csharp
protected override void OnDestroy()
{
    try
    {
        // 执行清理逻辑
        CleanupResources();
        
        Debug.Log($"[{GetType().Name}] 资源清理完成");
    }
    catch (Exception ex)
    {
        Debug.LogError($"[{GetType().Name}] 资源清理失败: {ex.Message}");
    }
    finally
    {
        // 调用基类的清理方法
        base.OnDestroy();
    }
}
```

### 4. 配置集成

```csharp
protected override void InitializeManager()
{
    if (IsInitialized)
    {
        return;
    }
    
    try
    {
        // 等待配置管理器初始化
        var configManager = ConfigurationManager.Instance;
        if (configManager == null || !configManager.IsInitialized)
        {
            Debug.LogWarning($"[{GetType().Name}] 配置管理器未初始化，使用默认配置");
        }
        
        // 加载配置
        LoadConfiguration();
        
        // 执行初始化
        PerformInitialization();
        
        IsInitialized = true;
    }
    catch (Exception ex)
    {
        Debug.LogError($"[{GetType().Name}] 初始化失败: {ex.Message}");
        IsInitialized = false;
        throw;
    }
}
```

## 线程安全性

### 双重检查锁定模式

```csharp
public static T Instance
{
    get
    {
        if (_applicationIsQuitting)
        {
            return null;
        }
        
        // 第一次检查（无锁）
        if (_instance == null)
        {
            lock (_lock)
            {
                // 第二次检查（有锁）
                if (_instance == null)
                {
                    // 创建实例
                    _instance = FindObjectOfType<T>();
                    
                    if (_instance == null)
                    {
                        GameObject singletonObject = new GameObject();
                        _instance = singletonObject.AddComponent<T>();
                        singletonObject.name = $"{typeof(T).Name} (Singleton)";
                        DontDestroyOnLoad(singletonObject);
                    }
                    
                    _instance.InitializeManager();
                }
            }
        }
        
        return _instance;
    }
}
```

### 线程安全保证

- **读取操作**: Instance 属性的读取是线程安全的
- **创建操作**: 实例创建使用双重检查锁定，确保只创建一次
- **销毁操作**: OnDestroy 中的清理操作是线程安全的

## 性能考虑

### 1. 延迟初始化

- 实例只在首次访问时创建，避免不必要的资源消耗
- 初始化逻辑只执行一次，后续访问直接返回实例

### 2. 内存管理

- 使用 `DontDestroyOnLoad` 确保实例在场景切换时不被销毁
- 自动检测和销毁重复实例，避免内存泄漏

### 3. 锁优化

- 使用双重检查锁定减少锁竞争
- 只在实例创建时使用锁，正常访问无锁开销

## 调试和故障排除

### 常见问题

1. **管理器未初始化**
   ```csharp
   // 检查初始化状态
   if (manager == null || !manager.IsInitialized)
   {
       Debug.LogWarning("管理器未初始化");
       return;
   }
   ```

2. **重复实例警告**
   ```
   [SingletonManager] 检测到重复的 SomeManager 实例，销毁多余实例
   ```
   - 这是正常行为，系统会自动处理重复实例

3. **应用退出时访问实例**
   ```
   [SingletonManager] 应用程序正在退出，无法访问 SomeManager 实例
   ```
   - 在应用退出过程中访问实例会返回 null，这是预期行为

### 调试工具

```csharp
public static class SingletonDebugger
{
    /// <summary>
    /// 检查所有单例管理器的状态
    /// </summary>
    public static void CheckAllManagers()
    {
        var managers = new[]
        {
            ("LogManager", LogManager.Instance),
            ("ConfigurationManager", ConfigurationManager.Instance),
            ("AudioManager", AudioManager.Instance)
        };
        
        foreach (var (name, instance) in managers)
        {
            if (instance == null)
            {
                Debug.LogWarning($"{name}: 实例为空");
            }
            else if (!instance.IsInitialized)
            {
                Debug.LogWarning($"{name}: 未初始化");
            }
            else
            {
                Debug.Log($"{name}: 正常运行");
            }
        }
    }
}
```

## 扩展功能

### 异步初始化支持

```csharp
public abstract class AsyncSingletonManager<T> : SingletonManager<T> where T : AsyncSingletonManager<T>
{
    protected bool _isInitializing = false;
    
    protected override void InitializeManager()
    {
        if (IsInitialized || _isInitializing)
        {
            return;
        }
        
        _isInitializing = true;
        StartCoroutine(InitializeAsync());
    }
    
    protected virtual IEnumerator InitializeAsync()
    {
        Debug.Log($"[{GetType().Name}] 开始异步初始化");
        
        yield return StartCoroutine(PerformAsyncInitialization());
        
        IsInitialized = true;
        _isInitializing = false;
        
        Debug.Log($"[{GetType().Name}] 异步初始化完成");
    }
    
    protected abstract IEnumerator PerformAsyncInitialization();
}
```

## 依赖项

- `UnityEngine`: Unity 引擎核心功能
- `System`: 基础系统类型和异常处理

## 相关文档

- [LogManager 日志管理器文档](../logging/LogManager.md)
- [ConfigurationManager 配置管理器文档](../configuration/ConfigurationManager.md)
- [事件系统文档](../EventSystem.md)
- [项目架构文档](../../README.md)

## 版本历史

- **v1.0**: 初始实现，提供基础单例模式支持
- **v1.1**: 添加线程安全保护和重复实例检测
- **v1.2**: 增强异常处理和调试功能