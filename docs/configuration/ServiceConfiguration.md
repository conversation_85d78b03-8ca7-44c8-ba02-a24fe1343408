# ServiceConfiguration 服务配置文档

## 概述

`ServiceConfiguration` 是数字人系统的服务配置数据模型，负责管理各种外部服务的连接配置，包括大模型API、语音服务、视觉服务、天气服务等。该配置支持私有化部署、网络配置、安全设置等功能，为系统提供灵活的服务集成能力。

## 命名空间

```csharp
DigitalHuman.Core.Configuration.Models
```

## 主要类定义

### ServiceConfiguration

服务配置主类，包含所有外部服务的配置信息。

#### 属性

##### 大模型API配置
- `LLMConfigurations` (List<LLMApiConfiguration>): 大模型API配置列表
- `ActiveLLMConfigId` (string): 当前激活的大模型配置ID

##### 语音服务配置
- `TTSConfig` (TTSServiceConfiguration): TTS服务配置
- `ASRConfig` (ASRServiceConfiguration): ASR服务配置

##### 视觉服务配置
- `EmotionConfig` (EmotionAnalysisConfiguration): 情感分析服务配置
- `FaceDetectionConfig` (FaceDetectionConfiguration): 人脸检测服务配置

##### 天气服务配置
- `WeatherConfig` (WeatherServiceConfiguration): 天气API配置

##### 私有化部署配置
- `EnablePrivateDeployment` (bool): 是否启用私有化部署
- `PrivateServiceBaseUrl` (string): 私有化服务基础URL
- `PrivateAuthConfig` (PrivateAuthConfiguration): 私有化服务认证配置

##### 网络配置
- `RequestTimeoutSeconds` (int): 请求超时时间（秒），默认30秒
- `RetryCount` (int): 重试次数，默认3次
- `RetryIntervalMs` (int): 重试间隔（毫秒），默认1000毫秒
- `EnableRequestCache` (bool): 是否启用请求缓存，默认true
- `CacheExpirationMinutes` (int): 缓存过期时间（分钟），默认60分钟

##### 安全配置
- `EnableSSLVerification` (bool): 是否启用SSL验证，默认true
- `CustomCertificatePath` (string): 自定义证书路径
- `EnableRequestLogging` (bool): 是否启用请求日志，默认false

##### 元数据
- `CreatedAt` (DateTime): 配置创建时间
- `LastModifiedAt` (DateTime): 配置最后修改时间

#### 公共方法

##### Validate

验证服务配置的有效性。

```csharp
public ValidationResult Validate()
```

**返回值：** `ValidationResult` - 包含验证结果的对象

**验证内容：**
- 大模型配置的完整性和有效性
- 激活配置ID的存在性
- 语音服务配置的有效性
- 网络配置参数的合理性
- 私有化部署配置的完整性

**使用示例：**
```csharp
var serviceConfig = new ServiceConfiguration();
var validationResult = serviceConfig.Validate();

if (validationResult.IsValid)
{
    Console.WriteLine("配置验证通过");
}
else
{
    Console.WriteLine($"配置验证失败：{validationResult.GetErrorsAsString()}");
}

if (validationResult.HasWarnings)
{
    Console.WriteLine($"警告信息：{validationResult.GetWarningsAsString()}");
}
```

##### GetActiveLLMConfiguration

获取当前激活的大模型配置。

```csharp
public LLMApiConfiguration GetActiveLLMConfiguration()
```

**返回值：** `LLMApiConfiguration` - 激活的配置对象，如果未找到则返回null

**逻辑：**
- 如果 `ActiveLLMConfigId` 为空，返回第一个配置
- 否则根据ID查找对应的配置

**使用示例：**
```csharp
var activeConfig = serviceConfig.GetActiveLLMConfiguration();
if (activeConfig != null)
{
    Console.WriteLine($"当前使用的模型：{activeConfig.ModelName}");
    Console.WriteLine($"API端点：{activeConfig.ApiEndpoint}");
}
else
{
    Console.WriteLine("未找到激活的大模型配置");
}
```

##### AddLLMConfiguration

添加或更新大模型配置。

```csharp
public void AddLLMConfiguration(LLMApiConfiguration config)
```

**参数：**
- `config` (LLMApiConfiguration): 要添加的配置对象

**功能：**
- 自动生成配置ID（如果为空）
- 检查ID唯一性，存在则更新，不存在则添加
- 如果是第一个配置，自动设置为激活配置
- 更新最后修改时间

**使用示例：**
```csharp
var llmConfig = new LLMApiConfiguration
{
    Name = "OpenAI GPT-4",
    Provider = "OpenAI",
    ApiEndpoint = "https://api.openai.com/v1/chat/completions",
    ApiKey = "your-api-key",
    ModelName = "gpt-4",
    MaxTokens = 4096,
    Temperature = 0.7f,
    IsEnabled = true
};

serviceConfig.AddLLMConfiguration(llmConfig);
Console.WriteLine($"添加配置成功，ID：{llmConfig.ConfigId}");
```

##### RemoveLLMConfiguration

移除指定的大模型配置。

```csharp
public bool RemoveLLMConfiguration(string configId)
```

**参数：**
- `configId` (string): 要移除的配置ID

**返回值：** `bool` - 是否成功移除

**功能：**
- 根据ID查找并移除配置
- 如果移除的是激活配置，自动选择新的激活配置
- 更新最后修改时间

**使用示例：**
```csharp
bool removed = serviceConfig.RemoveLLMConfiguration("config-id-123");
if (removed)
{
    Console.WriteLine("配置移除成功");
}
else
{
    Console.WriteLine("未找到指定的配置");
}
```

##### ResetToDefaults

重置配置为默认值。

```csharp
public void ResetToDefaults()
```

**功能：**
- 清空所有配置列表
- 重置所有属性为默认值
- 更新最后修改时间

**使用示例：**
```csharp
serviceConfig.ResetToDefaults();
Console.WriteLine("配置已重置为默认值");
```

## 子配置类

### LLMApiConfiguration

大模型API配置类。

#### 属性
- `ConfigId` (string): 配置ID
- `Name` (string): 配置名称
- `Provider` (string): API提供商
- `ApiEndpoint` (string): API端点URL
- `ApiKey` (string): API密钥
- `ModelName` (string): 模型名称
- `MaxTokens` (int): 最大令牌数，默认2048
- `Temperature` (float): 温度参数，默认0.7
- `IsEnabled` (bool): 是否启用，默认true

#### 方法
```csharp
public ValidationResult Validate()
```

**验证内容：**
- 配置名称不能为空
- API端点URL格式正确性
- API密钥不能为空
- 模型名称不能为空
- 最大令牌数范围（1-32768）
- 温度参数范围（0-2.0）

### TTSServiceConfiguration

TTS服务配置类。

#### 属性
- `Provider` (string): 服务提供商，默认"Azure"
- `ApiEndpoint` (string): API端点
- `ApiKey` (string): API密钥
- `DefaultVoice` (string): 默认音色，默认"zh-CN-XiaoxiaoNeural"
- `AudioFormat` (string): 音频格式，默认"audio-16khz-128kbitrate-mono-mp3"
- `IsEnabled` (bool): 是否启用，默认true

#### 方法
```csharp
public ValidationResult Validate()
```

### ASRServiceConfiguration

ASR服务配置类。

#### 属性
- `Provider` (string): 服务提供商，默认"Azure"
- `ApiEndpoint` (string): API端点
- `ApiKey` (string): API密钥
- `Language` (string): 识别语言，默认"zh-CN"
- `AudioFormat` (string): 音频格式，默认"wav"
- `SampleRate` (int): 采样率，默认16000
- `IsEnabled` (bool): 是否启用，默认true

#### 方法
```csharp
public ValidationResult Validate()
```

### EmotionAnalysisConfiguration

情感分析服务配置类。

#### 属性
- `Provider` (string): 服务提供商，默认"Custom"
- `ApiEndpoint` (string): API端点
- `ApiKey` (string): API密钥
- `IsEnabled` (bool): 是否启用，默认false
- `AnalysisIntervalMs` (int): 分析间隔（毫秒），默认1000

#### 方法
```csharp
public ValidationResult Validate()
```

### FaceDetectionConfiguration

人脸检测服务配置类。

#### 属性
- `Provider` (string): 服务提供商，默认"OpenCV"
- `IsEnabled` (bool): 是否启用，默认true
- `DetectionIntervalMs` (int): 检测间隔（毫秒），默认100
- `MinFaceSize` (int): 最小人脸尺寸，默认30

#### 方法
```csharp
public ValidationResult Validate()
```

### WeatherServiceConfiguration

天气服务配置类。

#### 属性
- `Provider` (string): 服务提供商，默认"OpenWeatherMap"
- `ApiEndpoint` (string): API端点，默认OpenWeatherMap API
- `ApiKey` (string): API密钥
- `DefaultCity` (string): 默认城市，默认"Beijing"
- `TemperatureUnit` (string): 温度单位，默认"metric"
- `IsEnabled` (bool): 是否启用，默认false
- `UpdateIntervalMinutes` (int): 更新间隔（分钟），默认30

#### 方法
```csharp
public ValidationResult Validate()
```

### PrivateAuthConfiguration

私有化认证配置类。

#### 属性
- `AuthType` (string): 认证类型，默认"ApiKey"
- `Username` (string): 用户名
- `Password` (string): 密码
- `Token` (string): 令牌
- `CertificatePath` (string): 证书路径
- `CertificatePassword` (string): 证书密码

## 使用示例

### 基本配置设置

```csharp
// 创建服务配置实例
var serviceConfig = new ServiceConfiguration();

// 配置大模型API
var openaiConfig = new LLMApiConfiguration
{
    Name = "OpenAI GPT-4",
    Provider = "OpenAI",
    ApiEndpoint = "https://api.openai.com/v1/chat/completions",
    ApiKey = "sk-your-openai-api-key",
    ModelName = "gpt-4",
    MaxTokens = 4096,
    Temperature = 0.7f,
    IsEnabled = true
};

serviceConfig.AddLLMConfiguration(openaiConfig);

// 配置TTS服务
serviceConfig.TTSConfig.Provider = "Azure";
serviceConfig.TTSConfig.ApiEndpoint = "https://your-region.tts.speech.microsoft.com/";
serviceConfig.TTSConfig.ApiKey = "your-azure-speech-key";
serviceConfig.TTSConfig.DefaultVoice = "zh-CN-XiaoxiaoNeural";
serviceConfig.TTSConfig.IsEnabled = true;

// 配置ASR服务
serviceConfig.ASRConfig.Provider = "Azure";
serviceConfig.ASRConfig.ApiEndpoint = "https://your-region.stt.speech.microsoft.com/";
serviceConfig.ASRConfig.ApiKey = "your-azure-speech-key";
serviceConfig.ASRConfig.Language = "zh-CN";
serviceConfig.ASRConfig.IsEnabled = true;

// 配置网络参数
serviceConfig.RequestTimeoutSeconds = 30;
serviceConfig.RetryCount = 3;
serviceConfig.RetryIntervalMs = 1000;
serviceConfig.EnableRequestCache = true;
serviceConfig.CacheExpirationMinutes = 60;

// 验证配置
var validationResult = serviceConfig.Validate();
if (validationResult.IsValid)
{
    Console.WriteLine("服务配置验证通过");
}
else
{
    Console.WriteLine($"配置验证失败：{validationResult.GetErrorsAsString()}");
}
```

### 私有化部署配置

```csharp
// 启用私有化部署
serviceConfig.EnablePrivateDeployment = true;
serviceConfig.PrivateServiceBaseUrl = "https://your-private-server.com/api";

// 配置私有化认证
serviceConfig.PrivateAuthConfig.AuthType = "Bearer";
serviceConfig.PrivateAuthConfig.Token = "your-private-token";

// 配置私有化大模型
var privateConfig = new LLMApiConfiguration
{
    Name = "私有化大模型",
    Provider = "Private",
    ApiEndpoint = "https://your-private-server.com/api/v1/chat",
    ApiKey = "private-api-key",
    ModelName = "private-model-v1",
    MaxTokens = 2048,
    Temperature = 0.8f,
    IsEnabled = true
};

serviceConfig.AddLLMConfiguration(privateConfig);
serviceConfig.ActiveLLMConfigId = privateConfig.ConfigId;
```

### 多模型配置管理

```csharp
// 添加多个大模型配置
var configs = new[]
{
    new LLMApiConfiguration
    {
        Name = "OpenAI GPT-4",
        Provider = "OpenAI",
        ApiEndpoint = "https://api.openai.com/v1/chat/completions",
        ApiKey = "openai-key",
        ModelName = "gpt-4",
        MaxTokens = 4096,
        Temperature = 0.7f
    },
    new LLMApiConfiguration
    {
        Name = "Claude-3",
        Provider = "Anthropic",
        ApiEndpoint = "https://api.anthropic.com/v1/messages",
        ApiKey = "anthropic-key",
        ModelName = "claude-3-opus-20240229",
        MaxTokens = 4096,
        Temperature = 0.7f
    },
    new LLMApiConfiguration
    {
        Name = "通义千问",
        Provider = "Alibaba",
        ApiEndpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
        ApiKey = "alibaba-key",
        ModelName = "qwen-turbo",
        MaxTokens = 2048,
        Temperature = 0.8f
    }
};

foreach (var config in configs)
{
    serviceConfig.AddLLMConfiguration(config);
}

// 设置默认激活的配置
serviceConfig.ActiveLLMConfigId = configs[0].ConfigId;

// 获取当前激活的配置
var activeConfig = serviceConfig.GetActiveLLMConfiguration();
Console.WriteLine($"当前激活的模型：{activeConfig.Name}");
```

### 配置验证和错误处理

```csharp
// 创建配置并验证
var serviceConfig = new ServiceConfiguration();

// 添加一个不完整的配置（用于演示验证）
var incompleteConfig = new LLMApiConfiguration
{
    Name = "测试配置",
    // 缺少必要的ApiEndpoint和ApiKey
    ModelName = "test-model"
};

serviceConfig.AddLLMConfiguration(incompleteConfig);

// 验证配置
var validationResult = serviceConfig.Validate();

if (!validationResult.IsValid)
{
    Console.WriteLine("配置验证失败：");
    foreach (var error in validationResult.Errors)
    {
        Console.WriteLine($"- {error}");
    }
}

if (validationResult.HasWarnings)
{
    Console.WriteLine("配置警告：");
    foreach (var warning in validationResult.Warnings)
    {
        Console.WriteLine($"- {warning}");
    }
}

// 获取完整的验证摘要
Console.WriteLine(validationResult.GetSummary());
```

### 与ConfigurationManager集成

```csharp
// 通过ConfigurationManager保存和加载配置
var configManager = ConfigurationManager.Instance;

// 保存配置
configManager.SaveConfiguration(serviceConfig, ConfigurationType.Service);

// 加载配置
var loadedConfig = configManager.LoadConfiguration<ServiceConfiguration>(ConfigurationType.Service);

// 验证加载的配置
var validationResult = loadedConfig.Validate();
if (validationResult.IsValid)
{
    Console.WriteLine("配置加载并验证成功");
}
```

## 最佳实践

### 1. 配置验证
```csharp
// 在使用配置前始终进行验证
var validationResult = serviceConfig.Validate();
if (!validationResult.IsValid)
{
    throw new InvalidOperationException($"服务配置无效：{validationResult.GetErrorsAsString()}");
}
```

### 2. 敏感信息处理
```csharp
// API密钥等敏感信息应该通过安全的方式获取
serviceConfig.TTSConfig.ApiKey = Environment.GetEnvironmentVariable("AZURE_SPEECH_KEY");
serviceConfig.ASRConfig.ApiKey = Environment.GetEnvironmentVariable("AZURE_SPEECH_KEY");
```

### 3. 配置更新
```csharp
// 更新配置时记录修改时间
serviceConfig.LastModifiedAt = DateTime.Now;

// 重新验证更新后的配置
var validationResult = serviceConfig.Validate();
if (validationResult.IsValid)
{
    configManager.SaveConfiguration(serviceConfig, ConfigurationType.Service);
}
```

### 4. 错误处理
```csharp
try
{
    var activeConfig = serviceConfig.GetActiveLLMConfiguration();
    if (activeConfig == null)
    {
        throw new InvalidOperationException("未找到激活的大模型配置");
    }
    
    // 使用配置进行API调用
}
catch (Exception ex)
{
    Console.WriteLine($"配置使用失败：{ex.Message}");
    // 记录错误日志或执行恢复操作
}
```

## 注意事项

1. **安全性**: API密钥等敏感信息会通过ConfigurationManager自动加密存储
2. **验证**: 在使用配置前应始终调用Validate()方法进行验证
3. **线程安全**: 配置对象本身不是线程安全的，在多线程环境中需要适当的同步机制
4. **版本兼容**: 配置结构变更时需要考虑向后兼容性
5. **默认值**: 所有配置项都有合理的默认值，确保系统的基本可用性

## 依赖项

- `System`: 基础系统类型
- `System.Collections.Generic`: 集合类型
- `UnityEngine`: Unity序列化支持
- `DigitalHuman.Core.Configuration.Models.ValidationResult`: 验证结果类

## 相关文档

- [ConfigurationManager 配置管理器文档](ConfigurationManager.md)
- [ValidationResult 验证结果文档](ValidationResult.md)
- [UserConfiguration 用户配置文档](UserConfiguration.md)
- [SecurityConfiguration 安全配置文档](SecurityConfiguration.md)