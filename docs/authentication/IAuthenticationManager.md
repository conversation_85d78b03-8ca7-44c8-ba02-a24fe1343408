# IAuthenticationManager 接口文档

## 概述

`IAuthenticationManager` 是认证管理系统的核心接口，定义了用户身份验证和会话管理的标准操作。

## 接口定义

```csharp
namespace DigitalHuman.Core.Authentication
{
    public interface IAuthenticationManager
    {
        // 属性
        bool IsLoggedIn { get; }
        UserInfo CurrentUser { get; }
        
        // 事件
        event Action<UserInfo> OnUserLoggedIn;
        event Action OnUserLoggedOut;
        event Action<AuthenticationStatus> OnAuthenticationStatusChanged;
        
        // 方法
        Task<AuthenticationResult> LoginAsync(string username, string password);
        Task<bool> LogoutAsync();
        Task<bool> RefreshTokenAsync();
        Task<bool> ValidateSessionAsync();
        string GetAuthToken();
        void ClearAuthenticationData();
    }
}
```

## 核心功能

### 用户认证
- 支持用户名密码登录
- 异步认证处理
- 认证结果反馈

### 会话管理
- 会话状态跟踪
- 自动令牌刷新
- 会话验证

### 事件通知
- 登录状态变化通知
- 认证状态变化通知
- 实时状态更新

## 使用示例

### 基本登录流程

```csharp
var authManager = AuthenticationManager.Instance;

// 订阅事件
authManager.OnUserLoggedIn += (userInfo) => {
    Debug.Log($"用户登录成功: {userInfo.Username}");
};

// 执行登录
var result = await authManager.LoginAsync("admin", "admin123");
if (result.IsSuccess)
{
    Debug.Log("登录成功");
}
```

### 会话验证

```csharp
// 检查会话是否有效
var isValid = await authManager.ValidateSessionAsync();
if (!isValid)
{
    // 会话无效，需要重新登录
    Debug.Log("会话已过期");
}
```

## 实现要求

1. **线程安全**: 所有方法必须是线程安全的
2. **异步支持**: 网络操作必须异步执行
3. **事件管理**: 正确管理事件订阅和取消订阅
4. **错误处理**: 提供详细的错误信息和恢复机制

## 相关文档

- [AuthenticationManager 实现](../core/authentication/AuthenticationManager.md)
- [认证系统使用指南](AuthenticationGuide.md)