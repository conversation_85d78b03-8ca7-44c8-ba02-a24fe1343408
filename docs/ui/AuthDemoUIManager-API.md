# AuthDemoUIManager API 参考

## 类定义

```csharp
namespace DigitalHuman.UI
{
    public class AuthDemoUIManager : MonoBehaviour
}
```

## 公共属性

```csharp
// 状态属性
public bool IsUserLoggedIn { get; private set; }
public UserInfo CurrentUser { get; private set; }
```

## 公共方法

### 对话框管理
```csharp
// 显示登录对话框
public void ShowLoginDialog()

// 隐藏登录对话框
public void HideLoginDialog()
```

## 私有方法（内部实现）

### 初始化方法
```csharp
private void InitializeUI()
private void InitializeAuthentication()
private void SetupEventHandlers()
```

### 认证处理方法
```csharp
private async void HandleLogin()
private async void HandleLogout()
private void OnUserLoggedIn(UserInfo userInfo)
private void OnUserLoggedOut()
private void OnAuthenticationStatusChanged(AuthenticationStatus status)
```

### UI更新方法
```csharp
private void UpdateAuthenticationUI()
private void ShowLoginStatus(string message, bool isProcessing)
private void HideLoginStatus()
private void ClearLoginForm()
private void SetLoginFormEnabled(bool enabled)
```

## 使用示例

### 基本使用
```csharp
var authDemoUI = FindObjectOfType<AuthDemoUIManager>();

// 显示登录对话框
authDemoUI.ShowLoginDialog();

// 检查登录状态
if (authDemoUI.IsUserLoggedIn)
{
    Debug.Log($"当前用户: {authDemoUI.CurrentUser.Username}");
}
```

### 程序化控制
```csharp
public class AuthDemoController : MonoBehaviour
{
    private AuthDemoUIManager authDemoUI;
    
    void Start()
    {
        authDemoUI = FindObjectOfType<AuthDemoUIManager>();
    }
    
    [ContextMenu("测试登录流程")]
    public void TestLoginFlow()
    {
        authDemoUI.ShowLoginDialog();
    }
}
```

## 依赖项

### 必需组件
- `UIDocument`: Unity UI Toolkit文档组件
- `AuthenticationManager`: 认证管理器实例

### 必需资源
- 包含认证UI元素的UXML文档
- 认证相关的USS样式表

### Unity包依赖
- Unity UI Toolkit
- DigitalHuman.Core.Authentication

## 配置要求

### Inspector设置
```csharp
[Header("UI文档")]
[SerializeField] private UIDocument uiDocument;
```

### UXML元素要求
必须包含以下命名元素：
- `auth-icon`: 认证状态图标
- `auth-text`: 认证状态文本
- `login-button`: 登录按钮
- `user-info`: 用户信息容器
- `login-dialog`: 登录对话框
- `username-input`: 用户名输入框
- `password-input`: 密码输入框
- `login-submit`: 登录提交按钮
- `login-status`: 登录状态显示

## 状态管理

### 认证状态
- `IsUserLoggedIn`: 用户登录状态
- `CurrentUser`: 当前登录用户信息

### UI状态
- **未登录**: 显示登录按钮，隐藏用户信息
- **已登录**: 显示用户信息，隐藏登录按钮

## 事件处理

### 内部事件订阅
```csharp
// 认证管理器事件
authManager.OnUserLoggedIn += OnUserLoggedIn;
authManager.OnUserLoggedOut += OnUserLoggedOut;
authManager.OnAuthenticationStatusChanged += OnAuthenticationStatusChanged;
```

### UI交互事件
```csharp
// 按钮点击事件
loginButton.RegisterCallback<ClickEvent>(evt => ShowLoginDialog());
logoutButton.RegisterCallback<ClickEvent>(evt => HandleLogout());

// 键盘事件
passwordInput.RegisterCallback<KeyDownEvent>(evt => {
    if (evt.keyCode == KeyCode.Return) HandleLogin();
});
```

## 错误处理

### 常见异常
- `NullReferenceException`: UI元素未正确初始化
- `InvalidOperationException`: 认证管理器未找到
- `ArgumentException`: 无效的登录凭据

### 调试建议
1. 检查UIDocument组件配置
2. 验证UXML文件中的元素名称
3. 确保AuthenticationManager已初始化
4. 查看Unity Console中的错误日志

## 性能参数

### 内存使用
- UI组件: ~1-2MB
- 事件处理器: ~100KB
- 总增量: ~2MB

### 响应时间
- 登录对话框显示: <100ms
- 认证处理: ~1s (取决于网络)
- UI状态更新: <50ms

## 测试账户

### 默认测试凭据
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: 管理员

## 最佳实践

### 1. 初始化顺序
```csharp
void Start()
{
    InitializeUI();           // 1. 初始化UI元素
    InitializeAuthentication(); // 2. 初始化认证管理器
    SetupEventHandlers();     // 3. 设置事件处理器
}
```

### 2. 错误处理
```csharp
private async void HandleLogin()
{
    try
    {
        // 登录逻辑
    }
    catch (Exception ex)
    {
        ShowLoginStatus($"登录异常: {ex.Message}", false);
    }
    finally
    {
        SetLoginFormEnabled(true);
    }
}
```

### 3. 资源清理
```csharp
private void OnDestroy()
{
    if (authManager != null)
    {
        authManager.OnUserLoggedIn -= OnUserLoggedIn;
        authManager.OnUserLoggedOut -= OnUserLoggedOut;
        authManager.OnAuthenticationStatusChanged -= OnAuthenticationStatusChanged;
    }
}
```