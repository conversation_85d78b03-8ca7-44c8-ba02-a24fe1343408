# MainUIManager 主界面管理器文档

## 概述

`MainUIManager` 是数字人对话系统的核心UI管理组件，负责主界面的导航、状态管理、用户认证集成和响应式布局。该组件基于Unity UI Toolkit构建，提供了完整的用户界面管理功能。

## 类签名

```csharp
namespace DigitalHuman.UI
{
    public class MainUIManager : MonoBehaviour
}
```

## 核心功能

### 1. 页面管理
- **多页面导航**: 支持欢迎页、对话页、设置页之间的切换
- **页面状态管理**: 自动管理页面的显示/隐藏状态
- **导航按钮同步**: 导航按钮状态与当前页面自动同步

### 2. 用户认证集成
- **认证管理器集成**: 与 `AuthenticationManager` 深度集成
- **登录/登出界面**: 完整的用户登录和登出流程
- **用户状态显示**: 实时显示用户登录状态和信息
- **权限管理**: 基于用户权限控制界面元素

### 3. 系统状态监控
- **网络状态**: 实时显示网络连接状态
- **激活状态**: 显示设备激活状态
- **服务状态**: 监控语音引擎、AI服务等外部服务状态
- **系统信息**: 显示内存使用、FPS等系统信息

### 4. 对话框管理
- **激活对话框**: 设备激活码输入和验证
- **登录对话框**: 用户登录凭据输入和验证
- **模态窗口**: 统一的模态对话框管理

## 主要属性

### UI元素引用
```csharp
// 页面容器
private VisualElement welcomePage;
private VisualElement chatPage;
private VisualElement settingsPage;

// 导航按钮
private Button navHome;
private Button navChat;
private Button navSettings;

// 状态指示器
private VisualElement networkIcon;
private Label networkText;
private VisualElement activationIcon;
private Label activationText;
private VisualElement authIcon;
private Label authText;

// 认证相关UI元素
private Button loginButton;
private Button logoutButton;
private VisualElement userInfo;
private Label userDisplayName;
private VisualElement loginDialog;
private TextField usernameInput;
private TextField passwordInput;
```

### 状态属性
```csharp
public bool IsOnline { get; private set; } = true;
public bool IsActivated { get; private set; } = true;
public string CurrentRenderMode { get; private set; } = "3D模型";
public bool IsVoiceEngineConnected { get; private set; } = true;
public bool IsAIServiceConnected { get; private set; } = true;
public bool IsUserLoggedIn { get; private set; } = false;
public UserInfo CurrentUser { get; private set; } = null;
```

### 认证管理器
```csharp
private AuthenticationManager authManager;
```

## 主要方法

### 初始化方法

#### `InitializeUI()`
```csharp
private void InitializeUI()
```
**功能**: 初始化所有UI元素引用，设置初始状态
- 获取UXML中定义的所有UI元素
- 设置初始页面状态
- 更新状态指示器
- 生成设备ID

#### `InitializeAuthentication()`
```csharp
private void InitializeAuthentication()
```
**功能**: 初始化认证管理器集成
- 获取认证管理器实例
- 订阅认证相关事件
- 检查当前登录状态
- 更新认证UI状态

#### `SetupEventHandlers()`
```csharp
private void SetupEventHandlers()
```
**功能**: 设置所有UI事件处理器
- 导航按钮点击事件
- 快速操作按钮事件
- 对话框交互事件
- 键盘输入事件

### 页面管理方法

#### `ShowPage(string pageName)`
```csharp
public void ShowPage(string pageName)
```
**参数**:
- `pageName`: 页面名称 ("welcome", "chat", "settings")

**功能**: 显示指定页面并更新导航状态
- 隐藏所有其他页面
- 显示目标页面
- 更新导航按钮激活状态
- 触发页面变化事件

**使用示例**:
```csharp
// 切换到对话页面
mainUIManager.ShowPage("chat");

// 切换到设置页面
mainUIManager.ShowPage("settings");
```

### 认证相关方法

#### `ShowLoginDialog()`
```csharp
public void ShowLoginDialog()
```
**功能**: 显示登录对话框
- 显示登录模态窗口
- 聚焦用户名输入框
- 清空登录表单
- 隐藏状态消息

#### `HideLoginDialog()`
```csharp
public void HideLoginDialog()
```
**功能**: 隐藏登录对话框
- 隐藏登录模态窗口
- 清空表单数据
- 重置状态显示

#### `HandleLogin()`
```csharp
private async void HandleLogin()
```
**功能**: 处理用户登录操作
- 验证输入数据
- 调用认证管理器登录
- 显示登录状态
- 处理登录结果

**登录流程**:
1. 验证用户名和密码不为空
2. 显示"正在登录..."状态
3. 禁用登录表单
4. 调用 `authManager.LoginAsync()`
5. 根据结果显示成功或错误消息
6. 成功时自动关闭对话框

#### `HandleLogout()`
```csharp
private async void HandleLogout()
```
**功能**: 处理用户登出操作
- 调用认证管理器登出
- 更新UI状态
- 处理异常情况

### 认证事件处理方法

#### `OnUserLoggedIn(UserInfo userInfo)`
```csharp
private void OnUserLoggedIn(UserInfo userInfo)
```
**参数**:
- `userInfo`: 登录用户的信息

**功能**: 处理用户登录成功事件
- 更新登录状态
- 保存当前用户信息
- 更新认证UI显示
- 记录登录日志

#### `OnUserLoggedOut()`
```csharp
private void OnUserLoggedOut()
```
**功能**: 处理用户登出事件
- 清除登录状态
- 清空用户信息
- 更新认证UI显示
- 记录登出日志

#### `OnAuthenticationStatusChanged(AuthenticationStatus status)`
```csharp
private void OnAuthenticationStatusChanged(AuthenticationStatus status)
```
**参数**:
- `status`: 新的认证状态

**功能**: 处理认证状态变化事件
- 记录状态变化日志
- 更新UI显示
- 处理状态相关逻辑

### 状态更新方法

#### `UpdateNetworkStatus(bool isOnline)`
```csharp
public void UpdateNetworkStatus(bool isOnline)
```
**参数**:
- `isOnline`: 网络连接状态

**功能**: 更新网络状态显示

#### `UpdateActivationStatus(bool isActivated)`
```csharp
public void UpdateActivationStatus(bool isActivated)
```
**参数**:
- `isActivated`: 设备激活状态

**功能**: 更新设备激活状态显示

#### `UpdateAuthenticationUI()`
```csharp
private void UpdateAuthenticationUI()
```
**功能**: 更新认证相关UI元素
- 根据登录状态显示/隐藏相应元素
- 更新用户信息显示
- 更新认证状态指示器

**UI更新逻辑**:
- **已登录**: 显示用户信息，隐藏登录按钮，更新状态为"已登录"
- **未登录**: 显示登录按钮，隐藏用户信息，更新状态为"未登录"

### 对话框管理方法

#### `ShowActivationDialog()`
```csharp
public void ShowActivationDialog()
```
**功能**: 显示设备激活对话框

#### `HideActivationDialog()`
```csharp
public void HideActivationDialog()
```
**功能**: 隐藏设备激活对话框

#### `HandleActivation()`
```csharp
private void HandleActivation()
```
**功能**: 处理设备激活操作

### 辅助方法

#### `ShowLoginStatus(string message, bool isProcessing)`
```csharp
private void ShowLoginStatus(string message, bool isProcessing)
```
**参数**:
- `message`: 状态消息文本
- `isProcessing`: 是否为处理中状态

**功能**: 显示登录状态消息
- 显示状态消息
- 根据消息类型设置样式
- 支持处理中、成功、错误三种状态

#### `SetLoginFormEnabled(bool enabled)`
```csharp
private void SetLoginFormEnabled(bool enabled)
```
**参数**:
- `enabled`: 是否启用表单

**功能**: 设置登录表单的启用状态
- 控制输入框和按钮的可用性
- 防止重复提交

#### `AdaptToDisplay(int screenWidth, int screenHeight)`
```csharp
public void AdaptToDisplay(int screenWidth, int screenHeight)
```
**参数**:
- `screenWidth`: 屏幕宽度
- `screenHeight`: 屏幕高度

**功能**: 适配不同显示设备
- 检测大屏设备
- 检测竖屏模式
- 添加相应CSS类

## 事件系统

### 公共事件
```csharp
public event Action<string> OnPageChanged;
public event Action OnStartChat;
public event Action OnOpenVoiceSettings;
public event Action OnOpenAvatarSettings;
public event Action OnOpenSettings;
public event Action<string> OnActivationCodeSubmitted;
public event Action<bool> OnMVPModeToggled;
public event Action<string, string> OnLoginRequested;
public event Action OnLogoutRequested;
```

### 事件使用示例
```csharp
// 订阅页面变化事件
mainUIManager.OnPageChanged += (pageName) =>
{
    Debug.Log($"页面切换到: {pageName}");
};

// 订阅开始对话事件
mainUIManager.OnStartChat += () =>
{
    // 初始化对话系统
    chatManager.StartNewConversation();
};
```

## 认证集成详解

### 认证管理器集成
`MainUIManager` 与 `AuthenticationManager` 深度集成，提供完整的用户认证UI支持：

```csharp
// 初始化认证集成
private void InitializeAuthentication()
{
    authManager = AuthenticationManager.Instance;
    
    if (authManager != null)
    {
        // 订阅认证事件
        authManager.OnUserLoggedIn += OnUserLoggedIn;
        authManager.OnUserLoggedOut += OnUserLoggedOut;
        authManager.OnAuthenticationStatusChanged += OnAuthenticationStatusChanged;
        
        // 检查当前状态
        IsUserLoggedIn = authManager.IsLoggedIn;
        CurrentUser = authManager.CurrentUser;
    }
}
```

### 登录流程
1. 用户点击登录按钮 → `ShowLoginDialog()`
2. 用户输入凭据并提交 → `HandleLogin()`
3. 调用认证管理器 → `authManager.LoginAsync()`
4. 处理登录结果 → `OnUserLoggedIn()` 或错误处理
5. 更新UI状态 → `UpdateAuthenticationUI()`

### 权限控制
```csharp
// 基于用户权限控制UI元素
private void UpdateUIBasedOnPermissions()
{
    if (CurrentUser != null)
    {
        // 检查管理员权限
        if (CurrentUser.HasPermission("admin_access"))
        {
            adminPanel.style.display = DisplayStyle.Flex;
        }
        
        // 检查编辑权限
        if (CurrentUser.HasRole("editor"))
        {
            editButton.SetEnabled(true);
        }
    }
}
```

## 使用示例

### 基本使用
```csharp
public class GameManager : MonoBehaviour
{
    private MainUIManager mainUI;
    
    void Start()
    {
        // 获取主界面管理器
        mainUI = FindObjectOfType<MainUIManager>();
        
        // 订阅事件
        mainUI.OnPageChanged += OnPageChanged;
        mainUI.OnStartChat += OnStartChat;
        
        // 设置初始状态
        mainUI.UpdateNetworkStatus(true);
        mainUI.UpdateActivationStatus(true);
    }
    
    private void OnPageChanged(string pageName)
    {
        Debug.Log($"当前页面: {pageName}");
        
        // 根据页面执行相应逻辑
        switch (pageName)
        {
            case "chat":
                InitializeChatSystem();
                break;
            case "settings":
                LoadUserSettings();
                break;
        }
    }
    
    private void OnStartChat()
    {
        // 启动对话系统
        chatManager.StartNewConversation();
    }
}
```

### 认证集成使用
```csharp
public class AuthenticationController : MonoBehaviour
{
    private MainUIManager mainUI;
    private AuthenticationManager authManager;
    
    void Start()
    {
        mainUI = FindObjectOfType<MainUIManager>();
        authManager = AuthenticationManager.Instance;
        
        // 监听认证状态变化
        if (authManager != null)
        {
            authManager.OnUserLoggedIn += OnUserLoggedIn;
            authManager.OnSessionExpired += OnSessionExpired;
        }
    }
    
    private void OnUserLoggedIn(UserInfo userInfo)
    {
        Debug.Log($"用户 {userInfo.DisplayName} 登录成功");
        
        // 根据用户角色显示不同界面
        if (userInfo.HasRole("admin"))
        {
            mainUI.ShowPage("admin");
        }
        else
        {
            mainUI.ShowPage("welcome");
        }
    }
    
    private void OnSessionExpired()
    {
        // 会话过期，显示登录对话框
        mainUI.ShowLoginDialog();
    }
}
```

### 状态监控使用
```csharp
public class SystemMonitor : MonoBehaviour
{
    private MainUIManager mainUI;
    
    void Start()
    {
        mainUI = FindObjectOfType<MainUIManager>();
        StartCoroutine(MonitorSystemStatus());
    }
    
    private IEnumerator MonitorSystemStatus()
    {
        while (true)
        {
            // 检查网络状态
            bool isOnline = CheckNetworkConnection();
            mainUI.UpdateNetworkStatus(isOnline);
            
            // 检查服务状态
            bool voiceConnected = CheckVoiceEngineStatus();
            mainUI.UpdateVoiceEngineStatus(voiceConnected);
            
            bool aiConnected = CheckAIServiceStatus();
            mainUI.UpdateAIServiceStatus(aiConnected);
            
            yield return new WaitForSeconds(5f);
        }
    }
}
```

## 配置和自定义

### UXML结构要求
`MainUIManager` 依赖特定的UXML结构，主要元素包括：

```xml
<!-- 页面容器 -->
<ui:VisualElement name="welcome-page" class="page-container active-page" />
<ui:VisualElement name="chat-page" class="page-container" />
<ui:VisualElement name="settings-page" class="page-container" />

<!-- 导航按钮 -->
<ui:Button name="nav-home" class="nav-button active" />
<ui:Button name="nav-chat" class="nav-button" />
<ui:Button name="nav-settings" class="nav-button" />

<!-- 认证相关元素 -->
<ui:Button name="login-button" class="login-button" />
<ui:VisualElement name="user-info" class="user-info" />
<ui:VisualElement name="login-dialog" class="modal-overlay" />
```

### CSS类定义
```css
.page-container {
    display: none;
}

.active-page {
    display: flex;
}

.nav-button.active {
    background-color: #007acc;
}

.modal-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}
```

## 性能优化

### 内存管理
- 使用对象池管理UI元素
- 及时清理事件订阅
- 避免频繁的UI更新

### 渲染优化
- 使用CSS类切换而非直接样式修改
- 批量更新UI元素
- 避免不必要的重绘

### 异步操作
```csharp
// 异步登录避免阻塞UI
private async void HandleLogin()
{
    try
    {
        ShowLoginStatus("正在登录...", true);
        SetLoginFormEnabled(false);
        
        var result = await authManager.LoginAsync(username, password);
        
        if (result.IsSuccess)
        {
            ShowLoginStatus("登录成功", true);
            await Task.Delay(1000); // 显示成功消息
            HideLoginDialog();
        }
        else
        {
            ShowLoginStatus(result.ErrorMessage, false);
        }
    }
    finally
    {
        SetLoginFormEnabled(true);
    }
}
```

## 错误处理

### 异常处理
```csharp
private async void HandleLogin()
{
    try
    {
        // 登录逻辑
    }
    catch (Exception ex)
    {
        ShowLoginStatus($"登录异常: {ex.Message}", false);
        Debug.LogError($"登录异常: {ex}");
    }
}
```

### 空引用检查
```csharp
private void UpdateAuthenticationUI()
{
    if (loginButton != null)
    {
        loginButton.style.display = IsUserLoggedIn ? DisplayStyle.None : DisplayStyle.Flex;
    }
    
    if (userInfo != null)
    {
        userInfo.style.display = IsUserLoggedIn ? DisplayStyle.Flex : DisplayStyle.None;
    }
}
```

## 调试和测试

### 调试信息
```csharp
// 启用详细日志
[SerializeField] private bool enableDebugLogs = true;

private void DebugLog(string message)
{
    if (enableDebugLogs)
    {
        Debug.Log($"[MainUIManager] {message}");
    }
}
```

### 测试方法
```csharp
// 测试页面切换
[ContextMenu("Test Page Navigation")]
private void TestPageNavigation()
{
    ShowPage("chat");
    StartCoroutine(TestPageSequence());
}

private IEnumerator TestPageSequence()
{
    yield return new WaitForSeconds(2f);
    ShowPage("settings");
    yield return new WaitForSeconds(2f);
    ShowPage("welcome");
}
```

## 最佳实践

### 1. 事件管理
- 及时取消事件订阅避免内存泄漏
- 使用弱引用处理长生命周期对象
- 在 `OnDestroy()` 中清理所有订阅

### 2. UI更新
- 批量更新UI状态
- 避免频繁的DOM操作
- 使用CSS类而非直接样式修改

### 3. 异步操作
- 所有网络操作使用异步方法
- 提供用户反馈和取消机制
- 正确处理异常和超时

### 4. 可访问性
- 提供键盘导航支持
- 添加适当的ARIA标签
- 支持屏幕阅读器

## 相关文档

- [认证系统文档](../authentication/README.md)
- [UI Toolkit 使用指南](../ui/UIToolkit.md)
- [事件系统文档](../core/EventSystem.md)
- [配置管理文档](../configuration/README.md)

## 更新日志

### v1.0.0
- 初始版本发布
- 基础页面管理功能
- 认证系统集成
- 状态监控功能

### v1.1.0 (计划中)
- 增强的权限控制
- 更多自定义选项
- 性能优化改进