# MainUIManager API 参考

## 类定义

```csharp
namespace DigitalHuman.UI
{
    public class MainUIManager : MonoBehaviour
}
```

## 公共属性

```csharp
// 状态属性
public bool IsOnline { get; private set; }
public bool IsActivated { get; private set; }
public string CurrentRenderMode { get; private set; }
public bool IsVoiceEngineConnected { get; private set; }
public bool IsAIServiceConnected { get; private set; }
public bool IsUserLoggedIn { get; private set; }
public UserInfo CurrentUser { get; private set; }
```

## 公共方法

### 页面管理
```csharp
// 显示指定页面
public void ShowPage(string pageName)

// 显示激活对话框
public void ShowActivationDialog()

// 隐藏激活对话框
public void HideActivationDialog()

// 显示登录对话框
public void ShowLoginDialog()

// 隐藏登录对话框
public void HideLoginDialog()
```

### 状态更新
```csharp
// 更新网络状态
public void UpdateNetworkStatus(bool isOnline)

// 更新激活状态
public void UpdateActivationStatus(bool isActivated)

// 更新渲染模式
public void UpdateRenderMode(string mode)

// 更新语音引擎状态
public void UpdateVoiceEngineStatus(bool isConnected)

// 更新AI服务状态
public void UpdateAIServiceStatus(bool isConnected)

// 适配显示设备
public void AdaptToDisplay(int screenWidth, int screenHeight)
```

## 事件

```csharp
// 页面变化事件
public event Action<string> OnPageChanged;

// 快速操作事件
public event Action OnStartChat;
public event Action OnOpenVoiceSettings;
public event Action OnOpenAvatarSettings;
public event Action OnOpenSettings;

// 激活相关事件
public event Action<string> OnActivationCodeSubmitted;
public event Action<bool> OnMVPModeToggled;

// 认证相关事件
public event Action<string, string> OnLoginRequested;
public event Action OnLogoutRequested;
```

## 私有方法（内部实现）

### 初始化方法
```csharp
private void InitializeUI()
private void InitializeAuthentication()
private void SetupEventHandlers()
```

### 认证处理方法
```csharp
private async void HandleLogin()
private async void HandleLogout()
private void OnUserLoggedIn(UserInfo userInfo)
private void OnUserLoggedOut()
private void OnAuthenticationStatusChanged(AuthenticationStatus status)
```

### UI更新方法
```csharp
private void UpdateAuthenticationUI()
private void UpdateStatusIndicators()
private void UpdateSystemInfo()
private void ShowLoginStatus(string message, bool isProcessing)
private void HideLoginStatus()
private void ClearLoginForm()
private void SetLoginFormEnabled(bool enabled)
```

### 辅助方法
```csharp
private void HandleActivation()
private void ShowHelpDialog()
private void GenerateDeviceId()
private IEnumerator UpdateTimeDisplay()
private IEnumerator UpdateSystemStats()
```

## 使用示例

### 基本页面导航
```csharp
var mainUI = FindObjectOfType<MainUIManager>();

// 切换到对话页面
mainUI.ShowPage("chat");

// 订阅页面变化事件
mainUI.OnPageChanged += (pageName) =>
{
    Debug.Log($"当前页面: {pageName}");
};
```

### 状态管理
```csharp
// 更新系统状态
mainUI.UpdateNetworkStatus(true);
mainUI.UpdateActivationStatus(true);
mainUI.UpdateVoiceEngineStatus(true);
mainUI.UpdateAIServiceStatus(true);

// 检查当前状态
if (mainUI.IsUserLoggedIn)
{
    Debug.Log($"当前用户: {mainUI.CurrentUser.DisplayName}");
}
```

### 事件订阅
```csharp
// 订阅用户操作事件
mainUI.OnStartChat += () =>
{
    // 启动对话系统
    chatManager.StartNewConversation();
};

mainUI.OnOpenSettings += () =>
{
    // 打开设置界面
    settingsManager.ShowSettings();
};
```

## 依赖项

### 必需组件
- `UIDocument`: Unity UI Toolkit文档组件
- `AuthenticationManager`: 用户认证管理器

### 必需资源
- `MainUI.uxml`: 主界面UXML文档
- `MainUI.uss`: 主界面样式表

### Unity包依赖
- Unity UI Toolkit
- Unity Addressables (可选)

## 配置要求

### Inspector设置
```csharp
[Header("UI文档")]
[SerializeField] private UIDocument uiDocument;

[Header("页面管理")]
[SerializeField] private string currentPage = "welcome";
```

### UXML结构要求
主界面UXML必须包含以下关键元素：
- `welcome-page`, `chat-page`, `settings-page`: 页面容器
- `nav-home`, `nav-chat`, `nav-settings`: 导航按钮
- `login-button`, `user-info`: 认证相关元素
- `activation-dialog`, `login-dialog`: 模态对话框

## 错误处理

### 常见异常
- `NullReferenceException`: UI元素未正确初始化
- `InvalidOperationException`: 认证管理器未找到
- `ArgumentException`: 无效的页面名称

### 调试建议
1. 检查UIDocument组件是否正确配置
2. 验证UXML文件中的元素名称
3. 确保认证管理器已正确初始化
4. 查看Unity Console中的错误日志