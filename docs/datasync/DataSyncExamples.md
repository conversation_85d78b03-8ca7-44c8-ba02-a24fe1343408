# DataSync 使用示例文档

## 概述

本文档提供了数据同步系统的详细使用示例，包括基本操作、高级功能、集成方案和最佳实践。通过这些示例，开发者可以快速了解如何在项目中使用数据同步功能。

## 基本使用示例

### 1. 初始化和基本配置

```csharp
public class DataSyncInitializer : MonoBehaviour
{
    private DataSyncManager _syncManager;
    private ILogger _logger;
    
    void Start()
    {
        // 获取管理器实例
        _syncManager = DataSyncManager.Instance;
        _logger = LogManager.Instance.GetLogger("DataSync");
        
        // 配置同步参数
        _syncManager.AutoSyncIntervalMinutes = 15;  // 15分钟自动同步
        _syncManager.MaxRetryCount = 3;             // 最大重试3次
        _syncManager.SyncTimeoutSeconds = 120;      // 超时时间2分钟
        
        // 启用云端同步
        _syncManager.EnableCloudSync(true);
        
        _logger.Info("数据同步系统初始化完成");
    }
}
```

### 2. 事件订阅和处理

```csharp
public class SyncEventHandler : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
        
        // 订阅同步进度事件
        _syncManager.OnSyncProgress += HandleSyncProgress;
        
        // 订阅同步完成事件
        _syncManager.OnSyncCompleted += HandleSyncCompleted;
        
        // 订阅同步错误事件
        _syncManager.OnSyncError += HandleSyncError;
    }
    
    private void HandleSyncProgress(SyncProgressInfo progressInfo)
    {
        Debug.Log($"同步进度: {progressInfo.ProgressPercentage:F1}%");
        Debug.Log($"当前操作: {progressInfo.CurrentOperation}");
        Debug.Log($"已处理: {progressInfo.ProcessedItems}/{progressInfo.TotalItems}");
        
        // 更新UI进度条
        UpdateProgressBar(progressInfo.ProgressPercentage);
    }
    
    private void HandleSyncCompleted(SyncResult result)
    {
        if (result.IsSuccess)
        {
            Debug.Log($"同步成功完成，耗时: {result.Duration.TotalSeconds:F2}秒");
            ShowSuccessNotification("数据同步完成");
        }
        else
        {
            Debug.LogError($"同步失败: {result.ErrorMessage}");
            ShowErrorNotification($"同步失败: {result.ErrorMessage}");
        }
    }
    
    private void HandleSyncError(string errorMessage)
    {
        Debug.LogError($"同步错误: {errorMessage}");
        HandleSyncFailure(errorMessage);
    }
}
```#
## 3. 基本同步操作

```csharp
public class BasicSyncOperations : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    // 同步用户设置
    public async void SyncUserSettings()
    {
        try
        {
            var result = await _syncManager.SyncUserSettingsAsync();
            if (result.IsSuccess)
            {
                Debug.Log("用户设置同步成功");
                ApplyUserSettings();
            }
            else
            {
                Debug.LogError($"用户设置同步失败: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"同步过程异常: {ex.Message}");
        }
    }
    
    // 同步对话历史
    public async void SyncConversationHistory(bool includePrivate = false)
    {
        var result = await _syncManager.SyncConversationHistoryAsync(includePrivate);
        
        if (result.IsSuccess)
        {
            Debug.Log($"对话历史同步完成，处理了 {result.UploadedItems + result.DownloadedItems} 条对话");
            RefreshConversationList();
        }
        else
        {
            Debug.LogError($"对话历史同步失败: {result.ErrorMessage}");
        }
    }
    
    // 执行完整同步
    public async void PerformFullSync()
    {
        var result = await _syncManager.PerformFullSyncAsync();
        
        Debug.Log($"完整同步结果: {(result.IsSuccess ? "成功" : "失败")}");
        Debug.Log($"同步耗时: {result.Duration.TotalSeconds:F2}秒");
        Debug.Log($"同步数据类型: {string.Join(", ", result.SyncedDataTypes)}");
        Debug.Log($"处理冲突: {result.ConflictCount}个");
    }
}
```

## 高级功能示例

### 1. 冲突检测和解决

```csharp
public class ConflictResolutionExample : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    public async void HandleSyncConflicts()
    {
        // 检查同步冲突
        var conflicts = await _syncManager.CheckSyncConflictsAsync();
        
        if (conflicts.Count == 0)
        {
            Debug.Log("未发现同步冲突");
            return;
        }
        
        Debug.Log($"发现 {conflicts.Count} 个同步冲突");
        
        foreach (var conflict in conflicts)
        {
            Debug.Log($"冲突详情:");
            Debug.Log($"  数据类型: {conflict.DataType}");
            Debug.Log($"  数据键: {conflict.DataKey}");
            Debug.Log($"  本地修改时间: {conflict.LocalModifiedTime}");
            Debug.Log($"  远程修改时间: {conflict.RemoteModifiedTime}");
            Debug.Log($"  描述: {conflict.Description}");
            
            // 自动解决策略
            var resolution = DetermineResolutionStrategy(conflict);
            
            bool resolved = await _syncManager.ResolveSyncConflictAsync(conflict, resolution);
            Debug.Log($"冲突解决结果: {(resolved ? "成功" : "失败")} - 策略: {resolution}");
        }
    }
    
    private ConflictResolution DetermineResolutionStrategy(SyncConflict conflict)
    {
        // 根据数据类型和修改时间选择策略
        switch (conflict.DataType)
        {
            case "UserSettings":
                // 用户设置优先使用最新修改的版本
                return conflict.LocalModifiedTime > conflict.RemoteModifiedTime
                    ? ConflictResolution.UseLocal
                    : ConflictResolution.UseRemote;
            
            case "ConversationHistory":
                // 对话历史尝试合并
                return ConflictResolution.Merge;
            
            case "UserConfiguration":
                // 配置数据优先使用本地版本
                return ConflictResolution.UseLocal;
            
            default:
                // 默认使用远程版本
                return ConflictResolution.UseRemote;
        }
    }
}
```### 2.
 自定义数据上传下载

```csharp
public class CustomDataSyncExample : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    // 上传用户偏好设置
    public async void UploadUserPreferences()
    {
        var preferences = new UserPreferences
        {
            Theme = "Dark",
            Language = "zh-CN",
            NotificationsEnabled = true,
            AutoSaveInterval = 300,
            CustomSettings = new Dictionary<string, object>
            {
                ["fontSize"] = 14,
                ["showLineNumbers"] = true,
                ["enableAutoComplete"] = true
            }
        };
        
        bool success = await _syncManager.UploadDataAsync("UserPreferences", preferences);
        
        if (success)
        {
            Debug.Log("用户偏好设置上传成功");
        }
        else
        {
            Debug.LogError("用户偏好设置上传失败");
        }
    }
    
    // 下载用户偏好设置
    public async void DownloadUserPreferences()
    {
        var preferences = await _syncManager.DownloadDataAsync<UserPreferences>("UserPreferences");
        
        if (preferences != null)
        {
            Debug.Log($"下载用户偏好设置成功:");
            Debug.Log($"  主题: {preferences.Theme}");
            Debug.Log($"  语言: {preferences.Language}");
            Debug.Log($"  通知: {preferences.NotificationsEnabled}");
            
            // 应用设置
            ApplyUserPreferences(preferences);
        }
        else
        {
            Debug.LogError("下载用户偏好设置失败");
        }
    }
    
    // 批量上传数据
    public async void BatchUploadData()
    {
        var dataItems = new Dictionary<string, object>
        {
            ["UserProfile"] = CreateUserProfile(),
            ["AppSettings"] = CreateAppSettings(),
            ["RecentFiles"] = GetRecentFiles(),
            ["Bookmarks"] = GetUserBookmarks()
        };
        
        var uploadTasks = dataItems.Select(kvp => 
            _syncManager.UploadDataAsync(kvp.Key, kvp.Value));
        
        var results = await Task.WhenAll(uploadTasks);
        
        int successCount = results.Count(r => r);
        Debug.Log($"批量上传完成: {successCount}/{results.Length} 成功");
    }
}

[Serializable]
public class UserPreferences
{
    public string Theme { get; set; }
    public string Language { get; set; }
    public bool NotificationsEnabled { get; set; }
    public int AutoSaveInterval { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; }
}
```

### 3. 同步历史管理

```csharp
public class SyncHistoryManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    public async void DisplaySyncHistory()
    {
        var history = await _syncManager.GetSyncHistoryAsync(20);
        
        Debug.Log($"最近 {history.Count} 次同步记录:");
        Debug.Log("时间\t\t类型\t\t状态\t耗时\t项目数");
        Debug.Log("".PadRight(60, '-'));
        
        foreach (var record in history)
        {
            string status = record.IsSuccess ? "✓ 成功" : "✗ 失败";
            string duration = $"{record.DurationMs / 1000.0:F1}s";
            
            Debug.Log($"{record.SyncTime:MM-dd HH:mm}\t{record.SyncType}\t{status}\t{duration}\t{record.ItemCount}");
            
            if (!record.IsSuccess && !string.IsNullOrEmpty(record.ErrorMessage))
            {
                Debug.Log($"  错误: {record.ErrorMessage}");
            }
        }
        
        // 计算统计信息
        CalculateSyncStatistics(history);
    }
    
    private void CalculateSyncStatistics(List<SyncHistoryRecord> history)
    {
        if (history.Count == 0) return;
        
        int successCount = history.Count(r => r.IsSuccess);
        int failureCount = history.Count - successCount;
        float successRate = (float)successCount / history.Count * 100;
        
        long totalDuration = history.Sum(r => r.DurationMs);
        double avgDuration = totalDuration / (double)history.Count / 1000.0;
        
        int totalItems = history.Sum(r => r.ItemCount);
        long totalDataSize = history.Sum(r => r.DataSize);
        
        Debug.Log($"\n同步统计信息:");
        Debug.Log($"  成功率: {successRate:F1}% ({successCount}/{history.Count})");
        Debug.Log($"  平均耗时: {avgDuration:F2}秒");
        Debug.Log($"  总项目数: {totalItems}");
        Debug.Log($"  总数据量: {FormatBytes(totalDataSize)}");
    }
    
    private string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB" };
        int counter = 0;
        decimal number = bytes;
        
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        
        return $"{number:n1} {suffixes[counter]}";
    }
}
```## 
集成示例

### 1. 应用启动时的自动同步

```csharp
public class AppStartupSyncManager : MonoBehaviour
{
    [SerializeField] private bool enableStartupSync = true;
    [SerializeField] private float syncDelay = 2.0f;
    
    private DataSyncManager _syncManager;
    private AuthenticationManager _authManager;
    
    async void Start()
    {
        _syncManager = DataSyncManager.Instance;
        _authManager = AuthenticationManager.Instance;
        
        if (enableStartupSync)
        {
            await Task.Delay(TimeSpan.FromSeconds(syncDelay));
            await PerformStartupSync();
        }
    }
    
    private async Task PerformStartupSync()
    {
        Debug.Log("开始启动同步...");
        
        // 检查用户登录状态
        if (!_authManager.IsLoggedIn)
        {
            Debug.Log("用户未登录，跳过启动同步");
            return;
        }
        
        // 检查网络连接
        if (Application.internetReachability == NetworkReachability.NotReachable)
        {
            Debug.Log("网络不可用，跳过启动同步");
            return;
        }
        
        try
        {
            // 启用云端同步
            _syncManager.EnableCloudSync(true);
            
            // 执行用户设置同步
            var settingsResult = await _syncManager.SyncUserSettingsAsync();
            if (settingsResult.IsSuccess)
            {
                Debug.Log("启动时用户设置同步完成");
                ApplyUserSettings();
            }
            
            // 执行用户配置同步
            var configResult = await _syncManager.SyncUserConfigurationAsync();
            if (configResult.IsSuccess)
            {
                Debug.Log("启动时用户配置同步完成");
                ApplyUserConfiguration();
            }
            
            Debug.Log("启动同步完成");
        }
        catch (Exception ex)
        {
            Debug.LogError($"启动同步失败: {ex.Message}");
        }
    }
    
    private void ApplyUserSettings()
    {
        // 应用用户设置到UI和系统
        Debug.Log("应用用户设置");
    }
    
    private void ApplyUserConfiguration()
    {
        // 应用用户配置
        Debug.Log("应用用户配置");
    }
}
```

### 2. 设置变更时的增量同步

```csharp
public class IncrementalSyncManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    private Dictionary<string, object> _pendingChanges;
    private float _syncDelay = 5.0f;
    private Coroutine _syncCoroutine;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
        _pendingChanges = new Dictionary<string, object>();
    }
    
    public void OnSettingChanged(string settingKey, object newValue)
    {
        Debug.Log($"设置变更: {settingKey} = {newValue}");
        
        // 立即保存到本地
        SaveSettingLocally(settingKey, newValue);
        
        // 如果启用了云端同步，准备上传
        if (_syncManager.IsCloudSyncEnabled)
        {
            _pendingChanges[settingKey] = newValue;
            
            // 延迟同步，避免频繁上传
            if (_syncCoroutine != null)
            {
                StopCoroutine(_syncCoroutine);
            }
            _syncCoroutine = StartCoroutine(DelayedSync());
        }
    }
    
    private System.Collections.IEnumerator DelayedSync()
    {
        yield return new WaitForSeconds(_syncDelay);
        
        if (_pendingChanges.Count > 0)
        {
            _ = UploadPendingChanges();
        }
    }
    
    private async Task UploadPendingChanges()
    {
        var changesToUpload = new Dictionary<string, object>(_pendingChanges);
        _pendingChanges.Clear();
        
        Debug.Log($"上传 {changesToUpload.Count} 个设置变更");
        
        foreach (var change in changesToUpload)
        {
            var settingData = new
            {
                key = change.Key,
                value = change.Value,
                lastModified = DateTime.Now,
                deviceId = SystemInfo.deviceUniqueIdentifier
            };
            
            bool uploaded = await _syncManager.UploadDataAsync($"Setting_{change.Key}", settingData);
            
            if (uploaded)
            {
                Debug.Log($"设置 {change.Key} 同步成功");
            }
            else
            {
                Debug.LogError($"设置 {change.Key} 同步失败");
                // 重新加入待同步队列
                _pendingChanges[change.Key] = change.Value;
            }
        }
    }
    
    private void SaveSettingLocally(string key, object value)
    {
        // 保存到本地存储
        PlayerPrefs.SetString($"Setting_{key}", JsonUtility.ToJson(value));
        PlayerPrefs.Save();
    }
}
```### 3.
 UI集成示例

```csharp
public class SyncUIManager : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private Slider progressBar;
    [SerializeField] private Text statusText;
    [SerializeField] private Text detailText;
    [SerializeField] private Button syncButton;
    [SerializeField] private Toggle autoSyncToggle;
    [SerializeField] private GameObject conflictPanel;
    
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
        
        // 订阅同步事件
        _syncManager.OnSyncProgress += UpdateSyncProgress;
        _syncManager.OnSyncCompleted += OnSyncCompleted;
        _syncManager.OnSyncError += OnSyncError;
        
        // 设置UI事件
        syncButton.onClick.AddListener(StartManualSync);
        autoSyncToggle.onValueChanged.AddListener(OnAutoSyncToggleChanged);
        
        // 初始化UI状态
        UpdateUIState();
    }
    
    private void UpdateSyncProgress(SyncProgressInfo progressInfo)
    {
        // 更新进度条
        progressBar.value = progressInfo.ProgressPercentage / 100f;
        
        // 更新状态文本
        statusText.text = progressInfo.CurrentOperation;
        
        // 更新详细信息
        detailText.text = $"已处理: {progressInfo.ProcessedItems}/{progressInfo.TotalItems}";
        
        // 显示剩余时间
        if (progressInfo.EstimatedRemainingSeconds > 0)
        {
            detailText.text += $"\n剩余时间: {progressInfo.EstimatedRemainingSeconds}秒";
        }
        
        // 禁用同步按钮
        syncButton.interactable = false;
    }
    
    private void OnSyncCompleted(SyncResult result)
    {
        if (result.IsSuccess)
        {
            statusText.text = "同步完成";
            detailText.text = $"耗时: {result.Duration.TotalSeconds:F1}秒\n" +
                             $"上传: {result.UploadedItems} 项\n" +
                             $"下载: {result.DownloadedItems} 项";
            
            // 显示成功动画
            ShowSuccessAnimation();
        }
        else
        {
            statusText.text = "同步失败";
            detailText.text = result.ErrorMessage;
            
            // 显示错误提示
            ShowErrorMessage(result.ErrorMessage);
        }
        
        // 重新启用同步按钮
        syncButton.interactable = true;
        
        // 检查冲突
        if (result.ConflictCount > 0)
        {
            ShowConflictPanel();
        }
    }
    
    private void OnSyncError(string errorMessage)
    {
        statusText.text = "同步错误";
        detailText.text = errorMessage;
        syncButton.interactable = true;
        
        ShowErrorMessage(errorMessage);
    }
    
    private async void StartManualSync()
    {
        statusText.text = "准备同步...";
        progressBar.value = 0;
        
        try
        {
            await _syncManager.PerformFullSyncAsync();
        }
        catch (Exception ex)
        {
            OnSyncError($"同步异常: {ex.Message}");
        }
    }
    
    private void OnAutoSyncToggleChanged(bool enabled)
    {
        _syncManager.EnableCloudSync(enabled);
        UpdateUIState();
    }
    
    private void UpdateUIState()
    {
        autoSyncToggle.isOn = _syncManager.IsCloudSyncEnabled;
        
        // 根据同步状态更新UI
        switch (_syncManager.CurrentSyncStatus)
        {
            case SyncStatus.Idle:
                statusText.text = "就绪";
                syncButton.interactable = true;
                break;
            case SyncStatus.Syncing:
                statusText.text = "同步中...";
                syncButton.interactable = false;
                break;
            case SyncStatus.Failed:
                statusText.text = "同步失败";
                syncButton.interactable = true;
                break;
        }
    }
    
    private async void ShowConflictPanel()
    {
        conflictPanel.SetActive(true);
        
        var conflicts = await _syncManager.CheckSyncConflictsAsync();
        
        // 显示冲突解决界面
        var conflictResolver = conflictPanel.GetComponent<ConflictResolverUI>();
        conflictResolver.ShowConflicts(conflicts);
    }
    
    private void ShowSuccessAnimation()
    {
        // 实现成功动画
        Debug.Log("显示同步成功动画");
    }
    
    private void ShowErrorMessage(string message)
    {
        // 显示错误提示
        Debug.LogError($"同步错误: {message}");
    }
}
```

## 错误处理和重试示例

```csharp
public class RobustSyncManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    private int _maxRetryAttempts = 3;
    private float _retryDelay = 2.0f;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    public async Task<SyncResult> SafeSyncWithRetry(Func<Task<SyncResult>> syncOperation)
    {
        for (int attempt = 1; attempt <= _maxRetryAttempts; attempt++)
        {
            try
            {
                Debug.Log($"同步尝试 {attempt}/{_maxRetryAttempts}");
                
                // 检查前置条件
                if (!CheckPreconditions())
                {
                    return SyncResult.Failure("前置条件检查失败");
                }
                
                var result = await syncOperation();
                
                if (result.IsSuccess)
                {
                    Debug.Log($"同步成功 (尝试 {attempt})");
                    return result;
                }
                
                Debug.LogWarning($"同步尝试 {attempt} 失败: {result.ErrorMessage}");
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < _maxRetryAttempts)
                {
                    float delay = _retryDelay * Mathf.Pow(2, attempt - 1); // 指数退避
                    Debug.Log($"等待 {delay} 秒后重试");
                    await Task.Delay(TimeSpan.FromSeconds(delay));
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"同步尝试 {attempt} 异常: {ex.Message}");
                
                if (attempt == _maxRetryAttempts)
                {
                    return SyncResult.Failure($"重试 {_maxRetryAttempts} 次后仍然失败: {ex.Message}");
                }
            }
        }
        
        return SyncResult.Failure("所有重试尝试都失败了");
    }
    
    private bool CheckPreconditions()
    {
        // 检查网络连接
        if (Application.internetReachability == NetworkReachability.NotReachable)
        {
            Debug.LogError("网络不可用");
            return false;
        }
        
        // 检查用户认证
        var authManager = AuthenticationManager.Instance;
        if (!authManager.IsLoggedIn)
        {
            Debug.LogError("用户未登录");
            return false;
        }
        
        // 检查云端同步是否启用
        if (!_syncManager.IsCloudSyncEnabled)
        {
            Debug.LogError("云端同步未启用");
            return false;
        }
        
        return true;
    }
    
    public async void PerformRobustFullSync()
    {
        var result = await SafeSyncWithRetry(() => _syncManager.PerformFullSyncAsync());
        
        if (result.IsSuccess)
        {
            Debug.Log("健壮同步完成");
        }
        else
        {
            Debug.LogError($"健壮同步最终失败: {result.ErrorMessage}");
            HandleSyncFailure(result);
        }
    }
    
    private void HandleSyncFailure(SyncResult result)
    {
        // 记录失败信息
        Debug.LogError($"同步失败处理: {result.ErrorMessage}");
        
        // 可以实现以下策略:
        // 1. 保存失败状态到本地
        // 2. 安排稍后重试
        // 3. 通知用户
        // 4. 降级到离线模式
    }
}
```