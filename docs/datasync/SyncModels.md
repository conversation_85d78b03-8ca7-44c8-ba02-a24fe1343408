# 数据同步模型文档

## 概述

本文档详细描述了数据同步系统中使用的所有数据模型和枚举类型。这些模型定义了同步操作的状态、进度、结果以及相关的数据结构，为整个同步系统提供了统一的数据契约。

## 命名空间

```csharp
DigitalHuman.Core.DataSync
```

## 枚举类型

### SyncStatus

同步状态枚举，表示当前同步操作所处的状态。

```csharp
public enum SyncStatus
{
    /// <summary>
    /// 空闲状态 - 没有进行任何同步操作
    /// </summary>
    Idle,
    
    /// <summary>
    /// 同步中 - 正在执行同步操作
    /// </summary>
    Syncing,
    
    /// <summary>
    /// 上传中 - 正在上传数据到云端
    /// </summary>
    Uploading,
    
    /// <summary>
    /// 下载中 - 正在从云端下载数据
    /// </summary>
    Downloading,
    
    /// <summary>
    /// 检查冲突 - 正在检查数据冲突
    /// </summary>
    CheckingConflicts,
    
    /// <summary>
    /// 同步完成 - 同步操作已成功完成
    /// </summary>
    Completed,
    
    /// <summary>
    /// 同步失败 - 同步操作失败
    /// </summary>
    Failed
}
```

**使用示例：**
```csharp
// 检查同步状态
var syncManager = DataSyncManager.Instance;
switch (syncManager.CurrentSyncStatus)
{
    case SyncStatus.Idle:
        Debug.Log("可以开始新的同步操作");
        break;
    case SyncStatus.Syncing:
        Debug.Log("同步正在进行中，请等待");
        break;
    case SyncStatus.Failed:
        Debug.Log("上次同步失败，可以重试");
        break;
}
```

### ConflictResolution

冲突解决方案枚举，定义了处理同步冲突的不同策略。

```csharp
public enum ConflictResolution
{
    /// <summary>
    /// 使用本地版本 - 本地数据覆盖云端数据
    /// </summary>
    UseLocal,
    
    /// <summary>
    /// 使用远程版本 - 云端数据覆盖本地数据
    /// </summary>
    UseRemote,
    
    /// <summary>
    /// 合并版本 - 尝试智能合并本地和云端数据
    /// </summary>
    Merge,
    
    /// <summary>
    /// 跳过此冲突 - 保持现状，不处理此冲突
    /// </summary>
    Skip
}
```

**使用示例：**
```csharp
// 根据冲突类型选择解决策略
ConflictResolution GetResolutionStrategy(SyncConflict conflict)
{
    // 如果本地修改更新，使用本地版本
    if (conflict.LocalModifiedTime > conflict.RemoteModifiedTime)
    {
        return ConflictResolution.UseLocal;
    }
    
    // 如果是配置类数据，尝试合并
    if (conflict.DataType == "UserConfiguration")
    {
        return ConflictResolution.Merge;
    }
    
    // 默认使用远程版本
    return ConflictResolution.UseRemote;
}
```

## 数据模型类

### SyncProgressInfo

同步进度信息类，用于报告同步操作的实时进度和状态。

```csharp
[Serializable]
public class SyncProgressInfo
{
    /// <summary>
    /// 当前同步状态
    /// </summary>
    public SyncStatus Status { get; set; }
    
    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public float ProgressPercentage { get; set; }
    
    /// <summary>
    /// 当前操作描述
    /// </summary>
    public string CurrentOperation { get; set; }
    
    /// <summary>
    /// 已处理项目数
    /// </summary>
    public int ProcessedItems { get; set; }
    
    /// <summary>
    /// 总项目数
    /// </summary>
    public int TotalItems { get; set; }
    
    /// <summary>
    /// 估计剩余时间（秒）
    /// </summary>
    public int EstimatedRemainingSeconds { get; set; }
}
```

**属性说明：**

- **Status**: 当前同步操作的状态，对应 `SyncStatus` 枚举值
- **ProgressPercentage**: 完成百分比，范围 0-100，用于显示进度条
- **CurrentOperation**: 当前正在执行的操作描述，如"同步用户设置..."
- **ProcessedItems**: 已经处理完成的数据项数量
- **TotalItems**: 需要处理的总数据项数量
- **EstimatedRemainingSeconds**: 基于当前进度估算的剩余完成时间

**使用示例：**
```csharp
// 订阅进度事件
syncManager.OnSyncProgress += (progressInfo) =>
{
    // 更新进度条
    progressBar.value = progressInfo.ProgressPercentage / 100f;
    
    // 更新状态文本
    statusText.text = progressInfo.CurrentOperation;
    
    // 更新详细信息
    detailText.text = $"已处理: {progressInfo.ProcessedItems}/{progressInfo.TotalItems}";
    
    // 显示剩余时间
    if (progressInfo.EstimatedRemainingSeconds > 0)
    {
        timeText.text = $"剩余时间: {progressInfo.EstimatedRemainingSeconds}秒";
    }
};
```

### SyncResult

同步结果类，包含同步操作完成后的详细结果信息。

```csharp
[Serializable]
public class SyncResult
{
    /// <summary>
    /// 同步是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 错误消息（失败时）
    /// </summary>
    public string ErrorMessage { get; set; }
    
    /// <summary>
    /// 同步开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 同步结束时间
    /// </summary>
    public DateTime EndTime { get; set; }
    
    /// <summary>
    /// 同步持续时间
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;
    
    /// <summary>
    /// 上传的数据项数量
    /// </summary>
    public int UploadedItems { get; set; }
    
    /// <summary>
    /// 下载的数据项数量
    /// </summary>
    public int DownloadedItems { get; set; }
    
    /// <summary>
    /// 冲突数量
    /// </summary>
    public int ConflictCount { get; set; }
    
    /// <summary>
    /// 同步的数据类型列表
    /// </summary>
    public List<string> SyncedDataTypes { get; set; }
    
    /// <summary>
    /// 详细信息字典
    /// </summary>
    public Dictionary<string, object> Details { get; set; }
    
    /// <summary>
    /// 构造函数
    /// </summary>
    public SyncResult()
    {
        SyncedDataTypes = new List<string>();
        Details = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// 创建成功结果的静态方法
    /// </summary>
    /// <returns>表示成功的同步结果</returns>
    public static SyncResult Success()
    {
        return new SyncResult
        {
            IsSuccess = true,
            StartTime = DateTime.Now,
            EndTime = DateTime.Now
        };
    }
    
    /// <summary>
    /// 创建失败结果的静态方法
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>表示失败的同步结果</returns>
    public static SyncResult Failure(string errorMessage)
    {
        return new SyncResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            StartTime = DateTime.Now,
            EndTime = DateTime.Now
        };
    }
}
```

**使用示例：**
```csharp
// 处理同步结果
syncManager.OnSyncCompleted += (result) =>
{
    if (result.IsSuccess)
    {
        Debug.Log($"同步成功完成！");
        Debug.Log($"耗时: {result.Duration.TotalSeconds:F2}秒");
        Debug.Log($"上传项目: {result.UploadedItems}");
        Debug.Log($"下载项目: {result.DownloadedItems}");
        Debug.Log($"处理冲突: {result.ConflictCount}个");
        Debug.Log($"同步数据类型: {string.Join(", ", result.SyncedDataTypes)}");
        
        // 显示成功消息
        ShowSuccessMessage($"同步完成，处理了 {result.UploadedItems + result.DownloadedItems} 个项目");
    }
    else
    {
        Debug.LogError($"同步失败: {result.ErrorMessage}");
        ShowErrorMessage($"同步失败: {result.ErrorMessage}");
    }
};

// 创建自定义结果
var customResult = SyncResult.Success();
customResult.SyncedDataTypes.Add("UserSettings");
customResult.SyncedDataTypes.Add("ConversationHistory");
customResult.Details["totalDataSize"] = 1024 * 1024; // 1MB
customResult.Details["compressionRatio"] = 0.7f;
```

### SyncConflict

同步冲突信息类，描述本地和云端数据之间的冲突详情。

```csharp
[Serializable]
public class SyncConflict
{
    /// <summary>
    /// 冲突唯一标识符
    /// </summary>
    public string ConflictId { get; set; }
    
    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; }
    
    /// <summary>
    /// 数据键
    /// </summary>
    public string DataKey { get; set; }
    
    /// <summary>
    /// 本地版本数据
    /// </summary>
    public object LocalVersion { get; set; }
    
    /// <summary>
    /// 远程版本数据
    /// </summary>
    public object RemoteVersion { get; set; }
    
    /// <summary>
    /// 本地修改时间
    /// </summary>
    public DateTime LocalModifiedTime { get; set; }
    
    /// <summary>
    /// 远程修改时间
    /// </summary>
    public DateTime RemoteModifiedTime { get; set; }
    
    /// <summary>
    /// 冲突描述
    /// </summary>
    public string Description { get; set; }
}
```

**使用示例：**
```csharp
// 处理同步冲突
var conflicts = await syncManager.CheckSyncConflictsAsync();

foreach (var conflict in conflicts)
{
    Debug.Log($"发现冲突: {conflict.Description}");
    Debug.Log($"数据类型: {conflict.DataType}");
    Debug.Log($"数据键: {conflict.DataKey}");
    Debug.Log($"本地修改: {conflict.LocalModifiedTime}");
    Debug.Log($"远程修改: {conflict.RemoteModifiedTime}");
    
    // 根据修改时间自动选择解决策略
    ConflictResolution resolution;
    if (conflict.LocalModifiedTime > conflict.RemoteModifiedTime)
    {
        resolution = ConflictResolution.UseLocal;
        Debug.Log("选择本地版本（更新）");
    }
    else
    {
        resolution = ConflictResolution.UseRemote;
        Debug.Log("选择远程版本（更新）");
    }
    
    // 解决冲突
    bool resolved = await syncManager.ResolveSyncConflictAsync(conflict, resolution);
    Debug.Log($"冲突解决结果: {(resolved ? "成功" : "失败")}");
}
```

### SyncHistoryRecord

同步历史记录类，记录每次同步操作的详细信息。

```csharp
[Serializable]
public class SyncHistoryRecord
{
    /// <summary>
    /// 记录唯一标识符
    /// </summary>
    public string RecordId { get; set; }
    
    /// <summary>
    /// 同步时间
    /// </summary>
    public DateTime SyncTime { get; set; }
    
    /// <summary>
    /// 同步类型
    /// </summary>
    public string SyncType { get; set; }
    
    /// <summary>
    /// 同步结果（成功/失败）
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 错误消息（失败时）
    /// </summary>
    public string ErrorMessage { get; set; }
    
    /// <summary>
    /// 同步持续时间（毫秒）
    /// </summary>
    public long DurationMs { get; set; }
    
    /// <summary>
    /// 同步的数据项数量
    /// </summary>
    public int ItemCount { get; set; }
    
    /// <summary>
    /// 数据大小（字节）
    /// </summary>
    public long DataSize { get; set; }
}
```

**同步类型说明：**
- `"UserSettings"`: 用户设置同步
- `"ConversationHistory"`: 对话历史同步
- `"UserConfiguration"`: 用户配置同步
- `"FullSync"`: 完整同步
- `"ManualSync"`: 手动触发的同步

**使用示例：**
```csharp
// 获取和显示同步历史
var history = await syncManager.GetSyncHistoryAsync(20);

Debug.Log($"最近 {history.Count} 次同步记录:");
foreach (var record in history)
{
    string status = record.IsSuccess ? "✓ 成功" : "✗ 失败";
    string duration = $"{record.DurationMs / 1000.0:F1}秒";
    string dataSize = FormatBytes(record.DataSize);
    
    Debug.Log($"{record.SyncTime:MM-dd HH:mm} | {record.SyncType} | {status} | {duration} | {record.ItemCount}项 | {dataSize}");
    
    if (!record.IsSuccess && !string.IsNullOrEmpty(record.ErrorMessage))
    {
        Debug.Log($"  错误: {record.ErrorMessage}");
    }
}

// 统计成功率
int successCount = history.Count(r => r.IsSuccess);
float successRate = history.Count > 0 ? (float)successCount / history.Count * 100 : 0;
Debug.Log($"同步成功率: {successRate:F1}%");
```

### SyncDataItem

数据同步项类，表示一个可同步的数据单元。

```csharp
[Serializable]
public class SyncDataItem
{
    /// <summary>
    /// 数据键（唯一标识符）
    /// </summary>
    public string Key { get; set; }
    
    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; }
    
    /// <summary>
    /// 数据内容
    /// </summary>
    public object Data { get; set; }
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; }
    
    /// <summary>
    /// 数据版本号
    /// </summary>
    public string Version { get; set; }
    
    /// <summary>
    /// 数据校验和
    /// </summary>
    public string Checksum { get; set; }
    
    /// <summary>
    /// 是否为私密数据
    /// </summary>
    public bool IsPrivate { get; set; }
    
    /// <summary>
    /// 数据标签列表
    /// </summary>
    public List<string> Tags { get; set; }
    
    /// <summary>
    /// 构造函数
    /// </summary>
    public SyncDataItem()
    {
        Tags = new List<string>();
        LastModified = DateTime.Now;
        Version = "1.0";
    }
}
```

**使用示例：**
```csharp
// 创建用户设置数据项
var userSettingsItem = new SyncDataItem
{
    Key = "user_theme_preference",
    DataType = "UserSettings",
    Data = new { Theme = "Dark", Language = "zh-CN" },
    IsPrivate = false,
    Tags = { "ui", "preference" }
};

// 创建私密对话数据项
var privateConversationItem = new SyncDataItem
{
    Key = $"conversation_{conversationId}",
    DataType = "ConversationHistory",
    Data = conversationData,
    IsPrivate = true,
    Tags = { "conversation", "private" }
};

// 计算数据校验和
userSettingsItem.Checksum = CalculateChecksum(userSettingsItem.Data);

// 上传数据项
bool uploaded = await syncManager.UploadDataAsync(userSettingsItem.DataType, userSettingsItem);
```

## 辅助方法和扩展

### 数据格式化辅助方法

```csharp
public static class SyncModelExtensions
{
    /// <summary>
    /// 格式化字节数为可读字符串
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化后的字符串</returns>
    public static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        
        return $"{number:n1} {suffixes[counter]}";
    }
    
    /// <summary>
    /// 格式化持续时间为可读字符串
    /// </summary>
    /// <param name="durationMs">持续时间（毫秒）</param>
    /// <returns>格式化后的字符串</returns>
    public static string FormatDuration(long durationMs)
    {
        var timeSpan = TimeSpan.FromMilliseconds(durationMs);
        
        if (timeSpan.TotalDays >= 1)
            return $"{timeSpan.TotalDays:F1}天";
        if (timeSpan.TotalHours >= 1)
            return $"{timeSpan.TotalHours:F1}小时";
        if (timeSpan.TotalMinutes >= 1)
            return $"{timeSpan.TotalMinutes:F1}分钟";
        if (timeSpan.TotalSeconds >= 1)
            return $"{timeSpan.TotalSeconds:F1}秒";
        
        return $"{durationMs}毫秒";
    }
    
    /// <summary>
    /// 获取同步状态的中文描述
    /// </summary>
    /// <param name="status">同步状态</param>
    /// <returns>中文描述</returns>
    public static string GetStatusDescription(this SyncStatus status)
    {
        return status switch
        {
            SyncStatus.Idle => "空闲",
            SyncStatus.Syncing => "同步中",
            SyncStatus.Uploading => "上传中",
            SyncStatus.Downloading => "下载中",
            SyncStatus.CheckingConflicts => "检查冲突",
            SyncStatus.Completed => "已完成",
            SyncStatus.Failed => "失败",
            _ => "未知状态"
        };
    }
    
    /// <summary>
    /// 获取冲突解决策略的中文描述
    /// </summary>
    /// <param name="resolution">解决策略</param>
    /// <returns>中文描述</returns>
    public static string GetResolutionDescription(this ConflictResolution resolution)
    {
        return resolution switch
        {
            ConflictResolution.UseLocal => "使用本地版本",
            ConflictResolution.UseRemote => "使用远程版本",
            ConflictResolution.Merge => "合并版本",
            ConflictResolution.Skip => "跳过冲突",
            _ => "未知策略"
        };
    }
}
```

## 数据验证

### 模型验证方法

```csharp
public static class SyncModelValidator
{
    /// <summary>
    /// 验证同步进度信息
    /// </summary>
    /// <param name="progressInfo">进度信息</param>
    /// <returns>验证结果</returns>
    public static bool ValidateProgressInfo(SyncProgressInfo progressInfo)
    {
        if (progressInfo == null) return false;
        
        // 验证进度百分比范围
        if (progressInfo.ProgressPercentage < 0 || progressInfo.ProgressPercentage > 100)
            return false;
        
        // 验证项目数量
        if (progressInfo.ProcessedItems < 0 || progressInfo.TotalItems < 0)
            return false;
        
        if (progressInfo.ProcessedItems > progressInfo.TotalItems)
            return false;
        
        // 验证剩余时间
        if (progressInfo.EstimatedRemainingSeconds < 0)
            return false;
        
        return true;
    }
    
    /// <summary>
    /// 验证同步结果
    /// </summary>
    /// <param name="result">同步结果</param>
    /// <returns>验证结果</returns>
    public static bool ValidateSyncResult(SyncResult result)
    {
        if (result == null) return false;
        
        // 验证时间逻辑
        if (result.EndTime < result.StartTime)
            return false;
        
        // 验证数据项数量
        if (result.UploadedItems < 0 || result.DownloadedItems < 0)
            return false;
        
        // 验证冲突数量
        if (result.ConflictCount < 0)
            return false;
        
        // 失败时必须有错误消息
        if (!result.IsSuccess && string.IsNullOrEmpty(result.ErrorMessage))
            return false;
        
        return true;
    }
}
```

## 序列化支持

所有模型类都标记了 `[Serializable]` 特性，支持以下序列化方式：

### JSON 序列化示例

```csharp
// 序列化同步结果
var result = new SyncResult
{
    IsSuccess = true,
    StartTime = DateTime.Now.AddMinutes(-5),
    EndTime = DateTime.Now,
    UploadedItems = 10,
    DownloadedItems = 5
};

string json = JsonUtility.ToJson(result, true);
Debug.Log(json);

// 反序列化
var deserializedResult = JsonUtility.FromJson<SyncResult>(json);
```

### 二进制序列化示例

```csharp
// 序列化同步历史记录
var historyRecord = new SyncHistoryRecord
{
    RecordId = Guid.NewGuid().ToString(),
    SyncTime = DateTime.Now,
    SyncType = "FullSync",
    IsSuccess = true,
    DurationMs = 5000,
    ItemCount = 15,
    DataSize = 1024 * 1024
};

// 使用二进制格式化器序列化
using (var stream = new MemoryStream())
{
    var formatter = new BinaryFormatter();
    formatter.Serialize(stream, historyRecord);
    byte[] data = stream.ToArray();
    
    // 保存到文件或数据库
    File.WriteAllBytes("sync_history.dat", data);
}
```

## 最佳实践

### 1. 数据完整性

```csharp
// 创建数据项时计算校验和
public static SyncDataItem CreateDataItem(string key, string dataType, object data)
{
    var item = new SyncDataItem
    {
        Key = key,
        DataType = dataType,
        Data = data,
        LastModified = DateTime.Now,
        Version = GenerateVersion(),
        Checksum = CalculateChecksum(data)
    };
    
    return item;
}
```

### 2. 错误处理

```csharp
// 安全地处理同步结果
public static void HandleSyncResult(SyncResult result)
{
    if (result == null)
    {
        Debug.LogError("同步结果为空");
        return;
    }
    
    if (!SyncModelValidator.ValidateSyncResult(result))
    {
        Debug.LogError("同步结果验证失败");
        return;
    }
    
    if (result.IsSuccess)
    {
        ProcessSuccessfulSync(result);
    }
    else
    {
        ProcessFailedSync(result);
    }
}
```

### 3. 性能优化

```csharp
// 批量处理同步项目
public static List<SyncDataItem> BatchProcessItems(IEnumerable<SyncDataItem> items, int batchSize = 100)
{
    var processedItems = new List<SyncDataItem>();
    var batch = new List<SyncDataItem>();
    
    foreach (var item in items)
    {
        batch.Add(item);
        
        if (batch.Count >= batchSize)
        {
            ProcessBatch(batch);
            processedItems.AddRange(batch);
            batch.Clear();
        }
    }
    
    // 处理剩余项目
    if (batch.Count > 0)
    {
        ProcessBatch(batch);
        processedItems.AddRange(batch);
    }
    
    return processedItems;
}
```

## 相关文档

- [DataSyncManager 实现文档](DataSyncManager.md)
- [IDataSyncManager 接口文档](IDataSyncManager.md)
- [数据同步最佳实践](DataSyncBestPractices.md)
- [同步冲突处理指南](SyncConflictResolution.md)