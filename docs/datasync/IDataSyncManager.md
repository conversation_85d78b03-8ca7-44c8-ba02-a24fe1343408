# IDataSyncManager 接口文档

## 概述

`IDataSyncManager` 是数据同步管理器的核心接口，定义了数据同步系统的标准契约。该接口规范了用户数据在本地和云端之间同步的所有必要操作，包括数据上传下载、冲突检测解决、同步历史管理等功能。

## 命名空间

```csharp
DigitalHuman.Core.DataSync
```

## 接口定义

```csharp
public interface IDataSyncManager
```

## 属性

### IsCloudSyncEnabled

```csharp
bool IsCloudSyncEnabled { get; }
```

**描述：** 获取云端同步是否启用的状态

**返回值：** `bool` - 云端同步启用状态

**用途：**
- 检查当前是否可以执行云端同步操作
- 在UI中显示同步功能的可用性
- 条件性执行同步相关逻辑

### CurrentSyncStatus

```csharp
SyncStatus CurrentSyncStatus { get; }
```

**描述：** 获取当前同步操作的状态

**返回值：** `SyncStatus` - 当前同步状态枚举值

**可能的状态值：**
- `Idle`: 空闲状态，没有进行同步操作
- `Syncing`: 正在执行同步操作
- `Uploading`: 正在上传数据到云端
- `Downloading`: 正在从云端下载数据
- `CheckingConflicts`: 正在检查同步冲突
- `Completed`: 同步操作已完成
- `Failed`: 同步操作失败

## 事件

### OnSyncProgress

```csharp
event Action<SyncProgressInfo> OnSyncProgress;
```

**描述：** 同步进度变化时触发的事件

**参数：** `SyncProgressInfo` - 包含详细进度信息的对象

**触发时机：**
- 同步操作开始时
- 同步进度发生变化时
- 切换到不同的同步阶段时

**使用示例：**
```csharp
syncManager.OnSyncProgress += (progressInfo) =>
{
    Console.WriteLine($"同步进度: {progressInfo.ProgressPercentage:F1}%");
    Console.WriteLine($"当前操作: {progressInfo.CurrentOperation}");
    Console.WriteLine($"剩余时间: {progressInfo.EstimatedRemainingSeconds}秒");
};
```

### OnSyncCompleted

```csharp
event Action<SyncResult> OnSyncCompleted;
```

**描述：** 同步操作完成时触发的事件

**参数：** `SyncResult` - 包含同步结果详细信息的对象

**触发时机：**
- 同步操作成功完成时
- 同步操作失败时
- 同步操作被取消时

**使用示例：**
```csharp
syncManager.OnSyncCompleted += (result) =>
{
    if (result.IsSuccess)
    {
        Console.WriteLine($"同步成功完成，耗时: {result.Duration.TotalSeconds:F2}秒");
        Console.WriteLine($"同步数据类型: {string.Join(", ", result.SyncedDataTypes)}");
    }
    else
    {
        Console.WriteLine($"同步失败: {result.ErrorMessage}");
    }
};
```

### OnSyncError

```csharp
event Action<string> OnSyncError;
```

**描述：** 同步过程中发生错误时触发的事件

**参数：** `string` - 错误消息描述

**触发时机：**
- 网络连接错误
- 认证失败
- 数据格式错误
- 服务器响应错误

## 方法

### EnableCloudSync

```csharp
void EnableCloudSync(bool enable);
```

**描述：** 启用或禁用云端同步功能

**参数：**
- `enable` (bool): 是否启用云端同步

**功能：**
- 控制云端同步功能的开关
- 影响所有需要云端连接的同步操作
- 启用时可能启动自动同步定时器
- 禁用时停止所有云端同步活动

**实现要求：**
- 状态变更应该立即生效
- 应该记录状态变更日志
- 可能需要通知其他组件状态变化

### SyncUserSettingsAsync

```csharp
Task<SyncResult> SyncUserSettingsAsync();
```

**描述：** 异步同步用户设置数据

**返回值：** `Task<SyncResult>` - 包含同步结果的任务

**功能：**
- 上传本地用户设置到云端
- 下载云端用户设置到本地
- 处理设置数据的合并和冲突
- 确保设置数据的一致性

**前置条件：**
- 云端同步必须启用
- 用户必须已认证登录
- 网络连接可用

**异常处理：**
- 网络不可用时返回失败结果
- 认证失败时返回相应错误
- 数据格式错误时进行适当处理

### SyncConversationHistoryAsync

```csharp
Task<SyncResult> SyncConversationHistoryAsync(bool includePrivate = false);
```

**描述：** 异步同步对话历史数据

**参数：**
- `includePrivate` (bool, 默认: false): 是否包含私密对话

**返回值：** `Task<SyncResult>` - 包含同步结果的任务

**功能：**
- 同步用户的对话历史记录
- 支持选择性同步私密对话
- 处理大量对话数据的分批传输
- 保持对话的时间顺序和完整性

**数据处理：**
- 对话数据可能很大，需要分批处理
- 私密对话需要额外的安全措施
- 保持对话的关联关系和上下文

### SyncUserConfigurationAsync

```csharp
Task<SyncResult> SyncUserConfigurationAsync();
```

**描述：** 异步同步用户配置数据

**返回值：** `Task<SyncResult>` - 包含同步结果的任务

**功能：**
- 同步用户的个性化配置
- 包括界面设置、偏好选项等
- 确保配置的完整性和一致性
- 处理配置项的版本兼容性

**配置类型：**
- 界面主题和布局设置
- 语言和地区偏好
- 通知和提醒设置
- 功能开关和选项

### PerformFullSyncAsync

```csharp
Task<SyncResult> PerformFullSyncAsync();
```

**描述：** 执行完整的数据同步操作

**返回值：** `Task<SyncResult>` - 包含综合同步结果的任务

**功能：**
- 依次同步所有类型的用户数据
- 检测和处理数据冲突
- 提供详细的进度报告
- 生成完整的同步历史记录

**同步范围：**
- 用户设置数据
- 用户配置数据
- 对话历史数据
- 其他用户相关数据

**进度报告：**
- 应该通过 `OnSyncProgress` 事件报告详细进度
- 包含当前操作描述和预估剩余时间
- 提供百分比进度和处理项目数量

### UploadDataAsync

```csharp
Task<bool> UploadDataAsync(string dataType, object data);
```

**描述：** 上传指定数据到云端

**参数：**
- `dataType` (string): 数据类型标识符
- `data` (object): 要上传的数据对象

**返回值：** `Task<bool>` - 上传是否成功

**功能：**
- 将任意类型的数据上传到云端存储
- 自动处理数据序列化
- 提供上传进度反馈
- 处理上传过程中的错误

**数据处理：**
- 支持各种数据类型的序列化
- 可能需要数据压缩以节省带宽
- 应该验证数据完整性

### DownloadDataAsync

```csharp
Task<T> DownloadDataAsync<T>(string dataType);
```

**描述：** 从云端下载指定类型的数据

**类型参数：**
- `T`: 期望的数据类型

**参数：**
- `dataType` (string): 数据类型标识符

**返回值：** `Task<T>` - 下载的数据对象，失败时返回默认值

**功能：**
- 从云端下载指定类型的数据
- 自动处理数据反序列化
- 提供类型安全的数据返回
- 处理下载过程中的错误

**类型安全：**
- 使用泛型确保类型安全
- 自动处理类型转换
- 验证数据格式的正确性

### CheckSyncConflictsAsync

```csharp
Task<List<SyncConflict>> CheckSyncConflictsAsync();
```

**描述：** 检查本地和云端数据之间的同步冲突

**返回值：** `Task<List<SyncConflict>>` - 检测到的冲突列表

**功能：**
- 比较本地和云端数据的版本
- 识别数据修改时间冲突
- 检测数据内容差异
- 生成详细的冲突报告

**冲突检测逻辑：**
- 比较数据的最后修改时间
- 检查数据版本号或校验和
- 识别结构性数据差异
- 分析数据依赖关系冲突

### ResolveSyncConflictAsync

```csharp
Task<bool> ResolveSyncConflictAsync(SyncConflict conflict, ConflictResolution resolution);
```

**描述：** 解决指定的同步冲突

**参数：**
- `conflict` (SyncConflict): 要解决的冲突信息
- `resolution` (ConflictResolution): 解决方案

**返回值：** `Task<bool>` - 冲突解决是否成功

**解决策略：**
- `UseLocal`: 使用本地版本覆盖云端
- `UseRemote`: 使用云端版本覆盖本地
- `Merge`: 尝试智能合并两个版本
- `Skip`: 跳过此冲突，保持现状

**实现要求：**
- 根据解决策略执行相应操作
- 确保数据一致性
- 记录冲突解决历史
- 处理解决过程中的错误

### GetSyncHistoryAsync

```csharp
Task<List<SyncHistoryRecord>> GetSyncHistoryAsync(int limit = 50);
```

**描述：** 获取同步历史记录

**参数：**
- `limit` (int, 默认: 50): 返回记录的最大数量

**返回值：** `Task<List<SyncHistoryRecord>>` - 同步历史记录列表

**功能：**
- 获取最近的同步操作历史
- 支持限制返回记录数量
- 按时间倒序排列记录
- 包含详细的同步统计信息

**历史记录内容：**
- 同步时间和持续时间
- 同步类型和结果状态
- 处理的数据量和项目数
- 错误信息和诊断数据

### ClearSyncCache

```csharp
void ClearSyncCache();
```

**描述：** 清除本地同步缓存数据

**功能：**
- 清空内存中的同步缓存
- 删除本地存储的临时数据
- 重置同步状态和历史
- 释放相关系统资源

**使用场景：**
- 用户注销时清理数据
- 同步错误需要重置状态
- 切换用户账户
- 系统维护和优化

**注意事项：**
- 操作不可逆，应谨慎使用
- 可能影响正在进行的同步操作
- 应该在安全的时机调用

## 实现指南

### 错误处理

实现接口时应该考虑以下错误处理策略：

```csharp
public async Task<SyncResult> SyncUserSettingsAsync()
{
    try
    {
        // 检查前置条件
        if (!IsCloudSyncEnabled)
            return SyncResult.Failure("云端同步未启用");
            
        if (!IsUserAuthenticated())
            return SyncResult.Failure("用户未认证");
            
        if (!IsNetworkAvailable())
            return SyncResult.Failure("网络不可用");
        
        // 执行同步逻辑
        var result = await PerformSyncOperation();
        return result;
    }
    catch (NetworkException ex)
    {
        return SyncResult.Failure($"网络错误: {ex.Message}");
    }
    catch (AuthenticationException ex)
    {
        return SyncResult.Failure($"认证错误: {ex.Message}");
    }
    catch (Exception ex)
    {
        return SyncResult.Failure($"未知错误: {ex.Message}");
    }
}
```

### 进度报告

同步操作应该提供详细的进度报告：

```csharp
private void ReportProgress(float percentage, string operation, int processed, int total)
{
    var progressInfo = new SyncProgressInfo
    {
        Status = CurrentSyncStatus,
        ProgressPercentage = percentage,
        CurrentOperation = operation,
        ProcessedItems = processed,
        TotalItems = total,
        EstimatedRemainingSeconds = CalculateRemainingTime(percentage)
    };
    
    OnSyncProgress?.Invoke(progressInfo);
}
```

### 事件触发

确保在适当的时机触发事件：

```csharp
public async Task<SyncResult> PerformSyncOperation()
{
    var result = new SyncResult { StartTime = DateTime.Now };
    
    try
    {
        // 开始同步
        ReportProgress(0, "开始同步...", 0, 100);
        
        // 执行同步步骤
        await SyncStep1();
        ReportProgress(33, "同步用户设置...", 33, 100);
        
        await SyncStep2();
        ReportProgress(66, "同步对话历史...", 66, 100);
        
        await SyncStep3();
        ReportProgress(100, "同步完成", 100, 100);
        
        result.IsSuccess = true;
        result.EndTime = DateTime.Now;
    }
    catch (Exception ex)
    {
        result.IsSuccess = false;
        result.ErrorMessage = ex.Message;
        result.EndTime = DateTime.Now;
        
        OnSyncError?.Invoke(ex.Message);
    }
    finally
    {
        OnSyncCompleted?.Invoke(result);
    }
    
    return result;
}
```

## 最佳实践

### 1. 异步操作

所有可能耗时的操作都应该是异步的：

```csharp
// 正确的异步实现
public async Task<SyncResult> SyncUserSettingsAsync()
{
    return await Task.Run(async () =>
    {
        // 执行同步逻辑
        return await PerformSyncLogic();
    });
}
```

### 2. 资源管理

确保正确管理资源和清理：

```csharp
public void ClearSyncCache()
{
    try
    {
        // 清理内存缓存
        _syncCache?.Clear();
        
        // 清理文件缓存
        ClearFileCache();
        
        // 重置状态
        _currentSyncStatus = SyncStatus.Idle;
        
        // 记录日志
        Logger.Info("同步缓存已清理");
    }
    catch (Exception ex)
    {
        Logger.Error($"清理同步缓存失败: {ex.Message}");
    }
}
```

### 3. 线程安全

确保多线程环境下的安全性：

```csharp
private readonly object _syncLock = new object();
private SyncStatus _currentSyncStatus = SyncStatus.Idle;

public SyncStatus CurrentSyncStatus
{
    get
    {
        lock (_syncLock)
        {
            return _currentSyncStatus;
        }
    }
}

private void SetSyncStatus(SyncStatus status)
{
    lock (_syncLock)
    {
        _currentSyncStatus = status;
    }
}
```

## 相关接口

- `IAuthenticationManager`: 用户认证管理
- `IConfigurationManager`: 配置管理
- `ILogger`: 日志记录
- `IEventSystem`: 事件系统

## 相关文档

- [DataSyncManager 实现文档](DataSyncManager.md)
- [数据同步模型文档](SyncModels.md)
- [同步冲突处理指南](SyncConflictResolution.md)
- [数据同步最佳实践](DataSyncBestPractices.md)