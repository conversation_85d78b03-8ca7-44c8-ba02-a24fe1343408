# DataSyncManager 数据同步管理器文档

## 概述

`DataSyncManager` 是数字人管理系统的数据同步核心组件，负责用户数据在本地和云端之间的同步。它采用单例模式实现，提供用户设置、对话历史、用户配置等数据的双向同步功能，支持冲突检测与解决、同步历史记录等高级特性。

## 命名空间

```csharp
DigitalHuman.Core.DataSync
```

## 类定义

### DataSyncManager

数据同步管理器主类，继承自 `SingletonManager<DataSyncManager>` 并实现 `IDataSyncManager` 接口。

#### 继承关系

```csharp
public class DataSyncManager : SingletonManager<DataSyncManager>, IDataSyncManager
```

#### 配置属性

##### EnableCloudSync

```csharp
[Header("同步配置")]
public bool EnableCloudSync = false;
```

**描述：** 是否启用云端同步功能

**默认值：** `false`

##### AutoSyncIntervalMinutes

```csharp
public int AutoSyncIntervalMinutes = 30;
```

**描述：** 自动同步间隔时间（分钟）

**默认值：** `30` 分钟

##### MaxRetryCount

```csharp
public int MaxRetryCount = 3;
```

**描述：** 同步失败时的最大重试次数

**默认值：** `3` 次

##### SyncTimeoutSeconds

```csharp
public int SyncTimeoutSeconds = 60;
```

**描述：** 同步操作的超时时间（秒）

**默认值：** `60` 秒

#### 属性

##### IsCloudSyncEnabled

```csharp
public bool IsCloudSyncEnabled => EnableCloudSync;
```

**描述：** 获取云端同步是否启用

**返回值：** `bool` - 云端同步启用状态

##### CurrentSyncStatus

```csharp
public SyncStatus CurrentSyncStatus => _currentSyncStatus;
```

**描述：** 获取当前同步状态

**返回值：** `SyncStatus` - 当前同步状态枚举值

#### 事件

##### OnSyncProgress

```csharp
public event Action<SyncProgressInfo> OnSyncProgress;
```

**描述：** 同步进度变化事件

**参数：** `SyncProgressInfo` - 包含进度百分比、当前操作等信息

**使用示例：**
```csharp
var syncManager = DataSyncManager.Instance;
syncManager.OnSyncProgress += (progressInfo) =>
{
    Debug.Log($"同步进度: {progressInfo.ProgressPercentage}% - {progressInfo.CurrentOperation}");
    UpdateProgressBar(progressInfo.ProgressPercentage);
};
```

##### OnSyncCompleted

```csharp
public event Action<SyncResult> OnSyncCompleted;
```

**描述：** 同步完成事件

**参数：** `SyncResult` - 同步结果信息

**使用示例：**
```csharp
syncManager.OnSyncCompleted += (result) =>
{
    if (result.IsSuccess)
    {
        Debug.Log($"同步成功，耗时: {result.Duration.TotalSeconds:F2}秒");
        Debug.Log($"上传项目: {result.UploadedItems}, 下载项目: {result.DownloadedItems}");
    }
    else
    {
        Debug.LogError($"同步失败: {result.ErrorMessage}");
    }
};
```

##### OnSyncError

```csharp
public event Action<string> OnSyncError;
```

**描述：** 同步错误事件

**参数：** `string` - 错误消息

#### 公共方法

##### EnableCloudSync

启用或禁用云端同步功能。

```csharp
public void EnableCloudSync(bool enable)
```

**参数：**
- `enable` (bool): 是否启用云端同步

**功能：**
- 设置云端同步开关状态
- 启用时自动启动定时同步器
- 禁用时停止定时同步器
- 记录状态变化日志

**使用示例：**
```csharp
var syncManager = DataSyncManager.Instance;

// 启用云端同步
syncManager.EnableCloudSync(true);

// 禁用云端同步
syncManager.EnableCloudSync(false);
```

##### SyncUserSettingsAsync

异步同步用户设置数据。

```csharp
public async Task<SyncResult> SyncUserSettingsAsync()
```

**返回值：** `Task<SyncResult>` - 同步结果

**功能：**
- 检查云端同步和用户登录状态
- 上传本地用户设置到云端
- 下载云端用户设置到本地
- 处理同步过程中的异常
- 发布进度和完成事件

**前置条件：**
- 云端同步必须启用 (`EnableCloudSync = true`)
- 用户必须已登录

**使用示例：**
```csharp
var syncManager = DataSyncManager.Instance;

try
{
    var result = await syncManager.SyncUserSettingsAsync();
    if (result.IsSuccess)
    {
        Debug.Log("用户设置同步成功");
        Debug.Log($"同步数据类型: {string.Join(", ", result.SyncedDataTypes)}");
    }
    else
    {
        Debug.LogError($"用户设置同步失败: {result.ErrorMessage}");
    }
}
catch (Exception ex)
{
    Debug.LogError($"同步过程中发生异常: {ex.Message}");
}
```

##### SyncConversationHistoryAsync

异步同步对话历史数据。

```csharp
public async Task<SyncResult> SyncConversationHistoryAsync(bool includePrivate = false)
```

**参数：**
- `includePrivate` (bool, 默认: false): 是否包含私密对话

**返回值：** `Task<SyncResult>` - 同步结果

**功能：**
- 同步用户的对话历史记录
- 可选择是否包含私密对话
- 处理大量对话数据的分批同步
- 保持对话的时间顺序和完整性

**使用示例：**
```csharp
// 同步普通对话历史
var result = await syncManager.SyncConversationHistoryAsync();

// 同步包含私密对话的历史
var fullResult = await syncManager.SyncConversationHistoryAsync(includePrivate: true);

if (fullResult.IsSuccess)
{
    Debug.Log($"对话历史同步完成，处理了 {fullResult.UploadedItems + fullResult.DownloadedItems} 条对话");
}
```

##### SyncUserConfigurationAsync

异步同步用户配置数据。

```csharp
public async Task<SyncResult> SyncUserConfigurationAsync()
```

**返回值：** `Task<SyncResult>` - 同步结果

**功能：**
- 同步用户的个性化配置
- 包括界面设置、偏好选项等
- 确保配置的一致性和完整性

**使用示例：**
```csharp
var result = await syncManager.SyncUserConfigurationAsync();
if (result.IsSuccess)
{
    Debug.Log("用户配置同步成功");
    // 可能需要重新加载配置
    ConfigurationManager.Instance.ReloadConfiguration();
}
```

##### PerformFullSyncAsync

执行完整的数据同步操作。

```csharp
public async Task<SyncResult> PerformFullSyncAsync()
```

**返回值：** `Task<SyncResult>` - 综合同步结果

**功能：**
- 依次同步用户设置、用户配置、对话历史
- 检测和处理同步冲突
- 提供详细的进度报告
- 记录完整的同步历史

**同步顺序：**
1. 用户设置 (20% 进度)
2. 用户配置 (40% 进度)  
3. 对话历史 (60% 进度)
4. 冲突检查 (80% 进度)
5. 完成处理 (100% 进度)

**使用示例：**
```csharp
var syncManager = DataSyncManager.Instance;

// 订阅进度事件以显示进度条
syncManager.OnSyncProgress += (progress) =>
{
    UpdateProgressUI(progress.ProgressPercentage, progress.CurrentOperation);
};

try
{
    var result = await syncManager.PerformFullSyncAsync();
    
    if (result.IsSuccess)
    {
        Debug.Log($"完整同步成功！");
        Debug.Log($"同步耗时: {result.Duration.TotalSeconds:F2}秒");
        Debug.Log($"同步数据类型: {string.Join(", ", result.SyncedDataTypes)}");
        Debug.Log($"处理冲突: {result.ConflictCount}个");
    }
    else
    {
        Debug.LogError($"完整同步失败: {result.ErrorMessage}");
    }
}
catch (Exception ex)
{
    Debug.LogError($"同步过程异常: {ex.Message}");
}
```

##### UploadDataAsync

上传指定数据到云端。

```csharp
public async Task<bool> UploadDataAsync(string dataType, object data)
```

**参数：**
- `dataType` (string): 数据类型标识
- `data` (object): 要上传的数据对象

**返回值：** `Task<bool>` - 上传是否成功

**功能：**
- 将本地数据上传到云端存储
- 支持任意类型的数据对象
- 自动处理数据序列化
- 提供上传状态反馈

**使用示例：**
```csharp
// 上传用户偏好设置
var userPreferences = new UserPreferences
{
    Theme = "Dark",
    Language = "zh-CN",
    NotificationsEnabled = true
};

bool success = await syncManager.UploadDataAsync("UserPreferences", userPreferences);
if (success)
{
    Debug.Log("用户偏好设置上传成功");
}

// 上传对话记录
var conversation = new ConversationRecord
{
    Id = Guid.NewGuid().ToString(),
    Messages = conversationMessages,
    CreatedTime = DateTime.Now
};

await syncManager.UploadDataAsync("Conversation", conversation);
```

##### DownloadDataAsync

从云端下载指定类型的数据。

```csharp
public async Task<T> DownloadDataAsync<T>(string dataType)
```

**类型参数：**
- `T`: 期望的数据类型

**参数：**
- `dataType` (string): 数据类型标识

**返回值：** `Task<T>` - 下载的数据对象，失败时返回默认值

**功能：**
- 从云端下载指定类型的数据
- 自动处理数据反序列化
- 类型安全的数据返回
- 异常处理和错误恢复

**使用示例：**
```csharp
// 下载用户偏好设置
var preferences = await syncManager.DownloadDataAsync<UserPreferences>("UserPreferences");
if (preferences != null)
{
    ApplyUserPreferences(preferences);
}

// 下载对话历史
var conversations = await syncManager.DownloadDataAsync<List<ConversationRecord>>("ConversationHistory");
if (conversations != null && conversations.Count > 0)
{
    LoadConversationHistory(conversations);
}

// 下载配置数据
var config = await syncManager.DownloadDataAsync<Dictionary<string, object>>("UserConfiguration");
```

##### CheckSyncConflictsAsync

检查本地和云端数据之间的同步冲突。

```csharp
public async Task<List<SyncConflict>> CheckSyncConflictsAsync()
```

**返回值：** `Task<List<SyncConflict>>` - 检测到的冲突列表

**功能：**
- 比较本地和云端数据的修改时间
- 识别数据版本冲突
- 生成详细的冲突报告
- 为冲突解决提供必要信息

**冲突检测规则：**
- 同一数据项在本地和云端都有修改
- 修改时间存在差异
- 数据内容不一致

**使用示例：**
```csharp
var conflicts = await syncManager.CheckSyncConflictsAsync();

if (conflicts.Count > 0)
{
    Debug.Log($"检测到 {conflicts.Count} 个同步冲突");
    
    foreach (var conflict in conflicts)
    {
        Debug.Log($"冲突: {conflict.DataType}.{conflict.DataKey}");
        Debug.Log($"本地修改时间: {conflict.LocalModifiedTime}");
        Debug.Log($"远程修改时间: {conflict.RemoteModifiedTime}");
        Debug.Log($"描述: {conflict.Description}");
        
        // 显示冲突解决界面
        ShowConflictResolutionDialog(conflict);
    }
}
else
{
    Debug.Log("未检测到同步冲突");
}
```

##### ResolveSyncConflictAsync

解决指定的同步冲突。

```csharp
public async Task<bool> ResolveSyncConflictAsync(SyncConflict conflict, ConflictResolution resolution)
```

**参数：**
- `conflict` (SyncConflict): 要解决的冲突信息
- `resolution` (ConflictResolution): 解决方案

**返回值：** `Task<bool>` - 冲突解决是否成功

**解决方案类型：**
- `UseLocal`: 使用本地版本覆盖云端
- `UseRemote`: 使用云端版本覆盖本地
- `Merge`: 尝试合并两个版本
- `Skip`: 跳过此冲突，保持现状

**使用示例：**
```csharp
// 自动解决冲突：优先使用最新修改的版本
foreach (var conflict in conflicts)
{
    ConflictResolution resolution;
    
    if (conflict.LocalModifiedTime > conflict.RemoteModifiedTime)
    {
        resolution = ConflictResolution.UseLocal;
        Debug.Log($"使用本地版本解决冲突: {conflict.DataKey}");
    }
    else
    {
        resolution = ConflictResolution.UseRemote;
        Debug.Log($"使用远程版本解决冲突: {conflict.DataKey}");
    }
    
    bool resolved = await syncManager.ResolveSyncConflictAsync(conflict, resolution);
    if (resolved)
    {
        Debug.Log($"冲突 {conflict.ConflictId} 解决成功");
    }
    else
    {
        Debug.LogError($"冲突 {conflict.ConflictId} 解决失败");
    }
}

// 手动解决冲突
var userChoice = ShowConflictDialog(conflict);
await syncManager.ResolveSyncConflictAsync(conflict, userChoice);
```

##### GetSyncHistoryAsync

获取同步历史记录。

```csharp
public async Task<List<SyncHistoryRecord>> GetSyncHistoryAsync(int limit = 50)
```

**参数：**
- `limit` (int, 默认: 50): 返回记录的最大数量

**返回值：** `Task<List<SyncHistoryRecord>>` - 同步历史记录列表

**功能：**
- 获取最近的同步操作历史
- 包含同步时间、类型、结果等信息
- 支持限制返回记录数量
- 按时间倒序排列

**使用示例：**
```csharp
// 获取最近10次同步记录
var recentHistory = await syncManager.GetSyncHistoryAsync(10);

Debug.Log($"最近 {recentHistory.Count} 次同步记录:");
foreach (var record in recentHistory)
{
    string status = record.IsSuccess ? "成功" : "失败";
    Debug.Log($"{record.SyncTime:yyyy-MM-dd HH:mm:ss} - {record.SyncType} - {status}");
    
    if (!record.IsSuccess)
    {
        Debug.Log($"  错误: {record.ErrorMessage}");
    }
    else
    {
        Debug.Log($"  耗时: {record.DurationMs}ms, 项目数: {record.ItemCount}");
    }
}

// 显示同步历史界面
DisplaySyncHistory(recentHistory);
```

##### ClearSyncCache

清除本地同步缓存数据。

```csharp
public void ClearSyncCache()
```

**功能：**
- 清空内存中的同步缓存
- 删除本地存储的同步历史
- 重置同步状态
- 释放相关资源

**使用场景：**
- 用户注销时清理数据
- 同步出现严重错误需要重置
- 切换用户账户
- 系统维护和清理

**使用示例：**
```csharp
// 用户注销时清理缓存
void OnUserLogout()
{
    var syncManager = DataSyncManager.Instance;
    syncManager.ClearSyncCache();
    Debug.Log("同步缓存已清理");
}

// 重置同步状态
void ResetSyncState()
{
    syncManager.ClearSyncCache();
    syncManager.EnableCloudSync(false);
    Debug.Log("同步状态已重置");
}
```

## 数据模型

### SyncStatus 枚举

同步状态枚举，表示当前同步操作的状态。

```csharp
public enum SyncStatus
{
    Idle,                // 空闲状态
    Syncing,            // 同步中
    Uploading,          // 上传中
    Downloading,        // 下载中
    CheckingConflicts,  // 检查冲突
    Completed,          // 同步完成
    Failed              // 同步失败
}
```

### SyncProgressInfo 类

同步进度信息，用于报告同步操作的实时进度。

```csharp
public class SyncProgressInfo
{
    public SyncStatus Status { get; set; }                    // 当前状态
    public float ProgressPercentage { get; set; }             // 进度百分比 (0-100)
    public string CurrentOperation { get; set; }              // 当前操作描述
    public int ProcessedItems { get; set; }                   // 已处理项目数
    public int TotalItems { get; set; }                       // 总项目数
    public int EstimatedRemainingSeconds { get; set; }        // 估计剩余时间（秒）
}
```

### SyncResult 类

同步结果信息，包含同步操作的完整结果。

```csharp
public class SyncResult
{
    public bool IsSuccess { get; set; }                       // 同步是否成功
    public string ErrorMessage { get; set; }                  // 错误消息
    public DateTime StartTime { get; set; }                   // 同步开始时间
    public DateTime EndTime { get; set; }                     // 同步结束时间
    public TimeSpan Duration => EndTime - StartTime;          // 同步持续时间
    public int UploadedItems { get; set; }                    // 上传的数据项数量
    public int DownloadedItems { get; set; }                  // 下载的数据项数量
    public int ConflictCount { get; set; }                    // 冲突数量
    public List<string> SyncedDataTypes { get; set; }         // 同步的数据类型列表
    public Dictionary<string, object> Details { get; set; }   // 详细信息
}
```

### SyncConflict 类

同步冲突信息，描述本地和云端数据之间的冲突。

```csharp
public class SyncConflict
{
    public string ConflictId { get; set; }                    // 冲突ID
    public string DataType { get; set; }                      // 数据类型
    public string DataKey { get; set; }                       // 数据键
    public object LocalVersion { get; set; }                  // 本地版本
    public object RemoteVersion { get; set; }                 // 远程版本
    public DateTime LocalModifiedTime { get; set; }           // 本地修改时间
    public DateTime RemoteModifiedTime { get; set; }          // 远程修改时间
    public string Description { get; set; }                   // 冲突描述
}
```

## 使用场景示例

### 1. 应用启动时的自动同步

```csharp
public class ApplicationManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    async void Start()
    {
        _syncManager = DataSyncManager.Instance;
        
        // 检查用户登录状态
        var authManager = AuthenticationManager.Instance;
        if (authManager.IsLoggedIn)
        {
            // 启用云端同步
            _syncManager.EnableCloudSync(true);
            
            // 执行启动同步
            try
            {
                var result = await _syncManager.SyncUserSettingsAsync();
                if (result.IsSuccess)
                {
                    Debug.Log("启动同步完成，应用用户设置");
                    ApplyUserSettings();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"启动同步失败: {ex.Message}");
            }
        }
    }
}
```

### 2. 用户设置变更时的增量同步

```csharp
public class SettingsManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    public async void OnSettingChanged(string settingKey, object newValue)
    {
        // 立即保存到本地
        SaveSettingLocally(settingKey, newValue);
        
        // 如果启用了云端同步，上传更改
        if (_syncManager.IsCloudSyncEnabled)
        {
            var settingData = new Dictionary<string, object>
            {
                [settingKey] = newValue,
                ["lastModified"] = DateTime.Now
            };
            
            bool uploaded = await _syncManager.UploadDataAsync($"Setting_{settingKey}", settingData);
            if (uploaded)
            {
                Debug.Log($"设置 {settingKey} 已同步到云端");
            }
        }
    }
}
```

### 3. 冲突解决的用户界面集成

```csharp
public class ConflictResolutionUI : MonoBehaviour
{
    [SerializeField] private GameObject conflictDialogPrefab;
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
        _syncManager.OnSyncCompleted += OnSyncCompleted;
    }
    
    private async void OnSyncCompleted(SyncResult result)
    {
        if (result.ConflictCount > 0)
        {
            // 检查具体冲突
            var conflicts = await _syncManager.CheckSyncConflictsAsync();
            
            foreach (var conflict in conflicts)
            {
                // 显示冲突解决对话框
                ShowConflictDialog(conflict);
            }
        }
    }
    
    private async void ShowConflictDialog(SyncConflict conflict)
    {
        var dialog = Instantiate(conflictDialogPrefab);
        var conflictUI = dialog.GetComponent<ConflictDialogUI>();
        
        conflictUI.SetConflictInfo(conflict);
        conflictUI.OnResolutionSelected += async (resolution) =>
        {
            bool resolved = await _syncManager.ResolveSyncConflictAsync(conflict, resolution);
            if (resolved)
            {
                Debug.Log($"冲突已解决: {conflict.DataKey}");
            }
            
            Destroy(dialog);
        };
    }
}
```

### 4. 同步历史和统计显示

```csharp
public class SyncHistoryUI : MonoBehaviour
{
    [SerializeField] private Transform historyContainer;
    [SerializeField] private GameObject historyItemPrefab;
    
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
        LoadSyncHistory();
    }
    
    private async void LoadSyncHistory()
    {
        var history = await _syncManager.GetSyncHistoryAsync(20);
        
        foreach (var record in history)
        {
            var item = Instantiate(historyItemPrefab, historyContainer);
            var itemUI = item.GetComponent<SyncHistoryItemUI>();
            
            itemUI.SetHistoryRecord(record);
        }
        
        // 显示统计信息
        DisplaySyncStatistics(history);
    }
    
    private void DisplaySyncStatistics(List<SyncHistoryRecord> history)
    {
        int successCount = history.Count(r => r.IsSuccess);
        int failureCount = history.Count - successCount;
        long totalDataSize = history.Sum(r => r.DataSize);
        
        Debug.Log($"同步统计 - 成功: {successCount}, 失败: {failureCount}, 总数据量: {FormatBytes(totalDataSize)}");
    }
}
```

## 最佳实践

### 1. 错误处理和重试机制

```csharp
public async Task<SyncResult> SafeSyncWithRetry(Func<Task<SyncResult>> syncOperation, int maxRetries = 3)
{
    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            var result = await syncOperation();
            if (result.IsSuccess)
            {
                return result;
            }
            
            Debug.LogWarning($"同步尝试 {attempt} 失败: {result.ErrorMessage}");
            
            if (attempt < maxRetries)
            {
                // 指数退避延迟
                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt)));
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"同步尝试 {attempt} 异常: {ex.Message}");
            
            if (attempt == maxRetries)
            {
                return SyncResult.Failure($"重试 {maxRetries} 次后仍然失败: {ex.Message}");
            }
        }
    }
    
    return SyncResult.Failure("所有重试尝试都失败了");
}
```

### 2. 网络状态检查

```csharp
private bool IsNetworkAvailable()
{
    return Application.internetReachability != NetworkReachability.NotReachable;
}

public async Task<SyncResult> SyncWithNetworkCheck()
{
    if (!IsNetworkAvailable())
    {
        return SyncResult.Failure("网络不可用");
    }
    
    return await _syncManager.PerformFullSyncAsync();
}
```

### 3. 数据备份和恢复

```csharp
public async void BackupDataBeforeSync()
{
    try
    {
        var userData = CollectUserData();
        var backupData = JsonUtility.ToJson(userData);
        
        // 保存备份到本地
        File.WriteAllText(GetBackupPath(), backupData);
        
        Debug.Log("数据备份完成");
    }
    catch (Exception ex)
    {
        Debug.LogError($"数据备份失败: {ex.Message}");
    }
}

public async void RestoreDataFromBackup()
{
    try
    {
        string backupPath = GetBackupPath();
        if (File.Exists(backupPath))
        {
            string backupData = File.ReadAllText(backupPath);
            var userData = JsonUtility.FromJson<UserData>(backupData);
            
            ApplyUserData(userData);
            Debug.Log("数据恢复完成");
        }
    }
    catch (Exception ex)
    {
        Debug.LogError($"数据恢复失败: {ex.Message}");
    }
}
```

## 注意事项

1. **网络依赖**: 云端同步功能需要稳定的网络连接
2. **用户认证**: 大部分同步操作需要用户已登录
3. **数据安全**: 敏感数据在传输和存储时应加密
4. **性能影响**: 大量数据同步可能影响应用性能，建议在后台执行
5. **存储限制**: 注意云端存储的容量限制
6. **版本兼容**: 确保不同版本的应用数据格式兼容

## 依赖项

- `DigitalHuman.Core.Base`: 单例管理器基类
- `DigitalHuman.Core.Authentication`: 用户认证管理
- `DigitalHuman.Core.Configuration`: 配置管理
- `DigitalHuman.Core.Logging`: 日志记录
- `UnityEngine`: Unity 引擎核心功能

## 相关文档

- [IDataSyncManager 接口文档](IDataSyncManager.md)
- [数据同步模型文档](SyncModels.md)
- [认证管理器文档](../authentication/AuthenticationManager.md)
- [配置管理器文档](../configuration/ConfigurationManager.md)
- [单例管理器文档](../base/SingletonManager.md)