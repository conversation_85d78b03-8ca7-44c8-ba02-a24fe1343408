# LogRotationManager 日志轮转管理器文档

## 概述

`LogRotationManager` 是日志系统的核心组件，负责日志文件的轮转、清理和归档功能。它提供了基于文件大小和数量的自动轮转策略，支持文件压缩和归档，确保日志文件不会无限增长。

## 命名空间

```csharp
DigitalHuman.Core.Logging.Rotation
```

## 类定义

### LogRotationManager

日志轮转管理器主类，提供完整的日志文件生命周期管理功能。

#### 构造函数

```csharp
public LogRotationManager(LogRotationConfiguration configuration, IEventSystem eventSystem = null)
```

**参数：**
- `configuration` (LogRotationConfiguration): 轮转配置对象，不能为空
- `eventSystem` (IEventSystem, 可选): 事件系统实例，用于发布轮转事件

**异常：**
- `ArgumentNullException`: 当 configuration 为 null 时抛出

#### 公共方法

##### CheckAndRotate

检查并执行日志轮转的主要方法。

```csharp
public bool CheckAndRotate(string logFilePath)
```

**参数：**
- `logFilePath` (string): 要检查的日志文件路径

**返回值：**
- `bool`: 如果执行了轮转返回 true，否则返回 false

**功能：**
- 检查文件大小是否超过配置限制
- 检查文件数量是否超过配置限制
- 根据配置策略决定是否执行轮转

**使用示例：**
```csharp
var config = new LogRotationConfiguration
{
    MaxFileSize = "10MB",
    MaxFileCount = 30,
    Strategy = "SizeAndCount"
};

var rotationManager = new LogRotationManager(config, eventSystem);
bool rotated = rotationManager.CheckAndRotate("Logs/app.log");

if (rotated)
{
    Console.WriteLine("日志文件已轮转");
}
```

##### RotateLogFile

手动执行日志文件轮转。

```csharp
public bool RotateLogFile(string logFilePath, string reason = "手动轮转")
```

**参数：**
- `logFilePath` (string): 要轮转的日志文件路径
- `reason` (string, 默认: "手动轮转"): 轮转原因描述

**返回值：**
- `bool`: 轮转成功返回 true，失败返回 false

**功能：**
- 生成带时间戳的新文件名
- 移动当前日志文件到新位置
- 发布轮转事件
- 执行后续处理（压缩、归档）
- 清理旧文件

**使用示例：**
```csharp
bool success = rotationManager.RotateLogFile("Logs/app.log", "文件大小超限");
```

##### CleanupOldLogFiles

清理超出数量限制的旧日志文件。

```csharp
public void CleanupOldLogFiles(string directory, string baseFileName, string extension)
```

**参数：**
- `directory` (string): 日志文件目录
- `baseFileName` (string): 基础文件名（不含扩展名）
- `extension` (string): 文件扩展名

**功能：**
- 获取所有相关日志文件
- 按创建时间排序
- 删除超出数量限制的旧文件
- 清理压缩文件

##### CleanupExpiredLogFiles

根据时间清理过期的日志文件。

```csharp
public void CleanupExpiredLogFiles(string directory, TimeSpan maxAge)
```

**参数：**
- `directory` (string): 日志文件目录
- `maxAge` (TimeSpan): 最大保留时间

**使用示例：**
```csharp
// 清理7天前的日志文件
rotationManager.CleanupExpiredLogFiles("Logs", TimeSpan.FromDays(7));
```

##### CompressLogFileAsync

异步压缩日志文件。

```csharp
public async Task<string> CompressLogFileAsync(string filePath)
```

**参数：**
- `filePath` (string): 要压缩的文件路径

**返回值：**
- `Task<string>`: 压缩后的文件路径，失败时返回 null

**功能：**
- 使用 GZip 压缩算法
- 删除原始文件
- 返回压缩文件路径

**使用示例：**
```csharp
string compressedPath = await rotationManager.CompressLogFileAsync("Logs/app_20250124.log");
if (compressedPath != null)
{
    Console.WriteLine($"文件已压缩: {compressedPath}");
}
```

##### DecompressLogFileAsync

异步解压缩日志文件。

```csharp
public async Task<string> DecompressLogFileAsync(string compressedFilePath)
```

**参数：**
- `compressedFilePath` (string): 压缩文件路径

**返回值：**
- `Task<string>`: 解压后的文件路径，失败时返回 null

##### GetLogFileStatistics

获取日志文件统计信息。

```csharp
public LogFileStatistics GetLogFileStatistics(string directory, string baseFileName)
```

**参数：**
- `directory` (string): 日志文件目录
- `baseFileName` (string): 基础文件名

**返回值：**
- `LogFileStatistics`: 包含文件统计信息的对象

**使用示例：**
```csharp
var stats = rotationManager.GetLogFileStatistics("Logs", "app");
Console.WriteLine($"总文件数: {stats.TotalFiles}");
Console.WriteLine($"总大小: {stats.GetReadableTotalSize()}");
Console.WriteLine($"压缩文件数: {stats.CompressedFiles}");
```

### LogFileStatistics

日志文件统计信息类。

#### 属性

- `TotalFiles` (int): 总文件数
- `TotalSizeBytes` (long): 总文件大小（字节）
- `ActiveFiles` (int): 活跃文件数
- `CompressedFiles` (int): 压缩文件数
- `CompressedSizeBytes` (long): 压缩文件大小（字节）
- `OldestFileDate` (DateTime): 最旧文件日期
- `NewestFileDate` (DateTime): 最新文件日期

#### 方法

##### GetReadableTotalSize

```csharp
public string GetReadableTotalSize()
```

**返回值：**
- `string`: 可读的总大小格式（如 "10.5 MB"）

##### GetReadableCompressedSize

```csharp
public string GetReadableCompressedSize()
```

**返回值：**
- `string`: 可读的压缩大小格式

## 配置示例

```csharp
var rotationConfig = new LogRotationConfiguration
{
    Strategy = "SizeAndCount",
    MaxFileSize = "10MB",
    MaxFileCount = 30,
    ArchiveOldFiles = true,
    CompressionEnabled = true
};

var eventSystem = EventSystem.Instance;
var rotationManager = new LogRotationManager(rotationConfig, eventSystem);
```

## 事件集成

轮转管理器会发布以下事件：

- `LogRotationEvent`: 当文件轮转完成时发布

```csharp
// 订阅轮转事件
eventSystem.Subscribe<LogRotationEvent>(evt =>
{
    Console.WriteLine($"文件轮转: {evt.OldFilePath} -> {evt.NewFilePath}");
    Console.WriteLine($"原因: {evt.Reason}");
    Console.WriteLine($"文件大小: {evt.FileSize} 字节");
});
```

## 最佳实践

1. **定期检查**: 在日志写入后定期调用 `CheckAndRotate` 方法
2. **配置合理**: 根据磁盘空间和性能要求配置合理的文件大小和数量限制
3. **启用压缩**: 对于长期保存的日志启用压缩以节省空间
4. **监控事件**: 订阅轮转事件以监控日志文件状态
5. **错误处理**: 所有方法都包含异常处理，但建议在调用时也进行适当的错误处理

## 注意事项

- 文件轮转是原子操作，使用 `File.Move` 确保数据完整性
- 压缩和归档操作在后台异步执行，不会阻塞主线程
- 清理操作会按文件创建时间排序，确保保留最新的文件
- 所有文件操作都包含异常处理，失败时会记录错误日志但不会抛出异常

## 依赖项

- `System.IO`: 文件操作
- `System.IO.Compression`: 文件压缩
- `System.Threading.Tasks`: 异步操作
- `UnityEngine`: Unity 日志输出
- `DigitalHuman.Core`: 事件系统集成