# LogManager 日志管理器文档

## 概述

`LogManager` 是日志系统的核心管理器，采用单例模式实现，提供完整的日志管理功能。它负责统一管理日志记录、写入器、配置、性能监控和日志导出等功能，是整个日志系统的中央控制器。

## 命名空间

```csharp
DigitalHuman.Core.Logging
```

## 类定义

### LogManager

日志管理器主类，继承自 `SingletonManager<LogManager>` 并实现 `ILogManager` 接口。

#### 继承关系

```csharp
public class LogManager : SingletonManager<LogManager>, ILogManager
```

#### 核心组件

- **配置管理**: `LogConfiguration` - 日志系统配置
- **写入器管理**: `List<ILogWriter>` - 日志写入器集合
- **日志记录器缓存**: `ConcurrentDictionary<string, ILogger>` - 模块化日志记录器缓存
- **统计信息**: `LogStatistics` - 日志统计数据
- **性能优化**: `LogEntryPool` 和 `LogPerformanceMonitor` - 性能优化组件
- **日志导出**: `ILogExporter` - 日志导出功能
- **事件系统**: `IEventSystem` - 事件发布和订阅

#### 属性

##### Instance (静态属性)

```csharp
public static LogManager Instance { get; }
```

**描述：** 获取日志管理器的单例实例

**返回值：** `LogManager` - 管理器实例

##### IsLoggingEnabled

```csharp
public bool IsLoggingEnabled { get; set; }
```

**描述：** 获取或设置日志功能是否启用

**功能：**
- 控制整个日志系统的开关
- 设置时会更新配置并发布状态变化事件
- 禁用时所有日志记录操作都会被跳过

**使用示例：**
```csharp
var logManager = LogManager.Instance;

// 检查日志是否启用
if (logManager.IsLoggingEnabled)
{
    Console.WriteLine("日志系统已启用");
}

// 禁用日志功能
logManager.IsLoggingEnabled = false;

// 重新启用日志功能
logManager.IsLoggingEnabled = true;
```

#### 公共方法

##### Log

核心日志记录方法，所有其他日志方法最终都会调用此方法。

```csharp
public void Log(LogLevel level, string message, string module = null, Exception exception = null)
```

**参数：**
- `level` (LogLevel): 日志级别
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称
- `exception` (Exception, 可选): 异常信息

**功能：**
- 检查日志是否启用和级别过滤
- 性能监控和降级策略检查
- 使用对象池优化内存分配
- 统计信息更新
- 多写入器并行写入
- 事件发布
- 性能指标记录

**使用示例：**
```csharp
var logManager = LogManager.Instance;

// 基本日志记录
logManager.Log(LogLevel.Info, "系统启动完成");

// 带模块的日志记录
logManager.Log(LogLevel.Error, "数据库连接失败", "Database");

// 带异常的日志记录
try
{
    // 一些可能抛出异常的代码
}
catch (Exception ex)
{
    logManager.Log(LogLevel.Error, "操作失败", "MyModule", ex);
}
```

##### LogTrace

记录跟踪级别日志的便捷方法。

```csharp
public void LogTrace(string message, string module = null)
```

**参数：**
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称

**使用示例：**
```csharp
logManager.LogTrace("进入方法 ProcessData", "DataProcessor");
logManager.LogTrace("循环处理第 5 个项目", "DataProcessor");
```

##### LogDebug

记录调试级别日志的便捷方法。

```csharp
public void LogDebug(string message, string module = null)
```

**参数：**
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称

**使用示例：**
```csharp
logManager.LogDebug("用户输入验证通过", "Authentication");
logManager.LogDebug($"缓存命中率: {hitRate:P2}", "Cache");
```

##### LogInfo

记录信息级别日志的便捷方法。

```csharp
public void LogInfo(string message, string module = null)
```

**参数：**
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称

**使用示例：**
```csharp
logManager.LogInfo("服务器启动完成", "Server");
logManager.LogInfo("用户登录成功", "Authentication");
```

##### LogWarn

记录警告级别日志的便捷方法。

```csharp
public void LogWarn(string message, string module = null)
```

**参数：**
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称

**使用示例：**
```csharp
logManager.LogWarn("内存使用率超过 80%", "System");
logManager.LogWarn("API 响应时间较慢", "Network");
```

##### LogError

记录错误级别日志的便捷方法。

```csharp
public void LogError(string message, string module = null, Exception exception = null)
```

**参数：**
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称
- `exception` (Exception, 可选): 异常信息

**使用示例：**
```csharp
logManager.LogError("数据库连接失败", "Database");

try
{
    // 数据库操作
}
catch (SqlException ex)
{
    logManager.LogError("SQL 查询执行失败", "Database", ex);
}
```

##### LogFatal

记录致命错误级别日志的便捷方法。

```csharp
public void LogFatal(string message, string module = null, Exception exception = null)
```

**参数：**
- `message` (string): 日志消息
- `module` (string, 可选): 模块名称
- `exception` (Exception, 可选): 异常信息

**使用示例：**
```csharp
try
{
    // 关键系统初始化
}
catch (Exception ex)
{
    logManager.LogFatal("系统初始化失败，无法继续运行", "System", ex);
    Application.Quit();
}
```

##### GetLogger

获取模块化日志记录器实例。

```csharp
public ILogger GetLogger(string moduleName)
```

**参数：**
- `moduleName` (string): 模块名称，为空时使用 "Default"

**返回值：** `ILogger` - 日志记录器实例

**功能：**
- 使用线程安全的缓存机制
- 自动应用模块级别的日志配置
- 支持层次化模块名称

**使用示例：**
```csharp
// 为不同模块创建专用日志记录器
var audioLogger = logManager.GetLogger("Audio");
var networkLogger = logManager.GetLogger("Network");
var databaseLogger = logManager.GetLogger("Database");

// 使用模块化日志记录器
audioLogger.Info("音频设备初始化完成");
networkLogger.Warn("网络连接不稳定");
databaseLogger.Error("查询超时", exception);

// 支持层次化模块名称
var audioDeviceLogger = logManager.GetLogger("Audio.Device");
var audioCodecLogger = logManager.GetLogger("Audio.Codec");
```

##### SetLogLevel

设置日志级别，支持全局和模块级别设置。

```csharp
public void SetLogLevel(LogLevel level, string module = null)
```

**参数：**
- `level` (LogLevel): 要设置的日志级别
- `module` (string, 可选): 模块名称，为空则设置全局级别

**功能：**
- 全局级别设置会影响所有没有特定设置的模块
- 模块级别设置只影响指定模块
- 自动更新现有日志记录器的级别
- 配置会被持久化保存

**使用示例：**
```csharp
// 设置全局日志级别
logManager.SetLogLevel(LogLevel.Debug);

// 设置特定模块的日志级别
logManager.SetLogLevel(LogLevel.Trace, "Audio");
logManager.SetLogLevel(LogLevel.Error, "Network");
logManager.SetLogLevel(LogLevel.Warn, "Database");

// 在开发环境启用详细日志
#if DEVELOPMENT
logManager.SetLogLevel(LogLevel.Trace);
#else
logManager.SetLogLevel(LogLevel.Info);
#endif
```

##### AddLogWriter

添加日志写入器到管理器。

```csharp
public void AddLogWriter(ILogWriter writer)
```

**参数：**
- `writer` (ILogWriter): 要添加的日志写入器

**功能：**
- 线程安全的写入器管理
- 防止重复添加同一写入器
- 自动记录写入器添加日志

**使用示例：**
```csharp
// 添加文件写入器
var fileWriter = new FileLogWriter(
    name: "MainLog",
    filePath: "Logs/app.log",
    formatter: new JsonLogFormatter(),
    bufferSize: 1000
);
logManager.AddLogWriter(fileWriter);

// 添加控制台写入器
var consoleWriter = new ConsoleLogWriter(
    name: "Console",
    formatter: new PlainTextLogFormatter()
);
logManager.AddLogWriter(consoleWriter);

// 添加自定义写入器
var databaseWriter = new DatabaseLogWriter(
    name: "Database",
    connectionString: "...",
    minLevel: LogLevel.Error
);
logManager.AddLogWriter(databaseWriter);
```

##### RemoveLogWriter

从管理器中移除日志写入器。

```csharp
public void RemoveLogWriter(ILogWriter writer)
```

**参数：**
- `writer` (ILogWriter): 要移除的日志写入器

**功能：**
- 线程安全的写入器移除
- 自动释放写入器资源
- 记录写入器移除日志

**使用示例：**
```csharp
// 移除特定写入器
logManager.RemoveLogWriter(fileWriter);

// 在系统关闭时移除所有写入器
foreach (var writer in writers)
{
    logManager.RemoveLogWriter(writer);
}
```

##### FlushAll

刷新所有日志写入器的缓冲区。

```csharp
public void FlushAll()
```

**功能：**
- 强制所有写入器立即写入缓冲的日志
- 线程安全操作
- 异常处理确保单个写入器失败不影响其他写入器

**使用示例：**
```csharp
// 在关键操作前确保日志已写入
logManager.FlushAll();

// 在应用程序退出前刷新日志
void OnApplicationQuit()
{
    logManager.FlushAll();
}

// 定期刷新日志
InvokeRepeating(nameof(FlushLogs), 0f, 30f);
void FlushLogs()
{
    logManager.FlushAll();
}
```

##### ExportLogsAsync

异步导出日志到文件。

```csharp
public async Task<string> ExportLogsAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null)
```

**参数：**
- `startTime` (DateTime?, 可选): 导出开始时间
- `endTime` (DateTime?, 可选): 导出结束时间
- `minLevel` (LogLevel?, 可选): 最小日志级别

**返回值：** `Task<string>` - 导出文件路径，失败时返回空字符串

**功能：**
- 按时间范围和级别过滤日志
- 支持多种导出格式
- 包含系统信息
- 异步操作不阻塞主线程

**使用示例：**
```csharp
// 导出最近一周的所有日志
string exportPath = await logManager.ExportLogsAsync(
    startTime: DateTime.Now.AddDays(-7),
    endTime: DateTime.Now
);

// 导出错误级别以上的日志
string errorLogPath = await logManager.ExportLogsAsync(
    minLevel: LogLevel.Error
);

// 导出特定时间段的警告日志
string warningLogPath = await logManager.ExportLogsAsync(
    startTime: new DateTime(2025, 1, 1),
    endTime: new DateTime(2025, 1, 31),
    minLevel: LogLevel.Warn
);

if (!string.IsNullOrEmpty(exportPath))
{
    Console.WriteLine($"日志导出成功: {exportPath}");
}
```

##### ExportLogsToZipAsync

异步导出日志到 ZIP 压缩文件。

```csharp
public async Task<string> ExportLogsToZipAsync(DateTime? startTime = null, DateTime? endTime = null, LogLevel? minLevel = null)
```

**参数：**
- `startTime` (DateTime?, 可选): 导出开始时间
- `endTime` (DateTime?, 可选): 导出结束时间
- `minLevel` (LogLevel?, 可选): 最小日志级别

**返回值：** `Task<string>` - 导出 ZIP 文件路径，失败时返回空字符串

**功能：**
- 创建包含多个日志文件的 ZIP 压缩包
- 包含系统信息和导出元数据
- 生成统计信息文件
- 节省存储空间

**使用示例：**
```csharp
// 导出压缩的日志包
string zipPath = await logManager.ExportLogsToZipAsync(
    startTime: DateTime.Now.AddMonths(-1),
    endTime: DateTime.Now,
    minLevel: LogLevel.Info
);

// 用于技术支持的完整日志包
string supportPackage = await logManager.ExportLogsToZipAsync();

if (!string.IsNullOrEmpty(zipPath))
{
    // 可以发送给技术支持或存档
    Console.WriteLine($"日志包导出成功: {zipPath}");
}
```

##### ClearLogs

清理日志文件。

```csharp
public void ClearLogs(DateTime? beforeTime = null)
```

**参数：**
- `beforeTime` (DateTime?, 可选): 清理此时间之前的日志，为空则清理所有日志

**注意：** 此方法目前为占位实现，完整功能将在日志轮转管理系统中实现。

##### GetLogStatistics

获取日志统计信息。

```csharp
public LogStatistics GetLogStatistics()
```

**返回值：** `LogStatistics` - 包含详细统计信息的对象

**统计信息包括：**
- 总日志数量
- 按级别分类的日志数量
- 按模块分类的日志数量
- 文件大小统计
- 时间范围信息

**使用示例：**
```csharp
var stats = logManager.GetLogStatistics();

Console.WriteLine($"总日志数: {stats.TotalLogCount:N0}");
Console.WriteLine($"错误日志数: {stats.LogCountByLevel[LogLevel.Error]:N0}");
Console.WriteLine($"统计开始时间: {stats.StartTime}");
Console.WriteLine($"最后日志时间: {stats.LastLogTime}");

// 显示按模块的统计
foreach (var kvp in stats.LogCountByModule)
{
    Console.WriteLine($"模块 {kvp.Key}: {kvp.Value:N0} 条日志");
}
```

#### 私有方法

##### ShouldLog

检查是否应该记录指定级别的日志。

```csharp
private bool ShouldLog(LogLevel level, string module)
```

**功能：**
- 检查日志系统是否启用
- 应用全局和模块级别的日志级别过滤
- 支持层次化模块配置

##### WriteToWriters

将日志条目写入到所有启用的写入器。

```csharp
private void WriteToWriters(LogEntry entry)
```

**功能：**
- 并行写入到多个写入器
- 每个写入器独立的级别过滤
- 异常隔离，单个写入器失败不影响其他写入器
- 错误事件发布

##### LoadConfiguration

从配置管理器加载日志配置。

```csharp
private void LoadConfiguration()
```

**功能：**
- 从 ConfigurationManager 加载配置
- 应用配置到当前实例
- 创建默认配置（如果不存在）
- 异常处理和恢复

##### SaveConfiguration

保存当前配置到配置管理器。

```csharp
private void SaveConfiguration()
```

**功能：**
- 持久化当前配置
- 异常处理确保系统稳定性

##### InitializeDefaultWriters

初始化默认的日志写入器。

```csharp
private void InitializeDefaultWriters()
```

**注意：** 此方法目前为占位实现，将在写入器系统完善时实现。

##### InitializeLogExporter

初始化日志导出器。

```csharp
private void InitializeLogExporter()
```

**功能：**
- 创建 LogExporter 实例
- 应用导出配置
- 集成事件系统

## 生命周期管理

### 初始化流程

1. **事件系统集成** - 获取 EventSystem 实例
2. **配置加载** - 从 ConfigurationManager 加载配置
3. **默认写入器初始化** - 设置基本的日志输出
4. **日志导出器初始化** - 准备日志导出功能

### 清理流程

1. **刷新所有写入器** - 确保缓冲的日志被写入
2. **释放写入器资源** - 逐个释放所有写入器
3. **清理缓存** - 清空日志记录器缓存

## 性能优化特性

### 对象池优化

```csharp
// 使用对象池减少 GC 压力
var entry = _logEntryPool.Get(level, message, module, exception);
try
{
    // 处理日志条目
}
finally
{
    _logEntryPool.Return(entry);
}
```

### 性能监控和降级

```csharp
// 自动性能监控和降级
if (_performanceMonitor.ShouldDegrade())
{
    var strategy = _performanceMonitor.GetDegradationStrategy();
    if (strategy.DropLowPriorityLogs && level < LogLevel.Warn)
    {
        return; // 丢弃低优先级日志
    }
}
```

### 异步写入

```csharp
// 非阻塞的异步写入
_ = writer.WriteAsync(entry);
```

## 事件集成

LogManager 与事件系统深度集成，发布以下事件：

### LogEvent

```csharp
// 每次日志记录时发布
_eventSystem?.Publish(new LogEvent(entry));
```

### LogErrorEvent

```csharp
// 日志系统内部错误时发布
_eventSystem?.Publish(new LogErrorEvent(ex, "记录日志时发生错误"));
```

### LoggingStateChangedEvent

```csharp
// 日志系统启用/禁用状态变化时发布
_eventSystem?.Publish(new LoggingStateChangedEvent(value, "手动设置"));
```

## 配置示例

### 基本配置

```csharp
var logManager = LogManager.Instance;

// 设置全局日志级别
logManager.SetLogLevel(LogLevel.Debug);

// 设置模块特定级别
logManager.SetLogLevel(LogLevel.Trace, "Audio");
logManager.SetLogLevel(LogLevel.Error, "Network");

// 启用日志功能
logManager.IsLoggingEnabled = true;
```

### 添加写入器

```csharp
// 文件写入器
var fileWriter = new FileLogWriter(
    name: "MainLog",
    filePath: "Logs/app.log",
    formatter: new JsonLogFormatter(),
    bufferSize: 1000,
    maxFileSize: 10 * 1024 * 1024
);
logManager.AddLogWriter(fileWriter);

// 控制台写入器
var consoleWriter = new ConsoleLogWriter(
    name: "Console",
    formatter: new PlainTextLogFormatter(),
    minLevel: LogLevel.Warn
);
logManager.AddLogWriter(consoleWriter);
```

## 最佳实践

### 1. 模块化使用

```csharp
public class AudioManager : MonoBehaviour
{
    private static readonly ILogger Logger = LogManager.Instance.GetLogger("Audio");
    
    void Start()
    {
        Logger.Info("音频管理器启动");
        try
        {
            InitializeAudio();
            Logger.Info("音频初始化完成");
        }
        catch (Exception ex)
        {
            Logger.Error("音频初始化失败", ex);
        }
    }
}
```

### 2. 性能敏感场景

```csharp
// 在性能敏感的循环中检查日志级别
var logger = LogManager.Instance.GetLogger("Performance");
if (logger.IsEnabled(LogLevel.Debug))
{
    logger.Debug($"处理项目 {i}: {item}");
}
```

### 3. 应用程序生命周期集成

```csharp
public class ApplicationManager : MonoBehaviour
{
    void Start()
    {
        // 应用启动时初始化日志
        var logManager = LogManager.Instance;
        logManager.LogInfo("应用程序启动", "Application");
    }
    
    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            LogManager.Instance.FlushAll();
        }
    }
    
    void OnApplicationQuit()
    {
        LogManager.Instance.LogInfo("应用程序退出", "Application");
        LogManager.Instance.FlushAll();
    }
}
```

### 4. 错误处理和恢复

```csharp
try
{
    // 关键业务逻辑
    ProcessCriticalData();
}
catch (Exception ex)
{
    var logger = LogManager.Instance.GetLogger("CriticalSystem");
    logger.Fatal("关键系统处理失败", ex);
    
    // 导出错误日志用于分析
    _ = LogManager.Instance.ExportLogsToZipAsync(
        startTime: DateTime.Now.AddHours(-1),
        minLevel: LogLevel.Error
    );
    
    // 执行恢复逻辑
    RecoverFromError();
}
```

## 注意事项

1. **线程安全**: LogManager 是线程安全的，可以在多线程环境中安全使用
2. **性能影响**: 日志记录有一定的性能开销，在性能敏感的代码中应适当控制日志级别
3. **内存管理**: 使用对象池和性能监控来优化内存使用
4. **配置持久化**: 配置更改会自动保存，重启后保持设置
5. **异常处理**: 日志系统内部异常不会影响应用程序的正常运行

## 依赖项

- `System.Collections.Concurrent`: 线程安全集合
- `System.Threading.Tasks`: 异步操作支持
- `System.Diagnostics`: 性能监控
- `UnityEngine`: Unity 集成
- `DigitalHuman.Core.Configuration`: 配置管理
- `DigitalHuman.Core.Logging.Performance`: 性能优化组件
- `DigitalHuman.Core.Logging.Export`: 日志导出功能

## 相关文档

- [ILogManager 接口文档](ILogManager.md)
- [Logger 模块化日志记录器文档](Logger.md)
- [LogConfiguration 配置文档](LogConfiguration.md)
- [LogPerformanceMonitor 性能监控文档](LogPerformanceMonitor.md)
- [LogExporter 日志导出文档](LogExporter.md)
- [日志系统总体架构](../README.md)