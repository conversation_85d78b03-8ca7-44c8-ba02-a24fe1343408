# LogManagerTests 日志管理器测试文档

## 概述

`LogManagerTests` 是日志管理器的核心功能单元测试组件，用于验证 `LogManager` 的基本功能、配置管理和模块化支持。该测试组件提供了完整的测试套件，确保日志系统的各项功能正常工作。

## 命名空间

```csharp
DigitalHuman.Core.Logging.Tests
```

## 类定义

### LogManagerTests

日志管理器测试主类，继承自 `MonoBehaviour`，提供完整的测试功能。

#### 属性

##### runTestsOnStart

```csharp
[SerializeField] private bool runTestsOnStart = true;
```

**描述：** 是否在组件启动时自动运行测试

**默认值：** `true`

##### enableDetailedOutput

```csharp
[SerializeField] private bool enableDetailedOutput = true;
```

**描述：** 是否启用详细的测试输出信息

**默认值：** `true`

#### 公共方法

##### RunAllTests

运行所有测试的主方法。

```csharp
[ContextMenu("运行所有测试")]
public void RunAllTests()
```

**功能：**
- 初始化测试环境
- 按顺序执行所有测试用例
- 输出测试结果摘要

**测试覆盖范围：**
- 基础日志记录功能
- 日志级别过滤功能
- 模块化日志记录功能
- 日志记录器缓存机制
- 写入器管理功能
- 配置管理功能
- 事件系统集成
- 性能监控功能

**使用示例：**
```csharp
// 在 Unity Inspector 中右键点击组件选择 "运行所有测试"
// 或者通过代码调用
var testComponent = GetComponent<LogManagerTests>();
testComponent.RunAllTests();
```

## 测试方法详解

### TestBasicLogging

测试基础日志记录功能。

```csharp
private void TestBasicLogging()
```

**测试内容：**
- 验证不同级别日志的正确记录
- 检查日志计数的准确性
- 验证日志内容的正确性

**验证点：**
- Debug、Info、Warn、Error、Fatal 级别日志都能正确记录
- 日志计数与预期一致
- 最后记录的日志内容正确

**测试流程：**
1. 设置测试写入器
2. 记录5条不同级别的日志
3. 验证日志计数
4. 检查最后一条日志的内容

### TestLogLevelFiltering

测试日志级别过滤功能。

```csharp
private void TestLogLevelFiltering()
```

**测试内容：**
- 验证日志级别过滤的正确性
- 测试全局最小级别设置
- 确保低于最小级别的日志被正确过滤

**验证点：**
- 设置最小级别为 Warning 后，Debug 和 Info 级别日志被过滤
- Warning 和 Error 级别日志正常记录
- 过滤后的日志计数正确

**测试流程：**
1. 设置最小日志级别为 Warning
2. 记录不同级别的日志
3. 验证只有 Warning 和 Error 级别的日志被记录
4. 恢复默认级别设置

### TestModularLogging

测试模块化日志记录功能。

```csharp
private void TestModularLogging()
```

**测试内容：**
- 验证模块化日志记录器的创建
- 测试不同模块的日志记录
- 检查模块名称的正确记录

**验证点：**
- 能够成功创建不同模块的日志记录器
- 不同模块的日志都能正确记录
- 日志条目中的模块名称正确

**测试流程：**
1. 获取两个不同模块的日志记录器
2. 使用不同模块记录日志
3. 验证日志计数和模块名称记录

### TestLoggerCaching

测试日志记录器缓存机制。

```csharp
private void TestLoggerCaching()
```

**测试内容：**
- 验证同一模块多次获取返回相同实例
- 测试不同模块返回不同实例
- 确保缓存机制正常工作

**验证点：**
- 同一模块名多次调用 `GetLogger` 返回相同实例
- 不同模块名返回不同的日志记录器实例
- 缓存机制提高性能

**测试流程：**
1. 多次获取同一模块的日志记录器
2. 验证返回的是同一个实例
3. 获取不同模块的日志记录器
4. 验证返回的是不同实例

### TestWriterManagement

测试写入器管理功能。

```csharp
private void TestWriterManagement()
```

**测试内容：**
- 验证写入器的添加和移除
- 测试多写入器同时工作
- 确保写入器管理的正确性

**验证点：**
- 添加的写入器能够接收日志
- 多个写入器都能接收到相同的日志
- 移除的写入器不再接收日志

**测试流程：**
1. 添加额外的测试写入器
2. 记录日志并验证两个写入器都收到
3. 移除额外的写入器
4. 验证只有主写入器收到后续日志

### TestConfigurationManagement

测试配置管理功能。

```csharp
private void TestConfigurationManagement()
```

**测试内容：**
- 验证日志开关的正确性
- 测试配置状态的保存和恢复
- 确保配置变更的即时生效

**验证点：**
- 关闭日志功能后不再记录日志
- 重新开启日志功能后恢复正常记录
- 配置状态正确保存和恢复

**测试流程：**
1. 保存当前日志开关状态
2. 关闭日志功能并验证日志被忽略
3. 重新开启日志功能并验证恢复正常
4. 恢复原始配置状态

### TestEventIntegration

测试事件系统集成。

```csharp
private void TestEventIntegration()
```

**测试内容：**
- 验证日志事件的发布
- 测试事件系统的基本集成
- 确保事件机制正常工作

**验证点：**
- 日志记录时能够正常发布事件
- 事件系统集成不影响基本功能
- 异常情况下的错误处理

### TestPerformanceMonitoring

测试性能监控功能。

```csharp
private void TestPerformanceMonitoring()
```

**测试内容：**
- 验证性能统计数据的获取
- 测试健康状态检查功能
- 确保监控功能正常工作

**验证点：**
- 能够成功获取性能统计数据
- 健康状态检查返回有效结果
- 统计数据包含预期的指标

**输出信息：**
- 总日志数量
- 错误率
- 每秒日志数
- 健康级别
- 状态消息

## 辅助类

### TestLogWriter

测试专用的日志写入器实现。

```csharp
public class TestLogWriter : ILogWriter
```

#### 属性

- `Name` (string): 写入器名称
- `IsEnabled` (bool): 是否启用，默认 `true`
- `MinLevel` (LogLevel): 最小日志级别，默认 `Debug`
- `LogCount` (int): 已记录的日志数量

#### 方法

##### WriteAsync

异步写入日志条目。

```csharp
public Task WriteAsync(LogEntry logEntry)
```

**功能：**
- 检查写入器是否启用
- 验证日志级别是否满足最小要求
- 将日志条目添加到内部列表

##### GetLastLog

获取最后记录的日志条目。

```csharp
public LogEntry GetLastLog()
```

**返回值：** 最后一条日志条目，如果没有日志则返回 `null`

##### GetAllLogs

获取所有记录的日志条目。

```csharp
public List<LogEntry> GetAllLogs()
```

**返回值：** 包含所有日志条目的列表副本

##### Clear

清空所有记录的日志。

```csharp
public void Clear()
```

**功能：** 清除内部日志列表，重置计数器

## 使用指南

### 在 Unity 中使用

1. **添加组件到 GameObject**
```csharp
var testObject = new GameObject("LogManagerTests");
var testComponent = testObject.AddComponent<LogManagerTests>();
```

2. **配置测试参数**
```csharp
testComponent.runTestsOnStart = true;
testComponent.enableDetailedOutput = true;
```

3. **运行测试**
```csharp
// 自动运行（如果 runTestsOnStart 为 true）
// 或手动运行
testComponent.RunAllTests();
```

### 在代码中使用

```csharp
public class LogSystemValidator : MonoBehaviour
{
    void Start()
    {
        // 创建测试实例
        var tests = gameObject.AddComponent<LogManagerTests>();
        
        // 配置测试
        tests.runTestsOnStart = false;
        tests.enableDetailedOutput = true;
        
        // 运行测试
        tests.RunAllTests();
        
        // 清理
        Destroy(tests);
    }
}
```

### 集成到 CI/CD 流程

```csharp
public static class LogManagerTestRunner
{
    [MenuItem("Tools/Run Log Manager Tests")]
    public static void RunTests()
    {
        var testObject = new GameObject("LogManagerTests");
        var testComponent = testObject.AddComponent<LogManagerTests>();
        
        testComponent.runTestsOnStart = false;
        testComponent.enableDetailedOutput = false;
        
        testComponent.RunAllTests();
        
        DestroyImmediate(testObject);
    }
}
```

## 测试结果解读

### 成功标识

- `✓` 表示测试通过
- 绿色日志输出表示功能正常

### 失败标识

- `✗` 表示测试失败
- 红色错误日志表示功能异常
- 包含期望值和实际值的对比

### 警告标识

- `⚠` 表示测试警告
- 黄色警告日志表示功能可能存在问题但不影响基本使用

### 示例输出

```
=== 开始 LogManager 核心功能测试 ===
--- 测试基础日志记录功能 ---
✓ 基础日志记录功能正常
✓ 日志内容记录正确
--- 基础日志记录功能测试完成 ---
--- 测试日志级别过滤功能 ---
✓ 日志级别过滤功能正常
--- 日志级别过滤功能测试完成 ---
...
=== LogManager 核心功能测试完成 ===
```

## 扩展测试

### 添加自定义测试

```csharp
public class ExtendedLogManagerTests : LogManagerTests
{
    protected void TestCustomFeature()
    {
        Debug.Log("--- 测试自定义功能 ---");
        
        // 自定义测试逻辑
        try
        {
            // 测试代码
            Debug.Log("✓ 自定义功能测试通过");
        }
        catch (Exception ex)
        {
            Debug.LogError($"✗ 自定义功能测试失败: {ex.Message}");
        }
        
        Debug.Log("--- 自定义功能测试完成 ---");
    }
    
    public override void RunAllTests()
    {
        base.RunAllTests();
        TestCustomFeature();
    }
}
```

### 性能测试扩展

```csharp
private void TestPerformanceUnderLoad()
{
    Debug.Log("--- 测试高负载性能 ---");
    
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();
    
    // 记录大量日志
    for (int i = 0; i < 10000; i++)
    {
        _logManager.LogInfo($"性能测试日志 {i}", "PerformanceTest");
    }
    
    stopwatch.Stop();
    
    Debug.Log($"✓ 10000条日志记录耗时: {stopwatch.ElapsedMilliseconds}ms");
    Debug.Log($"✓ 平均每条日志耗时: {stopwatch.ElapsedMilliseconds / 10000.0:F3}ms");
}
```

## 最佳实践

### 1. 测试隔离

```csharp
private void SetupTest()
{
    // 每个测试前的准备工作
    _testWriter.Clear();
    _logManager.SetLoggingEnabled(true);
    _logManager.SetGlobalMinLevel(LogLevel.Debug);
}

private void TeardownTest()
{
    // 每个测试后的清理工作
    _testWriter.Clear();
}
```

### 2. 异常处理

```csharp
private void TestWithExceptionHandling()
{
    try
    {
        // 测试逻辑
    }
    catch (Exception ex)
    {
        Debug.LogError($"✗ 测试异常: {ex.Message}");
        Debug.LogError($"堆栈跟踪: {ex.StackTrace}");
    }
}
```

### 3. 详细断言

```csharp
private void AssertEquals<T>(T expected, T actual, string testName)
{
    if (EqualityComparer<T>.Default.Equals(expected, actual))
    {
        Debug.Log($"✓ {testName}: 期望 {expected}, 实际 {actual}");
    }
    else
    {
        Debug.LogError($"✗ {testName}: 期望 {expected}, 实际 {actual}");
    }
}
```

## 注意事项

1. **线程安全**: 测试在主线程中运行，确保线程安全
2. **资源清理**: 测试完成后自动清理测试写入器
3. **状态恢复**: 测试不会影响日志系统的原始配置
4. **性能影响**: 测试过程中可能产生大量日志，注意性能影响
5. **依赖检查**: 确保日志管理器已正确初始化

## 故障排除

### 常见问题

1. **测试写入器未收到日志**
   - 检查日志级别设置
   - 验证写入器是否正确添加
   - 确认日志功能已启用

2. **缓存测试失败**
   - 检查是否有其他代码影响了日志记录器缓存
   - 验证模块名称的一致性

3. **性能测试异常**
   - 检查系统资源是否充足
   - 验证性能监控组件是否正确初始化

### 调试技巧

```csharp
private void DebugTestState()
{
    Debug.Log($"日志管理器状态: 启用={_logManager.IsLoggingEnabled}");
    Debug.Log($"测试写入器状态: 启用={_testWriter.IsEnabled}, 日志数={_testWriter.LogCount}");
    Debug.Log($"最小级别: {_logManager.GetGlobalMinLevel()}");
}
```

## 相关文档

- [LogManager 日志管理器文档](LogManager.md)
- [日志系统总体架构](../README.md)
- [ILogManager 接口文档](ILogManager.md)
- [Logger 模块化日志记录器文档](Logger.md)
- [测试框架文档](../../Testing/README.md)