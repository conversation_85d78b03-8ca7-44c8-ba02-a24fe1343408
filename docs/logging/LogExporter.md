# LogExporter 日志导出器文档

## 概述

`LogExporter` 是日志系统的导出组件，负责将日志文件导出为可分析的格式。它支持按时间范围和日志级别过滤、多种导出格式、ZIP 压缩、系统信息包含等功能，为日志分析和故障排除提供便利。

## 命名空间

```csharp
DigitalHuman.Core.Logging.Export
```

## 接口定义

### ILogExporter

日志导出器接口，定义了导出功能的核心方法。

```csharp
public interface ILogExporter
{
    Task<string> ExportAsync(LogExportRequest request);
    Task<string> ExportToZipAsync(LogExportRequest request);
    void CleanupExports(TimeSpan maxAge);
}
```

#### 方法

##### ExportAsync

导出日志到单个文件。

```csharp
Task<string> ExportAsync(LogExportRequest request)
```

**参数：**
- `request` (LogExportRequest): 导出请求参数

**返回值：** `Task<string>` - 导出文件路径

##### ExportToZipAsync

导出日志到 ZIP 压缩文件。

```csharp
Task<string> ExportToZipAsync(LogExportRequest request)
```

**参数：**
- `request` (LogExportRequest): 导出请求参数

**返回值：** `Task<string>` - 导出 ZIP 文件路径

##### CleanupExports

清理过期的导出文件。

```csharp
void CleanupExports(TimeSpan maxAge)
```

**参数：**
- `maxAge` (TimeSpan): 最大保留时间

## 类实现

### LogExporter

日志导出器的具体实现类。

#### 构造函数

```csharp
public LogExporter(LogExportConfiguration configuration, IEventSystem eventSystem = null)
```

**参数：**
- `configuration` (LogExportConfiguration): 导出配置，不能为空
- `eventSystem` (IEventSystem, 可选): 事件系统实例

**功能：**
- 验证配置参数
- 初始化事件系统集成
- 确保导出目录存在

**使用示例：**
```csharp
var exportConfig = new LogExportConfiguration
{
    ExportDirectory = "LogExports",
    IncludeSystemInfo = true,
    CompressExports = true,
    MaxExportSizeMB = 100,
    ExportFormat = "json"
};

var logExporter = new LogExporter(exportConfig, EventSystem.Instance);
```

#### 公共方法

##### ExportAsync

导出日志到单个文件的异步方法。

```csharp
public async Task<string> ExportAsync(LogExportRequest request)
```

**功能：**
- 收集符合条件的日志文件
- 按时间范围和级别过滤日志条目
- 生成统一格式的导出文件
- 可选包含系统信息
- 发布导出完成事件

**处理流程：**
1. 验证请求参数
2. 生成导出文件路径
3. 收集日志文件列表
4. 解析和过滤日志条目
5. 创建导出文件
6. 写入系统信息（可选）
7. 写入格式化的日志条目
8. 发布完成事件

**使用示例：**
```csharp
var exportRequest = new LogExportRequest
{
    StartTime = DateTime.Now.AddDays(-7),
    EndTime = DateTime.Now,
    MinLevel = LogLevel.Warn,
    Modules = new[] { "Audio", "Network" },
    Format = "json",
    IncludeSystemInfo = true
};

try
{
    string exportPath = await logExporter.ExportAsync(exportRequest);
    Console.WriteLine($"日志导出成功: {exportPath}");
}
catch (Exception ex)
{
    Console.WriteLine($"日志导出失败: {ex.Message}");
}
```

##### ExportToZipAsync

导出日志到 ZIP 压缩文件的异步方法。

```csharp
public async Task<string> ExportToZipAsync(LogExportRequest request)
```

**功能：**
- 创建包含多个文件的 ZIP 压缩包
- 分别存储不同来源的日志文件
- 包含导出元数据和统计信息
- 添加系统信息文件
- 生成统计报告

**ZIP 文件结构：**
```
LogExport_20250124_143022.zip
├── system_info.json          # 系统信息
├── export_metadata.json      # 导出元数据
├── statistics.json           # 统计信息
└── logs/                     # 日志文件目录
    ├── app_20250124.log      # 应用日志
    ├── error_20250123.log    # 错误日志
    └── network_20250122.log  # 网络日志
```

**使用示例：**
```csharp
var exportRequest = new LogExportRequest
{
    StartTime = DateTime.Now.AddMonths(-1),
    EndTime = DateTime.Now,
    MinLevel = LogLevel.Info,
    Format = "json",
    IncludeSystemInfo = true,
    Compress = true
};

string zipPath = await logExporter.ExportToZipAsync(exportRequest);
if (!string.IsNullOrEmpty(zipPath))
{
    // ZIP 文件可用于技术支持或长期存档
    Console.WriteLine($"日志包导出成功: {zipPath}");
    
    // 可以发送给技术支持团队
    await SendToSupport(zipPath);
}
```

##### CleanupExports

清理过期导出文件的方法。

```csharp
public void CleanupExports(TimeSpan maxAge)
```

**功能：**
- 扫描导出目录中的所有文件
- 按创建时间过滤过期文件
- 安全删除过期文件
- 记录清理操作日志

**使用示例：**
```csharp
// 清理 7 天前的导出文件
logExporter.CleanupExports(TimeSpan.FromDays(7));

// 定期清理任务
var timer = new Timer(_ => 
{
    logExporter.CleanupExports(TimeSpan.FromDays(30));
}, null, TimeSpan.Zero, TimeSpan.FromDays(1));
```

## 数据模型

### LogExportRequest

导出请求参数类。

```csharp
[Serializable]
public class LogExportRequest
{
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public LogLevel? MinLevel { get; set; }
    public string[] Modules { get; set; }
    public string Format { get; set; } = "json";
    public bool IncludeSystemInfo { get; set; } = true;
    public bool Compress { get; set; } = true;
}
```

#### 属性说明

- **StartTime**: 导出开始时间，为空则从最早日志开始
- **EndTime**: 导出结束时间，为空则到最新日志结束
- **MinLevel**: 最小日志级别，为空则包含所有级别
- **Modules**: 要导出的模块列表，为空则包含所有模块
- **Format**: 导出格式（json、text、structured）
- **IncludeSystemInfo**: 是否包含系统信息
- **Compress**: 是否使用压缩（仅对 ZIP 导出有效）

**使用示例：**
```csharp
// 导出最近一周的错误日志
var request = new LogExportRequest
{
    StartTime = DateTime.Now.AddDays(-7),
    EndTime = DateTime.Now,
    MinLevel = LogLevel.Error,
    Format = "json",
    IncludeSystemInfo = true
};

// 导出特定模块的调试日志
var debugRequest = new LogExportRequest
{
    StartTime = DateTime.Today,
    Modules = new[] { "Audio", "Network", "Database" },
    MinLevel = LogLevel.Debug,
    Format = "text",
    IncludeSystemInfo = false
};
```

### LogExportConfiguration

导出配置类。

```csharp
[Serializable]
public class LogExportConfiguration
{
    public string ExportDirectory { get; set; } = "LogExports";
    public bool IncludeSystemInfo { get; set; } = true;
    public bool CompressExports { get; set; } = true;
    public int MaxExportSizeMB { get; set; } = 100;
    public string ExportFormat { get; set; } = "json";
}
```

#### 属性说明

- **ExportDirectory**: 导出文件存储目录
- **IncludeSystemInfo**: 默认是否包含系统信息
- **CompressExports**: 默认是否压缩导出
- **MaxExportSizeMB**: 单个导出文件的最大大小限制
- **ExportFormat**: 默认导出格式

## 内部数据结构

### LogFileInfo

内部使用的日志文件信息类。

```csharp
internal class LogFileInfo
{
    public string FilePath { get; set; }
    public string FileName { get; set; }
    public DateTime CreationTime { get; set; }
    public long Size { get; set; }
    public List<LogEntry> Entries { get; set; }
}
```

### SystemInfo

系统信息数据结构。

```csharp
[Serializable]
internal class SystemInfo
{
    public string ApplicationVersion { get; set; }
    public string UnityVersion { get; set; }
    public string OperatingSystem { get; set; }
    public string DeviceModel { get; set; }
    public DateTime ExportTime { get; set; }
    public float ApplicationUptime { get; set; }
}
```

### ExportMetadata

导出元数据结构。

```csharp
[Serializable]
internal class ExportMetadata
{
    public DateTime ExportTime { get; set; }
    public LogExportRequest Request { get; set; }
    public int TotalFiles { get; set; }
    public int TotalEntries { get; set; }
}
```

### ExportStatistics

导出统计信息结构。

```csharp
[Serializable]
internal class ExportStatistics
{
    public int TotalFiles { get; set; }
    public int TotalEntries { get; set; }
    public long TotalSizeBytes { get; set; }
    public DateTime EarliestLogTime { get; set; }
    public DateTime LatestLogTime { get; set; }
    public Dictionary<LogLevel, long> LogCountByLevel { get; set; }
}
```

## 事件集成

### LogExportCompletedEvent

导出完成事件，在导出操作完成时发布。

```csharp
public class LogExportCompletedEvent : EventBase
{
    public string ExportPath { get; set; }
    public LogExportRequest Request { get; set; }
    public bool Success { get; set; }
    public string ErrorMessage { get; set; }
}
```

**使用示例：**
```csharp
// 订阅导出完成事件
eventSystem.Subscribe<LogExportCompletedEvent>(evt =>
{
    if (evt.Success)
    {
        Console.WriteLine($"导出成功: {evt.ExportPath}");
        
        // 可以进行后续处理，如发送邮件、上传到云端等
        ProcessExportedFile(evt.ExportPath);
    }
    else
    {
        Console.WriteLine($"导出失败: {evt.ErrorMessage}");
        
        // 记录失败原因，可能需要重试
        LogExportFailure(evt.Request, evt.ErrorMessage);
    }
});
```

## 支持的导出格式

### JSON 格式

结构化的 JSON 格式，适合程序化分析。

```json
{
  "timestamp": "2025-01-24T14:30:22.123Z",
  "level": "INFO",
  "module": "Audio",
  "thread": "1",
  "message": "音频设备初始化完成",
  "properties": {
    "deviceName": "Default Audio Device",
    "sampleRate": 44100
  }
}
```

### 纯文本格式

人类可读的文本格式。

```
[2025-01-24 14:30:22.123] [INFO] [Audio] [Thread:1] 音频设备初始化完成
```

### 结构化格式

键值对格式，便于日志解析工具处理。

```
timestamp="2025-01-24T14:30:22.123Z" level="INFO" module="Audio" thread="1" message="音频设备初始化完成" deviceName="Default Audio Device" sampleRate="44100"
```

## 高级功能

### 日志解析和过滤

LogExporter 支持解析多种日志格式：

```csharp
// 自动检测和解析 JSON 格式日志
if (line.TrimStart().StartsWith("{"))
{
    return JsonUtility.FromJson<LogEntry>(line);
}

// 解析纯文本格式日志
return ParsePlainTextLogEntry(line);
```

### 压缩文件处理

支持读取 GZip 压缩的日志文件：

```csharp
private async Task<string> ReadCompressedFileAsync(string filePath)
{
    using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
    using (var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress))
    using (var reader = new StreamReader(gzipStream))
    {
        return await reader.ReadToEndAsync();
    }
}
```

### 时间范围过滤

智能的时间范围过滤，支持文件级别和条目级别的过滤：

```csharp
// 文件级别过滤
private bool IsFileInTimeRange(FileInfo file, DateTime? startTime, DateTime? endTime)
{
    if (startTime.HasValue && file.CreationTime < startTime.Value)
        return false;
    
    if (endTime.HasValue && file.CreationTime > endTime.Value)
        return false;
    
    return true;
}

// 条目级别过滤
private bool ShouldIncludeEntry(LogEntry entry, LogExportRequest request)
{
    if (request.StartTime.HasValue && entry.Timestamp < request.StartTime.Value)
        return false;
    
    if (request.EndTime.HasValue && entry.Timestamp > request.EndTime.Value)
        return false;
    
    if (request.MinLevel.HasValue && entry.Level < request.MinLevel.Value)
        return false;
    
    return true;
}
```

## 使用场景

### 1. 故障排除

```csharp
// 导出错误发生前后的日志
var troubleshootRequest = new LogExportRequest
{
    StartTime = errorTime.AddHours(-2),
    EndTime = errorTime.AddHours(1),
    MinLevel = LogLevel.Warn,
    Format = "json",
    IncludeSystemInfo = true
};

string troubleshootPath = await logExporter.ExportToZipAsync(troubleshootRequest);
```

### 2. 性能分析

```csharp
// 导出性能相关的日志
var performanceRequest = new LogExportRequest
{
    StartTime = DateTime.Today,
    Modules = new[] { "Performance", "Memory", "CPU" },
    MinLevel = LogLevel.Debug,
    Format = "structured"
};

string performancePath = await logExporter.ExportAsync(performanceRequest);
```

### 3. 合规审计

```csharp
// 导出完整的审计日志
var auditRequest = new LogExportRequest
{
    StartTime = DateTime.Now.AddMonths(-3),
    EndTime = DateTime.Now,
    Modules = new[] { "Security", "Authentication", "Authorization" },
    Format = "json",
    IncludeSystemInfo = true
};

string auditPath = await logExporter.ExportToZipAsync(auditRequest);
```

### 4. 技术支持

```csharp
// 为技术支持创建完整的日志包
var supportRequest = new LogExportRequest
{
    StartTime = DateTime.Now.AddDays(-1),
    MinLevel = LogLevel.Info,
    Format = "json",
    IncludeSystemInfo = true
};

string supportPackage = await logExporter.ExportToZipAsync(supportRequest);

// 自动发送给技术支持
await SendSupportPackage(supportPackage);
```

## 最佳实践

### 1. 定期清理

```csharp
// 设置定期清理任务
var cleanupTimer = new Timer(_ =>
{
    logExporter.CleanupExports(TimeSpan.FromDays(30));
}, null, TimeSpan.Zero, TimeSpan.FromDays(1));
```

### 2. 异常处理

```csharp
try
{
    string exportPath = await logExporter.ExportAsync(request);
    ProcessExportedFile(exportPath);
}
catch (Exception ex)
{
    logger.Error($"日志导出失败: {ex.Message}", ex);
    
    // 可以尝试降级导出（减少时间范围或提高级别过滤）
    var fallbackRequest = new LogExportRequest
    {
        StartTime = request.StartTime?.AddHours(12),
        EndTime = request.EndTime,
        MinLevel = LogLevel.Warn,
        Format = "text"
    };
    
    string fallbackPath = await logExporter.ExportAsync(fallbackRequest);
}
```

### 3. 大文件处理

```csharp
// 对于大量日志，分批导出
var batchSize = TimeSpan.FromDays(1);
var currentTime = startTime;

while (currentTime < endTime)
{
    var batchRequest = new LogExportRequest
    {
        StartTime = currentTime,
        EndTime = currentTime.Add(batchSize),
        MinLevel = request.MinLevel,
        Format = request.Format
    };
    
    string batchPath = await logExporter.ExportAsync(batchRequest);
    ProcessBatch(batchPath);
    
    currentTime = currentTime.Add(batchSize);
}
```

### 4. 内存优化

```csharp
// 对于内存敏感的环境，使用流式处理
var exportConfig = new LogExportConfiguration
{
    MaxExportSizeMB = 50, // 限制单个导出文件大小
    CompressExports = true // 启用压缩节省空间
};
```

## 性能考虑

### 1. 异步操作

所有导出操作都是异步的，不会阻塞主线程：

```csharp
// 并行导出多个时间段
var tasks = new List<Task<string>>();
for (int i = 0; i < 7; i++)
{
    var dayRequest = new LogExportRequest
    {
        StartTime = DateTime.Today.AddDays(-i-1),
        EndTime = DateTime.Today.AddDays(-i),
        MinLevel = LogLevel.Info
    };
    
    tasks.Add(logExporter.ExportAsync(dayRequest));
}

string[] exportPaths = await Task.WhenAll(tasks);
```

### 2. 内存管理

使用流式处理避免大文件导致的内存问题：

```csharp
// 流式写入大文件
using (var writer = new StreamWriter(exportPath, false, Encoding.UTF8))
{
    foreach (var logFile in logFiles)
    {
        await ProcessLogFileStreamAsync(logFile, writer, request);
    }
}
```

### 3. 磁盘 I/O 优化

批量处理减少磁盘 I/O 操作：

```csharp
// 批量读取和处理日志条目
var buffer = new List<LogEntry>();
foreach (var entry in entries)
{
    buffer.Add(entry);
    
    if (buffer.Count >= 1000)
    {
        await WriteBatchAsync(buffer);
        buffer.Clear();
    }
}
```

## 故障排除

### 常见问题

1. **导出文件过大**
   - 减少时间范围
   - 提高日志级别过滤
   - 启用压缩

2. **导出速度慢**
   - 检查磁盘 I/O 性能
   - 减少并发导出任务
   - 优化日志文件存储结构

3. **内存使用过高**
   - 启用流式处理
   - 减少批处理大小
   - 限制导出文件大小

4. **导出文件损坏**
   - 检查磁盘空间
   - 验证文件权限
   - 检查日志文件完整性

### 调试工具

```csharp
// 启用详细的导出日志
var exportConfig = new LogExportConfiguration
{
    ExportDirectory = "LogExports",
    IncludeSystemInfo = true
};

var logExporter = new LogExporter(exportConfig, EventSystem.Instance);

// 订阅导出事件进行调试
eventSystem.Subscribe<LogExportCompletedEvent>(evt =>
{
    if (evt.Success)
    {
        var fileInfo = new FileInfo(evt.ExportPath);
        Console.WriteLine($"导出成功: {evt.ExportPath}");
        Console.WriteLine($"文件大小: {fileInfo.Length / 1024 / 1024:F2} MB");
    }
    else
    {
        Console.WriteLine($"导出失败: {evt.ErrorMessage}");
    }
});
```

## 扩展开发

### 自定义导出格式

```csharp
// 扩展支持 CSV 格式
public class CsvLogFormatter : ILogFormatter
{
    public bool SupportsStructuredData => false;
    
    public string Format(LogEntry entry)
    {
        return $"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}," +
               $"{entry.Level}," +
               $"{entry.Module ?? ""}," +
               $"\"{entry.Message?.Replace("\"", "\"\"")}\"";
    }
}

// 注册自定义格式化器
LogFormatterFactory.RegisterFormatter("csv", () => new CsvLogFormatter());
```

### 自定义导出目标

```csharp
public class CloudLogExporter : ILogExporter
{
    public async Task<string> ExportAsync(LogExportRequest request)
    {
        // 导出到云存储
        var localPath = await ExportToLocalFile(request);
        var cloudPath = await UploadToCloud(localPath);
        return cloudPath;
    }
    
    // 实现其他接口方法...
}
```

## 依赖项

- `System.IO`: 文件操作
- `System.IO.Compression`: ZIP 压缩
- `System.Threading.Tasks`: 异步操作
- `System.Text`: 文本编码
- `UnityEngine`: Unity 集成
- `DigitalHuman.Core.Logging.Formatters`: 日志格式化器

## 相关文档

- [LogManager 日志管理器文档](LogManager.md)
- [日志系统总体架构](../README.md)
- [LogConfiguration 配置文档](LogConfiguration.md)
- [事件系统文档](../../EventSystem.md)