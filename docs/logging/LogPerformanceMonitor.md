# LogPerformanceMonitor 日志性能监控器文档

## 概述

`LogPerformanceMonitor` 是日志系统的性能监控组件，负责实时监控日志系统的各项性能指标，包括写入时间、内存使用、队列深度和吞吐量等。它提供了性能阈值检查、降级策略建议和性能警告功能，确保日志系统在高负载情况下的稳定运行。

## 命名空间

```csharp
DigitalHuman.Core.Logging.Performance
```

## 类定义

### LogPerformanceMonitor

日志性能监控器主类，采用单例模式实现，提供完整的性能监控功能。

#### 属性

##### Instance (静态属性)

```csharp
public static LogPerformanceMonitor Instance { get; }
```

**描述：** 获取性能监控器的单例实例

**返回值：** `LogPerformanceMonitor` - 监控器实例

##### Thresholds

```csharp
public PerformanceThresholds Thresholds { get; }
```

**描述：** 获取性能阈值配置对象

**返回值：** `PerformanceThresholds` - 阈值配置

#### 公共方法

##### RecordWriteOperation

记录日志写入操作的性能数据。

```csharp
public void RecordWriteOperation(long writeTimeMs)
```

**参数：**
- `writeTimeMs` (long): 写入操作耗时（毫秒）

**功能：**
- 更新总写入日志数量
- 累计总写入时间
- 维护写入时间样本队列用于百分位数计算
- 自动管理样本队列大小（默认1000个样本）

**使用示例：**
```csharp
var stopwatch = Stopwatch.StartNew();
// 执行日志写入操作
await writer.WriteAsync(logEntry);
stopwatch.Stop();

// 记录性能数据
LogPerformanceMonitor.Instance.RecordWriteOperation(stopwatch.ElapsedMilliseconds);
```

##### UpdateQueueDepth

更新当前日志队列深度。

```csharp
public void UpdateQueueDepth(int depth)
```

**参数：**
- `depth` (int): 当前队列中待处理的日志条目数量

**功能：**
- 更新队列深度指标
- 检查是否超过阈值并触发警告事件

**使用示例：**
```csharp
// 在日志写入器中更新队列深度
var currentDepth = logChannel.Reader.Count;
LogPerformanceMonitor.Instance.UpdateQueueDepth(currentDepth);
```

##### UpdateMemoryUsage

更新内存使用量统计。

```csharp
public void UpdateMemoryUsage(long memoryBytes)
```

**参数：**
- `memoryBytes` (long): 当前内存使用量（字节）

**功能：**
- 更新内存使用指标
- 检查是否超过内存阈值并触发警告

**使用示例：**
```csharp
// 获取当前内存使用量
long memoryUsage = GC.GetTotalMemory(false);
LogPerformanceMonitor.Instance.UpdateMemoryUsage(memoryUsage);
```

##### GetMetrics

获取当前的性能指标快照。

```csharp
public LogPerformanceMetrics GetMetrics()
```

**返回值：** `LogPerformanceMetrics` - 包含所有性能指标的对象

**功能：**
- 计算平均写入时间
- 计算吞吐量（每秒日志数）
- 计算P95和P99写入时间
- 返回完整的性能指标快照

**使用示例：**
```csharp
var metrics = LogPerformanceMonitor.Instance.GetMetrics();
Console.WriteLine($"总日志数: {metrics.TotalLogsWritten}");
Console.WriteLine($"平均写入时间: {metrics.AverageWriteTimeMs:F2}ms");
Console.WriteLine($"内存使用: {metrics.GetReadableMemoryUsage()}");
Console.WriteLine($"吞吐量: {metrics.ThroughputPerSecond:F2} logs/sec");
Console.WriteLine($"P95写入时间: {metrics.P95WriteTimeMs:F2}ms");
```

##### ResetMetrics

重置所有性能指标。

```csharp
public void ResetMetrics()
```

**功能：**
- 清零所有计数器
- 清空写入时间样本队列
- 重启运行时间计时器
- 更新重置时间戳

**使用示例：**
```csharp
// 在系统维护或测试开始时重置指标
LogPerformanceMonitor.Instance.ResetMetrics();
```

##### ShouldDegrade

检查当前性能状况是否需要降级处理。

```csharp
public bool ShouldDegrade()
```

**返回值：** `bool` - 如果需要降级返回true，否则返回false

**检查条件：**
- 内存使用量是否超过阈值
- 队列深度是否超过阈值
- 平均写入时间是否超过阈值
- 吞吐量是否低于最小要求

**使用示例：**
```csharp
if (LogPerformanceMonitor.Instance.ShouldDegrade())
{
    // 执行降级策略
    var strategy = LogPerformanceMonitor.Instance.GetDegradationStrategy();
    ApplyDegradationStrategy(strategy);
}
```

##### GetDegradationStrategy

获取基于当前性能状况的降级策略建议。

```csharp
public DegradationStrategy GetDegradationStrategy()
```

**返回值：** `DegradationStrategy` - 包含具体降级建议的策略对象

**策略逻辑：**
- **内存超限**: 减少缓冲区大小，增加刷新频率，提升日志级别到Warn
- **队列超限**: 丢弃低优先级日志，增加刷新频率，提升日志级别到Error
- **写入时间过长**: 减少格式化复杂度，禁用非必要写入器
- **吞吐量过低**: 增加缓冲区大小，减少刷新频率

**使用示例：**
```csharp
var strategy = LogPerformanceMonitor.Instance.GetDegradationStrategy();

if (strategy.ReduceBufferSize)
{
    // 减少缓冲区大小
    writer.SetBufferSize(writer.BufferSize / 2);
}

if (strategy.DropLowPriorityLogs)
{
    // 提升最小日志级别
    logManager.SetLogLevel(LogLevel.Warn);
}

if (strategy.SuggestedLogLevel.HasValue)
{
    // 应用建议的日志级别
    logManager.SetLogLevel(strategy.SuggestedLogLevel.Value);
}
```

### LogPerformanceMetrics

性能指标数据类，包含所有监控的性能数据。

#### 属性

- `TotalLogsWritten` (long): 总写入日志数量
- `AverageWriteTimeMs` (double): 平均写入时间（毫秒）
- `MemoryUsageBytes` (long): 内存使用量（字节）
- `QueueDepth` (int): 当前队列深度
- `UptimeSeconds` (double): 系统运行时间（秒）
- `ThroughputPerSecond` (double): 每秒吞吐量
- `P95WriteTimeMs` (double): P95写入时间（毫秒）
- `P99WriteTimeMs` (double): P99写入时间（毫秒）
- `LastResetTime` (DateTime): 最后重置时间

#### 方法

##### GetReadableMemoryUsage

```csharp
public string GetReadableMemoryUsage()
```

**返回值：** `string` - 可读的内存使用格式（如 "45.6 MB"）

**使用示例：**
```csharp
var metrics = monitor.GetMetrics();
Console.WriteLine($"内存使用: {metrics.GetReadableMemoryUsage()}");
// 输出: 内存使用: 45.6 MB
```

### PerformanceThresholds

性能阈值配置类，定义各项性能指标的警告阈值。

#### 属性

- `MaxMemoryUsageBytes` (long): 最大内存使用量，默认50MB
- `MaxQueueDepth` (int): 最大队列深度，默认10000
- `MaxAverageWriteTimeMs` (double): 最大平均写入时间，默认100ms
- `MinThroughputPerSecond` (double): 最小吞吐量，默认100 logs/sec

**配置示例：**
```csharp
var monitor = LogPerformanceMonitor.Instance;
monitor.Thresholds.MaxMemoryUsageBytes = 100 * 1024 * 1024; // 100MB
monitor.Thresholds.MaxQueueDepth = 5000;
monitor.Thresholds.MaxAverageWriteTimeMs = 50;
monitor.Thresholds.MinThroughputPerSecond = 200;
```

### DegradationStrategy

降级策略类，包含具体的性能优化建议。

#### 属性

- `ReduceBufferSize` (bool): 是否减少缓冲区大小
- `IncreaseBufferSize` (bool): 是否增加缓冲区大小
- `IncreaseFlushFrequency` (bool): 是否增加刷新频率
- `ReduceFlushFrequency` (bool): 是否减少刷新频率
- `DropLowPriorityLogs` (bool): 是否丢弃低优先级日志
- `ReduceFormattingComplexity` (bool): 是否减少格式化复杂度
- `DisableNonEssentialWriters` (bool): 是否禁用非必要写入器
- `SuggestedLogLevel` (LogLevel?): 建议的日志级别

### LogPerformanceWarningEvent

性能警告事件类，当性能指标超过阈值时发布。

#### 属性

- `MetricName` (string): 超过阈值的指标名称
- `CurrentValue` (object): 当前值
- `Threshold` (object): 阈值

#### 构造函数

```csharp
public LogPerformanceWarningEvent(string metricName, object currentValue, object threshold)
```

**使用示例：**
```csharp
// 订阅性能警告事件
eventSystem.Subscribe<LogPerformanceWarningEvent>(evt =>
{
    Console.WriteLine($"性能警告: {evt.MetricName}");
    Console.WriteLine($"当前值: {evt.CurrentValue}");
    Console.WriteLine($"阈值: {evt.Threshold}");
    
    // 执行相应的处理逻辑
    HandlePerformanceWarning(evt);
});
```

## 集成示例

### 在LogManager中集成

```csharp
public class LogManager : SingletonManager<LogManager>, ILogManager
{
    private readonly LogPerformanceMonitor _performanceMonitor = LogPerformanceMonitor.Instance;
    
    public void Log(LogLevel level, string message, string module = null, Exception exception = null)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // 检查是否需要降级
            if (_performanceMonitor.ShouldDegrade())
            {
                var strategy = _performanceMonitor.GetDegradationStrategy();
                if (strategy.DropLowPriorityLogs && level < LogLevel.Warn)
                {
                    return; // 丢弃低优先级日志
                }
            }
            
            // 执行日志记录逻辑
            var entry = CreateLogEntry(level, message, module, exception);
            WriteToWriters(entry);
        }
        finally
        {
            stopwatch.Stop();
            _performanceMonitor.RecordWriteOperation(stopwatch.ElapsedMilliseconds);
        }
    }
}
```

### 在FileLogWriter中集成

```csharp
public class FileLogWriter : ILogWriter
{
    private readonly LogPerformanceMonitor _performanceMonitor = LogPerformanceMonitor.Instance;
    
    private async Task ProcessLogEntriesAsync()
    {
        while (!_cancellationToken.IsCancellationRequested)
        {
            // 更新队列深度
            int queueDepth = _logChannel.Reader.Count;
            _performanceMonitor.UpdateQueueDepth(queueDepth);
            
            // 处理日志条目
            await ProcessBatch();
            
            // 更新内存使用量
            long memoryUsage = GC.GetTotalMemory(false);
            _performanceMonitor.UpdateMemoryUsage(memoryUsage);
        }
    }
}
```

### 性能监控仪表板

```csharp
public class PerformanceDashboard : MonoBehaviour
{
    private LogPerformanceMonitor _monitor;
    
    void Start()
    {
        _monitor = LogPerformanceMonitor.Instance;
        InvokeRepeating(nameof(UpdateMetrics), 0f, 1f);
    }
    
    void UpdateMetrics()
    {
        var metrics = _monitor.GetMetrics();
        
        // 更新UI显示
        UpdateUI(metrics);
        
        // 检查性能状况
        if (_monitor.ShouldDegrade())
        {
            ShowPerformanceWarning(metrics);
        }
    }
    
    void UpdateUI(LogPerformanceMetrics metrics)
    {
        // 更新性能指标显示
        totalLogsText.text = $"总日志数: {metrics.TotalLogsWritten:N0}";
        avgWriteTimeText.text = $"平均写入时间: {metrics.AverageWriteTimeMs:F2}ms";
        memoryUsageText.text = $"内存使用: {metrics.GetReadableMemoryUsage()}";
        throughputText.text = $"吞吐量: {metrics.ThroughputPerSecond:F2} logs/sec";
        queueDepthText.text = $"队列深度: {metrics.QueueDepth}";
        
        // 更新百分位数显示
        p95WriteTimeText.text = $"P95写入时间: {metrics.P95WriteTimeMs:F2}ms";
        p99WriteTimeText.text = $"P99写入时间: {metrics.P99WriteTimeMs:F2}ms";
    }
}
```

## 最佳实践

### 1. 定期监控

```csharp
// 设置定期性能检查
var timer = new Timer(CheckPerformance, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));

void CheckPerformance(object state)
{
    var monitor = LogPerformanceMonitor.Instance;
    var metrics = monitor.GetMetrics();
    
    // 记录性能指标到监控系统
    RecordMetricsToMonitoring(metrics);
    
    // 检查是否需要告警
    if (monitor.ShouldDegrade())
    {
        SendPerformanceAlert(metrics);
    }
}
```

### 2. 自适应性能调优

```csharp
public class AdaptiveLogManager
{
    private readonly LogPerformanceMonitor _monitor = LogPerformanceMonitor.Instance;
    private readonly Timer _optimizationTimer;
    
    public AdaptiveLogManager()
    {
        _optimizationTimer = new Timer(OptimizePerformance, null, 
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }
    
    private void OptimizePerformance(object state)
    {
        if (_monitor.ShouldDegrade())
        {
            var strategy = _monitor.GetDegradationStrategy();
            ApplyOptimizations(strategy);
        }
    }
    
    private void ApplyOptimizations(DegradationStrategy strategy)
    {
        if (strategy.ReduceBufferSize)
        {
            // 动态调整缓冲区大小
            AdjustBufferSizes(0.8f);
        }
        
        if (strategy.IncreaseFlushFrequency)
        {
            // 增加刷新频率
            AdjustFlushInterval(0.5f);
        }
        
        if (strategy.SuggestedLogLevel.HasValue)
        {
            // 临时提升日志级别
            TemporarilyRaiseLogLevel(strategy.SuggestedLogLevel.Value);
        }
    }
}
```

### 3. 性能告警集成

```csharp
// 订阅性能警告事件
eventSystem.Subscribe<LogPerformanceWarningEvent>(evt =>
{
    // 发送告警通知
    var alert = new PerformanceAlert
    {
        Severity = GetSeverity(evt.MetricName),
        Message = $"日志系统性能警告: {evt.MetricName} = {evt.CurrentValue} (阈值: {evt.Threshold})",
        Timestamp = DateTime.Now,
        Source = "LogPerformanceMonitor"
    };
    
    alertingService.SendAlert(alert);
});
```

## 注意事项

1. **内存监控**: 内存监控使用定时器每5秒检查一次，避免过于频繁的GC调用
2. **样本管理**: 写入时间样本队列限制为1000个，自动管理内存使用
3. **线程安全**: 所有公共方法都是线程安全的，使用锁保护共享状态
4. **性能影响**: 监控本身的性能开销很小，不会显著影响日志系统性能
5. **阈值配置**: 根据实际系统负载和硬件配置调整性能阈值

## 依赖项

- `System.Diagnostics`: 性能计时
- `System.Threading`: 线程安全和定时器
- `UnityEngine`: Unity日志输出
- `DigitalHuman.Core`: 事件系统集成

## 相关文档

- [日志系统总体架构](../README.md)
- [LogEntryPool 对象池文档](LogEntryPool.md)
- [FileLogWriter 文件写入器文档](../Writers/FileLogWriter.md)
- [事件系统文档](../../EventSystem.md)