# 日志统计和监控系统

日志统计和监控系统提供了全面的日志系统性能监控、健康检查和数据分析功能。

## 核心组件

### LogStatistics - 统计数据收集器

负责收集和管理日志系统的各种统计信息。

#### 主要功能

- **基础统计**: 总日志数量、按级别统计、按模块统计
- **性能统计**: 平均写入时间、写入失败次数、写入成功率
- **文件统计**: 文件创建数量、写入字节数、轮转次数
- **内存统计**: 峰值内存使用、当前内存使用、队列大小
- **时间统计**: 系统启动时间、最后日志时间、运行时间

#### 使用示例

```csharp
// 获取统计信息
var statistics = logManager.GetLogStatistics();

// 查看基本统计
Debug.Log($"总日志数量: {statistics.TotalLogCount}");
Debug.Log($"错误率: {statistics.GetErrorRate():P2}");
Debug.Log($"每秒日志数量: {statistics.GetLogsPerSecond():F2}");

// 查看按级别统计
Debug.Log($"错误日志数量: {statistics.GetLogCount(LogLevel.Error)}");
Debug.Log($"警告日志数量: {statistics.GetLogCount(LogLevel.Warning)}");

// 查看按模块统计
Debug.Log($"UI模块日志数量: {statistics.GetModuleLogCount("UI")}");

// 查看性能统计
Debug.Log($"平均写入时间: {statistics.AverageWriteTime:F2}ms");
Debug.Log($"写入成功率: {statistics.GetWriteSuccessRate():P2}");
```

### LogHealthChecker - 健康状态检查器

监控日志系统的健康状态并提供诊断建议。

#### 健康级别

- **Healthy**: 系统运行正常
- **Warning**: 存在潜在问题，需要关注
- **Critical**: 存在严重问题，需要立即处理
- **Failed**: 系统故障，无法正常工作

#### 检查项目

1. **错误率检查**: 监控错误和致命错误的比例
2. **写入性能检查**: 监控写入失败率和平均写入时间
3. **内存使用检查**: 监控内存使用情况
4. **队列状态检查**: 监控日志队列积压情况
5. **日志频率检查**: 监控日志产生频率
6. **系统运行时间检查**: 监控系统运行时间

#### 使用示例

```csharp
// 执行健康检查
var healthStatus = logManager.GetHealthStatus();

Debug.Log($"健康状态: {healthStatus.Level}");
Debug.Log($"状态消息: {healthStatus.Message}");

if (!string.IsNullOrEmpty(healthStatus.Recommendation))
{
    Debug.Log($"建议: {healthStatus.Recommendation}");
}

// 查看相关指标
foreach (var metric in healthStatus.Metrics)
{
    Debug.Log($"指标 {metric.Key}: {metric.Value}");
}
```

#### 阈值配置

```csharp
var healthChecker = new LogHealthChecker(statistics);

// 配置健康检查阈值
healthChecker.ConfigureThresholds(
    errorRate: 0.05,           // 最大错误率 5%
    writeFailureRate: 0.02,    // 最大写入失败率 2%
    avgWriteTime: 50,          // 最大平均写入时间 50ms
    memoryUsage: 100 * 1024 * 1024, // 最大内存使用 100MB
    queueSize: 500             // 最大队列大小
);
```

### LogStatisticsPersistence - 统计数据持久化

负责统计数据的保存和加载，支持会话统计和历史数据管理。

#### 主要功能

- **当前会话统计**: 实时保存当前会话的统计数据
- **会话历史**: 保存每次会话结束时的统计快照
- **每日统计**: 按日期汇总统计数据
- **数据清理**: 自动清理过期的统计文件

#### 使用示例

```csharp
// 保存当前统计
await logManager.SaveStatisticsAsync();

// 获取历史统计
var historicalStats = await logManager.GetHistoricalStatisticsAsync(10);
foreach (var snapshot in historicalStats)
{
    Debug.Log($"历史记录 - 时间: {snapshot.snapshotTime}, " +
              $"总日志: {snapshot.totalLogCount}, " +
              $"错误率: {snapshot.errorRate:P2}");
}

// 获取指定日期的统计
var todayStats = await logManager.GetDailyStatisticsAsync(DateTime.Today);
if (todayStats != null)
{
    Debug.Log($"今日统计 - 总日志: {todayStats.totalLogCount}");
}
```

## 集成到LogManager

统计功能已完全集成到LogManager中，提供以下API：

### 统计查询

```csharp
// 获取统计信息
LogStatistics GetLogStatistics();

// 获取健康状态
LogHealthStatus GetHealthStatus();
LogHealthStatus GetLastHealthStatus();
```

### 统计管理

```csharp
// 重置统计数据
void ResetStatistics();

// 保存统计数据
Task SaveStatisticsAsync();
```

### 历史数据

```csharp
// 获取历史统计
Task<LogStatisticsSnapshot[]> GetHistoricalStatisticsAsync(int count = 10);

// 获取每日统计
Task<LogStatisticsSnapshot> GetDailyStatisticsAsync(DateTime date);
```

## 自动化监控

系统提供自动化的统计更新和监控：

- **定期更新**: 每5秒自动更新统计信息
- **内存监控**: 实时监控内存使用情况
- **健康检查**: 定期执行健康状态检查
- **数据持久化**: 自动保存统计数据

## 性能考虑

- **线程安全**: 所有统计操作都是线程安全的
- **低开销**: 统计收集对性能影响最小
- **内存优化**: 使用高效的数据结构和缓存机制
- **异步操作**: 数据持久化使用异步操作，不阻塞主线程

## 配置选项

统计功能可以通过LogConfiguration进行配置：

```csharp
// 在LogConfiguration中添加统计配置
public class LogStatisticsConfiguration
{
    public bool EnableStatistics { get; set; } = true;
    public float UpdateInterval { get; set; } = 5.0f;
    public int MaxHistoryCount { get; set; } = 100;
    public int DataRetentionDays { get; set; } = 30;
}
```

## 故障排除

### 常见问题

1. **统计数据不更新**
   - 检查LogManager是否正确初始化
   - 确认统计功能已启用

2. **健康检查显示警告**
   - 查看具体的警告消息和建议
   - 根据建议调整系统配置

3. **历史数据丢失**
   - 检查持久化目录权限
   - 确认磁盘空间充足

### 调试技巧

```csharp
// 启用详细的统计日志
logManager.SetLogLevel(LogLevel.Debug, "LogStatistics");
logManager.SetLogLevel(LogLevel.Debug, "LogHealthChecker");
logManager.SetLogLevel(LogLevel.Debug, "LogStatisticsPersistence");
```

## 最佳实践

1. **定期监控**: 定期检查健康状态和统计数据
2. **阈值调整**: 根据实际使用情况调整健康检查阈值
3. **数据分析**: 利用历史数据分析系统性能趋势
4. **及时响应**: 对健康检查警告及时采取措施
5. **资源管理**: 定期清理过期的统计数据

## 扩展性

系统设计支持扩展：

- **自定义指标**: 可以添加自定义的统计指标
- **自定义检查**: 可以添加自定义的健康检查项
- **自定义存储**: 可以实现自定义的数据持久化方式
- **自定义报告**: 可以生成自定义格式的统计报告