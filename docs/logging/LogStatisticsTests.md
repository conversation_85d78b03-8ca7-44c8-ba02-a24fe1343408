# LogStatisticsTests 日志统计测试文档

## 概述

`LogStatisticsTests` 是日志系统统计功能的综合测试类，用于验证日志统计数据收集、健康检查和持久化功能的正确性。该测试类提供了完整的测试套件，包括基础统计、健康检查、持久化和压力测试等功能。

## 命名空间

```csharp
DigitalHuman.Core.Logging.Tests
```

## 类定义

### LogStatisticsTests

日志统计功能测试类，继承自 `MonoBehaviour`，提供完整的统计功能测试套件。

#### 属性

##### runTestsOnStart

```csharp
[SerializeField] private bool runTestsOnStart = true;
```

**描述：** 是否在启动时自动运行测试

**类型：** `bool`

**默认值：** `true`

#### 私有字段

- `_logManager` (ILogManager): 日志管理器实例
- `_logger` (ILogger): 测试专用日志记录器

## 公共方法

### RunAllTests

运行所有统计功能测试的主方法。

```csharp
[ContextMenu("运行所有测试")]
public void RunAllTests()
```

**功能：**
- 初始化测试环境
- 重置统计数据确保测试环境干净
- 依次执行各项测试功能
- 输出测试结果

**使用示例：**
```csharp
// 在Unity编辑器中右键点击组件选择"运行所有测试"
// 或者通过代码调用
var testComponent = GetComponent<LogStatisticsTests>();
testComponent.RunAllTests();
```

### TestStatisticsReset

测试统计重置功能。

```csharp
[ContextMenu("测试统计重置")]
public void TestStatisticsReset()
```

**功能：**
- 生成测试日志数据
- 执行统计重置操作
- 验证重置后统计数据是否清零
- 输出验证结果

**验证项目：**
- 重置前后日志计数对比
- 重置操作的有效性

**使用示例：**
```csharp
// 通过上下文菜单调用
// 或者代码调用
testComponent.TestStatisticsReset();
```

### StressTest

执行统计功能的压力测试。

```csharp
[ContextMenu("压力测试")]
public void StressTest()
```

**功能：**
- 生成大量测试日志（默认1000条）
- 测试统计系统在高负载下的性能
- 验证统计数据的准确性
- 计算性能指标

**性能指标：**
- 总测试时间
- 平均处理时间
- 每秒处理能力
- 统计准确性验证

**使用示例：**
```csharp
// 执行压力测试
testComponent.StressTest();

// 输出示例：
// ✓ 压力测试完成
//   测试数量: 1000
//   实际统计: 1000
//   测试耗时: 45.67ms
//   平均耗时: 0.0457ms/条
//   每秒处理: 21897条/秒
```

## 私有测试方法

### TestBasicStatistics

测试基础统计功能的私有方法。

```csharp
private void TestBasicStatistics()
```

**测试项目：**
- 总日志数量统计
- 按级别分类统计（Debug、Info、Warn、Error）
- 按模块分类统计
- 错误率计算
- 每秒日志数量计算

**验证逻辑：**
```csharp
// 生成测试日志
_logger.LogDebug("测试调试日志");
_logger.LogInfo("测试信息日志");
_logger.LogWarn("测试警告日志");
_logger.LogError("测试错误日志");

// 验证统计数据
var statistics = _logManager.GetLogStatistics();
var expectedCount = initialCount + 4;

// 验证总数
if (statistics.TotalLogCount == expectedCount)
{
    Debug.Log("✓ 总日志数量统计正确");
}

// 验证按级别统计
if (statistics.GetLogCount(LogLevel.Debug) >= 1)
{
    Debug.Log("✓ 调试日志统计正确");
}
```

### TestHealthChecker

测试健康检查功能的私有方法。

```csharp
private void TestHealthChecker()
```

**测试项目：**
- 健康检查执行
- 健康状态获取
- 检查时间记录
- 健康指标收集
- 最后健康状态获取

**验证逻辑：**
```csharp
// 执行健康检查
var healthStatus = _logManager.GetHealthStatus();

if (healthStatus != null)
{
    Debug.Log("✓ 健康检查执行成功");
    Debug.Log($"  健康级别: {healthStatus.Level}");
    Debug.Log($"  状态消息: {healthStatus.Message}");
    
    // 验证检查时间
    if (healthStatus.CheckTime > DateTime.MinValue)
    {
        Debug.Log("✓ 检查时间记录正确");
    }
    
    // 验证健康指标
    if (healthStatus.Metrics != null && healthStatus.Metrics.Count > 0)
    {
        Debug.Log("✓ 健康指标收集正确");
        foreach (var metric in healthStatus.Metrics)
        {
            Debug.Log($"  指标 {metric.Key}: {metric.Value}");
        }
    }
}
```

### TestPersistence

测试持久化功能的异步私有方法。

```csharp
private async Task TestPersistence()
```

**测试项目：**
- 当前统计数据保存
- 历史统计数据获取
- 每日统计数据获取
- 异步操作处理

**验证逻辑：**
```csharp
try
{
    // 测试保存当前统计
    await _logManager.SaveStatisticsAsync();
    Debug.Log("✓ 保存当前统计成功");
    
    // 测试获取历史统计
    var historicalStats = await _logManager.GetHistoricalStatisticsAsync(5);
    if (historicalStats != null)
    {
        Debug.Log($"✓ 获取历史统计成功，共 {historicalStats.Length} 条记录");
        
        foreach (var snapshot in historicalStats)
        {
            Debug.Log($"  历史记录: {snapshot.snapshotTime}, 总日志: {snapshot.totalLogCount}");
        }
    }
    
    // 测试获取每日统计
    var todayStats = await _logManager.GetDailyStatisticsAsync(DateTime.Today);
    if (todayStats != null)
    {
        Debug.Log($"✓ 获取今日统计成功，总日志: {todayStats.totalLogCount}");
    }
}
catch (Exception ex)
{
    Debug.LogError($"✗ 持久化功能测试失败: {ex.Message}");
}
```

## 测试结果解读

### 成功标识

- `✓` - 测试通过
- `ℹ` - 信息提示（正常情况）
- `⚠` - 警告（可能的问题但不影响功能）

### 失败标识

- `✗` - 测试失败，需要检查相关功能

### 测试输出示例

```
=== 开始日志统计功能测试 ===
--- 测试基础统计功能 ---
✓ 总日志数量统计正确
✓ 调试日志统计正确
✓ 信息日志统计正确
✓ 警告日志统计正确
✓ 错误日志统计正确
✓ 模块日志统计正确
✓ 错误率计算正确: 25.00%
✓ 每秒日志数量计算正确: 156.78
--- 基础统计功能测试完成 ---

--- 测试健康检查功能 ---
✓ 健康检查执行成功
  健康级别: Healthy
  状态消息: 系统运行正常
✓ 检查时间记录正确
✓ 健康指标收集正确
  指标 TotalLogCount: 1234
  指标 ErrorRate: 0.05
  指标 MemoryUsage: 45.6MB
✓ 获取最后健康状态成功
--- 健康检查功能测试完成 ---

--- 测试持久化功能 ---
✓ 保存当前统计成功
✓ 获取历史统计成功，共 3 条记录
  历史记录: 2025-01-24 14:30:22, 总日志: 1234
  历史记录: 2025-01-24 14:25:15, 总日志: 1156
  历史记录: 2025-01-24 14:20:08, 总日志: 1089
✓ 获取今日统计成功，总日志: 1234
--- 持久化功能测试完成 ---

=== 日志统计功能测试完成 ===
```

## 使用指南

### 在Unity编辑器中使用

1. **添加测试组件**
   ```csharp
   // 将LogStatisticsTests组件添加到GameObject
   var testObject = new GameObject("LogStatisticsTests");
   var testComponent = testObject.AddComponent<LogStatisticsTests>();
   ```

2. **配置测试参数**
   - 在Inspector中设置 `runTestsOnStart` 决定是否自动运行
   - 可以通过上下文菜单手动执行特定测试

3. **查看测试结果**
   - 测试结果会输出到Unity Console
   - 使用过滤器查看特定类型的测试结果

### 在代码中使用

```csharp
public class LoggingSystemValidator : MonoBehaviour
{
    private LogStatisticsTests _statisticsTests;
    
    void Start()
    {
        _statisticsTests = GetComponent<LogStatisticsTests>();
        
        // 运行完整测试套件
        _statisticsTests.RunAllTests();
        
        // 或者运行特定测试
        _statisticsTests.TestStatisticsReset();
        _statisticsTests.StressTest();
    }
}
```

### 自动化测试集成

```csharp
public class AutomatedTestRunner : MonoBehaviour
{
    [SerializeField] private LogStatisticsTests statisticsTests;
    [SerializeField] private float testInterval = 300f; // 5分钟
    
    void Start()
    {
        // 定期运行统计测试
        InvokeRepeating(nameof(RunPeriodicTests), 0f, testInterval);
    }
    
    private void RunPeriodicTests()
    {
        Debug.Log("开始定期统计功能测试");
        statisticsTests.RunAllTests();
    }
}
```

## 测试最佳实践

### 1. 测试环境准备

```csharp
// 在测试前重置环境
_logManager.ResetStatistics();

// 确保日志系统启用
_logManager.IsLoggingEnabled = true;

// 设置适当的日志级别
_logManager.SetLogLevel(LogLevel.Debug);
```

### 2. 测试数据隔离

```csharp
// 使用专用的测试模块名
var testLogger = _logManager.GetLogger("TestModule");

// 避免与生产日志混合
var testStartTime = DateTime.Now;
// 执行测试...
var testEndTime = DateTime.Now;
```

### 3. 异常处理

```csharp
try
{
    // 执行测试逻辑
    await TestPersistence();
}
catch (Exception ex)
{
    Debug.LogError($"测试执行失败: {ex.Message}");
    // 记录详细的错误信息用于调试
    Debug.LogError($"堆栈跟踪: {ex.StackTrace}");
}
```

### 4. 性能测试注意事项

```csharp
// 压力测试时监控系统资源
var initialMemory = GC.GetTotalMemory(false);
StressTest();
var finalMemory = GC.GetTotalMemory(true);

Debug.Log($"内存使用变化: {(finalMemory - initialMemory) / 1024 / 1024:F2} MB");
```

## 故障排除

### 常见问题

1. **统计数据不准确**
   - 检查日志级别设置
   - 确认日志系统已启用
   - 验证测试环境是否干净

2. **健康检查失败**
   - 检查健康检查器是否正确初始化
   - 验证统计数据是否正常收集
   - 查看详细的错误信息

3. **持久化测试失败**
   - 检查文件系统权限
   - 确认存储路径可访问
   - 验证异步操作是否正确等待

4. **压力测试性能差**
   - 检查系统资源使用情况
   - 调整测试数据量
   - 验证日志写入器配置

### 调试技巧

```csharp
// 启用详细日志输出
_logManager.SetLogLevel(LogLevel.Trace, "StatisticsTests");

// 监控测试过程中的统计变化
var statistics = _logManager.GetLogStatistics();
Debug.Log($"测试前统计: {statistics.TotalLogCount}");

// 执行测试...

statistics = _logManager.GetLogStatistics();
Debug.Log($"测试后统计: {statistics.TotalLogCount}");
```

## 扩展开发

### 添加自定义测试

```csharp
/// <summary>
/// 测试自定义统计功能
/// </summary>
private void TestCustomStatistics()
{
    Debug.Log("--- 测试自定义统计功能 ---");
    
    // 实现自定义测试逻辑
    var customMetric = CalculateCustomMetric();
    
    if (ValidateCustomMetric(customMetric))
    {
        Debug.Log("✓ 自定义统计功能正常");
    }
    else
    {
        Debug.LogError("✗ 自定义统计功能异常");
    }
    
    Debug.Log("--- 自定义统计功能测试完成 ---");
}
```

### 集成外部测试框架

```csharp
#if UNITY_EDITOR
using NUnit.Framework;

[TestFixture]
public class LogStatisticsNUnitTests
{
    private LogStatisticsTests _statisticsTests;
    
    [SetUp]
    public void Setup()
    {
        var testObject = new GameObject("LogStatisticsTests");
        _statisticsTests = testObject.AddComponent<LogStatisticsTests>();
    }
    
    [Test]
    public void TestBasicStatisticsNUnit()
    {
        // 使用NUnit断言
        Assert.DoesNotThrow(() => _statisticsTests.RunAllTests());
    }
    
    [TearDown]
    public void Teardown()
    {
        if (_statisticsTests != null)
        {
            Object.DestroyImmediate(_statisticsTests.gameObject);
        }
    }
}
#endif
```

## 依赖项

- `UnityEngine`: Unity引擎核心功能
- `System.Threading.Tasks`: 异步操作支持
- `DigitalHuman.Core.Logging`: 日志系统核心
- `DigitalHuman.Core.Logging.Statistics`: 统计功能组件

## 相关文档

- [LogStatistics 统计数据收集器文档](LogStatistics.md)
- [LogHealthChecker 健康检查器文档](LogHealthChecker.md)
- [LogStatisticsPersistence 持久化文档](LogStatisticsPersistence.md)
- [LogManager 日志管理器文档](LogManager.md)
- [日志系统总体架构](../README.md)