# 数字人对话系统 - 项目概览

## 📋 项目简介

这是一个基于 Unity 开发的跨平台数字人对话程序，支持 Windows 和 Mac 系统，能够与大模型 API 集成，提供语音交互和摄像头视觉交互功能。

## 🏗️ 项目架构

### 核心模块

```
Assets/Scripts/Core/
├── Configuration/          # 配置管理系统
│   ├── ConfigurationManager.cs      # 配置管理器（单例）
│   ├── ConfigurationSerializer.cs   # 配置序列化工具
│   └── Models/                      # 配置数据模型
│       ├── UserConfiguration.cs     # 用户配置
│       ├── ServiceConfiguration.cs  # 服务配置
│       ├── SecurityConfiguration.cs # 安全配置
│       └── ValidationResult.cs      # 验证结果
├── Database/               # 数据库模块
│   └── SQLiteDatabase.cs           # SQLite数据库封装
├── EventSystem.cs          # 事件系统
├── JsonUtility.cs          # JSON工具类
├── IManager.cs             # 管理器接口
├── ManagerBase.cs          # 管理器基类
└── SingletonManager.cs     # 单例管理器基类
```

### UI 模块

```
Assets/Scripts/UI/Settings/
├── SettingsUIManager.cs    # 设置界面管理器
├── SettingsPanelBase.cs    # 设置面板基类
└── Panels/                 # 具体设置面板
    ├── GeneralSettingsPanel.cs      # 通用设置
    ├── APISettingsPanel.cs          # API设置
    ├── VoiceSettingsPanel.cs        # 语音设置
    ├── DigitalHumanSettingsPanel.cs # 数字人设置
    ├── LanguageSettingsPanel.cs     # 语言设置
    ├── DisplaySettingsPanel.cs      # 显示设置
    └── SecuritySettingsPanel.cs     # 安全设置
```

### UI 资源

```
Assets/UI/Settings/
├── SettingsUI.uxml         # 设置界面布局
└── SettingsUI.uss          # 设置界面样式
```

### 测试模块

```
Assets/Tests/Editor/
├── ConfigurationManagerTests.cs     # 配置管理器测试
├── ConfigurationSerializerTests.cs  # 序列化工具测试
├── SettingsUIManagerTests.cs        # 设置界面测试
└── [其他核心组件测试...]
```

## ✨ 已实现功能

### 🔧 配置管理系统

- **ConfigurationManager**: 单例配置管理器

  - AES-256 加密存储敏感配置
  - 支持用户、服务、安全三类配置
  - 配置验证和默认值处理
  - 配置导入导出功能

- **数据模型**:

  - **UserConfiguration**: 用户个人偏好设置
  - **ServiceConfiguration**: 外部 API 服务配置
  - **SecurityConfiguration**: 安全相关配置

- **序列化工具**:
  - JSON 序列化和反序列化
  - 配置文件读写
  - 数据加密和解密

### 🎨 设置界面系统

- **SettingsUIManager**: 设置界面管理器

  - 多标签页界面
  - 实时配置预览
  - 配置验证提示

- **设置面板**:
  - 通用设置：用户信息、数据管理
  - API 设置：大模型、TTS、ASR 配置
  - 语音设置：音色、速度、音量
  - 数字人设置：渲染模式、外观
  - 语言设置：控制词、多语言
  - 显示设置：分辨率、界面选项
  - 安全设置：设备激活、加密、审计

### 🧪 测试系统

- 完整的单元测试覆盖
- 配置管理功能测试
- UI 组件测试
- 集成测试支持

## 🎯 技术特性

### 安全性

- AES-256 加密存储敏感配置
- 设备激活和授权管理
- 审计日志记录
- 数据完整性校验

### 可扩展性

- 模块化设计架构
- 插件式设置面板
- 事件驱动系统
- 依赖注入模式

### 用户体验

- 直观的设置界面
- 实时配置预览
- 配置验证和错误提示
- 多语言支持

### 开发友好

- 完整的单元测试
- 详细的中文注释
- 标准化的代码结构
- 便捷的测试工具

## 📊 项目状态

### ✅ 已完成 (100%)

- 配置管理核心功能
- 数据加密存储
- 配置验证机制
- 序列化工具
- 单元测试框架

### ⚠️ 需要 Unity 环境测试 (90%)

- 设置界面 UI
- 用户交互功能

### 🔄 待开发功能

- 大模型 API 集成
- 语音识别和合成
- 数字人渲染
- 摄像头交互
- 网络通信模块

## 🚀 快速开始

### 环境要求

- Unity 2022.3 LTS 或更新版本
- macOS 10.15+ 或 Windows 10+
- .NET Framework 4.8+

### 安装步骤

1. 安装 Unity Hub 和 Unity 编辑器
2. 克隆项目到本地
3. 在 Unity 中打开项目
4. 运行单元测试验证功能
5. 测试设置界面

### 测试验证

```bash
# 运行环境检查
./verify_environment.sh

# 在Unity中运行测试
Window > General > Test Runner
```

## 📚 相关文档

- [Mac 环境安装指南](./Mac环境安装指南.md)
- [需求文档](./.kiro/specs/digital-human-chat/requirements.md)
- [设计文档](./.kiro/specs/digital-human-chat/design.md)
- [任务列表](./.kiro/specs/digital-human-chat/tasks.md)
- [测试总结](../TESTING_SUMMARY.md)

## 🤝 贡献指南

### 代码规范

- 所有注释使用中文
- 遵循 C#命名约定
- 每个功能必须有对应测试
- 提交前运行所有测试

### 开发流程

1. 创建功能分支
2. 实现功能和测试
3. 运行测试验证
4. 提交代码审查
5. 合并到主分支

## 📞 支持

如有问题，请查看：

1. 项目文档
2. 单元测试示例
3. Unity Console 错误信息
4. 环境安装指南
