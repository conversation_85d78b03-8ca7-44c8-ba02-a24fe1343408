# BuildScript 构建脚本文档

## 概述

`BuildScript` 是数字人对话系统的自动化构建工具，提供了多种构建选项以支持不同的开发和部署需求。该脚本基于Unity Editor构建，支持MVP版本、认证版本等多种构建配置。

## 类签名

```csharp
namespace DigitalHuman.Editor
{
    public class BuildScript
}
```

## 核心功能

### 1. 多版本构建支持
- **MVP版本**: 最小可行产品版本，用于快速验证和演示
- **认证版本**: 包含完整用户认证功能的版本
- **快速测试**: 仅验证编译状态，不生成实际构建文件

### 2. 自动化配置管理
- **播放器设置**: 自动配置应用名称、版本号、标识符等
- **构建选项**: 优化设置、代码剥离、分辨率配置
- **目录管理**: 自动创建和管理构建输出目录

### 3. 错误处理和日志
- **详细日志**: 完整的构建过程日志记录
- **错误报告**: 构建失败时的详细错误信息
- **结果展示**: 构建成功后自动在Finder中显示结果

## 主要方法

### 构建方法

#### `BuildMinimalMVP()`
```csharp
[MenuItem("DigitalHuman/Build/Build Minimal MVP")]
public static void BuildMinimalMVP()
```

**功能**: 构建最小MVP版本
- **场景**: `Assets/Scenes/MinimalMVPScene.unity`
- **输出路径**: `Builds/MinimalMVP/DigitalHuman_MVP.app`
- **配置**: 使用 `ConfigurePlayerSettings()` 配置

**构建流程**:
1. 设置构建路径和场景列表
2. 创建构建目录（如果不存在）
3. 配置构建选项和播放器设置
4. 执行构建并检查结果
5. 显示构建结果或错误信息

**使用示例**:
```csharp
// 通过Unity菜单调用
// DigitalHuman -> Build -> Build Minimal MVP

// 或者通过代码调用
BuildScript.BuildMinimalMVP();
```

#### `BuildWithAuthentication()`
```csharp
[MenuItem("DigitalHuman/Build/Build With Authentication")]
public static void BuildWithAuthentication()
```

**功能**: 构建包含认证功能的完整版本
- **场景**: `Assets/Scenes/MainScene.unity`
- **输出路径**: `Builds/WithAuthentication/DigitalHuman_WithAuth.app`
- **配置**: 使用 `ConfigureAuthPlayerSettings()` 配置

**特性**:
- 包含完整的用户认证系统
- 支持登录/登出功能
- 集成MainUIManager认证界面
- 优化的播放器设置

**使用示例**:
```csharp
// 通过Unity菜单调用
// DigitalHuman -> Build -> Build With Authentication

// 或者通过代码调用
BuildScript.BuildWithAuthentication();
```

#### `QuickBuildTest()`
```csharp
[MenuItem("DigitalHuman/Build/Quick Build Test")]
public static void QuickBuildTest()
```

**功能**: 快速构建测试，仅验证编译状态
- **用途**: 验证项目是否可以正常编译
- **优势**: 快速执行，不生成实际文件
- **检查项**: 场景配置、脚本编译状态

**验证流程**:
1. 检查构建设置中的场景配置
2. 验证脚本编译状态
3. 输出测试结果

#### `BuildMacOS()`
```csharp
public static void BuildMacOS()
```

**功能**: 命令行构建接口
- **用途**: 支持CI/CD自动化构建
- **实现**: 调用 `BuildWithAuthentication()` 方法
- **适用场景**: 自动化部署、批处理构建

**命令行使用**:
```bash
# 通过Unity命令行调用
/Applications/Unity/Hub/Editor/2022.3.x/Unity.app/Contents/MacOS/Unity \
    -batchmode \
    -quit \
    -projectPath . \
    -executeMethod BuildScript.BuildMacOS \
    -logFile build.log
```

### 配置方法

#### `ConfigurePlayerSettings()`
```csharp
private static void ConfigurePlayerSettings()
```

**功能**: 配置MVP版本的播放器设置

**配置项**:
- **产品名称**: "数字人管理系统 MVP"
- **公司名称**: "DigitalHuman Team"
- **版本号**: "1.0.0-MVP"
- **应用标识符**: "com.digitalhuman.mvp"
- **分辨率**: 原生分辨率，支持后台运行
- **优化**: 代码剥离，中等管理代码剥离级别

**详细配置**:
```csharp
// 基本信息
PlayerSettings.productName = "数字人管理系统 MVP";
PlayerSettings.companyName = "DigitalHuman Team";
PlayerSettings.bundleVersion = "1.0.0-MVP";

// 平台设置
PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.mvp");

// 显示设置
PlayerSettings.defaultIsNativeResolution = true;
PlayerSettings.runInBackground = true;

// 优化设置
PlayerSettings.stripEngineCode = true;
PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Medium);
```

#### `ConfigureAuthPlayerSettings()`
```csharp
private static void ConfigureAuthPlayerSettings()
```

**功能**: 配置认证版本的播放器设置

**配置项**:
- **产品名称**: "数字人对话系统 - 认证版"
- **公司名称**: "DigitalHuman Team"
- **版本号**: "1.1.0-Auth"
- **应用标识符**: "com.digitalhuman.auth"
- **优化设置**: 与MVP版本相同的优化配置

**版本差异**:
```csharp
// MVP版本
PlayerSettings.productName = "数字人管理系统 MVP";
PlayerSettings.bundleVersion = "1.0.0-MVP";
PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.mvp");

// 认证版本
PlayerSettings.productName = "数字人对话系统 - 认证版";
PlayerSettings.bundleVersion = "1.1.0-Auth";
PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.auth");
```

### 工具方法

#### `CleanBuildFiles()`
```csharp
[MenuItem("DigitalHuman/Build/Clean Build Files")]
public static void CleanBuildFiles()
```

**功能**: 清理所有构建文件
- **清理目录**: `Builds/` 整个目录
- **安全检查**: 检查目录是否存在
- **日志记录**: 记录清理操作结果

**使用场景**:
- 释放磁盘空间
- 清理旧版本构建文件
- 重新开始干净的构建

## 构建配置对比

### MVP版本 vs 认证版本

| 配置项 | MVP版本 | 认证版本 |
|--------|---------|----------|
| 产品名称 | 数字人管理系统 MVP | 数字人对话系统 - 认证版 |
| 版本号 | 1.0.0-MVP | 1.1.0-Auth |
| 应用标识符 | com.digitalhuman.mvp | com.digitalhuman.auth |
| 主场景 | MinimalMVPScene.unity | MainScene.unity |
| 功能范围 | 基础功能演示 | 完整认证系统 |
| 目标用户 | 开发测试 | 生产使用 |

### 构建输出结构

```
Builds/
├── MinimalMVP/
│   └── DigitalHuman_MVP.app
└── WithAuthentication/
    └── DigitalHuman_WithAuth.app
```

## 使用指南

### 通过Unity编辑器使用

1. **打开Unity编辑器**
2. **选择菜单项**:
   - `DigitalHuman -> Build -> Build Minimal MVP` - 构建MVP版本
   - `DigitalHuman -> Build -> Build With Authentication` - 构建认证版本
   - `DigitalHuman -> Build -> Quick Build Test` - 快速测试
   - `DigitalHuman -> Build -> Clean Build Files` - 清理构建文件

3. **查看构建结果**:
   - 构建成功后会自动在Finder中显示结果
   - 查看Unity Console获取详细日志

### 通过脚本调用

```csharp
using DigitalHuman.Editor;

public class BuildAutomation
{
    public static void AutoBuild()
    {
        // 清理旧文件
        BuildScript.CleanBuildFiles();
        
        // 快速测试
        BuildScript.QuickBuildTest();
        
        // 构建MVP版本
        BuildScript.BuildMinimalMVP();
        
        // 构建认证版本
        BuildScript.BuildWithAuthentication();
    }
}
```

### 通过命令行使用

```bash
#!/bin/bash

# 设置Unity路径
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
PROJECT_PATH="."

# 构建认证版本
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "$PROJECT_PATH" \
    -executeMethod BuildScript.BuildMacOS \
    -logFile build_auth.log

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "构建成功"
    open "./Builds/WithAuthentication/DigitalHuman_WithAuth.app"
else
    echo "构建失败，查看日志: build_auth.log"
fi
```

## 错误处理

### 常见错误和解决方案

#### 1. 场景文件缺失
```
错误: 没有场景添加到构建设置中
解决: 确保场景文件存在并添加到Build Settings
```

#### 2. 脚本编译错误
```
错误: 脚本正在编译中，请等待完成
解决: 等待Unity完成脚本编译后重试
```

#### 3. 构建目录权限问题
```
错误: 无法创建构建目录
解决: 检查文件系统权限，确保有写入权限
```

#### 4. Unity版本兼容性
```
错误: PlayerSettings.macOS.buildNumber 不可用
解决: 注释掉不兼容的设置，或升级Unity版本
```

### 调试技巧

#### 1. 启用详细日志
```csharp
// 在构建方法开始添加
Debug.Log($"[BuildScript] Unity版本: {Application.unityVersion}");
Debug.Log($"[BuildScript] 平台: {Application.platform}");
Debug.Log($"[BuildScript] 数据路径: {Application.dataPath}");
```

#### 2. 检查构建报告
```csharp
// 分析构建报告
var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
Debug.Log($"构建时间: {report.summary.buildEndedAt - report.summary.buildStartedAt}");
Debug.Log($"总资源数: {report.summary.totalSize}");
Debug.Log($"构建步骤数: {report.steps.Length}");
```

#### 3. 验证文件完整性
```csharp
// 构建完成后验证
if (File.Exists(buildPath))
{
    var fileInfo = new FileInfo(buildPath);
    Debug.Log($"构建文件大小: {fileInfo.Length} bytes");
    Debug.Log($"创建时间: {fileInfo.CreationTime}");
}
```

## 性能优化

### 构建速度优化

1. **增量构建**: 仅构建变更的资源
2. **并行处理**: 利用多核CPU加速构建
3. **缓存机制**: 复用之前的构建结果

```csharp
// 优化构建选项
BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
{
    scenes = scenes,
    locationPathName = buildPath,
    target = BuildTarget.StandaloneOSX,
    options = BuildOptions.None | BuildOptions.CompressWithLz4HC // 使用LZ4压缩
};
```

### 输出文件优化

1. **代码剥离**: 移除未使用的代码
2. **资源压缩**: 压缩纹理和音频资源
3. **管理代码优化**: 使用适当的剥离级别

```csharp
// 优化设置
PlayerSettings.stripEngineCode = true;
PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Medium);
PlayerSettings.SetScriptingBackend(BuildTargetGroup.Standalone, ScriptingImplementation.IL2CPP);
```

## 扩展功能

### 添加新的构建配置

```csharp
/// <summary>
/// 构建调试版本
/// </summary>
[MenuItem("DigitalHuman/Build/Build Debug Version")]
public static void BuildDebugVersion()
{
    Debug.Log("[BuildScript] 开始构建调试版本...");
    
    // 调试版本特定配置
    string buildPath = Path.Combine(Application.dataPath, "../Builds/Debug/DigitalHuman_Debug.app");
    string[] scenes = { "Assets/Scenes/DebugScene.unity" };
    
    BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
    {
        scenes = scenes,
        locationPathName = buildPath,
        target = BuildTarget.StandaloneOSX,
        options = BuildOptions.Development | BuildOptions.AllowDebugging
    };
    
    ConfigureDebugPlayerSettings();
    
    var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
    // ... 处理构建结果
}

private static void ConfigureDebugPlayerSettings()
{
    PlayerSettings.productName = "数字人对话系统 - 调试版";
    PlayerSettings.bundleVersion = "1.0.0-Debug";
    PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.debug");
    
    // 调试版本不进行代码剥离
    PlayerSettings.stripEngineCode = false;
    PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Disabled);
}
```

### 集成CI/CD流水线

```csharp
/// <summary>
/// CI/CD构建接口
/// </summary>
public static void BuildForCI()
{
    string buildType = System.Environment.GetEnvironmentVariable("BUILD_TYPE") ?? "auth";
    
    switch (buildType.ToLower())
    {
        case "mvp":
            BuildMinimalMVP();
            break;
        case "auth":
            BuildWithAuthentication();
            break;
        case "debug":
            BuildDebugVersion();
            break;
        default:
            Debug.LogError($"未知的构建类型: {buildType}");
            break;
    }
}
```

## 最佳实践

### 1. 版本管理
- 使用语义化版本号 (Semantic Versioning)
- 在版本号中包含构建类型标识
- 记录每个版本的功能变更

### 2. 构建验证
- 构建前进行快速测试
- 验证关键场景和资源
- 检查构建输出的完整性

### 3. 自动化流程
- 集成到CI/CD流水线
- 自动化测试和部署
- 构建结果通知机制

### 4. 错误处理
- 提供详细的错误信息
- 实现构建重试机制
- 记录构建历史和统计

## 相关文档

- [Unity构建系统文档](https://docs.unity3d.com/Manual/BuildPlayer.html)
- [PlayerSettings API参考](https://docs.unity3d.com/ScriptReference/PlayerSettings.html)
- [BuildPipeline API参考](https://docs.unity3d.com/ScriptReference/BuildPipeline.html)
- [项目构建指南](../build/README.md)

## 更新日志

### v1.0.0
- 初始版本，支持MVP构建
- 基础播放器设置配置
- 快速构建测试功能

### v1.1.0
- 新增认证版本构建支持
- 添加专用的认证版本配置
- 支持命令行构建接口
- 完善错误处理和日志记录

### v1.2.0 (计划中)
- 支持多平台构建 (Windows, Linux)
- 集成自动化测试
- 构建性能优化
- 增强的CI/CD集成