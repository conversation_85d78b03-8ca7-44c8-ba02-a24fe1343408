# BuildScript API 参考

## 类定义

```csharp
namespace DigitalHuman.Editor
{
    public class BuildScript
}
```

## 公共方法

### 构建方法

#### `BuildMinimalMVP()`
```csharp
[MenuItem("DigitalHuman/Build/Build Minimal MVP")]
public static void BuildMinimalMVP()
```
**功能**: 构建最小MVP版本  
**菜单路径**: `DigitalHuman -> Build -> Build Minimal MVP`  
**输出**: `Builds/MinimalMVP/DigitalHuman_MVP.app`  
**场景**: `Assets/Scenes/MinimalMVPScene.unity`

#### `BuildWithAuthentication()`
```csharp
[MenuItem("DigitalHuman/Build/Build With Authentication")]
public static void BuildWithAuthentication()
```
**功能**: 构建包含认证功能的完整版本  
**菜单路径**: `DigitalHuman -> Build -> Build With Authentication`  
**输出**: `Builds/WithAuthentication/DigitalHuman_WithAuth.app`  
**场景**: `Assets/Scenes/MainScene.unity`

#### `QuickBuildTest()`
```csharp
[MenuItem("DigitalHuman/Build/Quick Build Test")]
public static void QuickBuildTest()
```
**功能**: 快速构建测试，仅验证编译状态  
**菜单路径**: `DigitalHuman -> Build -> Quick Build Test`  
**输出**: 无文件输出，仅控制台日志

#### `BuildMacOS()`
```csharp
public static void BuildMacOS()
```
**功能**: 命令行构建接口  
**用途**: CI/CD自动化构建  
**实现**: 调用 `BuildWithAuthentication()`

#### `CleanBuildFiles()`
```csharp
[MenuItem("DigitalHuman/Build/Clean Build Files")]
public static void CleanBuildFiles()
```
**功能**: 清理所有构建文件  
**菜单路径**: `DigitalHuman -> Build -> Clean Build Files`  
**操作**: 删除整个 `Builds/` 目录

## 私有方法

### 配置方法

#### `ConfigurePlayerSettings()`
```csharp
private static void ConfigurePlayerSettings()
```
**功能**: 配置MVP版本的播放器设置  
**配置项**:
- 产品名称: "数字人管理系统 MVP"
- 版本号: "1.0.0-MVP"
- 应用标识符: "com.digitalhuman.mvp"

#### `ConfigureAuthPlayerSettings()`
```csharp
private static void ConfigureAuthPlayerSettings()
```
**功能**: 配置认证版本的播放器设置  
**配置项**:
- 产品名称: "数字人对话系统 - 认证版"
- 版本号: "1.1.0-Auth"
- 应用标识符: "com.digitalhuman.auth"

## 使用示例

### 基本构建操作
```csharp
// 构建MVP版本
BuildScript.BuildMinimalMVP();

// 构建认证版本
BuildScript.BuildWithAuthentication();

// 快速测试
BuildScript.QuickBuildTest();

// 清理构建文件
BuildScript.CleanBuildFiles();
```

### 自动化构建流程
```csharp
public class AutoBuild
{
    public static void BuildAll()
    {
        // 1. 清理旧文件
        BuildScript.CleanBuildFiles();
        
        // 2. 快速测试
        BuildScript.QuickBuildTest();
        
        // 3. 构建所有版本
        BuildScript.BuildMinimalMVP();
        BuildScript.BuildWithAuthentication();
    }
}
```

### 命令行调用
```bash
# 构建认证版本
/Applications/Unity/Hub/Editor/2022.3.x/Unity.app/Contents/MacOS/Unity \
    -batchmode \
    -quit \
    -projectPath . \
    -executeMethod BuildScript.BuildMacOS \
    -logFile build.log
```

## 构建配置

### MVP版本配置
```csharp
PlayerSettings.productName = "数字人管理系统 MVP";
PlayerSettings.companyName = "DigitalHuman Team";
PlayerSettings.bundleVersion = "1.0.0-MVP";
PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.mvp");
```

### 认证版本配置
```csharp
PlayerSettings.productName = "数字人对话系统 - 认证版";
PlayerSettings.companyName = "DigitalHuman Team";
PlayerSettings.bundleVersion = "1.1.0-Auth";
PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.auth");
```

### 通用优化设置
```csharp
PlayerSettings.defaultIsNativeResolution = true;
PlayerSettings.runInBackground = true;
PlayerSettings.stripEngineCode = true;
PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Medium);
```

## 依赖项

### Unity命名空间
- `UnityEngine`
- `UnityEditor`
- `System.IO`

### Unity版本要求
- Unity 2022.3.x 或更高版本
- macOS构建支持

### 必需场景文件
- `Assets/Scenes/MinimalMVPScene.unity` (MVP版本)
- `Assets/Scenes/MainScene.unity` (认证版本)

## 输出结构

```
Builds/
├── MinimalMVP/
│   └── DigitalHuman_MVP.app
└── WithAuthentication/
    └── DigitalHuman_WithAuth.app
```

## 错误处理

### 常见返回状态
- `BuildResult.Succeeded` - 构建成功
- `BuildResult.Failed` - 构建失败
- `BuildResult.Cancelled` - 构建取消
- `BuildResult.Unknown` - 未知状态

### 错误检查
```csharp
if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
{
    Debug.Log("构建成功");
}
else
{
    Debug.LogError($"构建失败: {report.summary.result}");
}
```

## 性能参数

### 构建选项
```csharp
BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
{
    scenes = scenes,                    // 场景列表
    locationPathName = buildPath,       // 输出路径
    target = BuildTarget.StandaloneOSX, // 目标平台
    options = BuildOptions.None         // 构建选项
};
```

### 优化选项
- `BuildOptions.Development` - 开发构建
- `BuildOptions.AllowDebugging` - 允许调试
- `BuildOptions.CompressWithLz4` - LZ4压缩
- `BuildOptions.CompressWithLz4HC` - LZ4高压缩