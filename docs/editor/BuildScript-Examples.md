# BuildScript 使用示例

## 基本使用示例

### 1. Unity编辑器中使用

#### 通过菜单构建
```
1. 打开Unity编辑器
2. 选择菜单: DigitalHuman → Build → Build With Authentication
3. 等待构建完成
4. 构建成功后会自动在Finder中显示结果
```

#### 快速测试
```
1. 选择菜单: DigitalHuman → Build → Quick Build Test
2. 查看Console输出验证编译状态
3. 确认项目可以正常编译后再进行完整构建
```

### 2. 脚本调用示例

#### 基本构建操作
```csharp
using DigitalHuman.Editor;

public class BuildAutomation
{
    // 构建所有版本
    public static void BuildAllVersions()
    {
        // 1. 清理旧文件
        BuildScript.CleanBuildFiles();
        
        // 2. 快速测试
        BuildScript.QuickBuildTest();
        
        // 3. 构建MVP版本
        BuildScript.BuildMinimalMVP();
        
        // 4. 构建认证版本
        BuildScript.BuildWithAuthentication();
    }
    
    // 仅构建认证版本
    public static void BuildAuthVersion()
    {
        BuildScript.BuildWithAuthentication();
    }
}
```

#### 条件构建
```csharp
public class ConditionalBuild
{
    public static void BuildBasedOnEnvironment()
    {
        string buildType = System.Environment.GetEnvironmentVariable("BUILD_TYPE");
        
        switch (buildType?.ToLower())
        {
            case "mvp":
                BuildScript.BuildMinimalMVP();
                break;
            case "auth":
                BuildScript.BuildWithAuthentication();
                break;
            case "test":
                BuildScript.QuickBuildTest();
                break;
            default:
                UnityEngine.Debug.LogWarning("未指定构建类型，执行快速测试");
                BuildScript.QuickBuildTest();
                break;
        }
    }
}
```

### 3. 命令行使用示例

#### 基本命令行构建
```bash
#!/bin/bash

# 设置Unity路径
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
PROJECT_PATH="."

# 构建认证版本
echo "开始构建认证版本..."
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "$PROJECT_PATH" \
    -executeMethod BuildScript.BuildMacOS \
    -logFile build_auth.log

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ 构建成功"
    ls -la "./Builds/WithAuthentication/"
else
    echo "❌ 构建失败，查看日志: build_auth.log"
    exit 1
fi
```

#### CI/CD流水线示例
```bash
#!/bin/bash
# ci_build.sh - CI/CD构建脚本

set -e  # 遇到错误立即退出

echo "=== CI/CD 自动化构建 ==="

# 环境检查
if [ ! -f "$UNITY_PATH" ]; then
    echo "❌ Unity未找到: $UNITY_PATH"
    exit 1
fi

# 清理环境
echo "1. 清理构建环境..."
rm -rf ./Builds/
rm -f *.log

# 快速测试
echo "2. 执行快速编译测试..."
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath . \
    -executeMethod BuildScript.QuickBuildTest \
    -logFile quick_test.log

if [ $? -ne 0 ]; then
    echo "❌ 快速测试失败"
    cat quick_test.log
    exit 1
fi

# 构建所有版本
echo "3. 构建MVP版本..."
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath . \
    -executeMethod BuildScript.BuildMinimalMVP \
    -logFile build_mvp.log

echo "4. 构建认证版本..."
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath . \
    -executeMethod BuildScript.BuildMacOS \
    -logFile build_auth.log

# 验证构建结果
echo "5. 验证构建结果..."
if [ -d "./Builds/MinimalMVP/DigitalHuman_MVP.app" ]; then
    echo "✅ MVP版本构建成功"
else
    echo "❌ MVP版本构建失败"
    exit 1
fi

if [ -d "./Builds/WithAuthentication/DigitalHuman_WithAuth.app" ]; then
    echo "✅ 认证版本构建成功"
else
    echo "❌ 认证版本构建失败"
    exit 1
fi

echo "🎉 所有版本构建完成"
```

### 4. 自定义构建脚本示例

#### 扩展BuildScript功能
```csharp
using UnityEngine;
using UnityEditor;
using DigitalHuman.Editor;

public class CustomBuildScript
{
    /// <summary>
    /// 构建调试版本
    /// </summary>
    [MenuItem("DigitalHuman/Build/Build Debug Version")]
    public static void BuildDebugVersion()
    {
        Debug.Log("[CustomBuild] 开始构建调试版本...");
        
        // 先清理旧文件
        BuildScript.CleanBuildFiles();
        
        // 快速测试
        BuildScript.QuickBuildTest();
        
        // 自定义调试构建逻辑
        string buildPath = Path.Combine(Application.dataPath, "../Builds/Debug/DigitalHuman_Debug.app");
        string[] scenes = { "Assets/Scenes/DebugScene.unity" };
        
        BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
        {
            scenes = scenes,
            locationPathName = buildPath,
            target = BuildTarget.StandaloneOSX,
            options = BuildOptions.Development | BuildOptions.AllowDebugging
        };
        
        // 配置调试版本设置
        ConfigureDebugSettings();
        
        var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
        
        if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
        {
            Debug.Log($"[CustomBuild] 调试版本构建成功: {buildPath}");
            EditorUtility.RevealInFinder(buildPath);
        }
        else
        {
            Debug.LogError($"[CustomBuild] 调试版本构建失败: {report.summary.result}");
        }
    }
    
    private static void ConfigureDebugSettings()
    {
        PlayerSettings.productName = "数字人对话系统 - 调试版";
        PlayerSettings.bundleVersion = "1.0.0-Debug";
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.debug");
        
        // 调试版本不进行代码剥离
        PlayerSettings.stripEngineCode = false;
        PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Standalone, ManagedStrippingLevel.Disabled);
    }
}
```

#### 批量构建管理器
```csharp
using System.Collections;
using UnityEngine;
using UnityEditor;
using DigitalHuman.Editor;

public class BatchBuildManager : EditorWindow
{
    private bool buildMVP = true;
    private bool buildAuth = true;
    private bool buildDebug = false;
    private bool cleanFirst = true;
    
    [MenuItem("DigitalHuman/Build/Batch Build Manager")]
    public static void ShowWindow()
    {
        GetWindow<BatchBuildManager>("批量构建管理器");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("批量构建管理器", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        // 构建选项
        GUILayout.Label("构建选项:", EditorStyles.label);
        buildMVP = EditorGUILayout.Toggle("构建MVP版本", buildMVP);
        buildAuth = EditorGUILayout.Toggle("构建认证版本", buildAuth);
        buildDebug = EditorGUILayout.Toggle("构建调试版本", buildDebug);
        
        EditorGUILayout.Space();
        
        cleanFirst = EditorGUILayout.Toggle("构建前清理", cleanFirst);
        
        EditorGUILayout.Space();
        
        // 构建按钮
        if (GUILayout.Button("开始批量构建", GUILayout.Height(30)))
        {
            StartBatchBuild();
        }
        
        EditorGUILayout.Space();
        
        // 单独构建按钮
        GUILayout.Label("单独构建:", EditorStyles.label);
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("MVP版本"))
        {
            BuildScript.BuildMinimalMVP();
        }
        if (GUILayout.Button("认证版本"))
        {
            BuildScript.BuildWithAuthentication();
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("快速测试"))
        {
            BuildScript.QuickBuildTest();
        }
        if (GUILayout.Button("清理文件"))
        {
            BuildScript.CleanBuildFiles();
        }
        EditorGUILayout.EndHorizontal();
    }
    
    private void StartBatchBuild()
    {
        Debug.Log("[BatchBuild] 开始批量构建...");
        
        try
        {
            // 清理文件
            if (cleanFirst)
            {
                Debug.Log("[BatchBuild] 清理旧文件...");
                BuildScript.CleanBuildFiles();
            }
            
            // 快速测试
            Debug.Log("[BatchBuild] 执行快速测试...");
            BuildScript.QuickBuildTest();
            
            // 构建选中的版本
            if (buildMVP)
            {
                Debug.Log("[BatchBuild] 构建MVP版本...");
                BuildScript.BuildMinimalMVP();
            }
            
            if (buildAuth)
            {
                Debug.Log("[BatchBuild] 构建认证版本...");
                BuildScript.BuildWithAuthentication();
            }
            
            if (buildDebug)
            {
                Debug.Log("[BatchBuild] 构建调试版本...");
                CustomBuildScript.BuildDebugVersion();
            }
            
            Debug.Log("[BatchBuild] 批量构建完成!");
            EditorUtility.DisplayDialog("构建完成", "所有选中的版本构建完成", "确定");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[BatchBuild] 批量构建失败: {ex.Message}");
            EditorUtility.DisplayDialog("构建失败", $"构建过程中发生错误:\n{ex.Message}", "确定");
        }
    }
}
```

### 5. 构建后处理示例

#### 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh - 构建后自动部署脚本

BUILD_DIR="./Builds"
DEPLOY_DIR="/Applications/DigitalHuman"
BACKUP_DIR="./Backups"

echo "=== 自动化部署脚本 ==="

# 检查构建结果
if [ ! -d "$BUILD_DIR/WithAuthentication/DigitalHuman_WithAuth.app" ]; then
    echo "❌ 找不到认证版本构建文件"
    exit 1
fi

# 创建备份
echo "1. 创建备份..."
mkdir -p "$BACKUP_DIR"
if [ -d "$DEPLOY_DIR/DigitalHuman_WithAuth.app" ]; then
    cp -r "$DEPLOY_DIR/DigitalHuman_WithAuth.app" "$BACKUP_DIR/DigitalHuman_WithAuth_$(date +%Y%m%d_%H%M%S).app"
    echo "✅ 备份完成"
fi

# 部署新版本
echo "2. 部署新版本..."
mkdir -p "$DEPLOY_DIR"
cp -r "$BUILD_DIR/WithAuthentication/DigitalHuman_WithAuth.app" "$DEPLOY_DIR/"

# 设置权限
chmod +x "$DEPLOY_DIR/DigitalHuman_WithAuth.app/Contents/MacOS/DigitalHuman_WithAuth"

echo "✅ 部署完成"
echo "应用路径: $DEPLOY_DIR/DigitalHuman_WithAuth.app"
```

#### 构建验证脚本
```bash
#!/bin/bash
# verify_build.sh - 构建结果验证脚本

echo "=== 构建结果验证 ==="

# 验证MVP版本
if [ -d "./Builds/MinimalMVP/DigitalHuman_MVP.app" ]; then
    echo "✅ MVP版本存在"
    
    # 检查应用结构
    if [ -f "./Builds/MinimalMVP/DigitalHuman_MVP.app/Contents/MacOS/DigitalHuman_MVP" ]; then
        echo "✅ MVP可执行文件存在"
    else
        echo "❌ MVP可执行文件缺失"
    fi
    
    # 检查文件大小
    SIZE=$(du -sh "./Builds/MinimalMVP/DigitalHuman_MVP.app" | cut -f1)
    echo "📦 MVP版本大小: $SIZE"
else
    echo "❌ MVP版本不存在"
fi

# 验证认证版本
if [ -d "./Builds/WithAuthentication/DigitalHuman_WithAuth.app" ]; then
    echo "✅ 认证版本存在"
    
    # 检查应用结构
    if [ -f "./Builds/WithAuthentication/DigitalHuman_WithAuth.app/Contents/MacOS/DigitalHuman_WithAuth" ]; then
        echo "✅ 认证版本可执行文件存在"
    else
        echo "❌ 认证版本可执行文件缺失"
    fi
    
    # 检查文件大小
    SIZE=$(du -sh "./Builds/WithAuthentication/DigitalHuman_WithAuth.app" | cut -f1)
    echo "📦 认证版本大小: $SIZE"
else
    echo "❌ 认证版本不存在"
fi

echo "验证完成"
```

## 最佳实践建议

### 1. 构建前检查
- 始终先执行 `QuickBuildTest()` 验证编译状态
- 确保所需的场景文件存在于项目中
- 检查Unity版本兼容性

### 2. 版本管理
- 使用语义化版本号
- 在构建前更新版本信息
- 记录每个版本的变更日志

### 3. 自动化集成
- 将构建脚本集成到CI/CD流水线
- 使用环境变量控制构建参数
- 实现构建结果的自动通知

### 4. 错误处理
- 检查构建日志文件
- 实现构建失败的回滚机制
- 提供详细的错误信息和解决建议

这些示例展示了BuildScript的各种使用方式，从简单的菜单操作到复杂的自动化流水线集成，为不同的开发和部署需求提供了完整的解决方案。