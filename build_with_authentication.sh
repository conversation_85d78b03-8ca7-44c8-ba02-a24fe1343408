#!/bin/bash

echo "=== 构建包含认证功能的数字人应用 ==="
echo ""

# 设置构建参数
BUILD_NAME="DigitalHuman_WithAuth"
BUILD_PATH="./Builds/WithAuthentication"
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
PROJECT_PATH="."
LOG_FILE="build_with_auth.log"

echo "1. 检查Unity安装..."
if [ ! -f "$UNITY_PATH" ]; then
    echo "❌ Unity未找到，请检查安装路径"
    exit 1
fi
echo "✅ Unity路径确认: $UNITY_PATH"

echo ""
echo "2. 检查项目文件..."
required_files=(
    "Assets/Scripts/UI/MainUIManager.cs"
    "Assets/Scripts/Core/Authentication/AuthenticationManager.cs"
    "Assets/UI/Main/MainUI.uxml"
    "Assets/UI/Main/MainUI.uss"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file 缺失"
        exit 1
    fi
done

echo ""
echo "3. 创建构建目录..."
mkdir -p "$BUILD_PATH"
echo "✅ 构建目录创建: $BUILD_PATH"

echo ""
echo "4. 开始Unity构建..."
echo "   - 项目路径: $PROJECT_PATH"
echo "   - 构建路径: $BUILD_PATH/$BUILD_NAME.app"
echo "   - 日志文件: $LOG_FILE"
echo ""

# 执行Unity构建命令
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "$PROJECT_PATH" \
    -buildTarget StandaloneOSX \
    -buildPath "$BUILD_PATH/$BUILD_NAME.app" \
    -logFile "$LOG_FILE" \
    -executeMethod BuildScript.BuildMacOS

# 检查构建结果
BUILD_EXIT_CODE=$?
echo ""

if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo "✅ 构建成功完成！"
    echo ""
    echo "📱 应用信息:"
    echo "   - 应用名称: $BUILD_NAME"
    echo "   - 构建路径: $BUILD_PATH/$BUILD_NAME.app"
    
    if [ -d "$BUILD_PATH/$BUILD_NAME.app" ]; then
        APP_SIZE=$(du -sh "$BUILD_PATH/$BUILD_NAME.app" | cut -f1)
        echo "   - 应用大小: $APP_SIZE"
        echo ""
        
        echo "🚀 运行应用:"
        echo "   方法1: 双击 $BUILD_PATH/$BUILD_NAME.app"
        echo "   方法2: 运行命令 open '$BUILD_PATH/$BUILD_NAME.app'"
        echo ""
        
        echo "🧪 测试认证功能:"
        echo "   1. 启动应用"
        echo "   2. 点击右上角'登录'按钮"
        echo "   3. 输入测试凭据: admin / admin123"
        echo "   4. 验证登录状态变化"
        echo "   5. 测试登出功能"
        echo ""
        
        # 自动打开应用
        read -p "是否立即运行应用？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🚀 启动应用..."
            open "$BUILD_PATH/$BUILD_NAME.app"
        fi
    else
        echo "❌ 构建文件未找到，请检查构建日志"
    fi
else
    echo "❌ 构建失败，退出代码: $BUILD_EXIT_CODE"
    echo ""
    echo "📋 检查构建日志:"
    echo "   cat $LOG_FILE"
    echo ""
    echo "🔍 常见问题排查:"
    echo "   1. 检查编译错误"
    echo "   2. 确认所有依赖项"
    echo "   3. 验证Unity版本兼容性"
fi

echo ""
echo "=== 构建过程完成 ==="