#!/bin/bash

echo "=== 测试认证功能编译 ==="

# 检查关键文件是否存在
echo "检查关键文件..."

files=(
    "Assets/Scripts/Core/Authentication/AuthenticationManager.cs"
    "Assets/Scripts/Core/Authentication/IAuthenticationManager.cs"
    "Assets/Scripts/Core/Authentication/Models/UserInfo.cs"
    "Assets/Scripts/Core/Authentication/Models/AuthenticationResult.cs"
    "Assets/Scripts/Core/Authentication/Models/AuthenticationStatus.cs"
    "Assets/Scripts/UI/MainUIManager.cs"
    "Assets/UI/Main/MainUI.uxml"
    "Assets/UI/Main/MainUI.uss"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
    fi
done

echo ""
echo "检查UXML文件中的认证元素..."
if grep -q "login-dialog" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 登录对话框元素存在"
else
    echo "✗ 登录对话框元素不存在"
fi

if grep -q "auth-status" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 认证状态元素存在"
else
    echo "✗ 认证状态元素不存在"
fi

echo ""
echo "检查CSS文件中的认证样式..."
if grep -q "login-button" Assets/UI/Main/MainUI.uss; then
    echo "✓ 登录按钮样式存在"
else
    echo "✗ 登录按钮样式不存在"
fi

if grep -q "auth-logged-in" Assets/UI/Main/MainUI.uss; then
    echo "✓ 认证状态样式存在"
else
    echo "✗ 认证状态样式不存在"
fi

echo ""
echo "=== 文件检查完成 ==="