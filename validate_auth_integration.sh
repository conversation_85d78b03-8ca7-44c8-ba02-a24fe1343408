#!/bin/bash

echo "=== 验证认证功能集成 ==="

echo ""
echo "1. 检查MainUIManager中是否集成了AuthenticationManager..."
if grep -q "AuthenticationManager" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ MainUIManager中包含AuthenticationManager引用"
else
    echo "✗ MainUIManager中未找到AuthenticationManager引用"
fi

echo ""
echo "2. 检查登录/登出界面元素..."
if grep -q "login-button" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 登录按钮存在"
else
    echo "✗ 登录按钮不存在"
fi

if grep -q "logout-button" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 登出按钮存在"
else
    echo "✗ 登出按钮不存在"
fi

if grep -q "user-info" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 用户信息显示元素存在"
else
    echo "✗ 用户信息显示元素不存在"
fi

echo ""
echo "3. 检查登录表单UI交互..."
if grep -q "username-input" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 用户名输入框存在"
else
    echo "✗ 用户名输入框不存在"
fi

if grep -q "password-input" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 密码输入框存在"
else
    echo "✗ 密码输入框不存在"
fi

if grep -q "login-submit" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 登录提交按钮存在"
else
    echo "✗ 登录提交按钮不存在"
fi

echo ""
echo "4. 检查状态显示和管理..."
if grep -q "auth-status" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 认证状态指示器存在"
else
    echo "✗ 认证状态指示器不存在"
fi

if grep -q "login-status" Assets/UI/Main/MainUI.uxml; then
    echo "✓ 登录状态消息元素存在"
else
    echo "✗ 登录状态消息元素不存在"
fi

echo ""
echo "5. 检查UI反馈功能..."
if grep -q "ShowLoginStatus" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ 登录状态显示方法存在"
else
    echo "✗ 登录状态显示方法不存在"
fi

if grep -q "UpdateAuthenticationUI" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ 认证UI更新方法存在"
else
    echo "✗ 认证UI更新方法不存在"
fi

echo ""
echo "6. 检查事件处理..."
if grep -q "OnUserLoggedIn" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ 用户登录事件处理存在"
else
    echo "✗ 用户登录事件处理不存在"
fi

if grep -q "OnUserLoggedOut" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ 用户登出事件处理存在"
else
    echo "✗ 用户登出事件处理不存在"
fi

echo ""
echo "7. 检查会话状态管理..."
if grep -q "IsUserLoggedIn" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ 用户登录状态属性存在"
else
    echo "✗ 用户登录状态属性不存在"
fi

if grep -q "CurrentUser" Assets/Scripts/UI/MainUIManager.cs; then
    echo "✓ 当前用户属性存在"
else
    echo "✗ 当前用户属性不存在"
fi

echo ""
echo "=== 验证完成 ==="