#!/bin/bash

echo "=== 数字人管理系统增强版构建脚本 ==="
echo "构建时间: $(date)"
echo ""

# Unity路径
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
PROJECT_PATH="$(pwd)"
BUILD_PATH="$PROJECT_PATH/Builds/EnhancedMVP"

# 检查Unity
if [ ! -f "$UNITY_PATH" ]; then
    echo "❌ Unity编辑器未找到: $UNITY_PATH"
    exit 1
fi

echo "🎮 Unity版本: $($UNITY_PATH -version 2>/dev/null | head -1)"
echo "📁 项目路径: $PROJECT_PATH"
echo "🏗️ 构建路径: $BUILD_PATH"
echo ""

# 创建构建目录
echo "📁 准备构建目录..."
mkdir -p "$BUILD_PATH"

# 检查必要文件
echo "🔍 检查必要文件..."
required_files=(
    "Assets/Scripts/MVP/EnhancedMVPManager.cs"
    "Assets/Scripts/Core/Authentication/AuthenticationManager.cs"
    "Assets/Scripts/Core/DataSync/DataSyncManager.cs"
    "Assets/Scripts/Core/Logging/LogManager.cs"
    "Assets/Scripts/Editor/BuildScript.cs"
)

all_files_exist=true
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = false ]; then
    echo ""
    echo "❌ 部分必要文件缺失，无法进行构建"
    exit 1
fi

echo ""
echo "✅ 所有必要文件检查通过"

# 创建增强版场景（如果不存在）
echo ""
echo "🎬 检查增强版场景..."
if [ ! -f "Assets/Scenes/EnhancedMVPTestScene.unity" ]; then
    echo "创建带测试仪表板的增强版场景..."

    # 使用Unity命令行创建场景
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "$PROJECT_PATH" \
        -executeMethod DigitalHuman.Editor.BuildScript.CreateEnhancedSceneWithTestDashboard \
        -logFile "$PROJECT_PATH/create_scene.log" &
    
    # 等待场景创建完成
    scene_pid=$!
    echo "等待场景创建完成..."
    
    # 等待最多30秒
    timeout=30
    while [ $timeout -gt 0 ] && kill -0 $scene_pid 2>/dev/null; do
        echo -n "."
        sleep 1
        timeout=$((timeout - 1))
    done
    echo ""
    
    if [ -f "Assets/Scenes/EnhancedMVPTestScene.unity" ]; then
        echo "✅ 带测试仪表板的增强版场景创建成功"
    else
        echo "⚠️ 场景创建可能失败，使用现有场景"
    fi
else
    echo "✅ 带测试仪表板的增强版场景已存在"
fi

# 执行构建
echo ""
echo "🚀 开始构建增强版MVP应用..."

# 创建构建日志文件
BUILD_LOG="$PROJECT_PATH/build_enhanced.log"

# 执行Unity构建
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "$PROJECT_PATH" \
    -executeMethod DigitalHuman.Editor.BuildScript.BuildEnhancedMVP \
    -logFile "$BUILD_LOG" &

build_pid=$!
echo "构建进程ID: $build_pid"
echo "构建日志: $BUILD_LOG"

# 显示构建进度
echo ""
echo "⏳ 构建进行中..."
start_time=$(date +%s)

while kill -0 $build_pid 2>/dev/null; do
    current_time=$(date +%s)
    elapsed=$((current_time - start_time))
    echo -n "构建中... ${elapsed}秒 "
    
    # 检查日志中的进度信息
    if [ -f "$BUILD_LOG" ]; then
        if grep -q "Building Player" "$BUILD_LOG"; then
            echo -n "[编译中] "
        elif grep -q "Compiling scripts" "$BUILD_LOG"; then
            echo -n "[脚本编译] "
        elif grep -q "Building scene" "$BUILD_LOG"; then
            echo -n "[场景构建] "
        fi
    fi
    
    echo ""
    sleep 3
done

# 等待进程完全结束
wait $build_pid
build_result=$?

echo ""
echo "🏁 构建完成"

# 检查构建结果
if [ $build_result -eq 0 ]; then
    echo "✅ 构建进程正常退出"
else
    echo "⚠️ 构建进程异常退出 (代码: $build_result)"
fi

# 检查构建产物
echo ""
echo "📦 检查构建产物..."

APP_PATH="$BUILD_PATH/DigitalHumanEnhanced.app"
if [ -d "$APP_PATH" ]; then
    APP_SIZE=$(du -sh "$APP_PATH" | cut -f1)
    echo "✅ 应用程序构建成功"
    echo "📍 路径: $APP_PATH"
    echo "📏 大小: $APP_SIZE"
    
    # 检查应用程序结构
    if [ -f "$APP_PATH/Contents/MacOS/DigitalHumanEnhanced" ]; then
        echo "✅ 可执行文件存在"
    else
        echo "❌ 可执行文件缺失"
    fi
    
    if [ -f "$APP_PATH/Contents/Info.plist" ]; then
        echo "✅ Info.plist 存在"
    else
        echo "❌ Info.plist 缺失"
    fi
    
else
    echo "❌ 应用程序构建失败"
    
    # 显示构建日志的错误信息
    if [ -f "$BUILD_LOG" ]; then
        echo ""
        echo "📋 构建日志错误信息:"
        grep -i "error\|exception\|failed" "$BUILD_LOG" | tail -10
    fi
    
    exit 1
fi

# 显示构建总结
echo ""
echo "=== 构建总结 ==="
end_time=$(date +%s)
total_time=$((end_time - start_time))

echo "构建时间: ${total_time}秒"
echo "应用大小: $APP_SIZE"
echo "输出路径: $APP_PATH"

# 验证应用程序
echo ""
echo "🔍 验证应用程序..."

# 检查应用程序是否可以启动（快速测试）
if [ -x "$APP_PATH/Contents/MacOS/DigitalHumanEnhanced" ]; then
    echo "✅ 应用程序可执行权限正常"
    
    # 尝试获取应用程序信息
    if command -v mdls >/dev/null 2>&1; then
        app_info=$(mdls -name kMDItemVersion "$APP_PATH" 2>/dev/null)
        if [ $? -eq 0 ]; then
            echo "✅ 应用程序元数据正常"
        fi
    fi
else
    echo "❌ 应用程序可执行权限异常"
fi

# 提供下一步操作建议
echo ""
echo "=== 下一步操作 ==="
echo "1. 🚀 运行应用程序:"
echo "   open '$APP_PATH'"
echo ""
echo "2. 🧪 运行测试验证:"
echo "   ./validate_enhanced_integration.sh"
echo ""
echo "3. 📋 查看详细日志:"
echo "   cat '$BUILD_LOG'"
echo ""
echo "4. 🔍 在Finder中查看:"
echo "   open '$BUILD_PATH'"

echo ""
echo "🎉 增强版MVP应用构建完成！"
echo "构建时间: $(date)"
