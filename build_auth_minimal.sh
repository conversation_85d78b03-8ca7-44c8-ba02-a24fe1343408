#!/bin/bash

echo "=== 构建最小认证功能演示版本 ==="
echo ""

# 创建临时构建目录
TEMP_BUILD_DIR="./Builds/AuthMinimal"
BUILD_NAME="DigitalHuman_AuthDemo"
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"

echo "1. 创建最小化项目结构..."
mkdir -p "$TEMP_BUILD_DIR"

# 创建一个简化的构建脚本
cat > "Assets/Scripts/Editor/MinimalAuthBuildScript.cs" << 'EOF'
using UnityEngine;
using UnityEditor;
using System.IO;

namespace DigitalHuman.Editor
{
    public class MinimalAuthBuildScript
    {
        public static void BuildAuthDemo()
        {
            Debug.Log("[MinimalAuthBuild] 开始构建认证演示版本...");
            
            // 只包含必要的场景
            string[] scenes = { "Assets/Scenes/MainScene.unity" };
            string buildPath = Path.Combine(Application.dataPath, "../Builds/AuthMinimal/DigitalHuman_AuthDemo.app");
            
            // 确保构建目录存在
            string buildDir = Path.GetDirectoryName(buildPath);
            if (!Directory.Exists(buildDir))
            {
                Directory.CreateDirectory(buildDir);
            }
            
            // 配置播放器设置
            PlayerSettings.productName = "数字人认证演示";
            PlayerSettings.companyName = "DigitalHuman";
            PlayerSettings.bundleVersion = "1.0.0-AuthDemo";
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.digitalhuman.authdemo");
            
            // 构建选项 - 使用开发构建以便调试
            BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
            {
                scenes = scenes,
                locationPathName = buildPath,
                target = BuildTarget.StandaloneOSX,
                options = BuildOptions.Development | BuildOptions.AllowDebugging
            };
            
            // 执行构建
            var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
            
            if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"[MinimalAuthBuild] 构建成功！位置: {buildPath}");
                EditorUtility.RevealInFinder(buildPath);
            }
            else
            {
                Debug.LogError($"[MinimalAuthBuild] 构建失败: {report.summary.result}");
            }
        }
    }
}
EOF

echo "✅ 创建了最小化构建脚本"

echo ""
echo "2. 尝试构建认证演示版本..."

# 执行Unity构建
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "." \
    -buildTarget StandaloneOSX \
    -logFile "build_auth_minimal.log" \
    -executeMethod MinimalAuthBuildScript.BuildAuthDemo

BUILD_EXIT_CODE=$?

echo ""
if [ $BUILD_EXIT_CODE -eq 0 ]; then
    echo "✅ 构建完成！"
    
    if [ -d "$TEMP_BUILD_DIR/$BUILD_NAME.app" ]; then
        echo ""
        echo "📱 应用信息:"
        echo "   - 名称: 数字人认证演示"
        echo "   - 路径: $TEMP_BUILD_DIR/$BUILD_NAME.app"
        
        APP_SIZE=$(du -sh "$TEMP_BUILD_DIR/$BUILD_NAME.app" | cut -f1)
        echo "   - 大小: $APP_SIZE"
        
        echo ""
        echo "🚀 运行应用:"
        echo "   open '$TEMP_BUILD_DIR/$BUILD_NAME.app'"
        
        echo ""
        echo "🧪 测试认证功能:"
        echo "   1. 启动应用后查看右上角状态"
        echo "   2. 点击'登录'按钮"
        echo "   3. 输入: admin / admin123"
        echo "   4. 观察状态变化"
        
        # 询问是否立即运行
        read -p "是否立即运行应用？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🚀 启动应用..."
            open "$TEMP_BUILD_DIR/$BUILD_NAME.app"
        fi
    else
        echo "❌ 构建文件未找到"
    fi
else
    echo "❌ 构建失败，检查日志: build_auth_minimal.log"
    
    # 显示最后几行错误
    echo ""
    echo "最近的错误信息:"
    tail -20 build_auth_minimal.log | grep -E "(error|Error|ERROR)" || echo "未找到明显错误，请查看完整日志"
fi

echo ""
echo "=== 构建过程完成 ==="