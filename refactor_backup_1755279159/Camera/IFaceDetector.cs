using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Camera
{
    /// <summary>
    /// 面部检测器接口
    /// 负责从视频帧中检测和跟踪用户面部
    /// </summary>
    public interface IFaceDetector
    {
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 是否正在检测
        /// </summary>
        bool IsDetecting { get; }
        
        /// <summary>
        /// 检测到的面部列表
        /// </summary>
        List<FaceInfo> DetectedFaces { get; }
        
        /// <summary>
        /// 主要面部（最大或最中心的面部）
        /// </summary>
        FaceInfo PrimaryFace { get; }
        
        /// <summary>
        /// 面部检测结果更新事件
        /// </summary>
        event Action<List<FaceInfo>> OnFacesDetected;
        
        /// <summary>
        /// 主要面部变化事件
        /// </summary>
        event Action<FaceInfo> OnPrimaryFaceChanged;
        
        /// <summary>
        /// 用户离开检测事件
        /// </summary>
        event Action OnUserLeft;
        
        /// <summary>
        /// 用户返回检测事件
        /// </summary>
        event Action<FaceInfo> OnUserReturned;
        
        /// <summary>
        /// 初始化面部检测器
        /// </summary>
        /// <param name="config">检测配置</param>
        /// <returns>是否初始化成功</returns>
        bool Initialize(FaceDetectionConfig config = null);
        
        /// <summary>
        /// 开始面部检测
        /// </summary>
        void StartDetection();
        
        /// <summary>
        /// 停止面部检测
        /// </summary>
        void StopDetection();
        
        /// <summary>
        /// 处理视频帧进行面部检测
        /// </summary>
        /// <param name="frame">视频帧</param>
        /// <returns>检测到的面部列表</returns>
        List<FaceInfo> ProcessFrame(Texture2D frame);
        
        /// <summary>
        /// 设置检测配置
        /// </summary>
        /// <param name="config">检测配置</param>
        void SetConfiguration(FaceDetectionConfig config);
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void Cleanup();
    }
}