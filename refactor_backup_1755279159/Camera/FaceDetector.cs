using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace DigitalHuman.Core.Camera
{
    /// <summary>
    /// 面部检测器实现
    /// 基于简单的图像处理算法进行面部检测
    /// 注意：这是一个基础实现，实际项目中建议集成专业的面部检测库
    /// </summary>
    public class FaceDetector : MonoBehaviour, IFaceDetector
    {
        [Header("检测配置")]
        [SerializeField] private FaceDetectionConfig config = new FaceDetectionConfig();
        [SerializeField] private bool autoStart = false;
        
        // 私有字段
        private FaceDetectionStatus status = FaceDetectionStatus.NotInitialized;
        private List<FaceInfo> detectedFaces = new List<FaceInfo>();
        private FaceInfo primaryFace;
        private UserPresenceStatus userStatus = UserPresenceStatus.Unknown;
        private float lastDetectionTime;
        private float lastUserSeenTime;
        private int nextFaceId = 1;
        private Coroutine detectionCoroutine;
        private Dictionary<int, FaceTrackingData> faceTracker = new Dictionary<int, FaceTrackingData>();
        
        // 公共属性
        public bool IsInitialized => status != FaceDetectionStatus.NotInitialized;
        public bool IsDetecting => status == FaceDetectionStatus.Detecting;
        public List<FaceInfo> DetectedFaces => new List<FaceInfo>(detectedFaces);
        public FaceInfo PrimaryFace => primaryFace;
        
        // 事件
        public event Action<List<FaceInfo>> OnFacesDetected;
        public event Action<FaceInfo> OnPrimaryFaceChanged;
        public event Action OnUserLeft;
        public event Action<FaceInfo> OnUserReturned;
        
        /// <summary>
        /// 面部跟踪数据
        /// </summary>
        private class FaceTrackingData
        {
            public FaceInfo lastFace;
            public float lastSeenTime;
            public Vector2 velocity;
            public int consecutiveDetections;
            
            public FaceTrackingData(FaceInfo face)
            {
                lastFace = face;
                lastSeenTime = Time.time;
                velocity = Vector2.zero;
                consecutiveDetections = 1;
            }
        }
        
        void Start()
        {
            if (autoStart)
            {
                Initialize();
                StartDetection();
            }
        }
        
        void OnDestroy()
        {
            Cleanup();
        }
        
        public bool Initialize(FaceDetectionConfig config = null)
        {
            try
            {
                if (config != null)
                {
                    this.config = config;
                }
                
                // 初始化检测器
                detectedFaces.Clear();
                faceTracker.Clear();
                primaryFace = null;
                userStatus = UserPresenceStatus.Unknown;
                lastDetectionTime = 0f;
                lastUserSeenTime = 0f;
                nextFaceId = 1;
                
                status = FaceDetectionStatus.Initialized;
                
                Debug.Log("[FaceDetector] 面部检测器初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FaceDetector] 初始化失败: {ex.Message}");
                status = FaceDetectionStatus.Error;
                return false;
            }
        }
        
        public void StartDetection()
        {
            if (status != FaceDetectionStatus.Initialized && status != FaceDetectionStatus.Paused)
            {
                Debug.LogWarning("[FaceDetector] 检测器未初始化或状态错误");
                return;
            }
            
            status = FaceDetectionStatus.Detecting;
            
            if (detectionCoroutine != null)
            {
                StopCoroutine(detectionCoroutine);
            }
            
            detectionCoroutine = StartCoroutine(DetectionCoroutine());
            
            Debug.Log("[FaceDetector] 开始面部检测");
        }
        
        public void StopDetection()
        {
            if (status != FaceDetectionStatus.Detecting)
                return;
            
            status = FaceDetectionStatus.Paused;
            
            if (detectionCoroutine != null)
            {
                StopCoroutine(detectionCoroutine);
                detectionCoroutine = null;
            }
            
            Debug.Log("[FaceDetector] 停止面部检测");
        }
        
        private IEnumerator DetectionCoroutine()
        {
            float detectionInterval = 1f / config.detectionFPS;
            
            while (status == FaceDetectionStatus.Detecting)
            {
                // 检查是否有摄像头管理器提供视频帧
                var cameraManager = FindObjectOfType<CameraManager>();
                if (cameraManager != null && cameraManager.IsCapturing)
                {
                    var frame = cameraManager.GetCurrentFrame();
                    if (frame != null)
                    {
                        ProcessFrame(frame);
                    }
                }
                
                // 检查用户离开状态
                CheckUserPresence();
                
                yield return new WaitForSeconds(detectionInterval);
            }
        }
        
        public List<FaceInfo> ProcessFrame(Texture2D frame)
        {
            if (frame == null || status != FaceDetectionStatus.Detecting)
            {
                return new List<FaceInfo>();
            }
            
            try
            {
                lastDetectionTime = Time.time;
                
                // 执行面部检测
                var newFaces = DetectFacesInFrame(frame);
                
                // 更新跟踪信息
                if (config.enableTracking)
                {
                    newFaces = UpdateFaceTracking(newFaces);
                }
                
                // 过滤和排序面部
                newFaces = FilterAndSortFaces(newFaces);
                
                // 更新检测结果
                detectedFaces = newFaces;
                
                // 更新主要面部
                UpdatePrimaryFace();
                
                // 触发事件
                OnFacesDetected?.Invoke(DetectedFaces);
                
                return DetectedFaces;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FaceDetector] 处理视频帧失败: {ex.Message}");
                return new List<FaceInfo>();
            }
        }
        
        /// <summary>
        /// 在视频帧中检测面部
        /// 注意：这是一个简化的实现，实际项目中应该使用专业的面部检测算法
        /// </summary>
        /// <param name="frame">视频帧</param>
        /// <returns>检测到的面部列表</returns>
        private List<FaceInfo> DetectFacesInFrame(Texture2D frame)
        {
            var faces = new List<FaceInfo>();
            
            // 简化的面部检测算法
            // 实际实现中，这里应该调用OpenCV、MediaPipe或其他面部检测库
            
            // 模拟检测结果 - 在实际项目中替换为真实的检测算法
            if (ShouldSimulateDetection())
            {
                // 创建一个模拟的面部检测结果
                var simulatedFace = CreateSimulatedFace(frame);
                if (simulatedFace != null)
                {
                    faces.Add(simulatedFace);
                }
            }
            
            return faces;
        }
        
        /// <summary>
        /// 判断是否应该模拟检测（用于演示和测试）
        /// </summary>
        /// <returns>是否模拟检测</returns>
        private bool ShouldSimulateDetection()
        {
            // 简单的模拟逻辑：70%的概率检测到面部
            return UnityEngine.Random.Range(0f, 1f) < 0.7f;
        }
        
        /// <summary>
        /// 创建模拟的面部检测结果
        /// </summary>
        /// <param name="frame">视频帧</param>
        /// <returns>模拟的面部信息</returns>
        private FaceInfo CreateSimulatedFace(Texture2D frame)
        {
            // 创建一个位于图像中心附近的模拟面部
            float centerX = 0.5f + UnityEngine.Random.Range(-0.1f, 0.1f);
            float centerY = 0.5f + UnityEngine.Random.Range(-0.1f, 0.1f);
            float size = UnityEngine.Random.Range(0.15f, 0.3f);
            
            var boundingBox = new Rect(
                centerX - size * 0.5f,
                centerY - size * 0.5f,
                size,
                size
            );
            
            var face = new FaceInfo(boundingBox, UnityEngine.Random.Range(0.7f, 0.95f))
            {
                faceId = GetNextFaceId(),
                timestamp = Time.time
            };
            
            return face;
        }
        
        /// <summary>
        /// 更新面部跟踪信息
        /// </summary>
        /// <param name="newFaces">新检测到的面部</param>
        /// <returns>更新跟踪信息后的面部列表</returns>
        private List<FaceInfo> UpdateFaceTracking(List<FaceInfo> newFaces)
        {
            var trackedFaces = new List<FaceInfo>();
            var currentTime = Time.time;
            
            // 匹配新检测的面部与已跟踪的面部
            foreach (var newFace in newFaces)
            {
                var bestMatch = FindBestTrackingMatch(newFace);
                
                if (bestMatch != null)
                {
                    // 更新已有跟踪
                    newFace.faceId = bestMatch.lastFace.faceId;
                    bestMatch.velocity = (newFace.center - bestMatch.lastFace.center) / (currentTime - bestMatch.lastSeenTime);
                    bestMatch.lastFace = newFace;
                    bestMatch.lastSeenTime = currentTime;
                    bestMatch.consecutiveDetections++;
                }
                else
                {
                    // 创建新的跟踪
                    newFace.faceId = GetNextFaceId();
                    faceTracker[newFace.faceId] = new FaceTrackingData(newFace);
                }
                
                trackedFaces.Add(newFace);
            }
            
            // 清理超时的跟踪
            var expiredIds = faceTracker.Where(kvp => currentTime - kvp.Value.lastSeenTime > config.trackingTimeout)
                                      .Select(kvp => kvp.Key)
                                      .ToList();
            
            foreach (var id in expiredIds)
            {
                faceTracker.Remove(id);
            }
            
            return trackedFaces;
        }
        
        /// <summary>
        /// 找到最佳的跟踪匹配
        /// </summary>
        /// <param name="newFace">新检测的面部</param>
        /// <returns>最佳匹配的跟踪数据</returns>
        private FaceTrackingData FindBestTrackingMatch(FaceInfo newFace)
        {
            FaceTrackingData bestMatch = null;
            float bestDistance = float.MaxValue;
            
            foreach (var tracker in faceTracker.Values)
            {
                float distance = Vector2.Distance(newFace.center, tracker.lastFace.center);
                
                // 考虑大小差异
                float sizeDiff = Mathf.Abs(newFace.size - tracker.lastFace.size);
                float totalDistance = distance + sizeDiff * 0.5f;
                
                if (totalDistance < bestDistance && totalDistance < 0.2f) // 阈值可调整
                {
                    bestDistance = totalDistance;
                    bestMatch = tracker;
                }
            }
            
            return bestMatch;
        }
        
        /// <summary>
        /// 过滤和排序面部
        /// </summary>
        /// <param name="faces">面部列表</param>
        /// <returns>过滤后的面部列表</returns>
        private List<FaceInfo> FilterAndSortFaces(List<FaceInfo> faces)
        {
            var filteredFaces = faces.Where(f => 
                f.confidence >= config.confidenceThreshold &&
                f.size >= config.minFaceSize &&
                f.size <= config.maxFaceSize
            ).ToList();
            
            // 按置信度和大小排序
            filteredFaces.Sort((a, b) => {
                float scoreA = a.confidence * 0.7f + a.size * 0.3f;
                float scoreB = b.confidence * 0.7f + b.size * 0.3f;
                return scoreB.CompareTo(scoreA);
            });
            
            // 限制最大面部数量
            if (filteredFaces.Count > config.maxFaceCount)
            {
                filteredFaces = filteredFaces.Take(config.maxFaceCount).ToList();
            }
            
            return filteredFaces;
        }
        
        /// <summary>
        /// 更新主要面部
        /// </summary>
        private void UpdatePrimaryFace()
        {
            FaceInfo newPrimaryFace = null;
            
            if (detectedFaces.Count > 0)
            {
                // 选择最大且置信度最高的面部作为主要面部
                newPrimaryFace = detectedFaces.First();
                newPrimaryFace.isPrimary = true;
                
                // 更新用户状态
                if (userStatus == UserPresenceStatus.Absent)
                {
                    userStatus = UserPresenceStatus.JustReturned;
                    OnUserReturned?.Invoke(newPrimaryFace);
                    Debug.Log("[FaceDetector] 用户返回");
                }
                else
                {
                    userStatus = UserPresenceStatus.Present;
                }
                
                lastUserSeenTime = Time.time;
            }
            
            // 检查主要面部是否发生变化
            if (newPrimaryFace != primaryFace)
            {
                if (primaryFace != null)
                {
                    primaryFace.isPrimary = false;
                }
                
                primaryFace = newPrimaryFace;
                OnPrimaryFaceChanged?.Invoke(primaryFace);
                
                if (primaryFace != null)
                {
                    Debug.Log($"[FaceDetector] 主要面部更新: {primaryFace}");
                }
            }
        }
        
        /// <summary>
        /// 检查用户在场状态
        /// </summary>
        private void CheckUserPresence()
        {
            if (!config.enableUserLeftDetection)
                return;
            
            float timeSinceLastSeen = Time.time - lastUserSeenTime;
            
            if (userStatus == UserPresenceStatus.Present && timeSinceLastSeen > config.userLeftTimeout)
            {
                userStatus = UserPresenceStatus.Absent;
                primaryFace = null;
                OnUserLeft?.Invoke();
                Debug.Log("[FaceDetector] 用户离开");
            }
        }
        
        /// <summary>
        /// 获取下一个面部ID
        /// </summary>
        /// <returns>新的面部ID</returns>
        private int GetNextFaceId()
        {
            return nextFaceId++;
        }
        
        public void SetConfiguration(FaceDetectionConfig config)
        {
            if (config == null)
            {
                Debug.LogError("[FaceDetector] 配置不能为空");
                return;
            }
            
            this.config = config;
            Debug.Log("[FaceDetector] 检测配置已更新");
        }
        
        public void Cleanup()
        {
            StopDetection();
            
            detectedFaces.Clear();
            faceTracker.Clear();
            primaryFace = null;
            
            status = FaceDetectionStatus.NotInitialized;
            
            Debug.Log("[FaceDetector] 面部检测器已清理");
        }
        
        /// <summary>
        /// 获取用户在场状态
        /// </summary>
        /// <returns>用户在场状态</returns>
        public UserPresenceStatus GetUserPresenceStatus()
        {
            return userStatus;
        }
        
        /// <summary>
        /// 获取检测统计信息
        /// </summary>
        /// <returns>检测统计信息</returns>
        public string GetDetectionStats()
        {
            return $"检测状态: {status}, 面部数量: {detectedFaces.Count}, 跟踪数量: {faceTracker.Count}, 用户状态: {userStatus}";
        }
    }
}