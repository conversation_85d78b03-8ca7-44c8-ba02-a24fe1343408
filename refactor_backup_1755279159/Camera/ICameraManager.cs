using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Camera
{
    /// <summary>
    /// 摄像头管理器接口
    /// 负责摄像头设备的检测、权限管理和视频流捕获
    /// </summary>
    public interface ICameraManager : IManager
    {
        /// <summary>
        /// 当前是否有摄像头权限
        /// </summary>
        bool HasCameraPermission { get; }
        
        /// <summary>
        /// 当前是否正在捕获视频
        /// </summary>
        bool IsCapturing { get; }
        
        /// <summary>
        /// 当前选中的摄像头设备
        /// </summary>
        CameraDevice CurrentCamera { get; }
        
        /// <summary>
        /// 可用的摄像头设备列表
        /// </summary>
        List<CameraDevice> AvailableCameras { get; }
        
        /// <summary>
        /// 摄像头权限状态变化事件
        /// </summary>
        event Action<bool> OnPermissionChanged;
        
        /// <summary>
        /// 视频帧更新事件
        /// </summary>
        event Action<Texture2D> OnFrameUpdated;
        
        /// <summary>
        /// 摄像头设备变化事件
        /// </summary>
        event Action<List<CameraDevice>> OnCameraDevicesChanged;
        
        /// <summary>
        /// 请求摄像头权限
        /// </summary>
        /// <returns>权限请求结果</returns>
        void RequestCameraPermission();
        
        /// <summary>
        /// 检测可用的摄像头设备
        /// </summary>
        void DetectCameraDevices();
        
        /// <summary>
        /// 切换到指定的摄像头设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>切换是否成功</returns>
        bool SwitchCamera(string deviceName);
        
        /// <summary>
        /// 开始视频捕获
        /// </summary>
        /// <param name="width">视频宽度</param>
        /// <param name="height">视频高度</param>
        /// <param name="fps">帧率</param>
        /// <returns>是否成功开始捕获</returns>
        bool StartCapture(int width = 1280, int height = 720, int fps = 30);
        
        /// <summary>
        /// 停止视频捕获
        /// </summary>
        void StopCapture();
        
        /// <summary>
        /// 获取当前视频帧
        /// </summary>
        /// <returns>当前帧的纹理</returns>
        Texture2D GetCurrentFrame();
        
        /// <summary>
        /// 设置视频参数
        /// </summary>
        /// <param name="config">视频配置</param>
        void SetVideoConfiguration(VideoConfiguration config);
    }
}