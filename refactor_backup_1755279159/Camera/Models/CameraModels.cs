using System;
using UnityEngine;

namespace DigitalHuman.Core.Camera
{
    /// <summary>
    /// 摄像头设备信息
    /// </summary>
    [Serializable]
    public class CameraDevice
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string deviceName;
        
        /// <summary>
        /// 设备显示名称
        /// </summary>
        public string displayName;
        
        /// <summary>
        /// 是否为前置摄像头
        /// </summary>
        public bool isFrontFacing;
        
        /// <summary>
        /// 支持的分辨率列表
        /// </summary>
        public Resolution[] supportedResolutions;
        
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool isAvailable;
        
        public CameraDevice()
        {
            supportedResolutions = new Resolution[0];
            isAvailable = true;
        }
        
        public CameraDevice(string name, string display, bool frontFacing = false)
        {
            deviceName = name;
            displayName = display;
            isFrontFacing = frontFacing;
            supportedResolutions = new Resolution[0];
            isAvailable = true;
        }
        
        public override string ToString()
        {
            return $"{displayName} ({deviceName})";
        }
    }
    
    /// <summary>
    /// 视频配置参数
    /// </summary>
    [Serializable]
    public class VideoConfiguration
    {
        /// <summary>
        /// 视频宽度
        /// </summary>
        public int width = 1280;
        
        /// <summary>
        /// 视频高度
        /// </summary>
        public int height = 720;
        
        /// <summary>
        /// 帧率
        /// </summary>
        public int fps = 30;
        
        /// <summary>
        /// 是否启用自动对焦
        /// </summary>
        public bool autoFocus = true;
        
        /// <summary>
        /// 是否启用自动曝光
        /// </summary>
        public bool autoExposure = true;
        
        /// <summary>
        /// 视频质量 (0-1)
        /// </summary>
        public float quality = 0.8f;
        
        public VideoConfiguration() { }
        
        public VideoConfiguration(int w, int h, int f)
        {
            width = w;
            height = h;
            fps = f;
        }
        
        public override string ToString()
        {
            return $"{width}x{height}@{fps}fps";
        }
    }
    
    /// <summary>
    /// 摄像头权限状态
    /// </summary>
    public enum CameraPermissionStatus
    {
        /// <summary>
        /// 未请求
        /// </summary>
        NotRequested,
        
        /// <summary>
        /// 请求中
        /// </summary>
        Requesting,
        
        /// <summary>
        /// 已授权
        /// </summary>
        Granted,
        
        /// <summary>
        /// 被拒绝
        /// </summary>
        Denied,
        
        /// <summary>
        /// 不支持
        /// </summary>
        NotSupported
    }
    
    /// <summary>
    /// 视频捕获状态
    /// </summary>
    public enum CaptureStatus
    {
        /// <summary>
        /// 停止
        /// </summary>
        Stopped,
        
        /// <summary>
        /// 启动中
        /// </summary>
        Starting,
        
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        
        /// <summary>
        /// 暂停
        /// </summary>
        Paused,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error
    }
    
    /// <summary>
    /// 摄像头错误类型
    /// </summary>
    public enum CameraError
    {
        /// <summary>
        /// 无错误
        /// </summary>
        None,
        
        /// <summary>
        /// 权限被拒绝
        /// </summary>
        PermissionDenied,
        
        /// <summary>
        /// 设备不可用
        /// </summary>
        DeviceNotAvailable,
        
        /// <summary>
        /// 设备被占用
        /// </summary>
        DeviceBusy,
        
        /// <summary>
        /// 不支持的分辨率
        /// </summary>
        UnsupportedResolution,
        
        /// <summary>
        /// 初始化失败
        /// </summary>
        InitializationFailed,
        
        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// 面部信息
    /// </summary>
    [Serializable]
    public class FaceInfo
    {
        /// <summary>
        /// 面部边界框（归一化坐标 0-1）
        /// </summary>
        public Rect boundingBox;
        
        /// <summary>
        /// 面部中心点（归一化坐标 0-1）
        /// </summary>
        public Vector2 center;
        
        /// <summary>
        /// 面部大小（相对于图像大小的比例）
        /// </summary>
        public float size;
        
        /// <summary>
        /// 检测置信度 (0-1)
        /// </summary>
        public float confidence;
        
        /// <summary>
        /// 面部角度（度）
        /// </summary>
        public Vector3 rotation;
        
        /// <summary>
        /// 面部关键点（如果支持）
        /// </summary>
        public Vector2[] landmarks;
        
        /// <summary>
        /// 面部ID（用于跟踪）
        /// </summary>
        public int faceId;
        
        /// <summary>
        /// 检测时间戳
        /// </summary>
        public float timestamp;
        
        /// <summary>
        /// 是否为主要面部
        /// </summary>
        public bool isPrimary;
        
        public FaceInfo()
        {
            boundingBox = new Rect();
            center = Vector2.zero;
            size = 0f;
            confidence = 0f;
            rotation = Vector3.zero;
            landmarks = new Vector2[0];
            faceId = -1;
            timestamp = Time.time;
            isPrimary = false;
        }
        
        public FaceInfo(Rect bbox, float conf)
        {
            boundingBox = bbox;
            center = new Vector2(bbox.x + bbox.width * 0.5f, bbox.y + bbox.height * 0.5f);
            size = Mathf.Max(bbox.width, bbox.height);
            confidence = conf;
            rotation = Vector3.zero;
            landmarks = new Vector2[0];
            faceId = -1;
            timestamp = Time.time;
            isPrimary = false;
        }
        
        /// <summary>
        /// 获取面部在屏幕坐标系中的位置
        /// </summary>
        /// <param name="screenWidth">屏幕宽度</param>
        /// <param name="screenHeight">屏幕高度</param>
        /// <returns>屏幕坐标</returns>
        public Vector2 GetScreenPosition(int screenWidth, int screenHeight)
        {
            return new Vector2(center.x * screenWidth, center.y * screenHeight);
        }
        
        /// <summary>
        /// 获取面部在世界坐标系中的方向
        /// </summary>
        /// <param name="cameraTransform">摄像机变换</param>
        /// <returns>世界坐标方向</returns>
        public Vector3 GetWorldDirection(Transform cameraTransform)
        {
            // 将归一化的面部位置转换为世界方向
            Vector3 screenPos = new Vector3(center.x * 2f - 1f, center.y * 2f - 1f, 1f);
            return cameraTransform.TransformDirection(screenPos.normalized);
        }
        
        public override string ToString()
        {
            return $"Face[ID:{faceId}, Center:({center.x:F2},{center.y:F2}), Size:{size:F2}, Confidence:{confidence:F2}]";
        }
    }
    
    /// <summary>
    /// 面部检测配置
    /// </summary>
    [Serializable]
    public class FaceDetectionConfig
    {
        /// <summary>
        /// 最小面部大小（相对于图像大小的比例）
        /// </summary>
        public float minFaceSize = 0.1f;
        
        /// <summary>
        /// 最大面部大小（相对于图像大小的比例）
        /// </summary>
        public float maxFaceSize = 0.8f;
        
        /// <summary>
        /// 检测置信度阈值
        /// </summary>
        public float confidenceThreshold = 0.7f;
        
        /// <summary>
        /// 检测频率（每秒检测次数）
        /// </summary>
        public int detectionFPS = 10;
        
        /// <summary>
        /// 是否启用面部跟踪
        /// </summary>
        public bool enableTracking = true;
        
        /// <summary>
        /// 跟踪超时时间（秒）
        /// </summary>
        public float trackingTimeout = 2f;
        
        /// <summary>
        /// 是否检测面部关键点
        /// </summary>
        public bool detectLandmarks = false;
        
        /// <summary>
        /// 是否启用用户离开检测
        /// </summary>
        public bool enableUserLeftDetection = true;
        
        /// <summary>
        /// 用户离开判定时间（秒）
        /// </summary>
        public float userLeftTimeout = 3f;
        
        /// <summary>
        /// 是否启用多面部检测
        /// </summary>
        public bool enableMultiFaceDetection = false;
        
        /// <summary>
        /// 最大检测面部数量
        /// </summary>
        public int maxFaceCount = 1;
        
        public FaceDetectionConfig()
        {
            // 使用默认值
        }
        
        public FaceDetectionConfig(float minSize, float maxSize, float confidence)
        {
            minFaceSize = minSize;
            maxFaceSize = maxSize;
            confidenceThreshold = confidence;
        }
    }
    
    /// <summary>
    /// 面部检测状态
    /// </summary>
    public enum FaceDetectionStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized,
        
        /// <summary>
        /// 已初始化但未开始检测
        /// </summary>
        Initialized,
        
        /// <summary>
        /// 正在检测
        /// </summary>
        Detecting,
        
        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,
        
        /// <summary>
        /// 错误状态
        /// </summary>
        Error
    }
    
    /// <summary>
    /// 用户状态
    /// </summary>
    public enum UserPresenceStatus
    {
        /// <summary>
        /// 未知
        /// </summary>
        Unknown,
        
        /// <summary>
        /// 用户在场
        /// </summary>
        Present,
        
        /// <summary>
        /// 用户离开
        /// </summary>
        Absent,
        
        /// <summary>
        /// 用户刚刚返回
        /// </summary>
        JustReturned
    }
}