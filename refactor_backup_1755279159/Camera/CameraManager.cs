using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace DigitalHuman.Core.Camera
{
    /// <summary>
    /// 摄像头管理器实现
    /// 负责摄像头设备的检测、权限管理和视频流捕获
    /// </summary>
    public class CameraManager : ManagerBase, ICameraManager
    {
        [Header("摄像头配置")]
        [SerializeField] private VideoConfiguration defaultConfig = new VideoConfiguration();
        [SerializeField] private bool autoStartCapture = false;
        [SerializeField] private float permissionTimeoutSeconds = 10f;
        
        // 私有字段
        private WebCamTexture webCamTexture;
        private CameraPermissionStatus permissionStatus = CameraPermissionStatus.NotRequested;
        private CaptureStatus captureStatus = CaptureStatus.Stopped;
        private List<CameraDevice> availableCameras = new List<CameraDevice>();
        private CameraDevice currentCamera;
        private Texture2D currentFrame;
        private VideoConfiguration currentConfig;
        private Coroutine permissionCoroutine;
        
        // 公共属性
        public bool HasCameraPermission => permissionStatus == CameraPermissionStatus.Granted;
        public bool IsCapturing => captureStatus == CaptureStatus.Running;
        public CameraDevice CurrentCamera => currentCamera;
        public List<CameraDevice> AvailableCameras => new List<CameraDevice>(availableCameras);
        
        // 事件
        public event Action<bool> OnPermissionChanged;
        public event Action<Texture2D> OnFrameUpdated;
        public event Action<List<CameraDevice>> OnCameraDevicesChanged;
        
        protected override void OnInitialize()
        {
            base.OnInitialize();
            currentConfig = new VideoConfiguration(defaultConfig.width, defaultConfig.height, defaultConfig.fps);
            
            // 检测摄像头设备
            DetectCameraDevices();
            
            // 如果设置了自动开始捕获，请求权限
            if (autoStartCapture)
            {
                RequestCameraPermission();
            }
            
            LogInfo("摄像头管理器初始化完成");
        }
        
        protected override void OnCleanup()
        {
            StopCapture();
            
            if (permissionCoroutine != null)
            {
                StopCoroutine(permissionCoroutine);
                permissionCoroutine = null;
            }
            
            if (currentFrame != null)
            {
                DestroyImmediate(currentFrame);
                currentFrame = null;
            }
            
            base.OnCleanup();
        }
        
        public void RequestCameraPermission()
        {
            if (permissionStatus == CameraPermissionStatus.Granted)
            {
                OnPermissionChanged?.Invoke(true);
                return;
            }
            
            if (permissionStatus == CameraPermissionStatus.Requesting)
            {
                LogWarning("权限请求已在进行中");
                return;
            }
            
            permissionStatus = CameraPermissionStatus.Requesting;
            LogInfo("请求摄像头权限");
            
            // 启动权限请求协程
            if (permissionCoroutine != null)
            {
                StopCoroutine(permissionCoroutine);
            }
            permissionCoroutine = StartCoroutine(RequestPermissionCoroutine());
        }
        
        private IEnumerator RequestPermissionCoroutine()
        {
            // 在不同平台上请求权限的方式不同
#if UNITY_ANDROID || UNITY_IOS
            // 移动平台权限请求
            if (!Application.HasUserAuthorization(UserAuthorization.WebCam))
            {
                yield return Application.RequestUserAuthorization(UserAuthorization.WebCam);
            }
            
            bool hasPermission = Application.HasUserAuthorization(UserAuthorization.WebCam);
#else
            // 桌面平台通常不需要显式权限请求，但需要检查设备可用性
            bool hasPermission = WebCamTexture.devices.Length > 0;
            yield return null; // 等待一帧
#endif
            
            // 等待权限结果，设置超时
            float timeoutTime = Time.time + permissionTimeoutSeconds;
            while (Time.time < timeoutTime)
            {
#if UNITY_ANDROID || UNITY_IOS
                hasPermission = Application.HasUserAuthorization(UserAuthorization.WebCam);
#endif
                if (hasPermission)
                {
                    break;
                }
                yield return new WaitForSeconds(0.1f);
            }
            
            // 更新权限状态
            if (hasPermission)
            {
                permissionStatus = CameraPermissionStatus.Granted;
                LogInfo("摄像头权限已获得");
                
                // 重新检测设备
                DetectCameraDevices();
                
                // 如果设置了自动开始捕获，开始捕获
                if (autoStartCapture && availableCameras.Count > 0)
                {
                    StartCapture();
                }
            }
            else
            {
                permissionStatus = CameraPermissionStatus.Denied;
                LogError("摄像头权限被拒绝");
            }
            
            OnPermissionChanged?.Invoke(hasPermission);
            permissionCoroutine = null;
        }
        
        public void DetectCameraDevices()
        {
            availableCameras.Clear();
            
            try
            {
                WebCamDevice[] devices = WebCamTexture.devices;
                LogInfo($"检测到 {devices.Length} 个摄像头设备");
                
                for (int i = 0; i < devices.Length; i++)
                {
                    var device = devices[i];
                    var cameraDevice = new CameraDevice(
                        device.name,
                        string.IsNullOrEmpty(device.name) ? $"摄像头 {i + 1}" : device.name,
                        device.isFrontFacing
                    );
                    
                    // 获取支持的分辨率（Unity的WebCamTexture不直接提供这个信息）
                    cameraDevice.supportedResolutions = GetSupportedResolutions();
                    
                    availableCameras.Add(cameraDevice);
                    LogInfo($"发现摄像头: {cameraDevice}");
                }
                
                // 如果没有当前摄像头或当前摄像头不可用，选择第一个可用的
                if (currentCamera == null || !availableCameras.Any(c => c.deviceName == currentCamera.deviceName))
                {
                    currentCamera = availableCameras.FirstOrDefault();
                    if (currentCamera != null)
                    {
                        LogInfo($"选择默认摄像头: {currentCamera}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"检测摄像头设备时发生错误: {ex.Message}");
            }
            
            OnCameraDevicesChanged?.Invoke(AvailableCameras);
        }
        
        private Resolution[] GetSupportedResolutions()
        {
            // Unity的WebCamTexture不直接提供支持的分辨率信息
            // 这里返回一些常见的分辨率
            return new Resolution[]
            {
                new Resolution { width = 640, height = 480 },
                new Resolution { width = 1280, height = 720 },
                new Resolution { width = 1920, height = 1080 }
            };
        }
        
        public bool SwitchCamera(string deviceName)
        {
            if (string.IsNullOrEmpty(deviceName))
            {
                LogError("设备名称不能为空");
                return false;
            }
            
            var targetCamera = availableCameras.FirstOrDefault(c => c.deviceName == deviceName);
            if (targetCamera == null)
            {
                LogError($"未找到摄像头设备: {deviceName}");
                return false;
            }
            
            // 如果正在捕获，先停止
            bool wasCapturing = IsCapturing;
            if (wasCapturing)
            {
                StopCapture();
            }
            
            currentCamera = targetCamera;
            LogInfo($"切换到摄像头: {currentCamera}");
            
            // 如果之前在捕获，重新开始捕获
            if (wasCapturing)
            {
                return StartCapture();
            }
            
            return true;
        }
        
        public bool StartCapture(int width = 1280, int height = 720, int fps = 30)
        {
            if (!HasCameraPermission)
            {
                LogError("没有摄像头权限，无法开始捕获");
                return false;
            }
            
            if (currentCamera == null)
            {
                LogError("没有可用的摄像头设备");
                return false;
            }
            
            if (IsCapturing)
            {
                LogWarning("视频捕获已在运行中");
                return true;
            }
            
            try
            {
                captureStatus = CaptureStatus.Starting;
                
                // 更新配置
                currentConfig.width = width;
                currentConfig.height = height;
                currentConfig.fps = fps;
                
                // 创建WebCamTexture
                webCamTexture = new WebCamTexture(currentCamera.deviceName, width, height, fps);
                
                // 开始播放
                webCamTexture.Play();
                
                // 等待摄像头启动
                StartCoroutine(WaitForCameraStart());
                
                LogInfo($"开始视频捕获: {currentConfig}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"开始视频捕获失败: {ex.Message}");
                captureStatus = CaptureStatus.Error;
                return false;
            }
        }
        
        private IEnumerator WaitForCameraStart()
        {
            float timeout = 5f;
            float startTime = Time.time;
            
            while (!webCamTexture.isPlaying && Time.time - startTime < timeout)
            {
                yield return new WaitForSeconds(0.1f);
            }
            
            if (webCamTexture.isPlaying)
            {
                captureStatus = CaptureStatus.Running;
                LogInfo("摄像头启动成功");
                
                // 开始帧更新协程
                StartCoroutine(UpdateFrameCoroutine());
            }
            else
            {
                captureStatus = CaptureStatus.Error;
                LogError("摄像头启动超时");
            }
        }
        
        private IEnumerator UpdateFrameCoroutine()
        {
            while (IsCapturing && webCamTexture != null && webCamTexture.isPlaying)
            {
                // 检查是否有新帧
                if (webCamTexture.didUpdateThisFrame)
                {
                    UpdateCurrentFrame();
                }
                
                yield return null; // 等待下一帧
            }
        }
        
        private void UpdateCurrentFrame()
        {
            if (webCamTexture == null || !webCamTexture.isPlaying)
                return;
            
            try
            {
                // 创建或更新当前帧纹理
                if (currentFrame == null || 
                    currentFrame.width != webCamTexture.width || 
                    currentFrame.height != webCamTexture.height)
                {
                    if (currentFrame != null)
                    {
                        DestroyImmediate(currentFrame);
                    }
                    
                    currentFrame = new Texture2D(webCamTexture.width, webCamTexture.height, TextureFormat.RGB24, false);
                }
                
                // 复制像素数据
                Color32[] pixels = webCamTexture.GetPixels32();
                currentFrame.SetPixels32(pixels);
                currentFrame.Apply();
                
                // 触发帧更新事件
                OnFrameUpdated?.Invoke(currentFrame);
            }
            catch (Exception ex)
            {
                LogError($"更新视频帧失败: {ex.Message}");
            }
        }
        
        public void StopCapture()
        {
            if (captureStatus == CaptureStatus.Stopped)
                return;
            
            try
            {
                captureStatus = CaptureStatus.Stopped;
                
                if (webCamTexture != null)
                {
                    if (webCamTexture.isPlaying)
                    {
                        webCamTexture.Stop();
                    }
                    
                    DestroyImmediate(webCamTexture);
                    webCamTexture = null;
                }
                
                LogInfo("视频捕获已停止");
            }
            catch (Exception ex)
            {
                LogError($"停止视频捕获失败: {ex.Message}");
            }
        }
        
        public Texture2D GetCurrentFrame()
        {
            return currentFrame;
        }
        
        public void SetVideoConfiguration(VideoConfiguration config)
        {
            if (config == null)
            {
                LogError("视频配置不能为空");
                return;
            }
            
            currentConfig = config;
            LogInfo($"更新视频配置: {config}");
            
            // 如果正在捕获，重新启动以应用新配置
            if (IsCapturing)
            {
                StopCapture();
                StartCapture(config.width, config.height, config.fps);
            }
        }
        
        protected override void LogInfo(string message)
        {
            Debug.Log($"[CameraManager] {message}");
        }
        
        protected override void LogWarning(string message)
        {
            Debug.LogWarning($"[CameraManager] {message}");
        }
        
        protected override void LogError(string message)
        {
            Debug.LogError($"[CameraManager] {message}");
        }
    }
}