using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Network.Models;
using DigitalHuman.Core.Configuration.Models;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 大模型API客户端实现
    /// </summary>
    public class LLMApiClient : MonoBehaviour, ILLMApiClient
    {
        [Header("组件引用")]
        [SerializeField] private ApiClientManager _apiClientManager;
        
        /// <summary>
        /// 当前使用的配置
        /// </summary>
        public LLMApiConfiguration CurrentConfiguration { get; private set; }
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; private set; }
        
        /// <summary>
        /// 请求开始事件
        /// </summary>
        public event Action<LLMChatRequest> OnRequestStarted;
        
        /// <summary>
        /// 请求完成事件
        /// </summary>
        public event Action<LLMChatRequest, LLMChatResponse> OnRequestCompleted;
        
        /// <summary>
        /// 请求失败事件
        /// </summary>
        public event Action<LLMChatRequest, ApiException> OnRequestFailed;
        
        /// <summary>
        /// Unity Awake生命周期
        /// </summary>
        private void Awake()
        {
            if (_apiClientManager == null)
            {
                _apiClientManager = FindObjectOfType<ApiClientManager>();
                if (_apiClientManager == null)
                {
                    Debug.LogError("未找到ApiClientManager组件");
                }
            }
        }
        
        /// <summary>
        /// 初始化客户端
        /// </summary>
        /// <param name="configuration">配置信息</param>
        public void Initialize(LLMApiConfiguration configuration)
        {
            if (configuration == null)
            {
                throw new ArgumentNullException(nameof(configuration));
            }
            
            var validation = configuration.Validate();
            if (!validation.IsValid)
            {
                throw new ArgumentException($"配置验证失败: {validation.GetErrorMessage()}");
            }
            
            CurrentConfiguration = configuration;
            IsInitialized = true;
            
            Debug.Log($"大模型API客户端初始化完成: {configuration.Name} ({configuration.Provider})");
        }
        
        /// <summary>
        /// 发送聊天请求
        /// </summary>
        /// <param name="request">聊天请求</param>
        /// <returns>聊天响应</returns>
        public async Task<ApiResponse<LLMChatResponse>> SendChatRequestAsync(LLMChatRequest request)
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException("客户端未初始化");
            }
            
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }
            
            // 触发请求开始事件
            OnRequestStarted?.Invoke(request);
            
            try
            {
                // 设置模型名称
                request.model = CurrentConfiguration.ModelName;
                
                // 应用配置参数
                if (request.temperature <= 0)
                {
                    request.temperature = CurrentConfiguration.Temperature;
                }
                
                if (request.max_tokens <= 0)
                {
                    request.max_tokens = CurrentConfiguration.MaxTokens;
                }
                
                // 构建API请求
                var apiRequest = BuildApiRequest(request);
                
                // 发送请求
                var response = await _apiClientManager.SendApiRequestAsync("LLM_Chat", apiRequest);
                
                if (response.IsSuccess)
                {
                    // 解析响应
                    var chatResponse = ParseChatResponse(response.Data);
                    
                    // 触发请求完成事件
                    OnRequestCompleted?.Invoke(request, chatResponse);
                    
                    return ApiResponse<LLMChatResponse>.Success(chatResponse, response.StatusCode);
                }
                else
                {
                    // 解析错误响应
                    var errorMessage = ParseErrorResponse(response.Data) ?? response.ErrorMessage;
                    var apiException = new ApiException(ApiErrorType.ServerError, errorMessage, response.StatusCode, response.Data);
                    
                    // 触发请求失败事件
                    OnRequestFailed?.Invoke(request, apiException);
                    
                    return ApiResponse<LLMChatResponse>.Failure(response.StatusCode, errorMessage);
                }
            }
            catch (ApiException ex)
            {
                OnRequestFailed?.Invoke(request, ex);
                throw;
            }
            catch (Exception ex)
            {
                var apiException = new ApiException(ApiErrorType.Unknown, $"请求处理异常: {ex.Message}", 0, null, ex);
                OnRequestFailed?.Invoke(request, apiException);
                throw apiException;
            }
        }
        
        /// <summary>
        /// 发送简单文本请求
        /// </summary>
        /// <param name="message">用户消息</param>
        /// <param name="systemPrompt">系统提示（可选）</param>
        /// <returns>响应文本</returns>
        public async Task<ApiResponse<string>> SendTextRequestAsync(string message, string systemPrompt = null)
        {
            if (string.IsNullOrEmpty(message))
            {
                throw new ArgumentException("消息不能为空", nameof(message));
            }
            
            var request = new LLMChatRequest();
            
            // 添加系统提示
            if (!string.IsNullOrEmpty(systemPrompt))
            {
                request.AddSystemMessage(systemPrompt);
            }
            
            // 添加用户消息
            request.AddUserMessage(message);
            
            var response = await SendChatRequestAsync(request);
            
            if (response.IsSuccess)
            {
                var content = response.Data.GetContent();
                return ApiResponse<string>.Success(content, response.StatusCode);
            }
            else
            {
                return ApiResponse<string>.Failure(response.StatusCode, response.ErrorMessage);
            }
        }
        
        /// <summary>
        /// 使用对话上下文发送请求
        /// </summary>
        /// <param name="context">对话上下文</param>
        /// <param name="userMessage">用户消息</param>
        /// <returns>响应文本</returns>
        public async Task<ApiResponse<string>> SendContextRequestAsync(ConversationContext context, string userMessage)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            if (string.IsNullOrEmpty(userMessage))
            {
                throw new ArgumentException("用户消息不能为空", nameof(userMessage));
            }
            
            // 添加用户消息到上下文
            context.AddUserMessage(userMessage);
            
            // 构建请求
            var request = new LLMChatRequest();
            request.messages = context.GetMessagesForRequest();
            
            var response = await SendChatRequestAsync(request);
            
            if (response.IsSuccess)
            {
                var content = response.Data.GetContent();
                
                // 将助手响应添加到上下文
                context.AddAssistantMessage(content);
                
                return ApiResponse<string>.Success(content, response.StatusCode);
            }
            else
            {
                // 如果请求失败，从上下文中移除用户消息
                if (context.Messages.Count > 0 && 
                    context.Messages[context.Messages.Count - 1].content == userMessage)
                {
                    context.Messages.RemoveAt(context.Messages.Count - 1);
                }
                
                return ApiResponse<string>.Failure(response.StatusCode, response.ErrorMessage);
            }
        }
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<ApiResponse<bool>> ValidateConfigurationAsync()
        {
            if (!IsInitialized)
            {
                return ApiResponse<bool>.Failure(0, "客户端未初始化");
            }
            
            try
            {
                // 发送简单的测试请求
                var testRequest = new LLMChatRequest();
                testRequest.AddUserMessage("Hello");
                testRequest.max_tokens = 10; // 限制令牌数以节省成本
                
                var response = await SendChatRequestAsync(testRequest);
                
                return ApiResponse<bool>.Success(response.IsSuccess, response.StatusCode);
            }
            catch (Exception ex)
            {
                return ApiResponse<bool>.Failure(0, $"配置验证失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取模型信息
        /// </summary>
        /// <returns>模型信息</returns>
        public async Task<ApiResponse<object>> GetModelInfoAsync()
        {
            if (!IsInitialized)
            {
                return ApiResponse<object>.Failure(0, "客户端未初始化");
            }
            
            // 构建模型信息请求
            var apiRequest = ApiRequest.Get(GetModelsEndpoint())
                .AddHeader("Authorization", $"Bearer {CurrentConfiguration.ApiKey}")
                .AddHeader("Content-Type", "application/json");
            
            try
            {
                var response = await _apiClientManager.SendApiRequestAsync("LLM_Models", apiRequest);
                
                if (response.IsSuccess)
                {
                    // 这里可以根据不同的提供商解析不同的响应格式
                    return ApiResponse<object>.Success(response.Data, response.StatusCode);
                }
                else
                {
                    return ApiResponse<object>.Failure(response.StatusCode, response.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.Failure(0, $"获取模型信息失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        public void UpdateConfiguration(LLMApiConfiguration configuration)
        {
            Initialize(configuration);
        }
        
        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            CurrentConfiguration = null;
            IsInitialized = false;
            
            // 清除事件订阅
            OnRequestStarted = null;
            OnRequestCompleted = null;
            OnRequestFailed = null;
            
            Debug.Log("大模型API客户端资源清理完成");
        }
        
        /// <summary>
        /// 构建API请求
        /// </summary>
        /// <param name="chatRequest">聊天请求</param>
        /// <returns>API请求对象</returns>
        private ApiRequest BuildApiRequest(LLMChatRequest chatRequest)
        {
            var jsonBody = JsonUtility.ToJson(chatRequest);
            var endpoint = GetChatEndpoint();
            
            var apiRequest = ApiRequest.Post(endpoint, jsonBody)
                .AddHeader("Authorization", GetAuthorizationHeader())
                .AddHeader("Content-Type", "application/json");
            
            // 根据提供商添加特定的请求头
            AddProviderSpecificHeaders(apiRequest);
            
            return apiRequest;
        }
        
        /// <summary>
        /// 获取聊天端点URL
        /// </summary>
        /// <returns>端点URL</returns>
        private string GetChatEndpoint()
        {
            var baseUrl = CurrentConfiguration.ApiEndpoint.TrimEnd('/');
            
            return CurrentConfiguration.Provider.ToLower() switch
            {
                "openai" => $"{baseUrl}/v1/chat/completions",
                "azure" => $"{baseUrl}/openai/deployments/{CurrentConfiguration.ModelName}/chat/completions?api-version=2023-12-01-preview",
                "baidu" => $"{baseUrl}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
                "alibaba" => $"{baseUrl}/v1/services/aigc/text-generation/generation",
                "tencent" => $"{baseUrl}/v1/chat/completions",
                "zhipu" => $"{baseUrl}/api/paas/v4/chat/completions",
                _ => $"{baseUrl}/v1/chat/completions"
            };
        }
        
        /// <summary>
        /// 获取模型端点URL
        /// </summary>
        /// <returns>端点URL</returns>
        private string GetModelsEndpoint()
        {
            var baseUrl = CurrentConfiguration.ApiEndpoint.TrimEnd('/');
            
            return CurrentConfiguration.Provider.ToLower() switch
            {
                "openai" => $"{baseUrl}/v1/models",
                "azure" => $"{baseUrl}/openai/models?api-version=2023-12-01-preview",
                _ => $"{baseUrl}/v1/models"
            };
        }
        
        /// <summary>
        /// 获取认证头
        /// </summary>
        /// <returns>认证头值</returns>
        private string GetAuthorizationHeader()
        {
            return CurrentConfiguration.Provider.ToLower() switch
            {
                "azure" => CurrentConfiguration.ApiKey,
                _ => $"Bearer {CurrentConfiguration.ApiKey}"
            };
        }
        
        /// <summary>
        /// 添加提供商特定的请求头
        /// </summary>
        /// <param name="request">API请求对象</param>
        private void AddProviderSpecificHeaders(ApiRequest request)
        {
            switch (CurrentConfiguration.Provider.ToLower())
            {
                case "azure":
                    request.AddHeader("api-key", CurrentConfiguration.ApiKey);
                    break;
                case "baidu":
                    // 百度需要access_token，这里简化处理
                    break;
                case "alibaba":
                    request.AddHeader("X-DashScope-SSE", "disable");
                    break;
            }
        }
        
        /// <summary>
        /// 解析聊天响应
        /// </summary>
        /// <param name="responseData">响应数据</param>
        /// <returns>聊天响应对象</returns>
        private LLMChatResponse ParseChatResponse(string responseData)
        {
            try
            {
                return JsonUtility.FromJson<LLMChatResponse>(responseData);
            }
            catch (Exception ex)
            {
                Debug.LogError($"解析聊天响应失败: {ex.Message}");
                throw new ApiException(ApiErrorType.Unknown, $"响应解析失败: {ex.Message}", 0, responseData, ex);
            }
        }
        
        /// <summary>
        /// 解析错误响应
        /// </summary>
        /// <param name="responseData">响应数据</param>
        /// <returns>错误消息</returns>
        private string ParseErrorResponse(string responseData)
        {
            try
            {
                if (string.IsNullOrEmpty(responseData))
                {
                    return null;
                }
                
                var errorResponse = JsonUtility.FromJson<LLMErrorResponse>(responseData);
                return errorResponse?.error?.message;
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary>
        /// Unity OnDestroy生命周期
        /// </summary>
        private void OnDestroy()
        {
            Cleanup();
        }
    }
}