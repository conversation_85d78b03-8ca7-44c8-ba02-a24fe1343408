using System;
using UnityEngine;

namespace DigitalHuman.Core.Network.Models
{
    /// <summary>
    /// 网络连接状态枚举
    /// </summary>
    public enum NetworkConnectionStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,
        
        /// <summary>
        /// 已连接
        /// </summary>
        Connected,
        
        /// <summary>
        /// 已断开
        /// </summary>
        Disconnected,
        
        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,
        
        /// <summary>
        /// 连接不稳定
        /// </summary>
        Unstable
    }
    
    /// <summary>
    /// 网络类型枚举
    /// </summary>
    public enum NetworkType
    {
        /// <summary>
        /// 未知
        /// </summary>
        Unknown,
        
        /// <summary>
        /// 无网络
        /// </summary>
        None,
        
        /// <summary>
        /// WiFi
        /// </summary>
        WiFi,
        
        /// <summary>
        /// 移动网络
        /// </summary>
        Mobile,
        
        /// <summary>
        /// 以太网
        /// </summary>
        Ethernet
    }
    
    /// <summary>
    /// 应用模式枚举
    /// </summary>
    public enum ApplicationMode
    {
        /// <summary>
        /// 在线模式
        /// </summary>
        Online,
        
        /// <summary>
        /// 离线模式
        /// </summary>
        Offline,
        
        /// <summary>
        /// 混合模式（部分功能离线）
        /// </summary>
        Hybrid
    }
    
    /// <summary>
    /// 网络状态信息
    /// </summary>
    [Serializable]
    public class NetworkStatusInfo
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        public NetworkConnectionStatus ConnectionStatus { get; set; }
        
        /// <summary>
        /// 网络类型
        /// </summary>
        public NetworkType NetworkType { get; set; }
        
        /// <summary>
        /// 应用模式
        /// </summary>
        public ApplicationMode ApplicationMode { get; set; }
        
        /// <summary>
        /// 是否可达互联网
        /// </summary>
        public bool IsInternetReachable { get; set; }
        
        /// <summary>
        /// 网络延迟（毫秒）
        /// </summary>
        public int LatencyMs { get; set; }
        
        /// <summary>
        /// 连接质量评分（0-100）
        /// </summary>
        public int QualityScore { get; set; }
        
        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastCheckTime { get; set; }
        
        /// <summary>
        /// 连接建立时间
        /// </summary>
        public DateTime? ConnectedSince { get; set; }
        
        /// <summary>
        /// 断开连接时间
        /// </summary>
        public DateTime? DisconnectedSince { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public NetworkStatusInfo()
        {
            ConnectionStatus = NetworkConnectionStatus.Unknown;
            NetworkType = NetworkType.Unknown;
            ApplicationMode = ApplicationMode.Online;
            IsInternetReachable = false;
            LatencyMs = -1;
            QualityScore = 0;
            LastCheckTime = DateTime.Now;
        }
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline => ConnectionStatus == NetworkConnectionStatus.Connected && IsInternetReachable;
        
        /// <summary>
        /// 是否离线
        /// </summary>
        public bool IsOffline => ConnectionStatus == NetworkConnectionStatus.Disconnected || !IsInternetReachable;
        
        /// <summary>
        /// 连接是否稳定
        /// </summary>
        public bool IsStable => ConnectionStatus == NetworkConnectionStatus.Connected && QualityScore >= 70;
        
        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <returns>状态描述文本</returns>
        public string GetStatusDescription()
        {
            return ConnectionStatus switch
            {
                NetworkConnectionStatus.Connected => IsInternetReachable ? "已连接到互联网" : "已连接到本地网络",
                NetworkConnectionStatus.Disconnected => "网络已断开",
                NetworkConnectionStatus.Connecting => "正在连接...",
                NetworkConnectionStatus.Unstable => "网络连接不稳定",
                _ => "网络状态未知"
            };
        }
        
        /// <summary>
        /// 获取网络类型描述
        /// </summary>
        /// <returns>网络类型描述</returns>
        public string GetNetworkTypeDescription()
        {
            return NetworkType switch
            {
                NetworkType.WiFi => "WiFi",
                NetworkType.Mobile => "移动网络",
                NetworkType.Ethernet => "以太网",
                NetworkType.None => "无网络",
                _ => "未知网络"
            };
        }
        
        /// <summary>
        /// 获取质量等级
        /// </summary>
        /// <returns>质量等级描述</returns>
        public string GetQualityLevel()
        {
            return QualityScore switch
            {
                >= 80 => "优秀",
                >= 60 => "良好",
                >= 40 => "一般",
                >= 20 => "较差",
                _ => "很差"
            };
        }
        
        /// <summary>
        /// 复制状态信息
        /// </summary>
        /// <returns>状态信息副本</returns>
        public NetworkStatusInfo Clone()
        {
            return new NetworkStatusInfo
            {
                ConnectionStatus = ConnectionStatus,
                NetworkType = NetworkType,
                ApplicationMode = ApplicationMode,
                IsInternetReachable = IsInternetReachable,
                LatencyMs = LatencyMs,
                QualityScore = QualityScore,
                LastCheckTime = LastCheckTime,
                ConnectedSince = ConnectedSince,
                DisconnectedSince = DisconnectedSince,
                ErrorMessage = ErrorMessage
            };
        }
    }
    
    /// <summary>
    /// 网络状态变化事件参数
    /// </summary>
    public class NetworkStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 之前的状态
        /// </summary>
        public NetworkStatusInfo PreviousStatus { get; }
        
        /// <summary>
        /// 当前状态
        /// </summary>
        public NetworkStatusInfo CurrentStatus { get; }
        
        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangeTime { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="previousStatus">之前的状态</param>
        /// <param name="currentStatus">当前状态</param>
        public NetworkStatusChangedEventArgs(NetworkStatusInfo previousStatus, NetworkStatusInfo currentStatus)
        {
            PreviousStatus = previousStatus;
            CurrentStatus = currentStatus;
            ChangeTime = DateTime.Now;
        }
    }
    
    /// <summary>
    /// 离线数据缓存项
    /// </summary>
    [Serializable]
    public class OfflineCacheItem
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 缓存数据
        /// </summary>
        public string Data { get; set; }
        
        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }
        
        /// <summary>
        /// 访问次数
        /// </summary>
        public int AccessCount { get; set; }
        
        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessedAt { get; set; }
        
        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public long SizeBytes { get; set; }
        
        /// <summary>
        /// 是否需要同步
        /// </summary>
        public bool NeedsSync { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public OfflineCacheItem()
        {
            CreatedAt = DateTime.Now;
            LastAccessedAt = DateTime.Now;
            AccessCount = 0;
            NeedsSync = false;
        }
        
        /// <summary>
        /// 是否已过期
        /// </summary>
        public bool IsExpired => DateTime.Now > ExpiresAt;
        
        /// <summary>
        /// 更新访问信息
        /// </summary>
        public void UpdateAccess()
        {
            AccessCount++;
            LastAccessedAt = DateTime.Now;
        }
        
        /// <summary>
        /// 设置过期时间
        /// </summary>
        /// <param name="expirationMinutes">过期时间（分钟）</param>
        public void SetExpiration(int expirationMinutes)
        {
            ExpiresAt = DateTime.Now.AddMinutes(expirationMinutes);
        }
    }
    
    /// <summary>
    /// 网络质量测试结果
    /// </summary>
    [Serializable]
    public class NetworkQualityTestResult
    {
        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime TestTime { get; set; }
        
        /// <summary>
        /// 延迟（毫秒）
        /// </summary>
        public int LatencyMs { get; set; }
        
        /// <summary>
        /// 下载速度（KB/s）
        /// </summary>
        public float DownloadSpeedKbps { get; set; }
        
        /// <summary>
        /// 上传速度（KB/s）
        /// </summary>
        public float UploadSpeedKbps { get; set; }
        
        /// <summary>
        /// 丢包率（百分比）
        /// </summary>
        public float PacketLossPercent { get; set; }
        
        /// <summary>
        /// 测试是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public NetworkQualityTestResult()
        {
            TestTime = DateTime.Now;
            LatencyMs = -1;
            DownloadSpeedKbps = 0;
            UploadSpeedKbps = 0;
            PacketLossPercent = 0;
            IsSuccessful = false;
        }
        
        /// <summary>
        /// 计算质量评分
        /// </summary>
        /// <returns>质量评分（0-100）</returns>
        public int CalculateQualityScore()
        {
            if (!IsSuccessful) return 0;
            
            int score = 100;
            
            // 延迟评分
            if (LatencyMs > 500) score -= 30;
            else if (LatencyMs > 200) score -= 20;
            else if (LatencyMs > 100) score -= 10;
            
            // 下载速度评分
            if (DownloadSpeedKbps < 100) score -= 25;
            else if (DownloadSpeedKbps < 500) score -= 15;
            else if (DownloadSpeedKbps < 1000) score -= 5;
            
            // 丢包率评分
            if (PacketLossPercent > 10) score -= 30;
            else if (PacketLossPercent > 5) score -= 20;
            else if (PacketLossPercent > 1) score -= 10;
            
            return Math.Max(0, score);
        }
    }
}