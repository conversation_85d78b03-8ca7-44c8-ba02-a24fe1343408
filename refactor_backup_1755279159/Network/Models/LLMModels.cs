using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Network.Models
{
    /// <summary>
    /// 大模型聊天请求
    /// </summary>
    [Serializable]
    public class LLMChatRequest
    {
        /// <summary>
        /// 模型名称
        /// </summary>
        public string model;
        
        /// <summary>
        /// 消息列表
        /// </summary>
        public List<LLMMessage> messages;
        
        /// <summary>
        /// 温度参数，控制随机性
        /// </summary>
        public float temperature = 0.7f;
        
        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int max_tokens = 2048;
        
        /// <summary>
        /// 是否流式输出
        /// </summary>
        public bool stream = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LLMChatRequest()
        {
            messages = new List<LLMMessage>();
        }
        
        /// <summary>
        /// 添加消息
        /// </summary>
        /// <param name="role">角色</param>
        /// <param name="content">内容</param>
        public void AddMessage(string role, string content)
        {
            messages.Add(new LLMMessage { role = role, content = content });
        }
        
        /// <summary>
        /// 添加系统消息
        /// </summary>
        /// <param name="content">内容</param>
        public void AddSystemMessage(string content)
        {
            AddMessage("system", content);
        }
        
        /// <summary>
        /// 添加用户消息
        /// </summary>
        /// <param name="content">内容</param>
        public void AddUserMessage(string content)
        {
            AddMessage("user", content);
        }
        
        /// <summary>
        /// 添加助手消息
        /// </summary>
        /// <param name="content">内容</param>
        public void AddAssistantMessage(string content)
        {
            AddMessage("assistant", content);
        }
    }
    
    /// <summary>
    /// 大模型消息
    /// </summary>
    [Serializable]
    public class LLMMessage
    {
        /// <summary>
        /// 消息角色（system, user, assistant）
        /// </summary>
        public string role;
        
        /// <summary>
        /// 消息内容
        /// </summary>
        public string content;
        
        /// <summary>
        /// 消息时间戳
        /// </summary>
        public long timestamp;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LLMMessage()
        {
            timestamp = DateTimeOffset.Now.ToUnixTimeSeconds();
        }
    }
    
    /// <summary>
    /// 大模型聊天响应
    /// </summary>
    [Serializable]
    public class LLMChatResponse
    {
        /// <summary>
        /// 响应ID
        /// </summary>
        public string id;
        
        /// <summary>
        /// 对象类型
        /// </summary>
        public string @object;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public long created;
        
        /// <summary>
        /// 模型名称
        /// </summary>
        public string model;
        
        /// <summary>
        /// 选择列表
        /// </summary>
        public List<LLMChoice> choices;
        
        /// <summary>
        /// 使用情况
        /// </summary>
        public LLMUsage usage;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LLMChatResponse()
        {
            choices = new List<LLMChoice>();
        }
        
        /// <summary>
        /// 获取第一个选择的内容
        /// </summary>
        /// <returns>内容文本</returns>
        public string GetContent()
        {
            return choices?.Count > 0 ? choices[0].message?.content : "";
        }
        
        /// <summary>
        /// 获取完成原因
        /// </summary>
        /// <returns>完成原因</returns>
        public string GetFinishReason()
        {
            return choices?.Count > 0 ? choices[0].finish_reason : "";
        }
    }
    
    /// <summary>
    /// 大模型选择
    /// </summary>
    [Serializable]
    public class LLMChoice
    {
        /// <summary>
        /// 索引
        /// </summary>
        public int index;
        
        /// <summary>
        /// 消息
        /// </summary>
        public LLMMessage message;
        
        /// <summary>
        /// 完成原因
        /// </summary>
        public string finish_reason;
    }
    
    /// <summary>
    /// 大模型使用情况
    /// </summary>
    [Serializable]
    public class LLMUsage
    {
        /// <summary>
        /// 提示令牌数
        /// </summary>
        public int prompt_tokens;
        
        /// <summary>
        /// 完成令牌数
        /// </summary>
        public int completion_tokens;
        
        /// <summary>
        /// 总令牌数
        /// </summary>
        public int total_tokens;
    }
    
    /// <summary>
    /// 对话上下文
    /// </summary>
    [Serializable]
    public class ConversationContext
    {
        /// <summary>
        /// 对话ID
        /// </summary>
        public string ConversationId { get; set; }
        
        /// <summary>
        /// 消息历史
        /// </summary>
        public List<LLMMessage> Messages { get; set; }
        
        /// <summary>
        /// 系统提示
        /// </summary>
        public string SystemPrompt { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedAt { get; set; }
        
        /// <summary>
        /// 最大消息数量
        /// </summary>
        public int MaxMessages { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ConversationContext()
        {
            ConversationId = Guid.NewGuid().ToString();
            Messages = new List<LLMMessage>();
            CreatedAt = DateTime.Now;
            LastUpdatedAt = DateTime.Now;
            MaxMessages = 50; // 默认保留最近50条消息
        }
        
        /// <summary>
        /// 添加消息
        /// </summary>
        /// <param name="message">消息对象</param>
        public void AddMessage(LLMMessage message)
        {
            Messages.Add(message);
            LastUpdatedAt = DateTime.Now;
            
            // 保持消息数量在限制范围内
            while (Messages.Count > MaxMessages)
            {
                // 保留系统消息，删除最早的用户或助手消息
                for (int i = 0; i < Messages.Count; i++)
                {
                    if (Messages[i].role != "system")
                    {
                        Messages.RemoveAt(i);
                        break;
                    }
                }
            }
        }
        
        /// <summary>
        /// 添加用户消息
        /// </summary>
        /// <param name="content">消息内容</param>
        public void AddUserMessage(string content)
        {
            AddMessage(new LLMMessage { role = "user", content = content });
        }
        
        /// <summary>
        /// 添加助手消息
        /// </summary>
        /// <param name="content">消息内容</param>
        public void AddAssistantMessage(string content)
        {
            AddMessage(new LLMMessage { role = "assistant", content = content });
        }
        
        /// <summary>
        /// 设置系统提示
        /// </summary>
        /// <param name="prompt">系统提示</param>
        public void SetSystemPrompt(string prompt)
        {
            SystemPrompt = prompt;
            
            // 移除现有的系统消息
            Messages.RemoveAll(m => m.role == "system");
            
            // 在开头添加新的系统消息
            if (!string.IsNullOrEmpty(prompt))
            {
                Messages.Insert(0, new LLMMessage { role = "system", content = prompt });
            }
            
            LastUpdatedAt = DateTime.Now;
        }
        
        /// <summary>
        /// 获取用于API请求的消息列表
        /// </summary>
        /// <returns>消息列表</returns>
        public List<LLMMessage> GetMessagesForRequest()
        {
            return new List<LLMMessage>(Messages);
        }
        
        /// <summary>
        /// 清除对话历史
        /// </summary>
        /// <param name="keepSystemPrompt">是否保留系统提示</param>
        public void ClearHistory(bool keepSystemPrompt = true)
        {
            if (keepSystemPrompt && !string.IsNullOrEmpty(SystemPrompt))
            {
                Messages.Clear();
                Messages.Add(new LLMMessage { role = "system", content = SystemPrompt });
            }
            else
            {
                Messages.Clear();
                SystemPrompt = "";
            }
            
            LastUpdatedAt = DateTime.Now;
        }
        
        /// <summary>
        /// 获取对话摘要信息
        /// </summary>
        /// <returns>摘要信息</returns>
        public ConversationSummary GetSummary()
        {
            return new ConversationSummary
            {
                ConversationId = ConversationId,
                MessageCount = Messages.Count,
                CreatedAt = CreatedAt,
                LastUpdatedAt = LastUpdatedAt,
                HasSystemPrompt = !string.IsNullOrEmpty(SystemPrompt)
            };
        }
    }
    
    /// <summary>
    /// 对话摘要信息
    /// </summary>
    [Serializable]
    public class ConversationSummary
    {
        /// <summary>
        /// 对话ID
        /// </summary>
        public string ConversationId { get; set; }
        
        /// <summary>
        /// 消息数量
        /// </summary>
        public int MessageCount { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedAt { get; set; }
        
        /// <summary>
        /// 是否有系统提示
        /// </summary>
        public bool HasSystemPrompt { get; set; }
    }
    
    /// <summary>
    /// 大模型提供商类型
    /// </summary>
    public enum LLMProvider
    {
        /// <summary>
        /// OpenAI
        /// </summary>
        OpenAI,
        
        /// <summary>
        /// Azure OpenAI
        /// </summary>
        AzureOpenAI,
        
        /// <summary>
        /// 百度文心一言
        /// </summary>
        BaiduErnie,
        
        /// <summary>
        /// 阿里通义千问
        /// </summary>
        AlibabaQwen,
        
        /// <summary>
        /// 腾讯混元
        /// </summary>
        TencentHunyuan,
        
        /// <summary>
        /// 智谱ChatGLM
        /// </summary>
        ZhipuChatGLM,
        
        /// <summary>
        /// 自定义API
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// 大模型API错误响应
    /// </summary>
    [Serializable]
    public class LLMErrorResponse
    {
        /// <summary>
        /// 错误信息
        /// </summary>
        public LLMError error;
    }
    
    /// <summary>
    /// 大模型错误信息
    /// </summary>
    [Serializable]
    public class LLMError
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string message;
        
        /// <summary>
        /// 错误类型
        /// </summary>
        public string type;
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public string code;
    }
}