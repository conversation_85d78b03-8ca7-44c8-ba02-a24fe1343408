using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Network.Models
{
    /// <summary>
    /// API响应基类，包含通用的响应信息
    /// </summary>
    [Serializable]
    public class ApiResponse
    {
        /// <summary>
        /// 响应是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 响应头信息
        /// </summary>
        public Dictionary<string, string> Headers { get; set; }
        
        /// <summary>
        /// 响应时间戳
        /// </summary>
        public DateTime ResponseTime { get; set; }
        
        /// <summary>
        /// 请求耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ApiResponse()
        {
            Headers = new Dictionary<string, string>();
            ResponseTime = DateTime.Now;
        }
        
        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="statusCode">状态码</param>
        /// <returns>成功响应对象</returns>
        public static ApiResponse Success(int statusCode = 200)
        {
            return new ApiResponse
            {
                IsSuccess = true,
                StatusCode = statusCode
            };
        }
        
        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <param name="statusCode">状态码</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>失败响应对象</returns>
        public static ApiResponse Failure(int statusCode, string errorMessage)
        {
            return new ApiResponse
            {
                IsSuccess = false,
                StatusCode = statusCode,
                ErrorMessage = errorMessage
            };
        }
    }
    
    /// <summary>
    /// 泛型API响应类，包含响应数据
    /// </summary>
    /// <typeparam name="T">响应数据类型</typeparam>
    [Serializable]
    public class ApiResponse<T> : ApiResponse
    {
        /// <summary>
        /// 响应数据
        /// </summary>
        public T Data { get; set; }
        
        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">响应数据</param>
        /// <param name="statusCode">状态码</param>
        /// <returns>成功响应对象</returns>
        public static ApiResponse<T> Success(T data, int statusCode = 200)
        {
            return new ApiResponse<T>
            {
                IsSuccess = true,
                StatusCode = statusCode,
                Data = data
            };
        }
        
        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <param name="statusCode">状态码</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>失败响应对象</returns>
        public static new ApiResponse<T> Failure(int statusCode, string errorMessage)
        {
            return new ApiResponse<T>
            {
                IsSuccess = false,
                StatusCode = statusCode,
                ErrorMessage = errorMessage
            };
        }
    }
    
    /// <summary>
    /// API请求参数
    /// </summary>
    [Serializable]
    public class ApiRequest
    {
        /// <summary>
        /// 请求URL
        /// </summary>
        public string Url { get; set; }
        
        /// <summary>
        /// HTTP方法
        /// </summary>
        public string Method { get; set; }
        
        /// <summary>
        /// 请求头
        /// </summary>
        public Dictionary<string, string> Headers { get; set; }
        
        /// <summary>
        /// 请求体
        /// </summary>
        public string Body { get; set; }
        
        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }
        
        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryIntervalMs { get; set; }
        
        /// <summary>
        /// 是否启用缓存
        /// </summary>
        public bool EnableCache { get; set; }
        
        /// <summary>
        /// 缓存过期时间（分钟）
        /// </summary>
        public int CacheExpirationMinutes { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ApiRequest()
        {
            Headers = new Dictionary<string, string>();
            Method = "GET";
            TimeoutSeconds = 30;
            RetryCount = 3;
            RetryIntervalMs = 1000;
            EnableCache = false;
            CacheExpirationMinutes = 60;
        }
        
        /// <summary>
        /// 创建GET请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <returns>GET请求对象</returns>
        public static ApiRequest Get(string url)
        {
            return new ApiRequest
            {
                Url = url,
                Method = "GET"
            };
        }
        
        /// <summary>
        /// 创建POST请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="body">请求体</param>
        /// <returns>POST请求对象</returns>
        public static ApiRequest Post(string url, string body = null)
        {
            return new ApiRequest
            {
                Url = url,
                Method = "POST",
                Body = body
            };
        }
        
        /// <summary>
        /// 添加请求头
        /// </summary>
        /// <param name="key">头名称</param>
        /// <param name="value">头值</param>
        /// <returns>当前请求对象</returns>
        public ApiRequest AddHeader(string key, string value)
        {
            Headers[key] = value;
            return this;
        }
        
        /// <summary>
        /// 设置超时时间
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <returns>当前请求对象</returns>
        public ApiRequest SetTimeout(int timeoutSeconds)
        {
            TimeoutSeconds = timeoutSeconds;
            return this;
        }
        
        /// <summary>
        /// 设置重试配置
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryIntervalMs">重试间隔（毫秒）</param>
        /// <returns>当前请求对象</returns>
        public ApiRequest SetRetry(int retryCount, int retryIntervalMs = 1000)
        {
            RetryCount = retryCount;
            RetryIntervalMs = retryIntervalMs;
            return this;
        }
        
        /// <summary>
        /// 启用缓存
        /// </summary>
        /// <param name="expirationMinutes">缓存过期时间（分钟）</param>
        /// <returns>当前请求对象</returns>
        public ApiRequest EnableCaching(int expirationMinutes = 60)
        {
            EnableCache = true;
            CacheExpirationMinutes = expirationMinutes;
            return this;
        }
    }
    
    /// <summary>
    /// API错误类型枚举
    /// </summary>
    public enum ApiErrorType
    {
        /// <summary>
        /// 网络连接错误
        /// </summary>
        NetworkError,
        
        /// <summary>
        /// 请求超时
        /// </summary>
        Timeout,
        
        /// <summary>
        /// 认证失败
        /// </summary>
        AuthenticationFailed,
        
        /// <summary>
        /// 权限不足
        /// </summary>
        Forbidden,
        
        /// <summary>
        /// 资源未找到
        /// </summary>
        NotFound,
        
        /// <summary>
        /// 服务器内部错误
        /// </summary>
        ServerError,
        
        /// <summary>
        /// 请求格式错误
        /// </summary>
        BadRequest,
        
        /// <summary>
        /// 服务不可用
        /// </summary>
        ServiceUnavailable,
        
        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// API异常类
    /// </summary>
    public class ApiException : Exception
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public ApiErrorType ErrorType { get; }
        
        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int StatusCode { get; }
        
        /// <summary>
        /// 响应内容
        /// </summary>
        public string ResponseContent { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="message">错误消息</param>
        /// <param name="statusCode">HTTP状态码</param>
        /// <param name="responseContent">响应内容</param>
        /// <param name="innerException">内部异常</param>
        public ApiException(ApiErrorType errorType, string message, int statusCode = 0, 
            string responseContent = null, Exception innerException = null) 
            : base(message, innerException)
        {
            ErrorType = errorType;
            StatusCode = statusCode;
            ResponseContent = responseContent;
        }
        
        /// <summary>
        /// 根据HTTP状态码创建API异常
        /// </summary>
        /// <param name="statusCode">HTTP状态码</param>
        /// <param name="message">错误消息</param>
        /// <param name="responseContent">响应内容</param>
        /// <returns>API异常对象</returns>
        public static ApiException FromStatusCode(int statusCode, string message, string responseContent = null)
        {
            ApiErrorType errorType = statusCode switch
            {
                400 => ApiErrorType.BadRequest,
                401 => ApiErrorType.AuthenticationFailed,
                403 => ApiErrorType.Forbidden,
                404 => ApiErrorType.NotFound,
                500 => ApiErrorType.ServerError,
                503 => ApiErrorType.ServiceUnavailable,
                _ => ApiErrorType.Unknown
            };
            
            return new ApiException(errorType, message, statusCode, responseContent);
        }
    }
}