using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 重连状态
    /// </summary>
    public enum ReconnectionStatus
    {
        /// <summary>
        /// 空闲
        /// </summary>
        Idle,
        
        /// <summary>
        /// 重连中
        /// </summary>
        Reconnecting,
        
        /// <summary>
        /// 重连成功
        /// </summary>
        Connected,
        
        /// <summary>
        /// 重连失败
        /// </summary>
        Failed,
        
        /// <summary>
        /// 已放弃
        /// </summary>
        Abandoned
    }
    
    /// <summary>
    /// 重连策略类型
    /// </summary>
    public enum ReconnectionStrategyType
    {
        /// <summary>
        /// 固定间隔
        /// </summary>
        FixedInterval,
        
        /// <summary>
        /// 指数退避
        /// </summary>
        ExponentialBackoff,
        
        /// <summary>
        /// 线性增长
        /// </summary>
        LinearBackoff,
        
        /// <summary>
        /// 自定义
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// API调用状态
    /// </summary>
    public enum ApiCallStatus
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success,
        
        /// <summary>
        /// 失败
        /// </summary>
        Failed,
        
        /// <summary>
        /// 超时
        /// </summary>
        Timeout,
        
        /// <summary>
        /// 网络错误
        /// </summary>
        NetworkError,
        
        /// <summary>
        /// 服务器错误
        /// </summary>
        ServerError,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled
    }
    
    /// <summary>
    /// 重连尝试信息
    /// </summary>
    [Serializable]
    public class ReconnectionAttemptInfo
    {
        /// <summary>
        /// 尝试ID
        /// </summary>
        public string AttemptId;
        
        /// <summary>
        /// 尝试次数
        /// </summary>
        public int AttemptNumber;
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime;
        
        /// <summary>
        /// 触发原因
        /// </summary>
        public string Reason;
        
        /// <summary>
        /// 使用的策略
        /// </summary>
        public ReconnectionStrategyType Strategy;
        
        /// <summary>
        /// 延迟时间（秒）
        /// </summary>
        public float DelaySeconds;
        
        public ReconnectionAttemptInfo(int attemptNumber, string reason, ReconnectionStrategyType strategy, float delaySeconds)
        {
            AttemptId = Guid.NewGuid().ToString();
            AttemptNumber = attemptNumber;
            StartTime = DateTime.Now;
            Reason = reason;
            Strategy = strategy;
            DelaySeconds = delaySeconds;
        }
    }
    
    /// <summary>
    /// 重连结果
    /// </summary>
    [Serializable]
    public class ReconnectionResult
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public string ResultId;
        
        /// <summary>
        /// 尝试信息
        /// </summary>
        public ReconnectionAttemptInfo AttemptInfo;
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful;
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime;
        
        /// <summary>
        /// 持续时间（毫秒）
        /// </summary>
        public long DurationMs;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage;
        
        /// <summary>
        /// 网络质量信息
        /// </summary>
        public NetworkQualityInfo NetworkQuality;
        
        /// <summary>
        /// 额外信息
        /// </summary>
        public Dictionary<string, object> AdditionalInfo;
        
        public ReconnectionResult(ReconnectionAttemptInfo attemptInfo, bool isSuccessful, string errorMessage = "")
        {
            ResultId = Guid.NewGuid().ToString();
            AttemptInfo = attemptInfo;
            IsSuccessful = isSuccessful;
            EndTime = DateTime.Now;
            DurationMs = (long)(EndTime - attemptInfo.StartTime).TotalMilliseconds;
            ErrorMessage = errorMessage;
            AdditionalInfo = new Dictionary<string, object>();
        }
    }
    
    /// <summary>
    /// API调用失败信息
    /// </summary>
    [Serializable]
    public class ApiCallFailureInfo
    {
        /// <summary>
        /// 失败ID
        /// </summary>
        public string FailureId;
        
        /// <summary>
        /// API端点
        /// </summary>
        public string Endpoint;
        
        /// <summary>
        /// 失败时间
        /// </summary>
        public DateTime FailureTime;
        
        /// <summary>
        /// 失败状态
        /// </summary>
        public ApiCallStatus Status;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage;
        
        /// <summary>
        /// HTTP状态码
        /// </summary>
        public int HttpStatusCode;
        
        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs;
        
        /// <summary>
        /// 是否会重试
        /// </summary>
        public bool WillRetry;
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount;
        
        public ApiCallFailureInfo(string endpoint, ApiCallStatus status, string errorMessage, int httpStatusCode = 0)
        {
            FailureId = Guid.NewGuid().ToString();
            Endpoint = endpoint;
            FailureTime = DateTime.Now;
            Status = status;
            ErrorMessage = errorMessage;
            HttpStatusCode = httpStatusCode;
            WillRetry = false;
            RetryCount = 0;
        }
    }
    
    /// <summary>
    /// API重试信息
    /// </summary>
    [Serializable]
    public class ApiRetryInfo
    {
        /// <summary>
        /// 重试ID
        /// </summary>
        public string RetryId;
        
        /// <summary>
        /// 原始失败信息
        /// </summary>
        public ApiCallFailureInfo OriginalFailure;
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryAttempt;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries;
        
        /// <summary>
        /// 重试时间
        /// </summary>
        public DateTime RetryTime;
        
        /// <summary>
        /// 延迟时间（秒）
        /// </summary>
        public float DelaySeconds;
        
        /// <summary>
        /// 重试策略
        /// </summary>
        public string RetryStrategy;
        
        public ApiRetryInfo(ApiCallFailureInfo originalFailure, int retryAttempt, int maxRetries, float delaySeconds, string retryStrategy)
        {
            RetryId = Guid.NewGuid().ToString();
            OriginalFailure = originalFailure;
            RetryAttempt = retryAttempt;
            MaxRetries = maxRetries;
            RetryTime = DateTime.Now;
            DelaySeconds = delaySeconds;
            RetryStrategy = retryStrategy;
        }
    }
    
    /// <summary>
    /// API调用结果
    /// </summary>
    /// <typeparam name="T">结果类型</typeparam>
    [Serializable]
    public class ApiCallResult<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }
        
        /// <summary>
        /// 结果数据
        /// </summary>
        public T Data { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 状态码
        /// </summary>
        public int StatusCode { get; set; }
        
        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs { get; set; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }
        
        /// <summary>
        /// 是否使用了降级
        /// </summary>
        public bool UsedFallback { get; set; }
        
        /// <summary>
        /// 额外信息
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; }
        
        public ApiCallResult()
        {
            Metadata = new Dictionary<string, object>();
        }
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static ApiCallResult<T> Success(T data, long responseTimeMs = 0, int retryCount = 0)
        {
            return new ApiCallResult<T>
            {
                IsSuccessful = true,
                Data = data,
                ResponseTimeMs = responseTimeMs,
                RetryCount = retryCount
            };
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static ApiCallResult<T> Failure(string errorMessage, int statusCode = 0, long responseTimeMs = 0, int retryCount = 0)
        {
            return new ApiCallResult<T>
            {
                IsSuccessful = false,
                ErrorMessage = errorMessage,
                StatusCode = statusCode,
                ResponseTimeMs = responseTimeMs,
                RetryCount = retryCount
            };
        }
    }
    
    /// <summary>
    /// 重连策略
    /// </summary>
    [Serializable]
    public class ReconnectionStrategy
    {
        /// <summary>
        /// 策略类型
        /// </summary>
        public ReconnectionStrategyType Type { get; set; }
        
        /// <summary>
        /// 基础间隔（秒）
        /// </summary>
        public float BaseInterval { get; set; } = 1.0f;
        
        /// <summary>
        /// 最大间隔（秒）
        /// </summary>
        public float MaxInterval { get; set; } = 60.0f;
        
        /// <summary>
        /// 退避倍数
        /// </summary>
        public float BackoffMultiplier { get; set; } = 2.0f;
        
        /// <summary>
        /// 随机化因子
        /// </summary>
        public float RandomizationFactor { get; set; } = 0.1f;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; } = 5;
        
        /// <summary>
        /// 自定义间隔计算函数
        /// </summary>
        public Func<int, float> CustomIntervalCalculator { get; set; }
        
        /// <summary>
        /// 计算重连间隔
        /// </summary>
        /// <param name="attemptNumber">尝试次数</param>
        /// <returns>间隔时间（秒）</returns>
        public float CalculateInterval(int attemptNumber)
        {
            float interval;
            
            switch (Type)
            {
                case ReconnectionStrategyType.FixedInterval:
                    interval = BaseInterval;
                    break;
                    
                case ReconnectionStrategyType.ExponentialBackoff:
                    interval = BaseInterval * Mathf.Pow(BackoffMultiplier, attemptNumber - 1);
                    break;
                    
                case ReconnectionStrategyType.LinearBackoff:
                    interval = BaseInterval * attemptNumber;
                    break;
                    
                case ReconnectionStrategyType.Custom:
                    interval = CustomIntervalCalculator?.Invoke(attemptNumber) ?? BaseInterval;
                    break;
                    
                default:
                    interval = BaseInterval;
                    break;
            }
            
            // 应用最大间隔限制
            interval = Mathf.Min(interval, MaxInterval);
            
            // 应用随机化
            if (RandomizationFactor > 0)
            {
                var randomFactor = UnityEngine.Random.Range(1.0f - RandomizationFactor, 1.0f + RandomizationFactor);
                interval *= randomFactor;
            }
            
            return interval;
        }
        
        /// <summary>
        /// 创建固定间隔策略
        /// </summary>
        public static ReconnectionStrategy FixedInterval(float interval, int maxRetries = 5)
        {
            return new ReconnectionStrategy
            {
                Type = ReconnectionStrategyType.FixedInterval,
                BaseInterval = interval,
                MaxRetries = maxRetries
            };
        }
        
        /// <summary>
        /// 创建指数退避策略
        /// </summary>
        public static ReconnectionStrategy ExponentialBackoff(float baseInterval = 1.0f, float multiplier = 2.0f, float maxInterval = 60.0f, int maxRetries = 5)
        {
            return new ReconnectionStrategy
            {
                Type = ReconnectionStrategyType.ExponentialBackoff,
                BaseInterval = baseInterval,
                BackoffMultiplier = multiplier,
                MaxInterval = maxInterval,
                MaxRetries = maxRetries
            };
        }
        
        /// <summary>
        /// 创建线性退避策略
        /// </summary>
        public static ReconnectionStrategy LinearBackoff(float baseInterval = 1.0f, float maxInterval = 30.0f, int maxRetries = 5)
        {
            return new ReconnectionStrategy
            {
                Type = ReconnectionStrategyType.LinearBackoff,
                BaseInterval = baseInterval,
                MaxInterval = maxInterval,
                MaxRetries = maxRetries
            };
        }
    }
    
    /// <summary>
    /// 网络质量信息
    /// </summary>
    [Serializable]
    public class NetworkQualityInfo
    {
        /// <summary>
        /// 延迟（毫秒）
        /// </summary>
        public int LatencyMs { get; set; }
        
        /// <summary>
        /// 下载速度（Kbps）
        /// </summary>
        public float DownloadSpeedKbps { get; set; }
        
        /// <summary>
        /// 上传速度（Kbps）
        /// </summary>
        public float UploadSpeedKbps { get; set; }
        
        /// <summary>
        /// 丢包率（百分比）
        /// </summary>
        public float PacketLossRate { get; set; }
        
        /// <summary>
        /// 质量评分（0-100）
        /// </summary>
        public int QualityScore { get; set; }
        
        /// <summary>
        /// 信号强度
        /// </summary>
        public string SignalStrength { get; set; }
        
        /// <summary>
        /// 网络类型
        /// </summary>
        public string NetworkType { get; set; }
    }
    
    /// <summary>
    /// API调用统计信息
    /// </summary>
    [Serializable]
    public class ApiCallStatistics
    {
        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 总调用次数
        /// </summary>
        public int TotalCalls { get; set; }
        
        /// <summary>
        /// 成功调用次数
        /// </summary>
        public int SuccessfulCalls { get; set; }
        
        /// <summary>
        /// 失败调用次数
        /// </summary>
        public int FailedCalls { get; set; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int TotalRetries { get; set; }
        
        /// <summary>
        /// 使用降级次数
        /// </summary>
        public int FallbackUsages { get; set; }
        
        /// <summary>
        /// 平均响应时间（毫秒）
        /// </summary>
        public float AverageResponseTimeMs { get; set; }
        
        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public float SuccessRate => TotalCalls > 0 ? (float)SuccessfulCalls / TotalCalls * 100f : 0f;
        
        /// <summary>
        /// 按端点分组的统计
        /// </summary>
        public Dictionary<string, EndpointStatistics> EndpointStats { get; set; }
        
        /// <summary>
        /// 按状态分组的统计
        /// </summary>
        public Dictionary<ApiCallStatus, int> StatusStats { get; set; }
        
        public ApiCallStatistics()
        {
            StartTime = DateTime.Now;
            EndpointStats = new Dictionary<string, EndpointStatistics>();
            StatusStats = new Dictionary<ApiCallStatus, int>();
        }
    }
    
    /// <summary>
    /// 端点统计信息
    /// </summary>
    [Serializable]
    public class EndpointStatistics
    {
        /// <summary>
        /// 端点URL
        /// </summary>
        public string Endpoint { get; set; }
        
        /// <summary>
        /// 调用次数
        /// </summary>
        public int CallCount { get; set; }
        
        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailureCount { get; set; }
        
        /// <summary>
        /// 平均响应时间（毫秒）
        /// </summary>
        public float AverageResponseTimeMs { get; set; }
        
        /// <summary>
        /// 最后调用时间
        /// </summary>
        public DateTime LastCallTime { get; set; }
        
        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public float SuccessRate => CallCount > 0 ? (float)SuccessCount / CallCount * 100f : 0f;
    }
}