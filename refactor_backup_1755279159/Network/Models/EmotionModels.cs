using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 情感类型枚举
    /// </summary>
    public enum EmotionType
    {
        /// <summary>
        /// 中性
        /// </summary>
        Neutral,
        
        /// <summary>
        /// 高兴
        /// </summary>
        Happy,
        
        /// <summary>
        /// 悲伤
        /// </summary>
        Sad,
        
        /// <summary>
        /// 愤怒
        /// </summary>
        Angry,
        
        /// <summary>
        /// 恐惧
        /// </summary>
        Fear,
        
        /// <summary>
        /// 惊讶
        /// </summary>
        Surprise,
        
        /// <summary>
        /// 厌恶
        /// </summary>
        Disgust,
        
        /// <summary>
        /// 困惑
        /// </summary>
        Confused,
        
        /// <summary>
        /// 兴奋
        /// </summary>
        Excited,
        
        /// <summary>
        /// 平静
        /// </summary>
        Calm
    }
    
    /// <summary>
    /// 情感强度等级
    /// </summary>
    public enum EmotionIntensity
    {
        /// <summary>
        /// 很低
        /// </summary>
        VeryLow,
        
        /// <summary>
        /// 低
        /// </summary>
        Low,
        
        /// <summary>
        /// 中等
        /// </summary>
        Medium,
        
        /// <summary>
        /// 高
        /// </summary>
        High,
        
        /// <summary>
        /// 很高
        /// </summary>
        VeryHigh
    }
    
    /// <summary>
    /// 情感数据
    /// </summary>
    [Serializable]
    public class EmotionData
    {
        /// <summary>
        /// 主要情感类型
        /// </summary>
        public EmotionType primaryEmotion;
        
        /// <summary>
        /// 情感置信度 (0-1)
        /// </summary>
        public float confidence;
        
        /// <summary>
        /// 情感强度 (0-1)
        /// </summary>
        public float intensity;
        
        /// <summary>
        /// 情感强度等级
        /// </summary>
        public EmotionIntensity intensityLevel;
        
        /// <summary>
        /// 所有检测到的情感及其分数
        /// </summary>
        public Dictionary<EmotionType, float> emotionScores;
        
        /// <summary>
        /// 检测时间戳
        /// </summary>
        public DateTime timestamp;
        
        /// <summary>
        /// 情感持续时间（秒）
        /// </summary>
        public float duration;
        
        /// <summary>
        /// 是否为有效的情感数据
        /// </summary>
        public bool isValid;
        
        /// <summary>
        /// 额外的元数据
        /// </summary>
        public Dictionary<string, object> metadata;
        
        public EmotionData()
        {
            primaryEmotion = EmotionType.Neutral;
            confidence = 0f;
            intensity = 0f;
            intensityLevel = EmotionIntensity.VeryLow;
            emotionScores = new Dictionary<EmotionType, float>();
            timestamp = DateTime.Now;
            duration = 0f;
            isValid = false;
            metadata = new Dictionary<string, object>();
        }
        
        public EmotionData(EmotionType emotion, float conf, float intens)
        {
            primaryEmotion = emotion;
            confidence = conf;
            intensity = intens;
            intensityLevel = GetIntensityLevel(intens);
            emotionScores = new Dictionary<EmotionType, float> { { emotion, conf } };
            timestamp = DateTime.Now;
            duration = 0f;
            isValid = conf > 0.5f;
            metadata = new Dictionary<string, object>();
        }
        
        /// <summary>
        /// 根据强度值获取强度等级
        /// </summary>
        /// <param name="intensity">强度值 (0-1)</param>
        /// <returns>强度等级</returns>
        public static EmotionIntensity GetIntensityLevel(float intensity)
        {
            if (intensity < 0.2f) return EmotionIntensity.VeryLow;
            if (intensity < 0.4f) return EmotionIntensity.Low;
            if (intensity < 0.6f) return EmotionIntensity.Medium;
            if (intensity < 0.8f) return EmotionIntensity.High;
            return EmotionIntensity.VeryHigh;
        }
        
        /// <summary>
        /// 获取情感的中文描述
        /// </summary>
        /// <returns>中文描述</returns>
        public string GetChineseDescription()
        {
            string emotionName = GetEmotionChineseName(primaryEmotion);
            string intensityName = GetIntensityChineseName(intensityLevel);
            return $"{intensityName}的{emotionName}";
        }
        
        /// <summary>
        /// 获取情感类型的中文名称
        /// </summary>
        /// <param name="emotion">情感类型</param>
        /// <returns>中文名称</returns>
        public static string GetEmotionChineseName(EmotionType emotion)
        {
            switch (emotion)
            {
                case EmotionType.Neutral: return "中性";
                case EmotionType.Happy: return "高兴";
                case EmotionType.Sad: return "悲伤";
                case EmotionType.Angry: return "愤怒";
                case EmotionType.Fear: return "恐惧";
                case EmotionType.Surprise: return "惊讶";
                case EmotionType.Disgust: return "厌恶";
                case EmotionType.Confused: return "困惑";
                case EmotionType.Excited: return "兴奋";
                case EmotionType.Calm: return "平静";
                default: return "未知";
            }
        }
        
        /// <summary>
        /// 获取强度等级的中文名称
        /// </summary>
        /// <param name="intensity">强度等级</param>
        /// <returns>中文名称</returns>
        public static string GetIntensityChineseName(EmotionIntensity intensity)
        {
            switch (intensity)
            {
                case EmotionIntensity.VeryLow: return "很轻微";
                case EmotionIntensity.Low: return "轻微";
                case EmotionIntensity.Medium: return "中等";
                case EmotionIntensity.High: return "强烈";
                case EmotionIntensity.VeryHigh: return "非常强烈";
                default: return "未知";
            }
        }
        
        public override string ToString()
        {
            return $"Emotion[{primaryEmotion}, Confidence:{confidence:F2}, Intensity:{intensity:F2}, Valid:{isValid}]";
        }
    }
    
    /// <summary>
    /// 情感分析请求
    /// </summary>
    [Serializable]
    public class EmotionAnalysisRequest
    {
        /// <summary>
        /// 图像数据（Base64编码）
        /// </summary>
        public string imageData;
        
        /// <summary>
        /// 图像格式（jpg, png等）
        /// </summary>
        public string imageFormat;
        
        /// <summary>
        /// 分析类型（面部表情、语音情感等）
        /// </summary>
        public string analysisType;
        
        /// <summary>
        /// 请求ID
        /// </summary>
        public string requestId;
        
        /// <summary>
        /// 额外参数
        /// </summary>
        public Dictionary<string, object> parameters;
        
        public EmotionAnalysisRequest()
        {
            imageData = "";
            imageFormat = "jpg";
            analysisType = "facial_expression";
            requestId = Guid.NewGuid().ToString();
            parameters = new Dictionary<string, object>();
        }
        
        public EmotionAnalysisRequest(string imgData, string format = "jpg")
        {
            imageData = imgData;
            imageFormat = format;
            analysisType = "facial_expression";
            requestId = Guid.NewGuid().ToString();
            parameters = new Dictionary<string, object>();
        }
    }
    
    /// <summary>
    /// 情感分析响应
    /// </summary>
    [Serializable]
    public class EmotionAnalysisResponse
    {
        /// <summary>
        /// 请求ID
        /// </summary>
        public string requestId;
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool success;
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string errorMessage;
        
        /// <summary>
        /// 情感数据
        /// </summary>
        public EmotionData emotionData;
        
        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long processingTime;
        
        /// <summary>
        /// 服务版本
        /// </summary>
        public string serviceVersion;
        
        public EmotionAnalysisResponse()
        {
            requestId = "";
            success = false;
            errorMessage = "";
            emotionData = null;
            processingTime = 0;
            serviceVersion = "";
        }
    }
    
    /// <summary>
    /// 情感分析配置
    /// </summary>
    [Serializable]
    public class EmotionAnalysisConfig
    {
        /// <summary>
        /// API端点URL
        /// </summary>
        public string apiEndpoint;
        
        /// <summary>
        /// API密钥
        /// </summary>
        public string apiKey;
        
        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int timeoutSeconds = 10;
        
        /// <summary>
        /// 最小置信度阈值
        /// </summary>
        public float minConfidence = 0.5f;
        
        /// <summary>
        /// 是否启用情感分析
        /// </summary>
        public bool enabled = true;
        
        /// <summary>
        /// 分析频率（每秒分析次数）
        /// </summary>
        public float analysisFrequency = 2f;
        
        /// <summary>
        /// 是否使用缓存
        /// </summary>
        public bool useCache = true;
        
        /// <summary>
        /// 缓存过期时间（秒）
        /// </summary>
        public int cacheExpirationSeconds = 30;
        
        /// <summary>
        /// 是否为私有化部署
        /// </summary>
        public bool isPrivateDeployment = false;
        
        /// <summary>
        /// 自定义请求头
        /// </summary>
        public Dictionary<string, string> customHeaders;
        
        public EmotionAnalysisConfig()
        {
            apiEndpoint = "";
            apiKey = "";
            customHeaders = new Dictionary<string, string>();
        }
        
        public EmotionAnalysisConfig(string endpoint, string key)
        {
            apiEndpoint = endpoint;
            apiKey = key;
            customHeaders = new Dictionary<string, string>();
        }
        
        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(apiEndpoint) && 
                   (isPrivateDeployment || !string.IsNullOrEmpty(apiKey));
        }
    }
}