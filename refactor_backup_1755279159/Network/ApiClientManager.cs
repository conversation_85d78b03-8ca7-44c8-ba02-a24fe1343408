using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Configuration;
using DigitalHuman.Core.Configuration.Models;
using DigitalHuman.Core.Network.Models;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// API客户端管理器，统一管理所有API调用
    /// </summary>
    public class ApiClientManager : ManagerBase
    {
        [Header("组件引用")]
        [SerializeField] private HttpClient _httpClient;
        [SerializeField] private ConfigurationManager _configurationManager;
        
        /// <summary>
        /// 当前服务配置
        /// </summary>
        private ServiceConfiguration _serviceConfig;
        
        /// <summary>
        /// API调用统计
        /// </summary>
        private readonly Dictionary<string, ApiCallStats> _apiStats = new Dictionary<string, ApiCallStats>();
        
        /// <summary>
        /// API调用开始事件
        /// </summary>
        public event Action<string, ApiRequest> OnApiCallStarted;
        
        /// <summary>
        /// API调用完成事件
        /// </summary>
        public event Action<string, ApiRequest, ApiResponse> OnApiCallCompleted;
        
        /// <summary>
        /// API调用失败事件
        /// </summary>
        public event Action<string, ApiRequest, ApiException> OnApiCallFailed;
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        protected override void OnInitializeInternal()
        {
            // 获取组件引用
            if (_httpClient == null)
            {
                _httpClient = GetComponent<HttpClient>();
                if (_httpClient == null)
                {
                    _httpClient = gameObject.AddComponent<HttpClient>();
                }
            }
            
            if (_configurationManager == null)
            {
                _configurationManager = FindObjectOfType<ConfigurationManager>();
                if (_configurationManager == null)
                {
                    throw new InvalidOperationException("未找到ConfigurationManager组件");
                }
            }
            
            // 加载服务配置
            LoadServiceConfiguration();
            
            // 订阅HTTP客户端事件
            _httpClient.OnRequestStarted += OnHttpRequestStarted;
            _httpClient.OnRequestCompleted += OnHttpRequestCompleted;
            _httpClient.OnRequestFailed += OnHttpRequestFailed;
            
            Debug.Log("API客户端管理器初始化完成");
        }
        
        /// <summary>
        /// 清理管理器资源
        /// </summary>
        protected override void OnCleanupInternal()
        {
            // 取消订阅事件
            if (_httpClient != null)
            {
                _httpClient.OnRequestStarted -= OnHttpRequestStarted;
                _httpClient.OnRequestCompleted -= OnHttpRequestCompleted;
                _httpClient.OnRequestFailed -= OnHttpRequestFailed;
            }
            
            // 清理统计数据
            _apiStats.Clear();
            
            Debug.Log("API客户端管理器清理完成");
        }
        
        /// <summary>
        /// 加载服务配置
        /// </summary>
        private void LoadServiceConfiguration()
        {
            try
            {
                _serviceConfig = _configurationManager.GetServiceConfiguration();
                ApplyServiceConfiguration();
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载服务配置失败: {ex.Message}");
                _serviceConfig = new ServiceConfiguration();
            }
        }
        
        /// <summary>
        /// 应用服务配置到HTTP客户端
        /// </summary>
        private void ApplyServiceConfiguration()
        {
            if (_serviceConfig == null || _httpClient == null) return;
            
            // 设置默认超时和重试
            _httpClient.SetDefaultTimeout(_serviceConfig.RequestTimeoutSeconds);
            _httpClient.SetDefaultRetry(_serviceConfig.RetryCount, _serviceConfig.RetryIntervalMs);
            
            // 设置SSL验证
            _httpClient.SetSSLVerification(_serviceConfig.EnableSSLVerification);
            
            if (!string.IsNullOrEmpty(_serviceConfig.CustomCertificatePath))
            {
                _httpClient.SetCustomCertificate(_serviceConfig.CustomCertificatePath);
            }
            
            Debug.Log("服务配置已应用到HTTP客户端");
        }
        
        /// <summary>
        /// 重新加载服务配置
        /// </summary>
        public void ReloadServiceConfiguration()
        {
            LoadServiceConfiguration();
        }
        
        /// <summary>
        /// 发送API请求
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="request">请求参数</param>
        /// <returns>响应结果</returns>
        public async Task<ApiResponse<string>> SendApiRequestAsync(string apiName, ApiRequest request)
        {
            if (string.IsNullOrEmpty(apiName))
            {
                throw new ArgumentException("API名称不能为空", nameof(apiName));
            }
            
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }
            
            // 应用服务配置到请求
            ApplyServiceConfigToRequest(request);
            
            // 触发API调用开始事件
            OnApiCallStarted?.Invoke(apiName, request);
            
            try
            {
                // 发送请求
                var response = await _httpClient.SendRequestAsync(request);
                
                // 更新统计信息
                UpdateApiStats(apiName, true, response.ElapsedMilliseconds);
                
                // 触发API调用完成事件
                OnApiCallCompleted?.Invoke(apiName, request, response);
                
                return response;
            }
            catch (ApiException ex)
            {
                // 更新统计信息
                UpdateApiStats(apiName, false, 0);
                
                // 触发API调用失败事件
                OnApiCallFailed?.Invoke(apiName, request, ex);
                
                throw;
            }
        }
        
        /// <summary>
        /// 应用服务配置到请求
        /// </summary>
        /// <param name="request">请求对象</param>
        private void ApplyServiceConfigToRequest(ApiRequest request)
        {
            if (_serviceConfig == null) return;
            
            // 如果请求没有设置超时时间，使用默认值
            if (request.TimeoutSeconds <= 0)
            {
                request.TimeoutSeconds = _serviceConfig.RequestTimeoutSeconds;
            }
            
            // 如果请求没有设置重试次数，使用默认值
            if (request.RetryCount < 0)
            {
                request.RetryCount = _serviceConfig.RetryCount;
                request.RetryIntervalMs = _serviceConfig.RetryIntervalMs;
            }
            
            // 如果启用了缓存且请求没有设置缓存，使用默认值
            if (_serviceConfig.EnableRequestCache && !request.EnableCache && request.Method.ToUpper() == "GET")
            {
                request.EnableCache = true;
                request.CacheExpirationMinutes = _serviceConfig.CacheExpirationMinutes;
            }
        }
        
        /// <summary>
        /// 创建认证请求头
        /// </summary>
        /// <param name="authConfig">认证配置</param>
        /// <returns>认证请求头</returns>
        public Dictionary<string, string> CreateAuthHeaders(PrivateAuthConfiguration authConfig)
        {
            var headers = new Dictionary<string, string>();
            
            if (authConfig == null) return headers;
            
            switch (authConfig.AuthType?.ToLower())
            {
                case "apikey":
                    if (!string.IsNullOrEmpty(authConfig.Token))
                    {
                        headers["Authorization"] = $"Bearer {authConfig.Token}";
                    }
                    break;
                    
                case "basic":
                    if (!string.IsNullOrEmpty(authConfig.Username) && !string.IsNullOrEmpty(authConfig.Password))
                    {
                        var credentials = Convert.ToBase64String(
                            System.Text.Encoding.UTF8.GetBytes($"{authConfig.Username}:{authConfig.Password}"));
                        headers["Authorization"] = $"Basic {credentials}";
                    }
                    break;
                    
                case "token":
                    if (!string.IsNullOrEmpty(authConfig.Token))
                    {
                        headers["Authorization"] = $"Token {authConfig.Token}";
                    }
                    break;
            }
            
            return headers;
        }
        
        /// <summary>
        /// 构建私有化部署URL
        /// </summary>
        /// <param name="endpoint">端点路径</param>
        /// <returns>完整URL</returns>
        public string BuildPrivateUrl(string endpoint)
        {
            if (_serviceConfig == null || !_serviceConfig.EnablePrivateDeployment)
            {
                return endpoint;
            }
            
            if (string.IsNullOrEmpty(_serviceConfig.PrivateServiceBaseUrl))
            {
                return endpoint;
            }
            
            var baseUrl = _serviceConfig.PrivateServiceBaseUrl.TrimEnd('/');
            var path = endpoint.TrimStart('/');
            
            return $"{baseUrl}/{path}";
        }
        
        /// <summary>
        /// 获取API调用统计信息
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <returns>统计信息</returns>
        public ApiCallStats GetApiStats(string apiName)
        {
            return _apiStats.TryGetValue(apiName, out var stats) ? stats : new ApiCallStats();
        }
        
        /// <summary>
        /// 获取所有API统计信息
        /// </summary>
        /// <returns>所有API统计信息</returns>
        public Dictionary<string, ApiCallStats> GetAllApiStats()
        {
            return new Dictionary<string, ApiCallStats>(_apiStats);
        }
        
        /// <summary>
        /// 清除API统计信息
        /// </summary>
        /// <param name="apiName">API名称，为空则清除所有</param>
        public void ClearApiStats(string apiName = null)
        {
            if (string.IsNullOrEmpty(apiName))
            {
                _apiStats.Clear();
            }
            else
            {
                _apiStats.Remove(apiName);
            }
        }
        
        /// <summary>
        /// 更新API统计信息
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="success">是否成功</param>
        /// <param name="elapsedMs">耗时（毫秒）</param>
        private void UpdateApiStats(string apiName, bool success, long elapsedMs)
        {
            if (!_apiStats.TryGetValue(apiName, out var stats))
            {
                stats = new ApiCallStats();
                _apiStats[apiName] = stats;
            }
            
            stats.TotalCalls++;
            if (success)
            {
                stats.SuccessfulCalls++;
            }
            else
            {
                stats.FailedCalls++;
            }
            
            stats.TotalElapsedMs += elapsedMs;
            stats.AverageElapsedMs = stats.TotalElapsedMs / stats.TotalCalls;
            stats.LastCallTime = DateTime.Now;
            
            if (elapsedMs > stats.MaxElapsedMs)
            {
                stats.MaxElapsedMs = elapsedMs;
            }
            
            if (stats.MinElapsedMs == 0 || elapsedMs < stats.MinElapsedMs)
            {
                stats.MinElapsedMs = elapsedMs;
            }
        }
        
        /// <summary>
        /// HTTP请求开始事件处理
        /// </summary>
        /// <param name="request">请求对象</param>
        private void OnHttpRequestStarted(ApiRequest request)
        {
            if (_serviceConfig?.EnableRequestLogging == true)
            {
                Debug.Log($"HTTP请求开始: {request.Method} {request.Url}");
            }
        }
        
        /// <summary>
        /// HTTP请求完成事件处理
        /// </summary>
        /// <param name="request">请求对象</param>
        /// <param name="response">响应对象</param>
        private void OnHttpRequestCompleted(ApiRequest request, ApiResponse response)
        {
            if (_serviceConfig?.EnableRequestLogging == true)
            {
                Debug.Log($"HTTP请求完成: {request.Method} {request.Url} - 状态码: {response.StatusCode}, 耗时: {response.ElapsedMilliseconds}ms");
            }
        }
        
        /// <summary>
        /// HTTP请求失败事件处理
        /// </summary>
        /// <param name="request">请求对象</param>
        /// <param name="exception">异常对象</param>
        private void OnHttpRequestFailed(ApiRequest request, ApiException exception)
        {
            Debug.LogError($"HTTP请求失败: {request.Method} {request.Url} - 错误: {exception.Message}");
        }
    }
    
    /// <summary>
    /// API调用统计信息
    /// </summary>
    [Serializable]
    public class ApiCallStats
    {
        /// <summary>
        /// 总调用次数
        /// </summary>
        public int TotalCalls { get; set; }
        
        /// <summary>
        /// 成功调用次数
        /// </summary>
        public int SuccessfulCalls { get; set; }
        
        /// <summary>
        /// 失败调用次数
        /// </summary>
        public int FailedCalls { get; set; }
        
        /// <summary>
        /// 成功率
        /// </summary>
        public float SuccessRate => TotalCalls > 0 ? (float)SuccessfulCalls / TotalCalls : 0f;
        
        /// <summary>
        /// 总耗时（毫秒）
        /// </summary>
        public long TotalElapsedMs { get; set; }
        
        /// <summary>
        /// 平均耗时（毫秒）
        /// </summary>
        public long AverageElapsedMs { get; set; }
        
        /// <summary>
        /// 最大耗时（毫秒）
        /// </summary>
        public long MaxElapsedMs { get; set; }
        
        /// <summary>
        /// 最小耗时（毫秒）
        /// </summary>
        public long MinElapsedMs { get; set; }
        
        /// <summary>
        /// 最后调用时间
        /// </summary>
        public DateTime LastCallTime { get; set; }
    }
}