using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using DigitalHuman.Core.Network.Models;
using DigitalHuman.Core.Database;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 网络状态管理器，负责监控网络连接状态和管理离线模式
    /// </summary>
    public class NetworkStatusManager : ManagerBase
    {
        [Header("监控配置")]
        [SerializeField] private float _checkInterval = 5.0f;
        [SerializeField] private int _qualityTestInterval = 30;
        [SerializeField] private bool _enableAutoModeSwitch = true;
        [SerializeField] private int _connectionTimeoutSeconds = 10;
        
        [Header("测试端点")]
        [SerializeField] private string[] _testUrls = {
            "https://www.google.com",
            "https://www.baidu.com",
            "https://httpbin.org/get"
        };
        
        [Header("离线缓存配置")]
        [SerializeField] private bool _enableOfflineCache = true;
        [SerializeField] private int _maxCacheItems = 1000;
        [SerializeField] private long _maxCacheSizeBytes = 50 * 1024 * 1024; // 50MB
        [SerializeField] private int _defaultCacheExpirationMinutes = 60;
        
        [Header("组件引用")]
        [SerializeField] private SQLiteDatabase _database;
        
        /// <summary>
        /// 当前网络状态
        /// </summary>
        private NetworkStatusInfo _currentStatus;
        
        /// <summary>
        /// 网络状态历史记录
        /// </summary>
        private readonly List<NetworkStatusInfo> _statusHistory = new List<NetworkStatusInfo>();
        
        /// <summary>
        /// 离线缓存
        /// </summary>
        private readonly Dictionary<string, OfflineCacheItem> _offlineCache = new Dictionary<string, OfflineCacheItem>();
        
        /// <summary>
        /// 监控协程
        /// </summary>
        private Coroutine _monitoringCoroutine;
        
        /// <summary>
        /// 质量测试协程
        /// </summary>
        private Coroutine _qualityTestCoroutine;
        
        /// <summary>
        /// 当前网络状态
        /// </summary>
        public NetworkStatusInfo CurrentStatus => _currentStatus?.Clone();
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline => _currentStatus?.IsOnline ?? false;
        
        /// <summary>
        /// 是否离线
        /// </summary>
        public bool IsOffline => _currentStatus?.IsOffline ?? true;
        
        /// <summary>
        /// 当前应用模式
        /// </summary>
        public ApplicationMode CurrentMode => _currentStatus?.ApplicationMode ?? ApplicationMode.Offline;
        
        /// <summary>
        /// 网络状态变化事件
        /// </summary>
        public event EventHandler<NetworkStatusChangedEventArgs> OnNetworkStatusChanged;
        
        /// <summary>
        /// 连接建立事件
        /// </summary>
        public event EventHandler<NetworkStatusInfo> OnNetworkConnected;
        
        /// <summary>
        /// 连接断开事件
        /// </summary>
        public event EventHandler<NetworkStatusInfo> OnNetworkDisconnected;
        
        /// <summary>
        /// 模式切换事件
        /// </summary>
        public event EventHandler<ApplicationMode> OnModeChanged;
        
        /// <summary>
        /// 缓存更新事件
        /// </summary>
        public event EventHandler<string> OnCacheUpdated;
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        protected override void OnInitializeInternal()
        {
            // 初始化网络状态
            _currentStatus = new NetworkStatusInfo();
            
            // 获取数据库组件
            if (_database == null)
            {
                _database = FindObjectOfType<SQLiteDatabase>();
                if (_database == null && _enableOfflineCache)
                {
                    Debug.LogWarning("未找到SQLiteDatabase组件，离线缓存功能将被禁用");
                    _enableOfflineCache = false;
                }
            }
            
            // 初始化数据库表
            if (_enableOfflineCache && _database != null)
            {
                InitializeCacheDatabase();
                LoadOfflineCache();
            }
            
            // 立即检查网络状态
            StartCoroutine(CheckNetworkStatusImmediate());
            
            // 启动监控协程
            _monitoringCoroutine = StartCoroutine(NetworkMonitoringCoroutine());
            _qualityTestCoroutine = StartCoroutine(QualityTestCoroutine());
            
            Debug.Log("网络状态管理器初始化完成");
        }
        
        /// <summary>
        /// 清理管理器资源
        /// </summary>
        protected override void OnCleanupInternal()
        {
            // 停止监控协程
            if (_monitoringCoroutine != null)
            {
                StopCoroutine(_monitoringCoroutine);
                _monitoringCoroutine = null;
            }
            
            if (_qualityTestCoroutine != null)
            {
                StopCoroutine(_qualityTestCoroutine);
                _qualityTestCoroutine = null;
            }
            
            // 保存离线缓存
            if (_enableOfflineCache)
            {
                SaveOfflineCache();
            }
            
            // 清理资源
            _statusHistory.Clear();
            _offlineCache.Clear();
            
            Debug.Log("网络状态管理器清理完成");
        }
        
        /// <summary>
        /// 立即检查网络状态
        /// </summary>
        /// <returns>当前网络状态</returns>
        public async Task<NetworkStatusInfo> CheckNetworkStatusAsync()
        {
            var previousStatus = _currentStatus?.Clone();
            
            // 检查Unity网络可达性
            var unityReachability = Application.internetReachability;
            var newStatus = new NetworkStatusInfo();
            
            // 设置网络类型
            newStatus.NetworkType = unityReachability switch
            {
                NetworkReachability.ReachableViaCarrierDataNetwork => NetworkType.Mobile,
                NetworkReachability.ReachableViaLocalAreaNetwork => NetworkType.WiFi,
                NetworkReachability.NotReachable => NetworkType.None,
                _ => NetworkType.Unknown
            };
            
            // 如果Unity报告无网络，直接设置为断开状态
            if (unityReachability == NetworkReachability.NotReachable)
            {
                newStatus.ConnectionStatus = NetworkConnectionStatus.Disconnected;
                newStatus.IsInternetReachable = false;
                newStatus.DisconnectedSince = DateTime.Now;
            }
            else
            {
                // 测试实际的互联网连接
                var connectivityTest = await TestInternetConnectivity();
                
                if (connectivityTest.IsSuccessful)
                {
                    newStatus.ConnectionStatus = NetworkConnectionStatus.Connected;
                    newStatus.IsInternetReachable = true;
                    newStatus.LatencyMs = connectivityTest.LatencyMs;
                    newStatus.QualityScore = connectivityTest.CalculateQualityScore();
                    newStatus.ConnectedSince = DateTime.Now;
                }
                else
                {
                    newStatus.ConnectionStatus = NetworkConnectionStatus.Disconnected;
                    newStatus.IsInternetReachable = false;
                    newStatus.ErrorMessage = connectivityTest.ErrorMessage;
                    newStatus.DisconnectedSince = DateTime.Now;
                }
            }
            
            // 根据网络状态自动切换应用模式
            if (_enableAutoModeSwitch)
            {
                newStatus.ApplicationMode = newStatus.IsOnline ? ApplicationMode.Online : ApplicationMode.Offline;
            }
            else
            {
                newStatus.ApplicationMode = _currentStatus?.ApplicationMode ?? ApplicationMode.Online;
            }
            
            // 更新状态
            UpdateNetworkStatus(previousStatus, newStatus);
            
            return newStatus;
        }
        
        /// <summary>
        /// 手动切换应用模式
        /// </summary>
        /// <param name="mode">目标模式</param>
        public void SwitchApplicationMode(ApplicationMode mode)
        {
            if (_currentStatus == null) return;
            
            var previousMode = _currentStatus.ApplicationMode;
            _currentStatus.ApplicationMode = mode;
            
            if (previousMode != mode)
            {
                Debug.Log($"应用模式切换: {previousMode} -> {mode}");
                OnModeChanged?.Invoke(this, mode);
            }
        }
        
        /// <summary>
        /// 获取网络状态历史
        /// </summary>
        /// <param name="maxCount">最大数量</param>
        /// <returns>状态历史列表</returns>
        public List<NetworkStatusInfo> GetStatusHistory(int maxCount = 50)
        {
            return _statusHistory.TakeLast(maxCount).ToList();
        }
        
        /// <summary>
        /// 添加离线缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="data">数据</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="expirationMinutes">过期时间（分钟）</param>
        public void AddToOfflineCache(string key, string data, string dataType = "json", int expirationMinutes = -1)
        {
            if (!_enableOfflineCache || string.IsNullOrEmpty(key) || string.IsNullOrEmpty(data))
            {
                return;
            }
            
            var expiration = expirationMinutes > 0 ? expirationMinutes : _defaultCacheExpirationMinutes;
            
            var cacheItem = new OfflineCacheItem
            {
                Key = key,
                Data = data,
                DataType = dataType,
                SizeBytes = System.Text.Encoding.UTF8.GetByteCount(data)
            };
            cacheItem.SetExpiration(expiration);
            
            // 检查缓存大小限制
            if (_offlineCache.Count >= _maxCacheItems || GetTotalCacheSize() + cacheItem.SizeBytes > _maxCacheSizeBytes)
            {
                CleanupExpiredCache();
                
                // 如果仍然超出限制，删除最旧的项目
                while (_offlineCache.Count >= _maxCacheItems || GetTotalCacheSize() + cacheItem.SizeBytes > _maxCacheSizeBytes)
                {
                    var oldestKey = _offlineCache.OrderBy(kvp => kvp.Value.LastAccessedAt).First().Key;
                    _offlineCache.Remove(oldestKey);
                }
            }
            
            _offlineCache[key] = cacheItem;
            
            // 持久化保存
            if (_database != null)
            {
                SaveCacheItemToDatabase(cacheItem);
            }
            
            OnCacheUpdated?.Invoke(this, key);
        }
        
        /// <summary>
        /// 从离线缓存获取数据
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>缓存数据，如果不存在或已过期则返回null</returns>
        public string GetFromOfflineCache(string key)
        {
            if (!_enableOfflineCache || string.IsNullOrEmpty(key))
            {
                return null;
            }
            
            if (_offlineCache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _offlineCache.Remove(key);
                    DeleteCacheItemFromDatabase(key);
                    return null;
                }
                
                cacheItem.UpdateAccess();
                return cacheItem.Data;
            }
            
            return null;
        }
        
        /// <summary>
        /// 检查缓存是否存在
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否存在且未过期</returns>
        public bool HasCacheItem(string key)
        {
            if (!_enableOfflineCache || string.IsNullOrEmpty(key))
            {
                return false;
            }
            
            if (_offlineCache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _offlineCache.Remove(key);
                    DeleteCacheItemFromDatabase(key);
                    return false;
                }
                
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 清除离线缓存
        /// </summary>
        /// <param name="key">缓存键，为空则清除所有</param>
        public void ClearOfflineCache(string key = null)
        {
            if (!_enableOfflineCache) return;
            
            if (string.IsNullOrEmpty(key))
            {
                _offlineCache.Clear();
                if (_database != null)
                {
                    _database.ExecuteNonQuery("DELETE FROM offline_cache");
                }
                Debug.Log("已清除所有离线缓存");
            }
            else
            {
                _offlineCache.Remove(key);
                DeleteCacheItemFromDatabase(key);
                Debug.Log($"已清除缓存项: {key}");
            }
        }
        
        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public (int Count, long SizeBytes, int ExpiredCount) GetCacheStats()
        {
            if (!_enableOfflineCache)
            {
                return (0, 0, 0);
            }
            
            var expiredCount = _offlineCache.Values.Count(item => item.IsExpired);
            var totalSize = GetTotalCacheSize();
            
            return (_offlineCache.Count, totalSize, expiredCount);
        }
        
        /// <summary>
        /// 网络监控协程
        /// </summary>
        /// <returns>协程</returns>
        private IEnumerator NetworkMonitoringCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(_checkInterval);
                
                // 异步检查网络状态
                StartCoroutine(CheckNetworkStatusImmediate());
            }
        }
        
        /// <summary>
        /// 立即检查网络状态的协程版本
        /// </summary>
        /// <returns>协程</returns>
        private IEnumerator CheckNetworkStatusImmediate()
        {
            var task = CheckNetworkStatusAsync();
            yield return new WaitUntil(() => task.IsCompleted);
        }
        
        /// <summary>
        /// 质量测试协程
        /// </summary>
        /// <returns>协程</returns>
        private IEnumerator QualityTestCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(_qualityTestInterval);
                
                if (IsOnline)
                {
                    StartCoroutine(PerformQualityTest());
                }
            }
        }
        
        /// <summary>
        /// 执行网络质量测试
        /// </summary>
        /// <returns>协程</returns>
        private IEnumerator PerformQualityTest()
        {
            var task = TestInternetConnectivity();
            yield return new WaitUntil(() => task.IsCompleted);
            
            var result = task.Result;
            if (result.IsSuccessful && _currentStatus != null)
            {
                _currentStatus.LatencyMs = result.LatencyMs;
                _currentStatus.QualityScore = result.CalculateQualityScore();
                _currentStatus.LastCheckTime = DateTime.Now;
            }
        }
        
        /// <summary>
        /// 测试互联网连接
        /// </summary>
        /// <returns>测试结果</returns>
        private async Task<NetworkQualityTestResult> TestInternetConnectivity()
        {
            var result = new NetworkQualityTestResult();
            
            foreach (var testUrl in _testUrls)
            {
                try
                {
                    var startTime = DateTime.Now;
                    
                    using (var request = UnityWebRequest.Get(testUrl))
                    {
                        request.timeout = _connectionTimeoutSeconds;
                        
                        var operation = request.SendWebRequest();
                        
                        // 等待请求完成
                        while (!operation.isDone)
                        {
                            await Task.Delay(50);
                        }
                        
                        var endTime = DateTime.Now;
                        var latency = (int)(endTime - startTime).TotalMilliseconds;
                        
                        if (request.result == UnityWebRequest.Result.Success)
                        {
                            result.IsSuccessful = true;
                            result.LatencyMs = latency;
                            result.DownloadSpeedKbps = request.downloadedBytes / (latency / 1000.0f) / 1024.0f;
                            break; // 成功连接到一个端点就足够了
                        }
                        else
                        {
                            result.ErrorMessage = request.error;
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.ErrorMessage = ex.Message;
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 更新网络状态
        /// </summary>
        /// <param name="previousStatus">之前的状态</param>
        /// <param name="newStatus">新状态</param>
        private void UpdateNetworkStatus(NetworkStatusInfo previousStatus, NetworkStatusInfo newStatus)
        {
            var statusChanged = previousStatus == null || 
                               previousStatus.ConnectionStatus != newStatus.ConnectionStatus ||
                               previousStatus.IsInternetReachable != newStatus.IsInternetReachable ||
                               previousStatus.ApplicationMode != newStatus.ApplicationMode;
            
            _currentStatus = newStatus;
            
            // 添加到历史记录
            _statusHistory.Add(newStatus.Clone());
            if (_statusHistory.Count > 100) // 保留最近100条记录
            {
                _statusHistory.RemoveAt(0);
            }
            
            if (statusChanged)
            {
                // 触发状态变化事件
                OnNetworkStatusChanged?.Invoke(this, new NetworkStatusChangedEventArgs(previousStatus, newStatus));
                
                // 触发连接/断开事件
                if (previousStatus != null)
                {
                    if (!previousStatus.IsOnline && newStatus.IsOnline)
                    {
                        OnNetworkConnected?.Invoke(this, newStatus);
                        Debug.Log("网络连接已建立");
                    }
                    else if (previousStatus.IsOnline && !newStatus.IsOnline)
                    {
                        OnNetworkDisconnected?.Invoke(this, newStatus);
                        Debug.Log("网络连接已断开");
                    }
                }
                
                // 触发模式变化事件
                if (previousStatus?.ApplicationMode != newStatus.ApplicationMode)
                {
                    OnModeChanged?.Invoke(this, newStatus.ApplicationMode);
                }
            }
        }
        
        /// <summary>
        /// 初始化缓存数据库
        /// </summary>
        private void InitializeCacheDatabase()
        {
            if (_database == null) return;
            
            var createTable = @"
                CREATE TABLE IF NOT EXISTS offline_cache (
                    key TEXT PRIMARY KEY,
                    data TEXT,
                    data_type TEXT,
                    created_at INTEGER,
                    expires_at INTEGER,
                    access_count INTEGER,
                    last_accessed_at INTEGER,
                    size_bytes INTEGER,
                    needs_sync INTEGER
                )";
            
            _database.ExecuteNonQuery(createTable);
        }
        
        /// <summary>
        /// 加载离线缓存
        /// </summary>
        private void LoadOfflineCache()
        {
            if (_database == null) return;
            
            try
            {
                var query = "SELECT * FROM offline_cache WHERE expires_at > ?";
                var currentTime = ((DateTimeOffset)DateTime.Now).ToUnixTimeSeconds();
                var results = _database.ExecuteQuery(query, currentTime);
                
                foreach (var row in results)
                {
                    var cacheItem = new OfflineCacheItem
                    {
                        Key = row["key"].ToString(),
                        Data = row["data"].ToString(),
                        DataType = row["data_type"].ToString(),
                        CreatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["created_at"])).DateTime,
                        ExpiresAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["expires_at"])).DateTime,
                        AccessCount = Convert.ToInt32(row["access_count"]),
                        LastAccessedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["last_accessed_at"])).DateTime,
                        SizeBytes = Convert.ToInt64(row["size_bytes"]),
                        NeedsSync = Convert.ToBoolean(row["needs_sync"])
                    };
                    
                    _offlineCache[cacheItem.Key] = cacheItem;
                }
                
                Debug.Log($"从数据库加载了 {_offlineCache.Count} 个缓存项");
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载离线缓存失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存离线缓存
        /// </summary>
        private void SaveOfflineCache()
        {
            if (_database == null) return;
            
            foreach (var cacheItem in _offlineCache.Values)
            {
                SaveCacheItemToDatabase(cacheItem);
            }
        }
        
        /// <summary>
        /// 保存缓存项到数据库
        /// </summary>
        /// <param name="cacheItem">缓存项</param>
        private void SaveCacheItemToDatabase(OfflineCacheItem cacheItem)
        {
            if (_database == null) return;
            
            try
            {
                var upsert = @"
                    INSERT OR REPLACE INTO offline_cache 
                    (key, data, data_type, created_at, expires_at, access_count, last_accessed_at, size_bytes, needs_sync)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                _database.ExecuteNonQuery(upsert,
                    cacheItem.Key,
                    cacheItem.Data,
                    cacheItem.DataType,
                    ((DateTimeOffset)cacheItem.CreatedAt).ToUnixTimeSeconds(),
                    ((DateTimeOffset)cacheItem.ExpiresAt).ToUnixTimeSeconds(),
                    cacheItem.AccessCount,
                    ((DateTimeOffset)cacheItem.LastAccessedAt).ToUnixTimeSeconds(),
                    cacheItem.SizeBytes,
                    cacheItem.NeedsSync ? 1 : 0);
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存缓存项失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 从数据库删除缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        private void DeleteCacheItemFromDatabase(string key)
        {
            if (_database == null) return;
            
            try
            {
                _database.ExecuteNonQuery("DELETE FROM offline_cache WHERE key = ?", key);
            }
            catch (Exception ex)
            {
                Debug.LogError($"删除缓存项失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 清理过期缓存
        /// </summary>
        private void CleanupExpiredCache()
        {
            var expiredKeys = _offlineCache
                .Where(kvp => kvp.Value.IsExpired)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var key in expiredKeys)
            {
                _offlineCache.Remove(key);
                DeleteCacheItemFromDatabase(key);
            }
            
            if (expiredKeys.Count > 0)
            {
                Debug.Log($"清理了 {expiredKeys.Count} 个过期缓存项");
            }
        }
        
        /// <summary>
        /// 获取缓存总大小
        /// </summary>
        /// <returns>总大小（字节）</returns>
        private long GetTotalCacheSize()
        {
            return _offlineCache.Values.Sum(item => item.SizeBytes);
        }
    }
}