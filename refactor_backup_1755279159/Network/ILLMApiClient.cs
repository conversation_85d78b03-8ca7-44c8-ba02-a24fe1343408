using System;
using System.Threading.Tasks;
using DigitalHuman.Core.Network.Models;
using DigitalHuman.Core.Configuration.Models;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 大模型API客户端接口
    /// </summary>
    public interface ILLMApiClient
    {
        /// <summary>
        /// 当前使用的配置
        /// </summary>
        LLMApiConfiguration CurrentConfiguration { get; }
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 初始化客户端
        /// </summary>
        /// <param name="configuration">配置信息</param>
        void Initialize(LLMApiConfiguration configuration);
        
        /// <summary>
        /// 发送聊天请求
        /// </summary>
        /// <param name="request">聊天请求</param>
        /// <returns>聊天响应</returns>
        Task<ApiResponse<LLMChatResponse>> SendChatRequestAsync(LLMChatRequest request);
        
        /// <summary>
        /// 发送简单文本请求
        /// </summary>
        /// <param name="message">用户消息</param>
        /// <param name="systemPrompt">系统提示（可选）</param>
        /// <returns>响应文本</returns>
        Task<ApiResponse<string>> SendTextRequestAsync(string message, string systemPrompt = null);
        
        /// <summary>
        /// 使用对话上下文发送请求
        /// </summary>
        /// <param name="context">对话上下文</param>
        /// <param name="userMessage">用户消息</param>
        /// <returns>响应文本</returns>
        Task<ApiResponse<string>> SendContextRequestAsync(ConversationContext context, string userMessage);
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        Task<ApiResponse<bool>> ValidateConfigurationAsync();
        
        /// <summary>
        /// 获取模型信息
        /// </summary>
        /// <returns>模型信息</returns>
        Task<ApiResponse<object>> GetModelInfoAsync();
        
        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        void UpdateConfiguration(LLMApiConfiguration configuration);
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void Cleanup();
        
        /// <summary>
        /// 请求开始事件
        /// </summary>
        event Action<LLMChatRequest> OnRequestStarted;
        
        /// <summary>
        /// 请求完成事件
        /// </summary>
        event Action<LLMChatRequest, LLMChatResponse> OnRequestCompleted;
        
        /// <summary>
        /// 请求失败事件
        /// </summary>
        event Action<LLMChatRequest, ApiException> OnRequestFailed;
    }
}