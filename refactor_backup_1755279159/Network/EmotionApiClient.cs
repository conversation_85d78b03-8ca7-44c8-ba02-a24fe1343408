using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 情感分析API客户端实现
    /// 支持多种情感分析服务的集成
    /// </summary>
    public class EmotionApiClient : MonoBehaviour, IEmotionApiClient
    {
        [Header("配置")]
        [SerializeField] private EmotionAnalysisConfig config = new EmotionAnalysisConfig();
        [SerializeField] private bool enableLogging = true;
        
        // 私有字段
        private bool isInitialized = false;
        private Dictionary<string, EmotionData> emotionCache = new Dictionary<string, EmotionData>();
        private float lastAnalysisTime = 0f;
        private IHttpClient httpClient;
        
        // 公共属性
        public bool IsInitialized => isInitialized;
        public EmotionAnalysisConfig Configuration => config;
        
        // 事件
        public event Action<EmotionData> OnEmotionAnalyzed;
        public event Action<string> OnAnalysisError;
        
        void Start()
        {
            // 获取HTTP客户端
            httpClient = FindObjectOfType<HttpClient>();
            if (httpClient == null)
            {
                var httpClientObject = new GameObject("HttpClient");
                httpClientObject.transform.SetParent(transform);
                httpClient = httpClientObject.AddComponent<HttpClient>();
            }
        }
        
        void OnDestroy()
        {
            Cleanup();
        }
        
        public bool Initialize(EmotionAnalysisConfig config)
        {
            try
            {
                if (config == null)
                {
                    LogError("配置不能为空");
                    return false;
                }
                
                if (!config.IsValid())
                {
                    LogError("配置无效：缺少必要的API端点或密钥");
                    return false;
                }
                
                this.config = config;
                emotionCache.Clear();
                lastAnalysisTime = 0f;
                
                isInitialized = true;
                LogInfo("情感分析API客户端初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"初始化失败: {ex.Message}");
                return false;
            }
        }
        
        public async Task<EmotionData> AnalyzeEmotionAsync(byte[] imageData, string format = "jpg")
        {
            if (!isInitialized)
            {
                LogError("客户端未初始化");
                return null;
            }
            
            if (!config.enabled)
            {
                LogWarning("情感分析已禁用");
                return CreateNeutralEmotion();
            }
            
            if (imageData == null || imageData.Length == 0)
            {
                LogError("图像数据为空");
                return null;
            }
            
            try
            {
                // 检查分析频率限制
                if (Time.time - lastAnalysisTime < 1f / config.analysisFrequency)
                {
                    LogWarning("分析频率过高，跳过此次分析");
                    return null;
                }
                
                // 检查缓存
                string cacheKey = GenerateCacheKey(imageData);
                if (config.useCache && emotionCache.ContainsKey(cacheKey))
                {
                    var cachedEmotion = emotionCache[cacheKey];
                    if ((DateTime.Now - cachedEmotion.timestamp).TotalSeconds < config.cacheExpirationSeconds)
                    {
                        LogInfo("使用缓存的情感分析结果");
                        return cachedEmotion;
                    }
                    else
                    {
                        emotionCache.Remove(cacheKey);
                    }
                }
                
                lastAnalysisTime = Time.time;
                
                // 创建请求
                var request = new EmotionAnalysisRequest(
                    Convert.ToBase64String(imageData), 
                    format
                );
                
                // 发送API请求
                var response = await SendAnalysisRequest(request);
                
                if (response != null && response.success && response.emotionData != null)
                {
                    // 验证结果
                    if (response.emotionData.confidence >= config.minConfidence)
                    {
                        response.emotionData.isValid = true;
                        
                        // 缓存结果
                        if (config.useCache)
                        {
                            emotionCache[cacheKey] = response.emotionData;
                        }
                        
                        // 触发事件
                        OnEmotionAnalyzed?.Invoke(response.emotionData);
                        
                        LogInfo($"情感分析完成: {response.emotionData}");
                        return response.emotionData;
                    }
                    else
                    {
                        LogWarning($"情感分析置信度过低: {response.emotionData.confidence}");
                        return CreateNeutralEmotion();
                    }
                }
                else
                {
                    string errorMsg = response?.errorMessage ?? "未知错误";
                    LogError($"情感分析失败: {errorMsg}");
                    OnAnalysisError?.Invoke(errorMsg);
                    return null;
                }
            }
            catch (Exception ex)
            {
                LogError($"情感分析异常: {ex.Message}");
                OnAnalysisError?.Invoke(ex.Message);
                return null;
            }
        }
        
        public async Task<EmotionData> AnalyzeEmotionAsync(Texture2D texture)
        {
            if (texture == null)
            {
                LogError("纹理对象为空");
                return null;
            }
            
            try
            {
                // 将纹理转换为字节数组
                byte[] imageData = texture.EncodeToJPG(80);
                return await AnalyzeEmotionAsync(imageData, "jpg");
            }
            catch (Exception ex)
            {
                LogError($"纹理转换失败: {ex.Message}");
                return null;
            }
        }
        
        public async Task<EmotionData[]> AnalyzeEmotionBatchAsync(byte[][] imageDataList)
        {
            if (imageDataList == null || imageDataList.Length == 0)
            {
                LogError("图像数据列表为空");
                return new EmotionData[0];
            }
            
            var results = new List<EmotionData>();
            
            foreach (var imageData in imageDataList)
            {
                var result = await AnalyzeEmotionAsync(imageData);
                results.Add(result);
                
                // 添加延迟以避免API限制
                await Task.Delay(100);
            }
            
            return results.ToArray();
        }
        
        public async Task<bool> TestConnectionAsync()
        {
            if (!isInitialized)
            {
                LogError("客户端未初始化");
                return false;
            }
            
            try
            {
                // 创建测试图像（1x1像素的白色图像）
                var testTexture = new Texture2D(1, 1);
                testTexture.SetPixel(0, 0, Color.white);
                testTexture.Apply();
                
                byte[] testImageData = testTexture.EncodeToJPG();
                DestroyImmediate(testTexture);
                
                // 发送测试请求
                var testRequest = new EmotionAnalysisRequest(
                    Convert.ToBase64String(testImageData),
                    "jpg"
                );
                
                var response = await SendAnalysisRequest(testRequest);
                
                bool isConnected = response != null && (response.success || !string.IsNullOrEmpty(response.errorMessage));
                
                if (isConnected)
                {
                    LogInfo("API连接测试成功");
                }
                else
                {
                    LogError("API连接测试失败");
                }
                
                return isConnected;
            }
            catch (Exception ex)
            {
                LogError($"连接测试异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 发送分析请求
        /// </summary>
        /// <param name="request">请求对象</param>
        /// <returns>响应对象</returns>
        private async Task<EmotionAnalysisResponse> SendAnalysisRequest(EmotionAnalysisRequest request)
        {
            try
            {
                // 构建请求数据
                var requestData = new Dictionary<string, object>
                {
                    ["image_data"] = request.imageData,
                    ["image_format"] = request.imageFormat,
                    ["analysis_type"] = request.analysisType,
                    ["request_id"] = request.requestId
                };
                
                // 添加自定义参数
                foreach (var param in request.parameters)
                {
                    requestData[param.Key] = param.Value;
                }
                
                string jsonData = JsonUtility.ToJson(new SerializableDict(requestData));
                
                // 构建请求头
                var headers = new Dictionary<string, string>
                {
                    ["Content-Type"] = "application/json"
                };
                
                if (!config.isPrivateDeployment && !string.IsNullOrEmpty(config.apiKey))
                {
                    headers["Authorization"] = $"Bearer {config.apiKey}";
                }
                
                // 添加自定义请求头
                foreach (var header in config.customHeaders)
                {
                    headers[header.Key] = header.Value;
                }
                
                // 发送HTTP请求
                var httpResponse = await httpClient.PostAsync(config.apiEndpoint, jsonData, headers);
                
                if (httpResponse.IsSuccess)
                {
                    // 解析响应
                    return ParseAnalysisResponse(httpResponse.Data, request.requestId);
                }
                else
                {
                    LogError($"HTTP请求失败: {httpResponse.ErrorMessage}");
                    return new EmotionAnalysisResponse
                    {
                        requestId = request.requestId,
                        success = false,
                        errorMessage = httpResponse.ErrorMessage
                    };
                }
            }
            catch (Exception ex)
            {
                LogError($"发送请求异常: {ex.Message}");
                return new EmotionAnalysisResponse
                {
                    requestId = request.requestId,
                    success = false,
                    errorMessage = ex.Message
                };
            }
        }
        
        /// <summary>
        /// 解析分析响应
        /// </summary>
        /// <param name="responseData">响应数据</param>
        /// <param name="requestId">请求ID</param>
        /// <returns>解析后的响应对象</returns>
        private EmotionAnalysisResponse ParseAnalysisResponse(string responseData, string requestId)
        {
            try
            {
                // 这里需要根据实际的API响应格式进行解析
                // 以下是一个通用的解析示例
                
                var response = new EmotionAnalysisResponse
                {
                    requestId = requestId,
                    success = true
                };
                
                // 简化的JSON解析（实际项目中建议使用专业的JSON库）
                if (responseData.Contains("\"success\":true") || responseData.Contains("\"status\":\"success\""))
                {
                    response.emotionData = ParseEmotionData(responseData);
                    response.success = response.emotionData != null;
                }
                else
                {
                    response.success = false;
                    response.errorMessage = ExtractErrorMessage(responseData);
                }
                
                return response;
            }
            catch (Exception ex)
            {
                LogError($"解析响应失败: {ex.Message}");
                return new EmotionAnalysisResponse
                {
                    requestId = requestId,
                    success = false,
                    errorMessage = $"解析失败: {ex.Message}"
                };
            }
        }
        
        /// <summary>
        /// 解析情感数据
        /// </summary>
        /// <param name="responseData">响应数据</param>
        /// <returns>情感数据对象</returns>
        private EmotionData ParseEmotionData(string responseData)
        {
            try
            {
                // 简化的情感数据解析
                // 实际项目中需要根据具体的API响应格式进行解析
                
                var emotionData = new EmotionData();
                
                // 模拟解析过程
                if (responseData.Contains("happy"))
                {
                    emotionData.primaryEmotion = EmotionType.Happy;
                    emotionData.confidence = 0.85f;
                }
                else if (responseData.Contains("sad"))
                {
                    emotionData.primaryEmotion = EmotionType.Sad;
                    emotionData.confidence = 0.75f;
                }
                else if (responseData.Contains("angry"))
                {
                    emotionData.primaryEmotion = EmotionType.Angry;
                    emotionData.confidence = 0.80f;
                }
                else
                {
                    emotionData.primaryEmotion = EmotionType.Neutral;
                    emotionData.confidence = 0.70f;
                }
                
                emotionData.intensity = emotionData.confidence;
                emotionData.intensityLevel = EmotionData.GetIntensityLevel(emotionData.intensity);
                emotionData.isValid = emotionData.confidence >= config.minConfidence;
                emotionData.timestamp = DateTime.Now;
                
                return emotionData;
            }
            catch (Exception ex)
            {
                LogError($"解析情感数据失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 提取错误消息
        /// </summary>
        /// <param name="responseData">响应数据</param>
        /// <returns>错误消息</returns>
        private string ExtractErrorMessage(string responseData)
        {
            // 简化的错误消息提取
            if (responseData.Contains("error"))
            {
                return "API返回错误";
            }
            return "未知错误";
        }
        
        /// <summary>
        /// 生成缓存键
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <returns>缓存键</returns>
        private string GenerateCacheKey(byte[] imageData)
        {
            // 使用图像数据的哈希值作为缓存键
            return imageData.GetHashCode().ToString();
        }
        
        /// <summary>
        /// 创建中性情感数据
        /// </summary>
        /// <returns>中性情感数据</returns>
        private EmotionData CreateNeutralEmotion()
        {
            return new EmotionData(EmotionType.Neutral, 1.0f, 0.5f)
            {
                isValid = true
            };
        }
        
        public void UpdateConfiguration(EmotionAnalysisConfig config)
        {
            if (config == null)
            {
                LogError("配置不能为空");
                return;
            }
            
            this.config = config;
            emotionCache.Clear(); // 清空缓存以应用新配置
            
            LogInfo("情感分析配置已更新");
        }
        
        public void Cleanup()
        {
            emotionCache.Clear();
            isInitialized = false;
            
            LogInfo("情感分析API客户端已清理");
        }
        
        private void LogInfo(string message)
        {
            if (enableLogging)
            {
                Debug.Log($"[EmotionApiClient] {message}");
            }
        }
        
        private void LogWarning(string message)
        {
            if (enableLogging)
            {
                Debug.LogWarning($"[EmotionApiClient] {message}");
            }
        }
        
        private void LogError(string message)
        {
            if (enableLogging)
            {
                Debug.LogError($"[EmotionApiClient] {message}");
            }
        }
        
        /// <summary>
        /// 可序列化的字典类（用于JSON序列化）
        /// </summary>
        [Serializable]
        private class SerializableDict
        {
            public Dictionary<string, object> data;
            
            public SerializableDict(Dictionary<string, object> dict)
            {
                data = dict;
            }
        }
    }
}