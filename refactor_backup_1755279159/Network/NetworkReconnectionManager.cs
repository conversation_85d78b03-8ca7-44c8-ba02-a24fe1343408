using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 网络重连管理器实现
    /// </summary>
    public class NetworkReconnectionManager : SingletonManager<NetworkReconnectionManager>, INetworkReconnectionManager
    {
        [Header("重连配置")]
        [SerializeField] private bool _isAutoReconnectionEnabled = true;
        [SerializeField] private int _maxReconnectionAttempts = 5;
        [SerializeField] private float _reconnectionInterval = 5.0f;
        [SerializeField] private float _apiTimeoutSeconds = 10.0f;
        
        [Header("API重试配置")]
        [SerializeField] private int _defaultMaxRetries = 3;
        [SerializeField] private float _defaultRetryDelay = 1.0f;
        [SerializeField] private bool _enableApiHealthCheck = true;
        [SerializeField] private float _healthCheckInterval = 30.0f;
        
        private ReconnectionStatus _currentReconnectionStatus = ReconnectionStatus.Idle;
        private int _reconnectionAttempts = 0;
        private ReconnectionStrategy _reconnectionStrategy;
        private Dictionary<string, Func<Task<bool>>> _apiEndpoints;
        private List<ReconnectionResult> _reconnectionHistory;
        private ApiCallStatistics _apiCallStatistics;
        
        private Coroutine _reconnectionCoroutine;
        private Coroutine _healthCheckCoroutine;
        private NetworkStatusManager _networkStatusManager;
        
        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        public bool IsAutoReconnectionEnabled
        {
            get => _isAutoReconnectionEnabled;
            set => _isAutoReconnectionEnabled = value;
        }
        
        /// <summary>
        /// 当前重连状态
        /// </summary>
        public ReconnectionStatus CurrentReconnectionStatus => _currentReconnectionStatus;
        
        /// <summary>
        /// 重连尝试次数
        /// </summary>
        public int ReconnectionAttempts => _reconnectionAttempts;
        
        /// <summary>
        /// 最大重连尝试次数
        /// </summary>
        public int MaxReconnectionAttempts
        {
            get => _maxReconnectionAttempts;
            set => _maxReconnectionAttempts = value;
        }
        
        /// <summary>
        /// 重连间隔（秒）
        /// </summary>
        public float ReconnectionInterval
        {
            get => _reconnectionInterval;
            set => _reconnectionInterval = value;
        }
        
        /// <summary>
        /// 网络重连开始事件
        /// </summary>
        public event Action<ReconnectionAttemptInfo> OnReconnectionStarted;
        
        /// <summary>
        /// 网络重连成功事件
        /// </summary>
        public event Action<ReconnectionResult> OnReconnectionSucceeded;
        
        /// <summary>
        /// 网络重连失败事件
        /// </summary>
        public event Action<ReconnectionResult> OnReconnectionFailed;
        
        /// <summary>
        /// 网络重连放弃事件
        /// </summary>
        public event Action<ReconnectionResult> OnReconnectionAbandoned;
        
        /// <summary>
        /// API调用失败事件
        /// </summary>
        public event Action<ApiCallFailureInfo> OnApiCallFailed;
        
        /// <summary>
        /// API调用重试事件
        /// </summary>
        public event Action<ApiRetryInfo> OnApiCallRetried;
        
        /// <summary>
        /// 初始化网络重连管理器
        /// </summary>
        protected override void OnInitializeInternal()
        {
            _reconnectionStrategy = ReconnectionStrategy.ExponentialBackoff();
            _apiEndpoints = new Dictionary<string, Func<Task<bool>>>();
            _reconnectionHistory = new List<ReconnectionResult>();
            _apiCallStatistics = new ApiCallStatistics();
            
            // 获取网络状态管理器
            _networkStatusManager = FindObjectOfType<NetworkStatusManager>();
            if (_networkStatusManager != null)
            {
                _networkStatusManager.OnNetworkDisconnected += OnNetworkDisconnected;
                _networkStatusManager.OnNetworkConnected += OnNetworkConnected;
            }
            
            // 启动API健康检查
            if (_enableApiHealthCheck)
            {
                _healthCheckCoroutine = StartCoroutine(ApiHealthCheckCoroutine());
            }
            
            Debug.Log("[NetworkReconnectionManager] 网络重连管理器初始化完成");
        }
        
        /// <summary>
        /// 清理网络重连管理器
        /// </summary>
        protected override void OnCleanupInternal()
        {
            // 停止重连
            StopReconnection();
            
            // 停止健康检查
            if (_healthCheckCoroutine != null)
            {
                StopCoroutine(_healthCheckCoroutine);
                _healthCheckCoroutine = null;
            }
            
            // 取消事件订阅
            if (_networkStatusManager != null)
            {
                _networkStatusManager.OnNetworkDisconnected -= OnNetworkDisconnected;
                _networkStatusManager.OnNetworkConnected -= OnNetworkConnected;
            }
            
            _apiEndpoints?.Clear();
            _reconnectionHistory?.Clear();
            
            Debug.Log("[NetworkReconnectionManager] 网络重连管理器清理完成");
        }
        
        /// <summary>
        /// 开始网络重连
        /// </summary>
        public async Task<bool> StartReconnectionAsync()
        {
            if (_currentReconnectionStatus == ReconnectionStatus.Reconnecting)
            {
                Debug.LogWarning("[NetworkReconnectionManager] 重连已在进行中");
                return false;
            }
            
            _currentReconnectionStatus = ReconnectionStatus.Reconnecting;
            _reconnectionAttempts = 0;
            
            Debug.Log("[NetworkReconnectionManager] 开始网络重连");
            
            // 启动重连协程
            _reconnectionCoroutine = StartCoroutine(ReconnectionCoroutine());
            
            // 等待重连完成
            while (_currentReconnectionStatus == ReconnectionStatus.Reconnecting)
            {
                await Task.Delay(100);
            }
            
            return _currentReconnectionStatus == ReconnectionStatus.Connected;
        }
        
        /// <summary>
        /// 停止网络重连
        /// </summary>
        public void StopReconnection()
        {
            if (_reconnectionCoroutine != null)
            {
                StopCoroutine(_reconnectionCoroutine);
                _reconnectionCoroutine = null;
            }
            
            if (_currentReconnectionStatus == ReconnectionStatus.Reconnecting)
            {
                _currentReconnectionStatus = ReconnectionStatus.Abandoned;
                Debug.Log("[NetworkReconnectionManager] 网络重连已停止");
            }
        }
        
        /// <summary>
        /// 手动触发重连
        /// </summary>
        public async Task<bool> TriggerManualReconnectionAsync()
        {
            Debug.Log("[NetworkReconnectionManager] 手动触发重连");
            return await StartReconnectionAsync();
        }
        
        /// <summary>
        /// 注册API端点监控
        /// </summary>
        public void RegisterApiEndpoint(string endpoint, Func<Task<bool>> healthChecker)
        {
            if (string.IsNullOrEmpty(endpoint) || healthChecker == null) return;
            
            _apiEndpoints[endpoint] = healthChecker;
            Debug.Log($"[NetworkReconnectionManager] 注册API端点监控: {endpoint}");
        }
        
        /// <summary>
        /// 取消注册API端点监控
        /// </summary>
        public void UnregisterApiEndpoint(string endpoint)
        {
            if (_apiEndpoints.ContainsKey(endpoint))
            {
                _apiEndpoints.Remove(endpoint);
                Debug.Log($"[NetworkReconnectionManager] 取消注册API端点监控: {endpoint}");
            }
        }
        
        /// <summary>
        /// 执行带重试的API调用
        /// </summary>
        public async Task<ApiCallResult<T>> ExecuteWithRetryAsync<T>(Func<Task<T>> apiCall, int maxRetries = 3, float retryDelay = 1.0f)
        {
            var startTime = DateTime.Now;
            var retryCount = 0;
            Exception lastException = null;
            
            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var result = await apiCall();
                    var responseTime = (long)(DateTime.Now - startTime).TotalMilliseconds;
                    
                    // 更新统计信息
                    UpdateApiCallStatistics(true, responseTime, retryCount, false);
                    
                    return ApiCallResult<T>.Success(result, responseTime, retryCount);
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    retryCount++;
                    
                    var responseTime = (long)(DateTime.Now - startTime).TotalMilliseconds;
                    var failureInfo = new ApiCallFailureInfo("Unknown", GetApiCallStatus(ex), ex.Message);
                    failureInfo.ResponseTimeMs = responseTime;
                    failureInfo.RetryCount = retryCount;
                    failureInfo.WillRetry = attempt < maxRetries;
                    
                    OnApiCallFailed?.Invoke(failureInfo);
                    
                    if (attempt < maxRetries)
                    {
                        var retryInfo = new ApiRetryInfo(failureInfo, attempt + 1, maxRetries, retryDelay, "FixedDelay");
                        OnApiCallRetried?.Invoke(retryInfo);
                        
                        Debug.LogWarning($"[NetworkReconnectionManager] API调用失败，{retryDelay}秒后重试 ({attempt + 1}/{maxRetries}): {ex.Message}");
                        await Task.Delay((int)(retryDelay * 1000));
                    }
                }
            }
            
            // 所有重试都失败了
            var finalResponseTime = (long)(DateTime.Now - startTime).TotalMilliseconds;
            UpdateApiCallStatistics(false, finalResponseTime, retryCount, false);
            
            return ApiCallResult<T>.Failure(lastException?.Message ?? "Unknown error", 0, finalResponseTime, retryCount);
        }
        
        /// <summary>
        /// 执行带降级的API调用
        /// </summary>
        public async Task<ApiCallResult<T>> ExecuteWithFallbackAsync<T>(Func<Task<T>> primaryCall, Func<Task<T>> fallbackCall, int maxRetries = 3)
        {
            // 首先尝试主要调用
            var primaryResult = await ExecuteWithRetryAsync(primaryCall, maxRetries);
            
            if (primaryResult.IsSuccessful)
            {
                return primaryResult;
            }
            
            // 主要调用失败，尝试降级调用
            Debug.LogWarning("[NetworkReconnectionManager] 主要API调用失败，尝试降级调用");
            
            try
            {
                var startTime = DateTime.Now;
                var fallbackData = await fallbackCall();
                var responseTime = (long)(DateTime.Now - startTime).TotalMilliseconds;
                
                // 更新统计信息
                UpdateApiCallStatistics(true, responseTime, primaryResult.RetryCount, true);
                
                var result = ApiCallResult<T>.Success(fallbackData, responseTime, primaryResult.RetryCount);
                result.UsedFallback = true;
                result.Metadata["PrimaryError"] = primaryResult.ErrorMessage;
                
                return result;
            }
            catch (Exception ex)
            {
                // 降级调用也失败了
                UpdateApiCallStatistics(false, 0, primaryResult.RetryCount, true);
                
                var result = ApiCallResult<T>.Failure($"Primary: {primaryResult.ErrorMessage}, Fallback: {ex.Message}");
                result.RetryCount = primaryResult.RetryCount;
                result.Metadata["PrimaryError"] = primaryResult.ErrorMessage;
                result.Metadata["FallbackError"] = ex.Message;
                
                return result;
            }
        }
        
        /// <summary>
        /// 获取重连历史记录
        /// </summary>
        public List<ReconnectionResult> GetReconnectionHistory()
        {
            return new List<ReconnectionResult>(_reconnectionHistory);
        }
        
        /// <summary>
        /// 获取API调用统计信息
        /// </summary>
        public ApiCallStatistics GetApiCallStatistics()
        {
            return _apiCallStatistics;
        }
        
        /// <summary>
        /// 重置重连统计
        /// </summary>
        public void ResetReconnectionStatistics()
        {
            _reconnectionHistory.Clear();
            _apiCallStatistics = new ApiCallStatistics();
            _reconnectionAttempts = 0;
            
            Debug.Log("[NetworkReconnectionManager] 重连统计已重置");
        }
        
        /// <summary>
        /// 设置重连策略
        /// </summary>
        public void SetReconnectionStrategy(ReconnectionStrategy strategy)
        {
            _reconnectionStrategy = strategy ?? ReconnectionStrategy.ExponentialBackoff();
            Debug.Log($"[NetworkReconnectionManager] 重连策略已设置: {_reconnectionStrategy.Type}");
        }
        
        /// <summary>
        /// 获取当前重连策略
        /// </summary>
        public ReconnectionStrategy GetReconnectionStrategy()
        {
            return _reconnectionStrategy;
        }
        
        /// <summary>
        /// 重连协程
        /// </summary>
        private IEnumerator ReconnectionCoroutine()
        {
            while (_reconnectionAttempts < _maxReconnectionAttempts && _currentReconnectionStatus == ReconnectionStatus.Reconnecting)
            {
                _reconnectionAttempts++;
                
                var delaySeconds = _reconnectionStrategy.CalculateInterval(_reconnectionAttempts);
                var attemptInfo = new ReconnectionAttemptInfo(_reconnectionAttempts, "Auto reconnection", _reconnectionStrategy.Type, delaySeconds);
                
                OnReconnectionStarted?.Invoke(attemptInfo);
                
                Debug.Log($"[NetworkReconnectionManager] 重连尝试 {_reconnectionAttempts}/{_maxReconnectionAttempts}，延迟 {delaySeconds:F1} 秒");
                
                // 等待延迟时间
                yield return new WaitForSeconds(delaySeconds);
                
                // 检查网络连接
                var isConnected = await CheckNetworkConnection();
                
                var result = new ReconnectionResult(attemptInfo, isConnected);
                
                if (isConnected)
                {
                    _currentReconnectionStatus = ReconnectionStatus.Connected;
                    result.NetworkQuality = await GetNetworkQuality();
                    
                    _reconnectionHistory.Add(result);
                    OnReconnectionSucceeded?.Invoke(result);
                    
                    Debug.Log("[NetworkReconnectionManager] 网络重连成功");
                    yield break;
                }
                else
                {
                    result.ErrorMessage = "网络连接检查失败";
                    _reconnectionHistory.Add(result);
                    OnReconnectionFailed?.Invoke(result);
                    
                    Debug.LogWarning($"[NetworkReconnectionManager] 重连尝试 {_reconnectionAttempts} 失败");
                }
            }
            
            // 所有重连尝试都失败了
            if (_currentReconnectionStatus == ReconnectionStatus.Reconnecting)
            {
                _currentReconnectionStatus = ReconnectionStatus.Abandoned;
                
                var abandonResult = new ReconnectionResult(
                    new ReconnectionAttemptInfo(_reconnectionAttempts, "Max attempts reached", _reconnectionStrategy.Type, 0),
                    false,
                    "已达到最大重连尝试次数"
                );
                
                _reconnectionHistory.Add(abandonResult);
                OnReconnectionAbandoned?.Invoke(abandonResult);
                
                Debug.LogError("[NetworkReconnectionManager] 网络重连已放弃，达到最大尝试次数");
            }
        }
        
        /// <summary>
        /// API健康检查协程
        /// </summary>
        private IEnumerator ApiHealthCheckCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(_healthCheckInterval);
                
                if (_apiEndpoints.Count > 0)
                {
                    StartCoroutine(PerformApiHealthCheck());
                }
            }
        }
        
        /// <summary>
        /// 执行API健康检查
        /// </summary>
        private IEnumerator PerformApiHealthCheck()
        {
            foreach (var kvp in _apiEndpoints)
            {
                var endpoint = kvp.Key;
                var healthChecker = kvp.Value;
                
                var task = healthChecker();
                yield return new WaitUntil(() => task.IsCompleted);
                
                var isHealthy = task.Result;
                
                if (!isHealthy)
                {
                    var failureInfo = new ApiCallFailureInfo(endpoint, ApiCallStatus.Failed, "健康检查失败");
                    OnApiCallFailed?.Invoke(failureInfo);
                    
                    Debug.LogWarning($"[NetworkReconnectionManager] API端点健康检查失败: {endpoint}");
                    
                    // 如果启用了自动重连，触发重连
                    if (_isAutoReconnectionEnabled && _currentReconnectionStatus == ReconnectionStatus.Idle)
                    {
                        StartCoroutine(StartReconnectionCoroutine());
                    }
                }
            }
        }
        
        /// <summary>
        /// 启动重连协程的包装器
        /// </summary>
        private IEnumerator StartReconnectionCoroutine()
        {
            var task = StartReconnectionAsync();
            yield return new WaitUntil(() => task.IsCompleted);
        }
        
        /// <summary>
        /// 检查网络连接
        /// </summary>
        private async Task<bool> CheckNetworkConnection()
        {
            try
            {
                // 使用网络状态管理器检查连接
                if (_networkStatusManager != null)
                {
                    var status = await _networkStatusManager.CheckNetworkStatusAsync();
                    return status.IsOnline;
                }
                
                // 备用检查方法
                using (var request = UnityWebRequest.Get("https://www.google.com"))
                {
                    request.timeout = (int)_apiTimeoutSeconds;
                    
                    var operation = request.SendWebRequest();
                    
                    while (!operation.isDone)
                    {
                        await Task.Delay(50);
                    }
                    
                    return request.result == UnityWebRequest.Result.Success;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[NetworkReconnectionManager] 网络连接检查失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取网络质量信息
        /// </summary>
        private async Task<NetworkQualityInfo> GetNetworkQuality()
        {
            var quality = new NetworkQualityInfo();
            
            try
            {
                var startTime = DateTime.Now;
                
                using (var request = UnityWebRequest.Get("https://httpbin.org/get"))
                {
                    request.timeout = (int)_apiTimeoutSeconds;
                    
                    var operation = request.SendWebRequest();
                    
                    while (!operation.isDone)
                    {
                        await Task.Delay(10);
                    }
                    
                    var endTime = DateTime.Now;
                    quality.LatencyMs = (int)(endTime - startTime).TotalMilliseconds;
                    
                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        quality.DownloadSpeedKbps = request.downloadedBytes / (quality.LatencyMs / 1000.0f) / 1024.0f;
                        quality.QualityScore = CalculateQualityScore(quality.LatencyMs, quality.DownloadSpeedKbps);
                    }
                }
                
                quality.NetworkType = Application.internetReachability.ToString();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[NetworkReconnectionManager] 获取网络质量信息失败: {ex.Message}");
            }
            
            return quality;
        }
        
        /// <summary>
        /// 计算网络质量评分
        /// </summary>
        private int CalculateQualityScore(int latencyMs, float downloadSpeedKbps)
        {
            var latencyScore = Mathf.Clamp(100 - (latencyMs / 10), 0, 100);
            var speedScore = Mathf.Clamp(downloadSpeedKbps / 10, 0, 100);
            
            return (int)((latencyScore + speedScore) / 2);
        }
        
        /// <summary>
        /// 更新API调用统计信息
        /// </summary>
        private void UpdateApiCallStatistics(bool isSuccessful, long responseTimeMs, int retryCount, bool usedFallback)
        {
            _apiCallStatistics.TotalCalls++;
            
            if (isSuccessful)
            {
                _apiCallStatistics.SuccessfulCalls++;
            }
            else
            {
                _apiCallStatistics.FailedCalls++;
            }
            
            _apiCallStatistics.TotalRetries += retryCount;
            
            if (usedFallback)
            {
                _apiCallStatistics.FallbackUsages++;
            }
            
            // 更新平均响应时间
            var totalResponseTime = _apiCallStatistics.AverageResponseTimeMs * (_apiCallStatistics.TotalCalls - 1) + responseTimeMs;
            _apiCallStatistics.AverageResponseTimeMs = totalResponseTime / _apiCallStatistics.TotalCalls;
        }
        
        /// <summary>
        /// 获取API调用状态
        /// </summary>
        private ApiCallStatus GetApiCallStatus(Exception ex)
        {
            if (ex is TimeoutException)
                return ApiCallStatus.Timeout;
            else if (ex.Message.Contains("network") || ex.Message.Contains("connection"))
                return ApiCallStatus.NetworkError;
            else if (ex.Message.Contains("server") || ex.Message.Contains("500"))
                return ApiCallStatus.ServerError;
            else
                return ApiCallStatus.Failed;
        }
        
        /// <summary>
        /// 网络断开事件处理
        /// </summary>
        private void OnNetworkDisconnected(object sender, Models.NetworkStatusInfo e)
        {
            Debug.Log("[NetworkReconnectionManager] 检测到网络断开");
            
            if (_isAutoReconnectionEnabled && _currentReconnectionStatus == ReconnectionStatus.Idle)
            {
                StartCoroutine(StartReconnectionCoroutine());
            }
        }
        
        /// <summary>
        /// 网络连接事件处理
        /// </summary>
        private void OnNetworkConnected(object sender, Models.NetworkStatusInfo e)
        {
            Debug.Log("[NetworkReconnectionManager] 检测到网络连接");
            
            if (_currentReconnectionStatus == ReconnectionStatus.Reconnecting)
            {
                _currentReconnectionStatus = ReconnectionStatus.Connected;
            }
        }
    }
}