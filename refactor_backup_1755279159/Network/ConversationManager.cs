using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DigitalHuman.Core.Network.Models;
using DigitalHuman.Core.Database;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 对话上下文管理器，负责管理多个对话会话
    /// </summary>
    public class ConversationManager : ManagerBase
    {
        [Header("配置")]
        [SerializeField] private int _maxConversations = 10;
        [SerializeField] private int _maxMessagesPerConversation = 50;
        [SerializeField] private bool _enablePersistence = true;
        
        [Header("组件引用")]
        [SerializeField] private SQLiteDatabase _database;
        
        /// <summary>
        /// 活跃的对话上下文字典
        /// </summary>
        private readonly Dictionary<string, ConversationContext> _activeConversations = new Dictionary<string, ConversationContext>();
        
        /// <summary>
        /// 当前活跃的对话ID
        /// </summary>
        private string _currentConversationId;
        
        /// <summary>
        /// 对话创建事件
        /// </summary>
        public event Action<ConversationContext> OnConversationCreated;
        
        /// <summary>
        /// 对话更新事件
        /// </summary>
        public event Action<ConversationContext> OnConversationUpdated;
        
        /// <summary>
        /// 对话删除事件
        /// </summary>
        public event Action<string> OnConversationDeleted;
        
        /// <summary>
        /// 消息添加事件
        /// </summary>
        public event Action<string, LLMMessage> OnMessageAdded;
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        protected override void OnInitializeInternal()
        {
            // 获取数据库组件
            if (_database == null)
            {
                _database = FindObjectOfType<SQLiteDatabase>();
                if (_database == null)
                {
                    Debug.LogWarning("未找到SQLiteDatabase组件，对话持久化功能将被禁用");
                    _enablePersistence = false;
                }
            }
            
            // 初始化数据库表
            if (_enablePersistence && _database != null)
            {
                InitializeDatabase();
                LoadPersistedConversations();
            }
            
            Debug.Log("对话管理器初始化完成");
        }
        
        /// <summary>
        /// 清理管理器资源
        /// </summary>
        protected override void OnCleanupInternal()
        {
            // 保存所有对话
            if (_enablePersistence)
            {
                SaveAllConversations();
            }
            
            // 清理内存中的对话
            _activeConversations.Clear();
            _currentConversationId = null;
            
            Debug.Log("对话管理器清理完成");
        }
        
        /// <summary>
        /// 创建新对话
        /// </summary>
        /// <param name="systemPrompt">系统提示（可选）</param>
        /// <returns>对话上下文</returns>
        public ConversationContext CreateConversation(string systemPrompt = null)
        {
            var context = new ConversationContext
            {
                MaxMessages = _maxMessagesPerConversation
            };
            
            if (!string.IsNullOrEmpty(systemPrompt))
            {
                context.SetSystemPrompt(systemPrompt);
            }
            
            // 检查对话数量限制
            if (_activeConversations.Count >= _maxConversations)
            {
                // 删除最旧的对话
                var oldestConversation = _activeConversations.Values
                    .OrderBy(c => c.LastUpdatedAt)
                    .First();
                
                RemoveConversation(oldestConversation.ConversationId);
            }
            
            // 添加到活跃对话列表
            _activeConversations[context.ConversationId] = context;
            _currentConversationId = context.ConversationId;
            
            // 持久化保存
            if (_enablePersistence)
            {
                SaveConversation(context);
            }
            
            // 触发事件
            OnConversationCreated?.Invoke(context);
            
            Debug.Log($"创建新对话: {context.ConversationId}");
            return context;
        }
        
        /// <summary>
        /// 获取对话上下文
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <returns>对话上下文，如果不存在则返回null</returns>
        public ConversationContext GetConversation(string conversationId)
        {
            if (string.IsNullOrEmpty(conversationId))
            {
                return null;
            }
            
            if (_activeConversations.TryGetValue(conversationId, out var context))
            {
                return context;
            }
            
            // 尝试从数据库加载
            if (_enablePersistence)
            {
                context = LoadConversationFromDatabase(conversationId);
                if (context != null)
                {
                    _activeConversations[conversationId] = context;
                    return context;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 获取当前活跃的对话
        /// </summary>
        /// <returns>当前对话上下文</returns>
        public ConversationContext GetCurrentConversation()
        {
            if (string.IsNullOrEmpty(_currentConversationId))
            {
                return CreateConversation();
            }
            
            return GetConversation(_currentConversationId);
        }
        
        /// <summary>
        /// 设置当前活跃的对话
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <returns>是否设置成功</returns>
        public bool SetCurrentConversation(string conversationId)
        {
            var context = GetConversation(conversationId);
            if (context != null)
            {
                _currentConversationId = conversationId;
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取所有对话摘要
        /// </summary>
        /// <returns>对话摘要列表</returns>
        public List<ConversationSummary> GetAllConversationSummaries()
        {
            var summaries = new List<ConversationSummary>();
            
            // 添加活跃对话的摘要
            foreach (var context in _activeConversations.Values)
            {
                summaries.Add(context.GetSummary());
            }
            
            // 如果启用持久化，还需要从数据库获取其他对话的摘要
            if (_enablePersistence && _database != null)
            {
                var persistedSummaries = LoadConversationSummariesFromDatabase();
                
                // 去重并合并
                foreach (var summary in persistedSummaries)
                {
                    if (!summaries.Any(s => s.ConversationId == summary.ConversationId))
                    {
                        summaries.Add(summary);
                    }
                }
            }
            
            return summaries.OrderByDescending(s => s.LastUpdatedAt).ToList();
        }
        
        /// <summary>
        /// 添加消息到对话
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <param name="message">消息对象</param>
        /// <returns>是否添加成功</returns>
        public bool AddMessage(string conversationId, LLMMessage message)
        {
            var context = GetConversation(conversationId);
            if (context == null)
            {
                return false;
            }
            
            context.AddMessage(message);
            
            // 持久化保存
            if (_enablePersistence)
            {
                SaveConversation(context);
            }
            
            // 触发事件
            OnConversationUpdated?.Invoke(context);
            OnMessageAdded?.Invoke(conversationId, message);
            
            return true;
        }
        
        /// <summary>
        /// 添加用户消息
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <param name="content">消息内容</param>
        /// <returns>是否添加成功</returns>
        public bool AddUserMessage(string conversationId, string content)
        {
            var message = new LLMMessage { role = "user", content = content };
            return AddMessage(conversationId, message);
        }
        
        /// <summary>
        /// 添加助手消息
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <param name="content">消息内容</param>
        /// <returns>是否添加成功</returns>
        public bool AddAssistantMessage(string conversationId, string content)
        {
            var message = new LLMMessage { role = "assistant", content = content };
            return AddMessage(conversationId, message);
        }
        
        /// <summary>
        /// 清除对话历史
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <param name="keepSystemPrompt">是否保留系统提示</param>
        /// <returns>是否清除成功</returns>
        public bool ClearConversationHistory(string conversationId, bool keepSystemPrompt = true)
        {
            var context = GetConversation(conversationId);
            if (context == null)
            {
                return false;
            }
            
            context.ClearHistory(keepSystemPrompt);
            
            // 持久化保存
            if (_enablePersistence)
            {
                SaveConversation(context);
            }
            
            // 触发事件
            OnConversationUpdated?.Invoke(context);
            
            return true;
        }
        
        /// <summary>
        /// 删除对话
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveConversation(string conversationId)
        {
            if (string.IsNullOrEmpty(conversationId))
            {
                return false;
            }
            
            // 从内存中移除
            bool removed = _activeConversations.Remove(conversationId);
            
            // 从数据库中删除
            if (_enablePersistence)
            {
                DeleteConversationFromDatabase(conversationId);
            }
            
            // 如果删除的是当前对话，重置当前对话ID
            if (_currentConversationId == conversationId)
            {
                _currentConversationId = null;
            }
            
            // 触发事件
            if (removed)
            {
                OnConversationDeleted?.Invoke(conversationId);
            }
            
            return removed;
        }
        
        /// <summary>
        /// 初始化数据库表
        /// </summary>
        private void InitializeDatabase()
        {
            if (_database == null) return;
            
            // 创建对话表
            var createConversationTable = @"
                CREATE TABLE IF NOT EXISTS conversations (
                    id TEXT PRIMARY KEY,
                    system_prompt TEXT,
                    created_at INTEGER,
                    last_updated_at INTEGER,
                    max_messages INTEGER
                )";
            
            // 创建消息表
            var createMessageTable = @"
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id TEXT,
                    role TEXT,
                    content TEXT,
                    timestamp INTEGER,
                    FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
                )";
            
            _database.ExecuteNonQuery(createConversationTable);
            _database.ExecuteNonQuery(createMessageTable);
        }
        
        /// <summary>
        /// 加载持久化的对话
        /// </summary>
        private void LoadPersistedConversations()
        {
            if (_database == null) return;
            
            try
            {
                var query = "SELECT id, system_prompt, created_at, last_updated_at, max_messages FROM conversations ORDER BY last_updated_at DESC LIMIT ?";
                var results = _database.ExecuteQuery(query, _maxConversations);
                
                foreach (var row in results)
                {
                    var context = new ConversationContext
                    {
                        ConversationId = row["id"].ToString(),
                        SystemPrompt = row["system_prompt"].ToString(),
                        CreatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["created_at"])).DateTime,
                        LastUpdatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["last_updated_at"])).DateTime,
                        MaxMessages = Convert.ToInt32(row["max_messages"])
                    };
                    
                    // 加载消息
                    LoadMessagesForConversation(context);
                    
                    _activeConversations[context.ConversationId] = context;
                }
                
                Debug.Log($"从数据库加载了 {_activeConversations.Count} 个对话");
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载持久化对话失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 为对话加载消息
        /// </summary>
        /// <param name="context">对话上下文</param>
        private void LoadMessagesForConversation(ConversationContext context)
        {
            if (_database == null) return;
            
            try
            {
                var query = "SELECT role, content, timestamp FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC";
                var results = _database.ExecuteQuery(query, context.ConversationId);
                
                foreach (var row in results)
                {
                    var message = new LLMMessage
                    {
                        role = row["role"].ToString(),
                        content = row["content"].ToString(),
                        timestamp = Convert.ToInt64(row["timestamp"])
                    };
                    
                    context.Messages.Add(message);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载对话消息失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存对话到数据库
        /// </summary>
        /// <param name="context">对话上下文</param>
        private void SaveConversation(ConversationContext context)
        {
            if (_database == null || context == null) return;
            
            try
            {
                // 保存或更新对话记录
                var upsertConversation = @"
                    INSERT OR REPLACE INTO conversations (id, system_prompt, created_at, last_updated_at, max_messages)
                    VALUES (?, ?, ?, ?, ?)";
                
                _database.ExecuteNonQuery(upsertConversation,
                    context.ConversationId,
                    context.SystemPrompt ?? "",
                    ((DateTimeOffset)context.CreatedAt).ToUnixTimeSeconds(),
                    ((DateTimeOffset)context.LastUpdatedAt).ToUnixTimeSeconds(),
                    context.MaxMessages);
                
                // 删除旧消息
                _database.ExecuteNonQuery("DELETE FROM messages WHERE conversation_id = ?", context.ConversationId);
                
                // 保存消息
                foreach (var message in context.Messages)
                {
                    var insertMessage = "INSERT INTO messages (conversation_id, role, content, timestamp) VALUES (?, ?, ?, ?)";
                    _database.ExecuteNonQuery(insertMessage,
                        context.ConversationId,
                        message.role,
                        message.content,
                        message.timestamp);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存对话失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存所有对话
        /// </summary>
        private void SaveAllConversations()
        {
            foreach (var context in _activeConversations.Values)
            {
                SaveConversation(context);
            }
        }
        
        /// <summary>
        /// 从数据库加载对话
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        /// <returns>对话上下文</returns>
        private ConversationContext LoadConversationFromDatabase(string conversationId)
        {
            if (_database == null) return null;
            
            try
            {
                var query = "SELECT id, system_prompt, created_at, last_updated_at, max_messages FROM conversations WHERE id = ?";
                var results = _database.ExecuteQuery(query, conversationId);
                
                if (results.Count == 0) return null;
                
                var row = results[0];
                var context = new ConversationContext
                {
                    ConversationId = row["id"].ToString(),
                    SystemPrompt = row["system_prompt"].ToString(),
                    CreatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["created_at"])).DateTime,
                    LastUpdatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["last_updated_at"])).DateTime,
                    MaxMessages = Convert.ToInt32(row["max_messages"])
                };
                
                LoadMessagesForConversation(context);
                
                return context;
            }
            catch (Exception ex)
            {
                Debug.LogError($"从数据库加载对话失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 从数据库加载对话摘要
        /// </summary>
        /// <returns>对话摘要列表</returns>
        private List<ConversationSummary> LoadConversationSummariesFromDatabase()
        {
            var summaries = new List<ConversationSummary>();
            
            if (_database == null) return summaries;
            
            try
            {
                var query = @"
                    SELECT c.id, c.system_prompt, c.created_at, c.last_updated_at, COUNT(m.id) as message_count
                    FROM conversations c
                    LEFT JOIN messages m ON c.id = m.conversation_id
                    GROUP BY c.id
                    ORDER BY c.last_updated_at DESC";
                
                var results = _database.ExecuteQuery(query);
                
                foreach (var row in results)
                {
                    var summary = new ConversationSummary
                    {
                        ConversationId = row["id"].ToString(),
                        MessageCount = Convert.ToInt32(row["message_count"]),
                        CreatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["created_at"])).DateTime,
                        LastUpdatedAt = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(row["last_updated_at"])).DateTime,
                        HasSystemPrompt = !string.IsNullOrEmpty(row["system_prompt"].ToString())
                    };
                    
                    summaries.Add(summary);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载对话摘要失败: {ex.Message}");
            }
            
            return summaries;
        }
        
        /// <summary>
        /// 从数据库删除对话
        /// </summary>
        /// <param name="conversationId">对话ID</param>
        private void DeleteConversationFromDatabase(string conversationId)
        {
            if (_database == null) return;
            
            try
            {
                _database.ExecuteNonQuery("DELETE FROM conversations WHERE id = ?", conversationId);
                _database.ExecuteNonQuery("DELETE FROM messages WHERE conversation_id = ?", conversationId);
            }
            catch (Exception ex)
            {
                Debug.LogError($"删除对话失败: {ex.Message}");
            }
        }
    }
}