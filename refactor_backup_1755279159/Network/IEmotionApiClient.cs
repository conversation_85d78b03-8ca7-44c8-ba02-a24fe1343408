using System;
using System.Threading.Tasks;
using UnityEngine;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 情感分析API客户端接口
    /// 负责与外部情感分析服务进行通信
    /// </summary>
    public interface IEmotionApiClient
    {
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 当前配置
        /// </summary>
        EmotionAnalysisConfig Configuration { get; }
        
        /// <summary>
        /// 情感分析结果事件
        /// </summary>
        event Action<EmotionData> OnEmotionAnalyzed;
        
        /// <summary>
        /// 分析错误事件
        /// </summary>
        event Action<string> OnAnalysisError;
        
        /// <summary>
        /// 初始化客户端
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否初始化成功</returns>
        bool Initialize(EmotionAnalysisConfig config);
        
        /// <summary>
        /// 分析图像中的情感
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="format">图像格式</param>
        /// <returns>情感分析结果</returns>
        Task<EmotionData> AnalyzeEmotionAsync(byte[] imageData, string format = "jpg");
        
        /// <summary>
        /// 分析纹理中的情感
        /// </summary>
        /// <param name="texture">纹理对象</param>
        /// <returns>情感分析结果</returns>
        Task<EmotionData> AnalyzeEmotionAsync(Texture2D texture);
        
        /// <summary>
        /// 批量分析情感
        /// </summary>
        /// <param name="imageDataList">图像数据列表</param>
        /// <returns>情感分析结果列表</returns>
        Task<EmotionData[]> AnalyzeEmotionBatchAsync(byte[][] imageDataList);
        
        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        Task<bool> TestConnectionAsync();
        
        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="config">新配置</param>
        void UpdateConfiguration(EmotionAnalysisConfig config);
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void Cleanup();
    }
}