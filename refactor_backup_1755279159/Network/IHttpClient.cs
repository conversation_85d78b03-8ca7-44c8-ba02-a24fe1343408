using System;
using System.Threading.Tasks;
using DigitalHuman.Core.Network.Models;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// HTTP客户端接口，定义网络请求的基本操作
    /// </summary>
    public interface IHttpClient
    {
        /// <summary>
        /// 发送HTTP请求
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>响应结果</returns>
        Task<ApiResponse<string>> SendRequestAsync(ApiRequest request);
        
        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        Task<ApiResponse<string>> GetAsync(string url, System.Collections.Generic.Dictionary<string, string> headers = null);
        
        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="body">请求体</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        Task<ApiResponse<string>> PostAsync(string url, string body, System.Collections.Generic.Dictionary<string, string> headers = null);
        
        /// <summary>
        /// 发送PUT请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="body">请求体</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        Task<ApiResponse<string>> PutAsync(string url, string body, System.Collections.Generic.Dictionary<string, string> headers = null);
        
        /// <summary>
        /// 发送DELETE请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        Task<ApiResponse<string>> DeleteAsync(string url, System.Collections.Generic.Dictionary<string, string> headers = null);
        
        /// <summary>
        /// 设置默认超时时间
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        void SetDefaultTimeout(int timeoutSeconds);
        
        /// <summary>
        /// 设置默认重试配置
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryIntervalMs">重试间隔（毫秒）</param>
        void SetDefaultRetry(int retryCount, int retryIntervalMs);
        
        /// <summary>
        /// 添加默认请求头
        /// </summary>
        /// <param name="key">头名称</param>
        /// <param name="value">头值</param>
        void AddDefaultHeader(string key, string value);
        
        /// <summary>
        /// 移除默认请求头
        /// </summary>
        /// <param name="key">头名称</param>
        void RemoveDefaultHeader(string key);
        
        /// <summary>
        /// 清除所有默认请求头
        /// </summary>
        void ClearDefaultHeaders();
        
        /// <summary>
        /// 设置SSL验证
        /// </summary>
        /// <param name="enableVerification">是否启用SSL验证</param>
        void SetSSLVerification(bool enableVerification);
        
        /// <summary>
        /// 设置自定义证书路径
        /// </summary>
        /// <param name="certificatePath">证书路径</param>
        void SetCustomCertificate(string certificatePath);
        
        /// <summary>
        /// 请求开始事件
        /// </summary>
        event Action<ApiRequest> OnRequestStarted;
        
        /// <summary>
        /// 请求完成事件
        /// </summary>
        event Action<ApiRequest, ApiResponse> OnRequestCompleted;
        
        /// <summary>
        /// 请求失败事件
        /// </summary>
        event Action<ApiRequest, ApiException> OnRequestFailed;
    }
}