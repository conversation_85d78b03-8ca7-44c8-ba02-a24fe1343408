using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// 网络重连管理器接口
    /// </summary>
    public interface INetworkReconnectionManager : IManager
    {
        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        bool IsAutoReconnectionEnabled { get; set; }
        
        /// <summary>
        /// 当前重连状态
        /// </summary>
        ReconnectionStatus CurrentReconnectionStatus { get; }
        
        /// <summary>
        /// 重连尝试次数
        /// </summary>
        int ReconnectionAttempts { get; }
        
        /// <summary>
        /// 最大重连尝试次数
        /// </summary>
        int MaxReconnectionAttempts { get; set; }
        
        /// <summary>
        /// 重连间隔（秒）
        /// </summary>
        float ReconnectionInterval { get; set; }
        
        /// <summary>
        /// 网络重连开始事件
        /// </summary>
        event Action<ReconnectionAttemptInfo> OnReconnectionStarted;
        
        /// <summary>
        /// 网络重连成功事件
        /// </summary>
        event Action<ReconnectionResult> OnReconnectionSucceeded;
        
        /// <summary>
        /// 网络重连失败事件
        /// </summary>
        event Action<ReconnectionResult> OnReconnectionFailed;
        
        /// <summary>
        /// 网络重连放弃事件
        /// </summary>
        event Action<ReconnectionResult> OnReconnectionAbandoned;
        
        /// <summary>
        /// API调用失败事件
        /// </summary>
        event Action<ApiCallFailureInfo> OnApiCallFailed;
        
        /// <summary>
        /// API调用重试事件
        /// </summary>
        event Action<ApiRetryInfo> OnApiCallRetried;
        
        /// <summary>
        /// 开始网络重连
        /// </summary>
        /// <returns>重连任务</returns>
        Task<bool> StartReconnectionAsync();
        
        /// <summary>
        /// 停止网络重连
        /// </summary>
        void StopReconnection();
        
        /// <summary>
        /// 手动触发重连
        /// </summary>
        /// <returns>重连是否成功</returns>
        Task<bool> TriggerManualReconnectionAsync();
        
        /// <summary>
        /// 注册API端点监控
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <param name="healthChecker">健康检查函数</param>
        void RegisterApiEndpoint(string endpoint, Func<Task<bool>> healthChecker);
        
        /// <summary>
        /// 取消注册API端点监控
        /// </summary>
        /// <param name="endpoint">API端点</param>
        void UnregisterApiEndpoint(string endpoint);
        
        /// <summary>
        /// 执行带重试的API调用
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="apiCall">API调用函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelay">重试延迟（秒）</param>
        /// <returns>API调用结果</returns>
        Task<ApiCallResult<T>> ExecuteWithRetryAsync<T>(Func<Task<T>> apiCall, int maxRetries = 3, float retryDelay = 1.0f);
        
        /// <summary>
        /// 执行带降级的API调用
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="primaryCall">主要API调用</param>
        /// <param name="fallbackCall">降级API调用</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns>API调用结果</returns>
        Task<ApiCallResult<T>> ExecuteWithFallbackAsync<T>(Func<Task<T>> primaryCall, Func<Task<T>> fallbackCall, int maxRetries = 3);
        
        /// <summary>
        /// 获取重连历史记录
        /// </summary>
        /// <returns>重连历史列表</returns>
        List<ReconnectionResult> GetReconnectionHistory();
        
        /// <summary>
        /// 获取API调用统计信息
        /// </summary>
        /// <returns>API调用统计</returns>
        ApiCallStatistics GetApiCallStatistics();
        
        /// <summary>
        /// 重置重连统计
        /// </summary>
        void ResetReconnectionStatistics();
        
        /// <summary>
        /// 设置重连策略
        /// </summary>
        /// <param name="strategy">重连策略</param>
        void SetReconnectionStrategy(ReconnectionStrategy strategy);
        
        /// <summary>
        /// 获取当前重连策略
        /// </summary>
        /// <returns>重连策略</returns>
        ReconnectionStrategy GetReconnectionStrategy();
    }
}