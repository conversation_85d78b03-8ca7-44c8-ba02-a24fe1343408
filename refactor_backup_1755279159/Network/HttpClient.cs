using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using DigitalHuman.Core.Network.Models;

namespace DigitalHuman.Core.Network
{
    /// <summary>
    /// HTTP客户端实现，基于Unity WebRequest
    /// </summary>
    public class HttpClient : MonoBehaviour, IHttpClient
    {
        [Header("默认配置")]
        [SerializeField] private int _defaultTimeoutSeconds = 30;
        [SerializeField] private int _defaultRetryCount = 3;
        [SerializeField] private int _defaultRetryIntervalMs = 1000;
        [SerializeField] private bool _enableSSLVerification = true;
        [SerializeField] private string _customCertificatePath = "";
        
        /// <summary>
        /// 默认请求头
        /// </summary>
        private readonly Dictionary<string, string> _defaultHeaders = new Dictionary<string, string>();
        
        /// <summary>
        /// 请求缓存
        /// </summary>
        private readonly Dictionary<string, CacheEntry> _requestCache = new Dictionary<string, CacheEntry>();
        
        /// <summary>
        /// 请求开始事件
        /// </summary>
        public event Action<ApiRequest> OnRequestStarted;
        
        /// <summary>
        /// 请求完成事件
        /// </summary>
        public event Action<ApiRequest, ApiResponse> OnRequestCompleted;
        
        /// <summary>
        /// 请求失败事件
        /// </summary>
        public event Action<ApiRequest, ApiException> OnRequestFailed;
        
        /// <summary>
        /// 初始化HTTP客户端
        /// </summary>
        private void Awake()
        {
            // 设置默认请求头
            _defaultHeaders["User-Agent"] = $"DigitalHuman/{Application.version}";
            _defaultHeaders["Accept"] = "application/json";
            _defaultHeaders["Content-Type"] = "application/json";
        }
        
        /// <summary>
        /// 发送HTTP请求
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>响应结果</returns>
        public async Task<ApiResponse<string>> SendRequestAsync(ApiRequest request)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }
            
            // 检查缓存
            if (request.EnableCache && request.Method.ToUpper() == "GET")
            {
                var cachedResponse = GetCachedResponse(request.Url);
                if (cachedResponse != null)
                {
                    UnityEngine.Debug.Log($"返回缓存响应: {request.Url}");
                    return cachedResponse;
                }
            }
            
            // 触发请求开始事件
            OnRequestStarted?.Invoke(request);
            
            var stopwatch = Stopwatch.StartNew();
            ApiResponse<string> response = null;
            ApiException lastException = null;
            
            // 重试逻辑
            for (int attempt = 0; attempt <= request.RetryCount; attempt++)
            {
                try
                {
                    response = await SendSingleRequestAsync(request);
                    if (response.IsSuccess)
                    {
                        break;
                    }
                    
                    // 如果不是最后一次尝试，等待重试间隔
                    if (attempt < request.RetryCount)
                    {
                        await Task.Delay(request.RetryIntervalMs);
                        UnityEngine.Debug.LogWarning($"请求失败，第 {attempt + 1} 次重试: {request.Url}");
                    }
                }
                catch (ApiException ex)
                {
                    lastException = ex;
                    
                    // 对于某些错误类型，不进行重试
                    if (ex.ErrorType == ApiErrorType.AuthenticationFailed || 
                        ex.ErrorType == ApiErrorType.Forbidden ||
                        ex.ErrorType == ApiErrorType.BadRequest)
                    {
                        break;
                    }
                    
                    // 如果不是最后一次尝试，等待重试间隔
                    if (attempt < request.RetryCount)
                    {
                        await Task.Delay(request.RetryIntervalMs);
                        UnityEngine.Debug.LogWarning($"请求异常，第 {attempt + 1} 次重试: {ex.Message}");
                    }
                }
            }
            
            stopwatch.Stop();
            
            if (response != null)
            {
                response.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                
                // 缓存成功的GET请求响应
                if (response.IsSuccess && request.EnableCache && request.Method.ToUpper() == "GET")
                {
                    CacheResponse(request.Url, response, request.CacheExpirationMinutes);
                }
                
                // 触发请求完成事件
                OnRequestCompleted?.Invoke(request, response);
                
                return response;
            }
            
            // 所有重试都失败了
            if (lastException != null)
            {
                OnRequestFailed?.Invoke(request, lastException);
                throw lastException;
            }
            
            // 创建通用失败响应
            var failureResponse = ApiResponse<string>.Failure(0, "请求失败，所有重试都已用尽");
            failureResponse.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            OnRequestCompleted?.Invoke(request, failureResponse);
            
            return failureResponse;
        }
        
        /// <summary>
        /// 发送单次HTTP请求
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>响应结果</returns>
        private async Task<ApiResponse<string>> SendSingleRequestAsync(ApiRequest request)
        {
            UnityWebRequest webRequest = null;
            
            try
            {
                // 创建WebRequest
                webRequest = CreateWebRequest(request);
                
                // 发送请求
                var operation = webRequest.SendWebRequest();
                
                // 等待请求完成或超时
                var timeoutTask = Task.Delay(request.TimeoutSeconds * 1000);
                var completedTask = await Task.WhenAny(
                    WaitForWebRequest(operation),
                    timeoutTask
                );
                
                if (completedTask == timeoutTask)
                {
                    webRequest.Abort();
                    throw new ApiException(ApiErrorType.Timeout, 
                        $"请求超时 ({request.TimeoutSeconds}秒): {request.Url}");
                }
                
                // 处理响应
                return ProcessWebResponse(webRequest);
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new ApiException(ApiErrorType.NetworkError, 
                    $"网络请求异常: {ex.Message}", 0, null, ex);
            }
            finally
            {
                webRequest?.Dispose();
            }
        }
        
        /// <summary>
        /// 创建UnityWebRequest对象
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>UnityWebRequest对象</returns>
        private UnityWebRequest CreateWebRequest(ApiRequest request)
        {
            UnityWebRequest webRequest;
            
            // 根据HTTP方法创建请求
            switch (request.Method.ToUpper())
            {
                case "GET":
                    webRequest = UnityWebRequest.Get(request.Url);
                    break;
                case "POST":
                    webRequest = new UnityWebRequest(request.Url, "POST");
                    if (!string.IsNullOrEmpty(request.Body))
                    {
                        byte[] bodyData = Encoding.UTF8.GetBytes(request.Body);
                        webRequest.uploadHandler = new UploadHandlerRaw(bodyData);
                    }
                    webRequest.downloadHandler = new DownloadHandlerBuffer();
                    break;
                case "PUT":
                    webRequest = UnityWebRequest.Put(request.Url, request.Body ?? "");
                    break;
                case "DELETE":
                    webRequest = UnityWebRequest.Delete(request.Url);
                    break;
                default:
                    throw new ArgumentException($"不支持的HTTP方法: {request.Method}");
            }
            
            // 设置请求头
            foreach (var header in _defaultHeaders)
            {
                webRequest.SetRequestHeader(header.Key, header.Value);
            }
            
            foreach (var header in request.Headers)
            {
                webRequest.SetRequestHeader(header.Key, header.Value);
            }
            
            // 设置SSL验证
            if (!_enableSSLVerification)
            {
                webRequest.certificateHandler = new AcceptAllCertificatesHandler();
            }
            
            return webRequest;
        }
        
        /// <summary>
        /// 等待WebRequest完成
        /// </summary>
        /// <param name="operation">异步操作</param>
        /// <returns>等待任务</returns>
        private Task WaitForWebRequest(UnityWebRequestAsyncOperation operation)
        {
            var tcs = new TaskCompletionSource<bool>();
            
            operation.completed += _ => tcs.SetResult(true);
            
            return tcs.Task;
        }
        
        /// <summary>
        /// 处理Web响应
        /// </summary>
        /// <param name="webRequest">Web请求对象</param>
        /// <returns>API响应</returns>
        private ApiResponse<string> ProcessWebResponse(UnityWebRequest webRequest)
        {
            var response = new ApiResponse<string>
            {
                StatusCode = (int)webRequest.responseCode,
                ResponseTime = DateTime.Now
            };
            
            // 复制响应头
            if (webRequest.GetResponseHeaders() != null)
            {
                foreach (var header in webRequest.GetResponseHeaders())
                {
                    response.Headers[header.Key] = header.Value;
                }
            }
            
            // 检查是否有网络错误
            if (webRequest.result == UnityWebRequest.Result.ConnectionError ||
                webRequest.result == UnityWebRequest.Result.DataProcessingError)
            {
                throw new ApiException(ApiErrorType.NetworkError, 
                    $"网络连接错误: {webRequest.error}", 
                    (int)webRequest.responseCode, 
                    webRequest.downloadHandler?.text);
            }
            
            // 检查HTTP错误
            if (webRequest.result == UnityWebRequest.Result.ProtocolError)
            {
                var statusCode = (int)webRequest.responseCode;
                var errorMessage = $"HTTP错误 {statusCode}: {webRequest.error}";
                var responseContent = webRequest.downloadHandler?.text;
                
                throw ApiException.FromStatusCode(statusCode, errorMessage, responseContent);
            }
            
            // 成功响应
            response.IsSuccess = true;
            response.Data = webRequest.downloadHandler?.text ?? "";
            
            return response;
        }
        
        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        public async Task<ApiResponse<string>> GetAsync(string url, Dictionary<string, string> headers = null)
        {
            var request = ApiRequest.Get(url)
                .SetTimeout(_defaultTimeoutSeconds)
                .SetRetry(_defaultRetryCount, _defaultRetryIntervalMs);
            
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    request.AddHeader(header.Key, header.Value);
                }
            }
            
            return await SendRequestAsync(request);
        }
        
        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="body">请求体</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        public async Task<ApiResponse<string>> PostAsync(string url, string body, Dictionary<string, string> headers = null)
        {
            var request = ApiRequest.Post(url, body)
                .SetTimeout(_defaultTimeoutSeconds)
                .SetRetry(_defaultRetryCount, _defaultRetryIntervalMs);
            
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    request.AddHeader(header.Key, header.Value);
                }
            }
            
            return await SendRequestAsync(request);
        }
        
        /// <summary>
        /// 发送PUT请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="body">请求体</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        public async Task<ApiResponse<string>> PutAsync(string url, string body, Dictionary<string, string> headers = null)
        {
            var request = new ApiRequest
            {
                Url = url,
                Method = "PUT",
                Body = body,
                TimeoutSeconds = _defaultTimeoutSeconds,
                RetryCount = _defaultRetryCount,
                RetryIntervalMs = _defaultRetryIntervalMs
            };
            
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    request.AddHeader(header.Key, header.Value);
                }
            }
            
            return await SendRequestAsync(request);
        }
        
        /// <summary>
        /// 发送DELETE请求
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应结果</returns>
        public async Task<ApiResponse<string>> DeleteAsync(string url, Dictionary<string, string> headers = null)
        {
            var request = new ApiRequest
            {
                Url = url,
                Method = "DELETE",
                TimeoutSeconds = _defaultTimeoutSeconds,
                RetryCount = _defaultRetryCount,
                RetryIntervalMs = _defaultRetryIntervalMs
            };
            
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    request.AddHeader(header.Key, header.Value);
                }
            }
            
            return await SendRequestAsync(request);
        }
        
        /// <summary>
        /// 设置默认超时时间
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        public void SetDefaultTimeout(int timeoutSeconds)
        {
            _defaultTimeoutSeconds = Mathf.Max(1, timeoutSeconds);
        }
        
        /// <summary>
        /// 设置默认重试配置
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryIntervalMs">重试间隔（毫秒）</param>
        public void SetDefaultRetry(int retryCount, int retryIntervalMs)
        {
            _defaultRetryCount = Mathf.Max(0, retryCount);
            _defaultRetryIntervalMs = Mathf.Max(0, retryIntervalMs);
        }
        
        /// <summary>
        /// 添加默认请求头
        /// </summary>
        /// <param name="key">头名称</param>
        /// <param name="value">头值</param>
        public void AddDefaultHeader(string key, string value)
        {
            _defaultHeaders[key] = value;
        }
        
        /// <summary>
        /// 移除默认请求头
        /// </summary>
        /// <param name="key">头名称</param>
        public void RemoveDefaultHeader(string key)
        {
            _defaultHeaders.Remove(key);
        }
        
        /// <summary>
        /// 清除所有默认请求头
        /// </summary>
        public void ClearDefaultHeaders()
        {
            _defaultHeaders.Clear();
        }
        
        /// <summary>
        /// 设置SSL验证
        /// </summary>
        /// <param name="enableVerification">是否启用SSL验证</param>
        public void SetSSLVerification(bool enableVerification)
        {
            _enableSSLVerification = enableVerification;
        }
        
        /// <summary>
        /// 设置自定义证书路径
        /// </summary>
        /// <param name="certificatePath">证书路径</param>
        public void SetCustomCertificate(string certificatePath)
        {
            _customCertificatePath = certificatePath;
        }
        
        /// <summary>
        /// 获取缓存响应
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <returns>缓存的响应，如果不存在或已过期则返回null</returns>
        private ApiResponse<string> GetCachedResponse(string url)
        {
            if (_requestCache.TryGetValue(url, out var cacheEntry))
            {
                if (DateTime.Now < cacheEntry.ExpirationTime)
                {
                    return cacheEntry.Response;
                }
                else
                {
                    _requestCache.Remove(url);
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 缓存响应
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <param name="response">响应对象</param>
        /// <param name="expirationMinutes">过期时间（分钟）</param>
        private void CacheResponse(string url, ApiResponse<string> response, int expirationMinutes)
        {
            var cacheEntry = new CacheEntry
            {
                Response = response,
                ExpirationTime = DateTime.Now.AddMinutes(expirationMinutes)
            };
            
            _requestCache[url] = cacheEntry;
        }
        
        /// <summary>
        /// 清理过期缓存
        /// </summary>
        private void Update()
        {
            // 每帧检查并清理过期缓存（可以优化为定时器）
            var expiredKeys = new List<string>();
            var now = DateTime.Now;
            
            foreach (var kvp in _requestCache)
            {
                if (now >= kvp.Value.ExpirationTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }
            
            foreach (var key in expiredKeys)
            {
                _requestCache.Remove(key);
            }
        }
        
        /// <summary>
        /// 缓存条目
        /// </summary>
        private class CacheEntry
        {
            public ApiResponse<string> Response { get; set; }
            public DateTime ExpirationTime { get; set; }
        }
        
        /// <summary>
        /// 接受所有证书的处理器（用于开发环境）
        /// </summary>
        private class AcceptAllCertificatesHandler : CertificateHandler
        {
            protected override bool ValidateCertificate(byte[] certificateData)
            {
                return true;
            }
        }
    }
}