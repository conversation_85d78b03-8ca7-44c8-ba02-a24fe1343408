using System;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Logging.Formatters;

namespace DigitalHuman.Core.Logging.Writers
{
    /// <summary>
    /// 控制台日志写入器，支持彩色输出和Unity Console集成
    /// </summary>
    public class ConsoleLogWriter : ILogWriter
    {
        private ILogFormatter _formatter;
        private bool _useUnityConsole;
        private bool _enableColors;
        private bool _disposed = false;
        
        /// <summary>
        /// 写入器名称
        /// </summary>
        public string Name { get; private set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        public LogLevel MinLevel { get; set; } = LogLevel.Debug;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">写入器名称</param>
        /// <param name="formatter">日志格式化器</param>
        /// <param name="useUnityConsole">是否使用Unity控制台</param>
        /// <param name="enableColors">是否启用彩色输出</param>
        public ConsoleLogWriter(
            string name = "ConsoleWriter",
            ILogFormatter formatter = null,
            bool useUnityConsole = true,
            bool enableColors = true)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            _formatter = formatter ?? LogFormatterFactory.CreateFormatter("plaintext_compact");
            _useUnityConsole = useUnityConsole;
            _enableColors = enableColors;
            
            Debug.Log($"[ConsoleLogWriter] 初始化完成: {Name}, Unity控制台: {_useUnityConsole}, 彩色输出: {_enableColors}");
        }
        
        /// <summary>
        /// 异步写入日志条目
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>写入任务</returns>
        public async Task WriteAsync(LogEntry entry)
        {
            if (_disposed || !IsEnabled || entry == null)
            {
                return;
            }
            
            if (!entry.Level.IsEnabled(MinLevel))
            {
                return;
            }
            
            try
            {
                await Task.Run(() => WriteToConsole(entry));
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConsoleLogWriter] 写入控制台失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 刷新缓冲区（控制台写入器无需缓冲）
        /// </summary>
        public void Flush()
        {
            // 控制台写入器无需刷新操作
        }
        
        /// <summary>
        /// 配置写入器
        /// </summary>
        /// <param name="config">配置对象</param>
        public void Configure(LogWriterConfiguration config)
        {
            if (config == null)
            {
                return;
            }
            
            try
            {
                IsEnabled = config.IsEnabled;
                MinLevel = config.MinLevel;
                
                // 更新格式化器
                string formatterType = config.GetConfigValue<string>("formatter", "plaintext_compact");
                _formatter = LogFormatterFactory.CreateFormatter(formatterType);
                
                // 更新Unity控制台设置
                _useUnityConsole = config.GetConfigValue<bool>("useUnityConsole", _useUnityConsole);
                
                // 更新彩色输出设置
                _enableColors = config.GetConfigValue<bool>("enableColors", _enableColors);
                
                Debug.Log($"[ConsoleLogWriter] 配置更新完成: {Name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConsoleLogWriter] 配置更新失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }
            
            _disposed = true;
            Debug.Log($"[ConsoleLogWriter] 资源释放完成: {Name}");
        }
        
        /// <summary>
        /// 写入到控制台
        /// </summary>
        /// <param name="entry">日志条目</param>
        private void WriteToConsole(LogEntry entry)
        {
            try
            {
                string formattedMessage = _formatter.Format(entry);
                
                if (_useUnityConsole)
                {
                    WriteToUnityConsole(entry, formattedMessage);
                }
                else
                {
                    WriteToSystemConsole(entry, formattedMessage);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConsoleLogWriter] 格式化或写入失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 写入到Unity控制台
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <param name="formattedMessage">格式化后的消息</param>
        private void WriteToUnityConsole(LogEntry entry, string formattedMessage)
        {
            // 根据日志级别选择Unity的日志方法
            switch (entry.Level)
            {
                case LogLevel.Trace:
                case LogLevel.Debug:
                    Debug.Log(formattedMessage);
                    break;
                    
                case LogLevel.Info:
                    Debug.Log(formattedMessage);
                    break;
                    
                case LogLevel.Warn:
                    Debug.LogWarning(formattedMessage);
                    break;
                    
                case LogLevel.Error:
                case LogLevel.Fatal:
                    if (entry.Exception != null)
                    {
                        Debug.LogError(formattedMessage);
                        Debug.LogException(entry.Exception);
                    }
                    else
                    {
                        Debug.LogError(formattedMessage);
                    }
                    break;
            }
        }
        
        /// <summary>
        /// 写入到系统控制台
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <param name="formattedMessage">格式化后的消息</param>
        private void WriteToSystemConsole(LogEntry entry, string formattedMessage)
        {
            if (_enableColors)
            {
                WriteColoredMessage(entry.Level, formattedMessage);
            }
            else
            {
                Console.WriteLine(formattedMessage);
            }
            
            // 如果有异常，单独输出异常信息
            if (entry.Exception != null)
            {
                if (_enableColors)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine(entry.Exception.ToString());
                    Console.ResetColor();
                }
                else
                {
                    Console.WriteLine(entry.Exception.ToString());
                }
            }
        }
        
        /// <summary>
        /// 写入彩色消息到系统控制台
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息内容</param>
        private void WriteColoredMessage(LogLevel level, string message)
        {
            ConsoleColor originalColor = Console.ForegroundColor;
            
            try
            {
                Console.ForegroundColor = GetConsoleColor(level);
                Console.WriteLine(message);
            }
            finally
            {
                Console.ForegroundColor = originalColor;
            }
        }
        
        /// <summary>
        /// 获取日志级别对应的控制台颜色
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>控制台颜色</returns>
        private ConsoleColor GetConsoleColor(LogLevel level)
        {
            return level switch
            {
                LogLevel.Trace => ConsoleColor.DarkGray,
                LogLevel.Debug => ConsoleColor.Gray,
                LogLevel.Info => ConsoleColor.White,
                LogLevel.Warn => ConsoleColor.Yellow,
                LogLevel.Error => ConsoleColor.Red,
                LogLevel.Fatal => ConsoleColor.DarkRed,
                _ => ConsoleColor.White
            };
        }
        
        /// <summary>
        /// 获取Unity日志类型对应的颜色标签
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>颜色标签</returns>
        private string GetUnityColorTag(LogLevel level)
        {
            return level switch
            {
                LogLevel.Trace => "<color=gray>",
                LogLevel.Debug => "<color=lightblue>",
                LogLevel.Info => "<color=white>",
                LogLevel.Warn => "<color=yellow>",
                LogLevel.Error => "<color=red>",
                LogLevel.Fatal => "<color=darkred>",
                _ => "<color=white>"
            };
        }
        
        /// <summary>
        /// 为Unity控制台添加颜色标签
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息内容</param>
        /// <returns>带颜色标签的消息</returns>
        private string AddUnityColorTags(LogLevel level, string message)
        {
            if (!_enableColors)
            {
                return message;
            }
            
            string colorTag = GetUnityColorTag(level);
            return $"{colorTag}{message}</color>";
        }
    }
}