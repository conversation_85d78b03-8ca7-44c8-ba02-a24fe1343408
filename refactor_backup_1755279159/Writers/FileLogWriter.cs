using System;
using System.IO;
using System.Threading.Tasks;
using System.Threading.Channels;
using System.Threading;
using System.Collections.Generic;
using UnityEngine;
using DigitalHuman.Core.Logging.Formatters;

namespace DigitalHuman.Core.Logging.Writers
{
    /// <summary>
    /// 文件日志写入器，支持异步写入和缓冲机制
    /// </summary>
    public class FileLogWriter : ILogWriter
    {
        private readonly Channel<LogEntry> _logChannel;
        private readonly ChannelWriter<LogEntry> _writer;
        private readonly ChannelReader<LogEntry> _reader;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Task _processingTask;
        private readonly object _fileLock = new object();
        
        private string _filePath;
        private ILogFormatter _formatter;
        private StreamWriter _fileWriter;
        private int _bufferSize;
        private TimeSpan _flushInterval;
        private long _maxFileSize;
        private bool _disposed = false;
        
        /// <summary>
        /// 写入器名称
        /// </summary>
        public string Name { get; private set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 最小日志级别
        /// </summary>
        public LogLevel MinLevel { get; set; } = LogLevel.Info;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">写入器名称</param>
        /// <param name="filePath">日志文件路径</param>
        /// <param name="formatter">日志格式化器</param>
        /// <param name="bufferSize">缓冲区大小</param>
        /// <param name="flushInterval">刷新间隔</param>
        /// <param name="maxFileSize">最大文件大小</param>
        public FileLogWriter(
            string name,
            string filePath,
            ILogFormatter formatter = null,
            int bufferSize = 1000,
            TimeSpan? flushInterval = null,
            long maxFileSize = 10 * 1024 * 1024) // 默认10MB
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            _filePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
            _formatter = formatter ?? LogFormatterFactory.CreateFormatter("json");
            _bufferSize = bufferSize;
            _flushInterval = flushInterval ?? TimeSpan.FromSeconds(5);
            _maxFileSize = maxFileSize;
            
            // 创建异步处理通道
            var options = new BoundedChannelOptions(_bufferSize)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = true,
                SingleWriter = false
            };
            
            _logChannel = Channel.CreateBounded<LogEntry>(options);
            _writer = _logChannel.Writer;
            _reader = _logChannel.Reader;
            
            _cancellationTokenSource = new CancellationTokenSource();
            
            // 初始化文件写入器
            InitializeFileWriter();
            
            // 启动后台处理任务
            _processingTask = Task.Run(ProcessLogEntriesAsync);
            
            Debug.Log($"[FileLogWriter] 初始化完成: {Name}, 文件路径: {_filePath}");
        }
        
        /// <summary>
        /// 异步写入日志条目
        /// </summary>
        /// <param name="entry">日志条目</param>
        /// <returns>写入任务</returns>
        public async Task WriteAsync(LogEntry entry)
        {
            if (_disposed || !IsEnabled || entry == null)
            {
                return;
            }
            
            if (!entry.Level.IsEnabled(MinLevel))
            {
                return;
            }
            
            try
            {
                await _writer.WriteAsync(entry, _cancellationTokenSource.Token);
            }
            catch (InvalidOperationException)
            {
                // 通道已关闭
            }
            catch (OperationCanceledException)
            {
                // 操作已取消
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 写入日志条目到通道失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 刷新缓冲区
        /// </summary>
        public void Flush()
        {
            lock (_fileLock)
            {
                try
                {
                    _fileWriter?.Flush();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[FileLogWriter] 刷新文件缓冲区失败: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 配置写入器
        /// </summary>
        /// <param name="config">配置对象</param>
        public void Configure(LogWriterConfiguration config)
        {
            if (config == null)
            {
                return;
            }
            
            try
            {
                IsEnabled = config.IsEnabled;
                MinLevel = config.MinLevel;
                
                // 更新文件路径
                string newFilePath = config.GetConfigValue<string>("filePath", _filePath);
                if (newFilePath != _filePath)
                {
                    _filePath = newFilePath;
                    ReinitializeFileWriter();
                }
                
                // 更新格式化器
                string formatterType = config.GetConfigValue<string>("formatter", "json");
                _formatter = LogFormatterFactory.CreateFormatter(formatterType);
                
                // 更新缓冲区大小
                _bufferSize = config.GetConfigValue<int>("bufferSize", _bufferSize);
                
                // 更新刷新间隔
                string flushIntervalStr = config.GetConfigValue<string>("flushInterval", "5s");
                _flushInterval = ParseTimeSpan(flushIntervalStr);
                
                // 更新最大文件大小
                string maxFileSizeStr = config.GetConfigValue<string>("maxFileSize", "10MB");
                _maxFileSize = ParseFileSize(maxFileSizeStr);
                
                Debug.Log($"[FileLogWriter] 配置更新完成: {Name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 配置更新失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }
            
            _disposed = true;
            
            try
            {
                // 停止接收新的日志条目
                _writer.Complete();
                
                // 等待处理任务完成
                _processingTask?.Wait(TimeSpan.FromSeconds(5));
                
                // 取消处理任务
                _cancellationTokenSource?.Cancel();
                
                // 释放文件写入器
                lock (_fileLock)
                {
                    _fileWriter?.Flush();
                    _fileWriter?.Dispose();
                    _fileWriter = null;
                }
                
                _cancellationTokenSource?.Dispose();
                
                Debug.Log($"[FileLogWriter] 资源释放完成: {Name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 释放资源失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 后台处理日志条目
        /// </summary>
        /// <returns>处理任务</returns>
        private async Task ProcessLogEntriesAsync()
        {
            var buffer = new List<LogEntry>();
            var lastFlushTime = DateTime.Now;
            
            try
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        // 尝试读取日志条目
                        if (await _reader.WaitToReadAsync(_cancellationTokenSource.Token))
                        {
                            // 批量读取日志条目
                            while (_reader.TryRead(out var entry) && buffer.Count < _bufferSize)
                            {
                                buffer.Add(entry);
                            }
                            
                            // 检查是否需要刷新
                            bool shouldFlush = buffer.Count >= _bufferSize ||
                                             DateTime.Now - lastFlushTime >= _flushInterval;
                            
                            if (shouldFlush && buffer.Count > 0)
                            {
                                await FlushBufferAsync(buffer);
                                buffer.Clear();
                                lastFlushTime = DateTime.Now;
                            }
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[FileLogWriter] 处理日志条目失败: {ex.Message}");
                        await Task.Delay(1000, _cancellationTokenSource.Token);
                    }
                }
                
                // 处理剩余的日志条目
                if (buffer.Count > 0)
                {
                    await FlushBufferAsync(buffer);
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 后台处理任务异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 刷新缓冲区到文件
        /// </summary>
        /// <param name="buffer">日志条目缓冲区</param>
        /// <returns>刷新任务</returns>
        private async Task FlushBufferAsync(List<LogEntry> buffer)
        {
            if (buffer.Count == 0)
            {
                return;
            }
            
            lock (_fileLock)
            {
                try
                {
                    // 检查文件大小是否超限
                    CheckFileSizeAndRotate();
                    
                    if (_fileWriter != null)
                    {
                        foreach (var entry in buffer)
                        {
                            string formattedLog = _formatter.Format(entry);
                            _fileWriter.WriteLine(formattedLog);
                        }
                        
                        _fileWriter.Flush();
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[FileLogWriter] 刷新缓冲区到文件失败: {ex.Message}");
                    
                    // 尝试重新初始化文件写入器
                    try
                    {
                        ReinitializeFileWriter();
                    }
                    catch (Exception reinitEx)
                    {
                        Debug.LogError($"[FileLogWriter] 重新初始化文件写入器失败: {reinitEx.Message}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 初始化文件写入器
        /// </summary>
        private void InitializeFileWriter()
        {
            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(_filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // 创建文件写入器
                _fileWriter = new StreamWriter(_filePath, append: true)
                {
                    AutoFlush = false
                };
                
                Debug.Log($"[FileLogWriter] 文件写入器初始化完成: {_filePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 初始化文件写入器失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 重新初始化文件写入器
        /// </summary>
        private void ReinitializeFileWriter()
        {
            lock (_fileLock)
            {
                try
                {
                    _fileWriter?.Flush();
                    _fileWriter?.Dispose();
                    _fileWriter = null;
                    
                    InitializeFileWriter();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[FileLogWriter] 重新初始化文件写入器失败: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 检查文件大小并进行轮转
        /// </summary>
        private void CheckFileSizeAndRotate()
        {
            try
            {
                if (File.Exists(_filePath))
                {
                    var fileInfo = new FileInfo(_filePath);
                    if (fileInfo.Length >= _maxFileSize)
                    {
                        RotateLogFile();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 检查文件大小失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 轮转日志文件
        /// </summary>
        private void RotateLogFile()
        {
            try
            {
                _fileWriter?.Flush();
                _fileWriter?.Dispose();
                _fileWriter = null;
                
                // 生成新的文件名
                string directory = Path.GetDirectoryName(_filePath);
                string fileName = Path.GetFileNameWithoutExtension(_filePath);
                string extension = Path.GetExtension(_filePath);
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string rotatedFileName = $"{fileName}_{timestamp}{extension}";
                string rotatedFilePath = Path.Combine(directory, rotatedFileName);
                
                // 移动当前文件
                File.Move(_filePath, rotatedFilePath);
                
                // 重新创建文件写入器
                InitializeFileWriter();
                
                Debug.Log($"[FileLogWriter] 日志文件轮转完成: {_filePath} -> {rotatedFilePath}");
                
                // 发布轮转事件
                var eventSystem = EventSystem.Instance;
                eventSystem?.Publish(new LogRotationEvent(_filePath, rotatedFilePath, "文件大小超限", new FileInfo(rotatedFilePath).Length));
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FileLogWriter] 日志文件轮转失败: {ex.Message}");
                
                // 轮转失败时尝试重新创建文件写入器
                try
                {
                    InitializeFileWriter();
                }
                catch (Exception reinitEx)
                {
                    Debug.LogError($"[FileLogWriter] 轮转失败后重新初始化失败: {reinitEx.Message}");
                }
            }
        }
        
        /// <summary>
        /// 解析时间跨度字符串
        /// </summary>
        /// <param name="timeSpanString">时间跨度字符串</param>
        /// <returns>时间跨度</returns>
        private TimeSpan ParseTimeSpan(string timeSpanString)
        {
            if (string.IsNullOrEmpty(timeSpanString))
            {
                return TimeSpan.FromSeconds(5);
            }
            
            timeSpanString = timeSpanString.ToLower().Trim();
            
            if (timeSpanString.EndsWith("ms"))
            {
                if (int.TryParse(timeSpanString.Substring(0, timeSpanString.Length - 2), out int ms))
                {
                    return TimeSpan.FromMilliseconds(ms);
                }
            }
            else if (timeSpanString.EndsWith("s"))
            {
                if (int.TryParse(timeSpanString.Substring(0, timeSpanString.Length - 1), out int s))
                {
                    return TimeSpan.FromSeconds(s);
                }
            }
            else if (timeSpanString.EndsWith("m"))
            {
                if (int.TryParse(timeSpanString.Substring(0, timeSpanString.Length - 1), out int m))
                {
                    return TimeSpan.FromMinutes(m);
                }
            }
            
            return TimeSpan.FromSeconds(5);
        }
        
        /// <summary>
        /// 解析文件大小字符串
        /// </summary>
        /// <param name="fileSizeString">文件大小字符串</param>
        /// <returns>文件大小（字节）</returns>
        private long ParseFileSize(string fileSizeString)
        {
            if (string.IsNullOrEmpty(fileSizeString))
            {
                return 10 * 1024 * 1024; // 默认10MB
            }
            
            fileSizeString = fileSizeString.ToUpper().Trim();
            
            long multiplier = 1;
            if (fileSizeString.EndsWith("KB"))
            {
                multiplier = 1024;
                fileSizeString = fileSizeString.Substring(0, fileSizeString.Length - 2);
            }
            else if (fileSizeString.EndsWith("MB"))
            {
                multiplier = 1024 * 1024;
                fileSizeString = fileSizeString.Substring(0, fileSizeString.Length - 2);
            }
            else if (fileSizeString.EndsWith("GB"))
            {
                multiplier = 1024 * 1024 * 1024;
                fileSizeString = fileSizeString.Substring(0, fileSizeString.Length - 2);
            }
            
            if (long.TryParse(fileSizeString.Trim(), out long size))
            {
                return size * multiplier;
            }
            
            return 10 * 1024 * 1024; // 默认10MB
        }
    }
}