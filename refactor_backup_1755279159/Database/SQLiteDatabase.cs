using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using UnityEngine;
using Mono.Data.Sqlite;

namespace DigitalHuman.Core.Database
{
    /// <summary>
    /// SQLite 数据库管理器，提供数据库操作的封装
    /// </summary>
    public class SQLiteDatabase : IDisposable
    {
        private SqliteConnection _connection;
        private readonly string _databasePath;
        private bool _disposed = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="databaseName">数据库文件名</param>
        public SQLiteDatabase(string databaseName)
        {
            string persistentDataPath = Application.persistentDataPath;
            _databasePath = Path.Combine(persistentDataPath, databaseName);
            
            InitializeDatabase();
        }
        
        /// <summary>
        /// 初始化数据库连接
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                string connectionString = $"URI=file:{_databasePath}";
                _connection = new SqliteConnection(connectionString);
                _connection.Open();
                
                Debug.Log($"[SQLiteDatabase] 数据库连接成功: {_databasePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SQLiteDatabase] 数据库连接失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 执行非查询SQL语句（INSERT, UPDATE, DELETE）
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>受影响的行数</returns>
        public int ExecuteNonQuery(string sql, Dictionary<string, object> parameters = null)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SQLiteDatabase));
            }
            
            try
            {
                using (SqliteCommand command = new SqliteCommand(sql, _connection))
                {
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }
                    
                    int result = command.ExecuteNonQuery();
                    Debug.Log($"[SQLiteDatabase] 执行SQL: {sql}, 影响行数: {result}");
                    return result;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SQLiteDatabase] 执行SQL失败: {sql}, 错误: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 执行查询SQL语句
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>查询结果</returns>
        public List<Dictionary<string, object>> ExecuteQuery(string sql, Dictionary<string, object> parameters = null)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SQLiteDatabase));
            }
            
            List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();
            
            try
            {
                using (SqliteCommand command = new SqliteCommand(sql, _connection))
                {
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }
                    
                    using (SqliteDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Dictionary<string, object> row = new Dictionary<string, object>();
                            
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                string columnName = reader.GetName(i);
                                object value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                                row[columnName] = value;
                            }
                            
                            results.Add(row);
                        }
                    }
                }
                
                Debug.Log($"[SQLiteDatabase] 查询SQL: {sql}, 结果行数: {results.Count}");
                return results;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SQLiteDatabase] 查询SQL失败: {sql}, 错误: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 执行标量查询（返回单个值）
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>查询结果</returns>
        public object ExecuteScalar(string sql, Dictionary<string, object> parameters = null)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SQLiteDatabase));
            }
            
            try
            {
                using (SqliteCommand command = new SqliteCommand(sql, _connection))
                {
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }
                    
                    object result = command.ExecuteScalar();
                    Debug.Log($"[SQLiteDatabase] 标量查询SQL: {sql}, 结果: {result}");
                    return result;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[SQLiteDatabase] 标量查询失败: {sql}, 错误: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 开始事务
        /// </summary>
        /// <returns>事务对象</returns>
        public SqliteTransaction BeginTransaction()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SQLiteDatabase));
            }
            
            return _connection.BeginTransaction();
        }
        
        /// <summary>
        /// 检查表是否存在
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <returns>是否存在</returns>
        public bool TableExists(string tableName)
        {
            string sql = "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=@tableName";
            var parameters = new Dictionary<string, object> { { "@tableName", tableName } };
            
            object result = ExecuteScalar(sql, parameters);
            return Convert.ToInt32(result) > 0;
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _connection?.Close();
                    _connection?.Dispose();
                    Debug.Log("[SQLiteDatabase] 数据库连接已关闭");
                }
                
                _disposed = true;
            }
        }
        
        /// <summary>
        /// 析构函数
        /// </summary>
        ~SQLiteDatabase()
        {
            Dispose(false);
        }
    }
}