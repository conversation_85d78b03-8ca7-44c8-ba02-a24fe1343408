using System;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 自动语音识别服务接口
    /// </summary>
    public interface IASRService
    {
        /// <summary>
        /// 语音识别完成事件
        /// </summary>
        event Action<ASRResponse> OnRecognitionCompleted;

        /// <summary>
        /// 实时识别结果事件
        /// </summary>
        event Action<RealTimeASRResult> OnRealTimeResult;

        /// <summary>
        /// 识别错误事件
        /// </summary>
        event Action<string> OnRecognitionError;

        /// <summary>
        /// 识别状态变化事件
        /// </summary>
        event Action<RealTimeRecognitionState> OnStateChanged;

        /// <summary>
        /// 初始化ASR服务
        /// </summary>
        /// <param name="configuration">ASR配置</param>
        /// <returns>初始化是否成功</returns>
        bool Initialize(ASRConfiguration configuration);

        /// <summary>
        /// 识别音频文件
        /// </summary>
        /// <param name="audioClip">音频剪辑</param>
        /// <param name="language">识别语言，为空则使用默认语言</param>
        /// <returns>识别结果</returns>
        Task<ASRResponse> RecognizeAsync(AudioClip audioClip, string language = null);

        /// <summary>
        /// 识别音频数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <param name="sampleRate">采样率</param>
        /// <param name="channels">声道数</param>
        /// <param name="language">识别语言</param>
        /// <returns>识别结果</returns>
        Task<ASRResponse> RecognizeAsync(float[] audioData, int sampleRate, int channels, string language = null);

        /// <summary>
        /// 开始实时语音识别
        /// </summary>
        /// <param name="language">识别语言</param>
        /// <returns>开始是否成功</returns>
        bool StartRealTimeRecognition(string language = null);

        /// <summary>
        /// 停止实时语音识别
        /// </summary>
        /// <returns>最终识别结果</returns>
        Task<ASRResponse> StopRealTimeRecognition();

        /// <summary>
        /// 发送实时音频数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        void SendRealTimeAudio(float[] audioData);

        /// <summary>
        /// 取消当前识别任务
        /// </summary>
        void CancelRecognition();

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns>ASR配置</returns>
        ASRConfiguration GetConfiguration();

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        void UpdateConfiguration(ASRConfiguration configuration);

        /// <summary>
        /// 测试服务连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        /// <returns>支持的语言列表</returns>
        string[] GetSupportedLanguages();

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }
}