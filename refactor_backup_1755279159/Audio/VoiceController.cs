using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 语音控制器实现
    /// </summary>
    public class VoiceController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IVoiceController
    {
        [Header("语音控制配置")]
        [SerializeField] private VoiceControlConfiguration configuration;
        
        [Header("调试设置")]
        [SerializeField] private bool enableDebugLog = true;

        // 事件定义
        public event Action<VoiceControlEvent> OnWakeWordDetected;
        public event Action<VoiceControlEvent> OnInterruptWordDetected;
        public event Action<VoiceControlEvent> OnTerminateWordDetected;
        public event Action<string, string> OnLanguageSwitched;
        public event Action<VoiceControlState> OnStateChanged;
        public event Action<string> OnConversationStarted;
        public event Action OnConversationEnded;
        public event Action<string> OnVoiceControlError;

        // 私有字段
        private bool isInitialized;
        private bool isListening;
        private VoiceControlState currentState;
        private ConversationState conversationState;
        private Coroutine timeoutCoroutine;
        private float lastWakeWordTime;

        // 常量定义
        private const float STATE_UPDATE_INTERVAL = 0.1f;

        private void Awake()
        {
            InitializeComponents();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 初始化配置
            if (configuration == null)
            {
                configuration = new VoiceControlConfiguration();
            }

            // 初始化状态
            currentState = VoiceControlState.Idle;
            conversationState = new ConversationState();
            isListening = false;
            lastWakeWordTime = 0f;
        }

        /// <summary>
        /// 初始化语音控制器
        /// </summary>
        /// <param name="configuration">语音控制配置</param>
        /// <returns>初始化是否成功</returns>
        public bool Initialize(VoiceControlConfiguration configuration)
        {
            try
            {
                if (configuration == null || !configuration.IsValid())
                {
                    LogError("语音控制配置无效");
                    return false;
                }

                this.configuration = configuration;
                
                // 重置状态
                ResetConversation();
                
                isInitialized = true;
                LogDebug("语音控制器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"语音控制器初始化失败: {ex.Message}");
                OnVoiceControlError?.Invoke($"语音控制器初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 开始语音控制监听
        /// </summary>
        /// <returns>开始是否成功</returns>
        public bool StartListening()
        {
            if (!isInitialized)
            {
                LogError("语音控制器未初始化");
                return false;
            }

            if (!configuration.EnableVoiceControl)
            {
                LogWarning("语音控制已禁用");
                return false;
            }

            if (isListening)
            {
                LogWarning("语音控制监听已在进行中");
                return true;
            }

            try
            {
                isListening = true;
                SetState(VoiceControlState.WaitingForWakeWord);
                
                // 启动超时检查协程
                if (timeoutCoroutine != null)
                {
                    StopCoroutine(timeoutCoroutine);
                }
                timeoutCoroutine = StartCoroutine(TimeoutCheckCoroutine());

                LogDebug("开始语音控制监听");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"开始语音控制监听失败: {ex.Message}");
                OnVoiceControlError?.Invoke($"开始语音控制监听失败: {ex.Message}");
                isListening = false;
                return false;
            }
        }

        /// <summary>
        /// 停止语音控制监听
        /// </summary>
        public void StopListening()
        {
            try
            {
                if (isListening)
                {
                    isListening = false;
                    
                    if (timeoutCoroutine != null)
                    {
                        StopCoroutine(timeoutCoroutine);
                        timeoutCoroutine = null;
                    }
                    
                    SetState(VoiceControlState.Idle);
                    LogDebug("停止语音控制监听");
                }
            }
            catch (Exception ex)
            {
                LogError($"停止语音控制监听失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理语音识别结果
        /// </summary>
        /// <param name="recognizedText">识别的文本</param>
        /// <param name="confidence">识别置信度</param>
        /// <param name="language">识别语言</param>
        /// <returns>是否检测到控制词</returns>
        public bool ProcessRecognitionResult(string recognizedText, float confidence, string language)
        {
            if (!isInitialized || !isListening || string.IsNullOrEmpty(recognizedText))
            {
                return false;
            }

            try
            {
                LogDebug($"处理语音识别结果: {recognizedText} (置信度: {confidence:F2}, 语言: {language})");

                // 更新对话状态活动时间
                conversationState.UpdateActivity();

                // 检查语言切换
                string switchedLanguage = DetectLanguageSwitch(recognizedText, confidence);
                if (!string.IsNullOrEmpty(switchedLanguage))
                {
                    return true;
                }

                // 根据当前状态处理不同的控制词
                switch (currentState)
                {
                    case VoiceControlState.WaitingForWakeWord:
                        return DetectWakeWord(recognizedText, confidence, language);

                    case VoiceControlState.Listening:
                    case VoiceControlState.Processing:
                    case VoiceControlState.Responding:
                        // 在对话过程中检查打断词和终止词
                        if (DetectInterruptWord(recognizedText, confidence, language))
                        {
                            return true;
                        }
                        if (DetectTerminateWord(recognizedText, confidence, language))
                        {
                            return true;
                        }
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError($"处理语音识别结果失败: {ex.Message}");
                OnVoiceControlError?.Invoke($"处理语音识别结果失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检测唤醒词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <param name="language">语言</param>
        /// <returns>是否检测到唤醒词</returns>
        public bool DetectWakeWord(string text, float confidence, string language)
        {
            if (string.IsNullOrEmpty(text) || confidence < configuration.GlobalConfidenceThreshold)
            {
                return false;
            }

            try
            {
                var wakeWords = configuration.GetControlWordsByType(VoiceControlType.WakeWord, language);
                
                foreach (var wakeWord in wakeWords)
                {
                    if (wakeWord.IsMatch(text, configuration.GlobalConfidenceThreshold))
                    {
                        // 检测到唤醒词
                        var controlEvent = new VoiceControlEvent(
                            VoiceControlType.WakeWord, 
                            text, 
                            wakeWord.Word, 
                            confidence, 
                            language
                        );

                        lastWakeWordTime = Time.time;
                        conversationState.IsActive = true;
                        conversationState.CurrentLanguage = language;
                        conversationState.TurnCount++;
                        conversationState.AddConversationRecord($"唤醒词检测: {wakeWord.Word}");

                        SetState(VoiceControlState.Listening);
                        
                        OnWakeWordDetected?.Invoke(controlEvent);
                        OnConversationStarted?.Invoke(language);
                        
                        LogDebug($"检测到唤醒词: {wakeWord.Word} (置信度: {confidence:F2})");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError($"检测唤醒词失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检测打断词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <param name="language">语言</param>
        /// <returns>是否检测到打断词</returns>
        public bool DetectInterruptWord(string text, float confidence, string language)
        {
            if (string.IsNullOrEmpty(text) || confidence < configuration.GlobalConfidenceThreshold)
            {
                return false;
            }

            try
            {
                var interruptWords = configuration.GetControlWordsByType(VoiceControlType.InterruptWord, language);
                
                foreach (var interruptWord in interruptWords)
                {
                    if (interruptWord.IsMatch(text, configuration.GlobalConfidenceThreshold))
                    {
                        // 检测到打断词
                        var controlEvent = new VoiceControlEvent(
                            VoiceControlType.InterruptWord, 
                            text, 
                            interruptWord.Word, 
                            confidence, 
                            language
                        );

                        conversationState.AddConversationRecord($"打断词检测: {interruptWord.Word}");
                        SetState(VoiceControlState.Interrupted);
                        
                        OnInterruptWordDetected?.Invoke(controlEvent);
                        
                        LogDebug($"检测到打断词: {interruptWord.Word} (置信度: {confidence:F2})");
                        
                        // 打断后重新开始监听
                        SetState(VoiceControlState.Listening);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError($"检测打断词失败: {ex.Message}");
                return false;
            }
        }      
  /// <summary>
        /// 检测终止词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <param name="language">语言</param>
        /// <returns>是否检测到终止词</returns>
        public bool DetectTerminateWord(string text, float confidence, string language)
        {
            if (string.IsNullOrEmpty(text) || confidence < configuration.GlobalConfidenceThreshold)
            {
                return false;
            }

            try
            {
                var terminateWords = configuration.GetControlWordsByType(VoiceControlType.TerminateWord, language);
                
                foreach (var terminateWord in terminateWords)
                {
                    if (terminateWord.IsMatch(text, configuration.GlobalConfidenceThreshold))
                    {
                        // 检测到终止词
                        var controlEvent = new VoiceControlEvent(
                            VoiceControlType.TerminateWord, 
                            text, 
                            terminateWord.Word, 
                            confidence, 
                            language
                        );

                        conversationState.AddConversationRecord($"终止词检测: {terminateWord.Word}");
                        SetState(VoiceControlState.Terminated);
                        
                        OnTerminateWordDetected?.Invoke(controlEvent);
                        OnConversationEnded?.Invoke();
                        
                        LogDebug($"检测到终止词: {terminateWord.Word} (置信度: {confidence:F2})");
                        
                        // 终止对话
                        ResetConversation();
                        if (configuration.EnableContinuousListening)
                        {
                            SetState(VoiceControlState.WaitingForWakeWord);
                        }
                        else
                        {
                            StopListening();
                        }
                        
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError($"检测终止词失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检测语言切换词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <returns>检测到的语言，如果没有则返回null</returns>
        public string DetectLanguageSwitch(string text, float confidence)
        {
            if (string.IsNullOrEmpty(text) || confidence < configuration.GlobalConfidenceThreshold)
            {
                return null;
            }

            try
            {
                var languageSwitchWords = configuration.GetControlWordsByType(VoiceControlType.LanguageSwitch);
                
                foreach (var switchWord in languageSwitchWords)
                {
                    if (switchWord.IsMatch(text, configuration.GlobalConfidenceThreshold))
                    {
                        string newLanguage = switchWord.Language;
                        string responseText = configuration.LanguageSwitchResponses.ContainsKey(newLanguage) 
                            ? configuration.LanguageSwitchResponses[newLanguage] 
                            : $"Language switched to {newLanguage}";

                        // 切换语言
                        conversationState.CurrentLanguage = newLanguage;
                        conversationState.AddConversationRecord($"语言切换: {newLanguage}");
                        
                        OnLanguageSwitched?.Invoke(newLanguage, responseText);
                        
                        LogDebug($"检测到语言切换: {switchWord.Word} -> {newLanguage}");
                        return newLanguage;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError($"检测语言切换失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前控制状态
        /// </summary>
        /// <returns>控制状态</returns>
        public VoiceControlState GetCurrentState()
        {
            return currentState;
        }

        /// <summary>
        /// 获取对话状态
        /// </summary>
        /// <returns>对话状态</returns>
        public ConversationState GetConversationState()
        {
            return conversationState;
        }

        /// <summary>
        /// 设置当前语言
        /// </summary>
        /// <param name="language">语言代码</param>
        public void SetCurrentLanguage(string language)
        {
            if (!string.IsNullOrEmpty(language))
            {
                conversationState.CurrentLanguage = language;
                LogDebug($"设置当前语言: {language}");
            }
        }

        /// <summary>
        /// 获取当前语言
        /// </summary>
        /// <returns>当前语言代码</returns>
        public string GetCurrentLanguage()
        {
            return conversationState.CurrentLanguage;
        }

        /// <summary>
        /// 添加自定义控制词
        /// </summary>
        /// <param name="controlWord">控制词</param>
        public void AddCustomControlWord(VoiceControlWord controlWord)
        {
            if (controlWord != null)
            {
                configuration.AddControlWord(controlWord);
                LogDebug($"添加自定义控制词: {controlWord.Word} ({controlWord.Type})");
            }
        }

        /// <summary>
        /// 移除控制词
        /// </summary>
        /// <param name="word">控制词文本</param>
        /// <param name="type">控制词类型</param>
        /// <param name="language">语言</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveControlWord(string word, VoiceControlType type, string language)
        {
            bool removed = configuration.RemoveControlWord(word, type, language);
            if (removed)
            {
                LogDebug($"移除控制词: {word} ({type}, {language})");
            }
            return removed;
        }

        /// <summary>
        /// 获取指定类型的控制词列表
        /// </summary>
        /// <param name="type">控制词类型</param>
        /// <param name="language">语言，为空则返回所有语言</param>
        /// <returns>控制词列表</returns>
        public VoiceControlWord[] GetControlWords(VoiceControlType type, string language = null)
        {
            return configuration.GetControlWordsByType(type, language).ToArray();
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        public void UpdateConfiguration(VoiceControlConfiguration configuration)
        {
            if (configuration != null && configuration.IsValid())
            {
                this.configuration = configuration;
                LogDebug("语音控制配置已更新");
            }
            else
            {
                LogError("无效的语音控制配置");
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns>语音控制配置</returns>
        public VoiceControlConfiguration GetConfiguration()
        {
            return configuration;
        }

        /// <summary>
        /// 重置对话状态
        /// </summary>
        public void ResetConversation()
        {
            try
            {
                conversationState.Reset();
                SetState(VoiceControlState.Idle);
                LogDebug("重置对话状态");
            }
            catch (Exception ex)
            {
                LogError($"重置对话状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启用/禁用语音控制
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetVoiceControlEnabled(bool enabled)
        {
            configuration.EnableVoiceControl = enabled;
            
            if (!enabled && isListening)
            {
                StopListening();
            }
            
            LogDebug($"语音控制{(enabled ? "启用" : "禁用")}");
        }

        /// <summary>
        /// 检查语音控制是否启用
        /// </summary>
        /// <returns>是否启用</returns>
        public bool IsVoiceControlEnabled()
        {
            return configuration.EnableVoiceControl;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopListening();
                ResetConversation();
                isInitialized = false;
                LogDebug("语音控制器资源释放完成");
            }
            catch (Exception ex)
            {
                LogError($"释放语音控制器资源失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 设置控制状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void SetState(VoiceControlState newState)
        {
            if (currentState != newState)
            {
                var previousState = currentState;
                currentState = newState;
                conversationState.ControlState = newState;
                conversationState.UpdateActivity();

                OnStateChanged?.Invoke(currentState);
                LogDebug($"语音控制状态变更: {previousState} -> {currentState}");
            }
        }

        /// <summary>
        /// 超时检查协程
        /// </summary>
        /// <returns>协程</returns>
        private IEnumerator TimeoutCheckCoroutine()
        {
            while (isListening)
            {
                yield return new WaitForSeconds(STATE_UPDATE_INTERVAL);

                try
                {
                    // 检查唤醒词超时
                    if (currentState == VoiceControlState.WaitingForWakeWord && 
                        configuration.WakeWordTimeout > 0)
                    {
                        if (Time.time - lastWakeWordTime > configuration.WakeWordTimeout)
                        {
                            LogDebug("唤醒词等待超时");
                            if (!configuration.EnableContinuousListening)
                            {
                                StopListening();
                                break;
                            }
                        }
                    }

                    // 检查对话超时
                    if (conversationState.IsActive && 
                        configuration.ListeningTimeout > 0 && 
                        conversationState.IsTimeout(configuration.ListeningTimeout))
                    {
                        LogDebug("对话监听超时");
                        OnConversationEnded?.Invoke();
                        ResetConversation();
                        
                        if (configuration.EnableContinuousListening)
                        {
                            SetState(VoiceControlState.WaitingForWakeWord);
                        }
                        else
                        {
                            StopListening();
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError($"超时检查失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[VoiceController] {message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[VoiceController] {message}");
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogError(string message)
        {
            Debug.LogError($"[VoiceController] {message}");
        }

        #endregion

        #region Unity生命周期

        private void Start()
        {
            // 如果配置有效，自动初始化
            if (configuration != null && configuration.IsValid())
            {
                Initialize(configuration);
            }
        }

        private void OnDestroy()
        {
            Dispose();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && isListening)
            {
                StopListening();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && isListening)
            {
                StopListening();
            }
        }

        #endregion

        #region 公共辅助方法

        /// <summary>
        /// 手动触发唤醒
        /// </summary>
        /// <param name="language">语言</param>
        public void ManualWakeUp(string language = "zh-CN")
        {
            if (!isInitialized)
            {
                LogError("语音控制器未初始化");
                return;
            }

            try
            {
                conversationState.IsActive = true;
                conversationState.CurrentLanguage = language;
                conversationState.TurnCount++;
                conversationState.AddConversationRecord("手动唤醒");

                SetState(VoiceControlState.Listening);
                OnConversationStarted?.Invoke(language);
                
                LogDebug($"手动唤醒对话 (语言: {language})");
            }
            catch (Exception ex)
            {
                LogError($"手动唤醒失败: {ex.Message}");
                OnVoiceControlError?.Invoke($"手动唤醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动终止对话
        /// </summary>
        public void ManualTerminate()
        {
            try
            {
                if (conversationState.IsActive)
                {
                    conversationState.AddConversationRecord("手动终止");
                    OnConversationEnded?.Invoke();
                    LogDebug("手动终止对话");
                }

                ResetConversation();
                
                if (configuration.EnableContinuousListening && isListening)
                {
                    SetState(VoiceControlState.WaitingForWakeWord);
                }
                else
                {
                    StopListening();
                }
            }
            catch (Exception ex)
            {
                LogError($"手动终止失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置对话状态为处理中
        /// </summary>
        public void SetProcessingState()
        {
            if (conversationState.IsActive)
            {
                SetState(VoiceControlState.Processing);
            }
        }

        /// <summary>
        /// 设置对话状态为响应中
        /// </summary>
        public void SetRespondingState()
        {
            if (conversationState.IsActive)
            {
                SetState(VoiceControlState.Responding);
            }
        }

        /// <summary>
        /// 恢复监听状态
        /// </summary>
        public void ResumeListening()
        {
            if (conversationState.IsActive)
            {
                SetState(VoiceControlState.Listening);
            }
        }

        #endregion
    }
}