using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 多语言管理器实现
    /// </summary>
    public class MultiLanguageManager : MonoBehaviour, IMultiLanguageManager
    {
        [Header("多语言配置")]
        [SerializeField] private string[] supportedLanguages = { "zh-CN", "en-US", "ja-JP" };
        [SerializeField] private string defaultLanguage = "zh-CN";
        [SerializeField] private string currentLanguage = "zh-CN";
        
        [Header("调试设置")]
        [SerializeField] private bool enableDebugLog = true;

        // 事件定义
        public event Action<string, string> OnLanguageChanged;
        public event Action<string> OnLanguageConfigurationUpdated;
        public event Action<string> OnMultiLanguageError;

        // 私有字段
        private bool isInitialized;
        private Dictionary<string, string> languageDisplayNames;
        private Dictionary<string, ASRConfiguration> languageASRConfigs;
        private Dictionary<string, TTSConfiguration> languageTTSConfigs;
        private Dictionary<string, string[]> languageActivationWords;
        private Dictionary<string, string> languageResponses;
        private Dictionary<string, LanguageUsageStats> languageStats;
        private DateTime currentLanguageStartTime;

        // 常量定义
        private const float CONFIDENCE_THRESHOLD = 0.8f;

        private void Awake()
        {
            InitializeComponents();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            languageDisplayNames = new Dictionary<string, string>();
            languageASRConfigs = new Dictionary<string, ASRConfiguration>();
            languageTTSConfigs = new Dictionary<string, TTSConfiguration>();
            languageActivationWords = new Dictionary<string, string[]>();
            languageResponses = new Dictionary<string, string>();
            languageStats = new Dictionary<string, LanguageUsageStats>();

            InitializeDefaultLanguageData();
        }

        /// <summary>
        /// 初始化默认语言数据
        /// </summary>
        private void InitializeDefaultLanguageData()
        {
            // 初始化语言显示名称
            languageDisplayNames["zh-CN"] = "中文";
            languageDisplayNames["en-US"] = "English";
            languageDisplayNames["ja-JP"] = "日本語";

            // 初始化语言激活词
            languageActivationWords["zh-CN"] = new string[] { "你好", "小助手", "中文模式" };
            languageActivationWords["en-US"] = new string[] { "hi", "hello", "english mode" };
            languageActivationWords["ja-JP"] = new string[] { "こんにちは", "日本語モード" };

            // 初始化语言响应词
            languageResponses["zh-CN"] = "您好，我现在使用中文与您对话。";
            languageResponses["en-US"] = "Hello, I'm now speaking English with you.";
            languageResponses["ja-JP"] = "こんにちは、日本語でお話しします。";

            // 初始化语言统计
            foreach (var language in supportedLanguages)
            {
                languageStats[language] = new LanguageUsageStats
                {
                    Language = language
                };
            }
        }

        /// <summary>
        /// 初始化多语言管理器
        /// </summary>
        /// <param name="supportedLanguages">支持的语言列表</param>
        /// <param name="defaultLanguage">默认语言</param>
        /// <returns>初始化是否成功</returns>
        public bool Initialize(string[] supportedLanguages, string defaultLanguage = "zh-CN")
        {
            try
            {
                if (supportedLanguages == null || supportedLanguages.Length == 0)
                {
                    LogError("支持的语言列表不能为空");
                    return false;
                }

                if (string.IsNullOrEmpty(defaultLanguage))
                {
                    LogError("默认语言不能为空");
                    return false;
                }

                this.supportedLanguages = supportedLanguages;
                this.defaultLanguage = defaultLanguage;
                this.currentLanguage = defaultLanguage;

                // 确保所有支持的语言都有基础数据
                foreach (var language in supportedLanguages)
                {
                    if (!languageDisplayNames.ContainsKey(language))
                    {
                        languageDisplayNames[language] = language;
                    }

                    if (!languageActivationWords.ContainsKey(language))
                    {
                        languageActivationWords[language] = new string[] { language.ToLower() };
                    }

                    if (!languageResponses.ContainsKey(language))
                    {
                        languageResponses[language] = $"Language switched to {language}";
                    }

                    if (!languageStats.ContainsKey(language))
                    {
                        languageStats[language] = new LanguageUsageStats
                        {
                            Language = language
                        };
                    }
                }

                currentLanguageStartTime = DateTime.Now;
                isInitialized = true;
                
                LogDebug($"多语言管理器初始化成功，支持 {supportedLanguages.Length} 种语言，默认语言: {defaultLanguage}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"多语言管理器初始化失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"多语言管理器初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 切换语言
        /// </summary>
        /// <param name="language">目标语言</param>
        /// <returns>切换是否成功</returns>
        public bool SwitchLanguage(string language)
        {
            if (!isInitialized)
            {
                LogError("多语言管理器未初始化");
                return false;
            }

            if (string.IsNullOrEmpty(language))
            {
                LogError("目标语言不能为空");
                return false;
            }

            if (!IsLanguageSupported(language))
            {
                LogError($"不支持的语言: {language}");
                return false;
            }

            if (currentLanguage == language)
            {
                LogDebug($"已经是当前语言: {language}");
                return true;
            }

            try
            {
                string oldLanguage = currentLanguage;
                
                // 更新当前语言使用时间
                UpdateCurrentLanguageUsageTime();
                
                // 切换语言
                currentLanguage = language;
                currentLanguageStartTime = DateTime.Now;

                // 更新统计信息
                if (languageStats.ContainsKey(language))
                {
                    languageStats[language].ActivationCount++;
                    languageStats[language].UsageCount++;
                    languageStats[language].LastUsed = DateTime.Now;
                }

                OnLanguageChanged?.Invoke(currentLanguage, oldLanguage);
                LogDebug($"语言切换成功: {oldLanguage} -> {currentLanguage}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"语言切换失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"语言切换失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取当前语言
        /// </summary>
        /// <returns>当前语言代码</returns>
        public string GetCurrentLanguage()
        {
            return currentLanguage;
        }

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        /// <returns>支持的语言列表</returns>
        public string[] GetSupportedLanguages()
        {
            return supportedLanguages.ToArray();
        }

        /// <summary>
        /// 检查是否支持指定语言
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>是否支持</returns>
        public bool IsLanguageSupported(string language)
        {
            return !string.IsNullOrEmpty(language) && 
                   supportedLanguages.Contains(language, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 配置语言特定的ASR设置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="asrConfig">ASR配置</param>
        public void ConfigureASRForLanguage(string language, ASRConfiguration asrConfig)
        {
            if (!IsLanguageSupported(language))
            {
                LogError($"不支持的语言: {language}");
                return;
            }

            if (asrConfig == null)
            {
                LogError("ASR配置不能为空");
                return;
            }

            try
            {
                languageASRConfigs[language] = asrConfig;
                OnLanguageConfigurationUpdated?.Invoke(language);
                LogDebug($"已配置 {language} 的ASR设置");
            }
            catch (Exception ex)
            {
                LogError($"配置ASR设置失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"配置ASR设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置语言特定的TTS设置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="ttsConfig">TTS配置</param>
        public void ConfigureTTSForLanguage(string language, TTSConfiguration ttsConfig)
        {
            if (!IsLanguageSupported(language))
            {
                LogError($"不支持的语言: {language}");
                return;
            }

            if (ttsConfig == null)
            {
                LogError("TTS配置不能为空");
                return;
            }

            try
            {
                languageTTSConfigs[language] = ttsConfig;
                OnLanguageConfigurationUpdated?.Invoke(language);
                LogDebug($"已配置 {language} 的TTS设置");
            }
            catch (Exception ex)
            {
                LogError($"配置TTS设置失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"配置TTS设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取指定语言的ASR配置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>ASR配置</returns>
        public ASRConfiguration GetASRConfiguration(string language)
        {
            if (languageASRConfigs.ContainsKey(language))
            {
                return languageASRConfigs[language];
            }

            // 返回默认配置
            var defaultConfig = new ASRConfiguration
            {
                DefaultLanguage = language
            };
            return defaultConfig;
        }

        /// <summary>
        /// 获取指定语言的TTS配置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>TTS配置</returns>
        public TTSConfiguration GetTTSConfiguration(string language)
        {
            if (languageTTSConfigs.ContainsKey(language))
            {
                return languageTTSConfigs[language];
            }

            // 返回默认配置
            var defaultConfig = new TTSConfiguration
            {
                DefaultLanguage = language
            };
            return defaultConfig;
        }    
    /// <summary>
        /// 设置语言激活词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="activationWords">激活词列表</param>
        public void SetLanguageActivationWords(string language, string[] activationWords)
        {
            if (!IsLanguageSupported(language))
            {
                LogError($"不支持的语言: {language}");
                return;
            }

            if (activationWords == null || activationWords.Length == 0)
            {
                LogError("激活词列表不能为空");
                return;
            }

            try
            {
                languageActivationWords[language] = activationWords.ToArray();
                LogDebug($"已设置 {language} 的激活词: {string.Join(", ", activationWords)}");
            }
            catch (Exception ex)
            {
                LogError($"设置语言激活词失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"设置语言激活词失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取语言激活词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>激活词列表</returns>
        public string[] GetLanguageActivationWords(string language)
        {
            if (languageActivationWords.ContainsKey(language))
            {
                return languageActivationWords[language].ToArray();
            }

            return new string[0];
        }

        /// <summary>
        /// 设置语言响应词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="responseText">响应文本</param>
        public void SetLanguageResponse(string language, string responseText)
        {
            if (!IsLanguageSupported(language))
            {
                LogError($"不支持的语言: {language}");
                return;
            }

            if (string.IsNullOrEmpty(responseText))
            {
                LogError("响应文本不能为空");
                return;
            }

            try
            {
                languageResponses[language] = responseText;
                LogDebug($"已设置 {language} 的响应词: {responseText}");
            }
            catch (Exception ex)
            {
                LogError($"设置语言响应词失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"设置语言响应词失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取语言响应词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>响应文本</returns>
        public string GetLanguageResponse(string language)
        {
            if (languageResponses.ContainsKey(language))
            {
                return languageResponses[language];
            }

            return $"Language switched to {language}";
        }

        /// <summary>
        /// 检测语言激活词
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <param name="confidence">置信度</param>
        /// <returns>检测到的语言，如果没有则返回null</returns>
        public string DetectLanguageActivation(string text, float confidence)
        {
            if (string.IsNullOrEmpty(text) || confidence < CONFIDENCE_THRESHOLD)
            {
                return null;
            }

            try
            {
                // 按优先级检测语言激活词
                foreach (var language in supportedLanguages)
                {
                    if (languageActivationWords.ContainsKey(language))
                    {
                        var activationWords = languageActivationWords[language];
                        foreach (var word in activationWords)
                        {
                            if (!string.IsNullOrEmpty(word) && 
                                text.Contains(word, StringComparison.OrdinalIgnoreCase))
                            {
                                LogDebug($"检测到语言激活词: {word} -> {language}");
                                return language;
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError($"检测语言激活词失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取语言显示名称
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>显示名称</returns>
        public string GetLanguageDisplayName(string language)
        {
            if (languageDisplayNames.ContainsKey(language))
            {
                return languageDisplayNames[language];
            }

            return language;
        }

        /// <summary>
        /// 添加支持的语言
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="displayName">显示名称</param>
        /// <returns>添加是否成功</returns>
        public bool AddSupportedLanguage(string language, string displayName = null)
        {
            if (string.IsNullOrEmpty(language))
            {
                LogError("语言代码不能为空");
                return false;
            }

            if (IsLanguageSupported(language))
            {
                LogWarning($"语言已存在: {language}");
                return true;
            }

            try
            {
                // 添加到支持的语言列表
                var newSupportedLanguages = supportedLanguages.ToList();
                newSupportedLanguages.Add(language);
                supportedLanguages = newSupportedLanguages.ToArray();

                // 设置显示名称
                languageDisplayNames[language] = displayName ?? language;

                // 初始化默认数据
                if (!languageActivationWords.ContainsKey(language))
                {
                    languageActivationWords[language] = new string[] { language.ToLower() };
                }

                if (!languageResponses.ContainsKey(language))
                {
                    languageResponses[language] = $"Language switched to {language}";
                }

                if (!languageStats.ContainsKey(language))
                {
                    languageStats[language] = new LanguageUsageStats
                    {
                        Language = language
                    };
                }

                LogDebug($"添加支持的语言: {language} ({displayName})");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"添加支持的语言失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"添加支持的语言失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除支持的语言
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>移除是否成功</returns>
        public bool RemoveSupportedLanguage(string language)
        {
            if (string.IsNullOrEmpty(language))
            {
                LogError("语言代码不能为空");
                return false;
            }

            if (!IsLanguageSupported(language))
            {
                LogWarning($"语言不存在: {language}");
                return true;
            }

            if (language == defaultLanguage)
            {
                LogError("不能移除默认语言");
                return false;
            }

            if (language == currentLanguage)
            {
                LogWarning($"正在移除当前语言 {language}，切换到默认语言 {defaultLanguage}");
                SwitchLanguage(defaultLanguage);
            }

            try
            {
                // 从支持的语言列表中移除
                var newSupportedLanguages = supportedLanguages.ToList();
                newSupportedLanguages.Remove(language);
                supportedLanguages = newSupportedLanguages.ToArray();

                // 清理相关数据
                languageDisplayNames.Remove(language);
                languageASRConfigs.Remove(language);
                languageTTSConfigs.Remove(language);
                languageActivationWords.Remove(language);
                languageResponses.Remove(language);
                languageStats.Remove(language);

                LogDebug($"移除支持的语言: {language}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"移除支持的语言失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"移除支持的语言失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取语言统计信息
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>使用统计</returns>
        public LanguageUsageStats GetLanguageStats(string language)
        {
            if (languageStats.ContainsKey(language))
            {
                var stats = languageStats[language];
                
                // 如果是当前语言，更新使用时间
                if (language == currentLanguage)
                {
                    var currentUsageTime = (float)(DateTime.Now - currentLanguageStartTime).TotalSeconds;
                    stats.TotalUsageTime += currentUsageTime;
                    currentLanguageStartTime = DateTime.Now;
                }
                
                return stats;
            }

            return new LanguageUsageStats { Language = language };
        }

        /// <summary>
        /// 重置语言统计
        /// </summary>
        /// <param name="language">语言代码，为空则重置所有</param>
        public void ResetLanguageStats(string language = null)
        {
            try
            {
                if (string.IsNullOrEmpty(language))
                {
                    // 重置所有语言统计
                    foreach (var lang in languageStats.Keys.ToArray())
                    {
                        languageStats[lang] = new LanguageUsageStats
                        {
                            Language = lang
                        };
                    }
                    LogDebug("重置所有语言统计");
                }
                else if (languageStats.ContainsKey(language))
                {
                    // 重置指定语言统计
                    languageStats[language] = new LanguageUsageStats
                    {
                        Language = language
                    };
                    LogDebug($"重置语言统计: {language}");
                }
            }
            catch (Exception ex)
            {
                LogError($"重置语言统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 更新当前语言使用时间
                UpdateCurrentLanguageUsageTime();

                // 清理数据
                languageDisplayNames?.Clear();
                languageASRConfigs?.Clear();
                languageTTSConfigs?.Clear();
                languageActivationWords?.Clear();
                languageResponses?.Clear();
                languageStats?.Clear();

                isInitialized = false;
                LogDebug("多语言管理器资源释放完成");
            }
            catch (Exception ex)
            {
                LogError($"释放多语言管理器资源失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 更新当前语言使用时间
        /// </summary>
        private void UpdateCurrentLanguageUsageTime()
        {
            if (!string.IsNullOrEmpty(currentLanguage) && languageStats.ContainsKey(currentLanguage))
            {
                var usageTime = (float)(DateTime.Now - currentLanguageStartTime).TotalSeconds;
                languageStats[currentLanguage].TotalUsageTime += usageTime;
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[MultiLanguageManager] {message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[MultiLanguageManager] {message}");
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogError(string message)
        {
            Debug.LogError($"[MultiLanguageManager] {message}");
        }

        #endregion

        #region Unity生命周期

        private void Start()
        {
            // 如果有预设的支持语言，自动初始化
            if (supportedLanguages != null && supportedLanguages.Length > 0)
            {
                Initialize(supportedLanguages, defaultLanguage);
            }
        }

        private void OnDestroy()
        {
            Dispose();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                UpdateCurrentLanguageUsageTime();
            }
            else
            {
                currentLanguageStartTime = DateTime.Now;
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                UpdateCurrentLanguageUsageTime();
            }
            else
            {
                currentLanguageStartTime = DateTime.Now;
            }
        }

        #endregion

        #region 公共辅助方法

        /// <summary>
        /// 获取所有语言统计信息
        /// </summary>
        /// <returns>语言统计字典</returns>
        public Dictionary<string, LanguageUsageStats> GetAllLanguageStats()
        {
            var result = new Dictionary<string, LanguageUsageStats>();
            
            foreach (var language in supportedLanguages)
            {
                result[language] = GetLanguageStats(language);
            }
            
            return result;
        }

        /// <summary>
        /// 获取最常用的语言
        /// </summary>
        /// <returns>最常用的语言代码</returns>
        public string GetMostUsedLanguage()
        {
            string mostUsedLanguage = defaultLanguage;
            float maxUsageTime = 0f;

            foreach (var language in supportedLanguages)
            {
                var stats = GetLanguageStats(language);
                if (stats.TotalUsageTime > maxUsageTime)
                {
                    maxUsageTime = stats.TotalUsageTime;
                    mostUsedLanguage = language;
                }
            }

            return mostUsedLanguage;
        }

        /// <summary>
        /// 检查语言配置完整性
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>配置是否完整</returns>
        public bool IsLanguageConfigurationComplete(string language)
        {
            if (!IsLanguageSupported(language))
            {
                return false;
            }

            return languageActivationWords.ContainsKey(language) &&
                   languageResponses.ContainsKey(language) &&
                   languageDisplayNames.ContainsKey(language);
        }

        /// <summary>
        /// 导出语言配置
        /// </summary>
        /// <returns>配置JSON字符串</returns>
        public string ExportLanguageConfiguration()
        {
            try
            {
                var config = new
                {
                    SupportedLanguages = supportedLanguages,
                    DefaultLanguage = defaultLanguage,
                    CurrentLanguage = currentLanguage,
                    LanguageDisplayNames = languageDisplayNames,
                    LanguageActivationWords = languageActivationWords,
                    LanguageResponses = languageResponses
                };

                return JsonUtility.ToJson(config, true);
            }
            catch (Exception ex)
            {
                LogError($"导出语言配置失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 导入语言配置
        /// </summary>
        /// <param name="configJson">配置JSON字符串</param>
        /// <returns>导入是否成功</returns>
        public bool ImportLanguageConfiguration(string configJson)
        {
            if (string.IsNullOrEmpty(configJson))
            {
                LogError("配置JSON不能为空");
                return false;
            }

            try
            {
                // 这里需要实现JSON反序列化逻辑
                // 由于Unity的JsonUtility有限制，这里简化处理
                LogDebug("语言配置导入功能需要进一步实现");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"导入语言配置失败: {ex.Message}");
                OnMultiLanguageError?.Invoke($"导入语言配置失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}