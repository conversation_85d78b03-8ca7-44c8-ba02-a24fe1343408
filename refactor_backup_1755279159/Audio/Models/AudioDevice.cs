using System;
using UnityEngine;

namespace DigitalHuman.Core.Audio.Models
{
    /// <summary>
    /// 音频设备信息模型
    /// </summary>
    [Serializable]
    public class AudioDevice
    {
        [SerializeField] private string deviceId;
        [SerializeField] private string deviceName;
        [SerializeField] private AudioDeviceType deviceType;
        [SerializeField] private bool isDefault;
        [SerializeField] private bool isActive;
        [SerializeField] private int sampleRate;
        [SerializeField] private int channels;

        /// <summary>
        /// 设备唯一标识符
        /// </summary>
        public string DeviceId 
        { 
            get => deviceId; 
            set => deviceId = value; 
        }

        /// <summary>
        /// 设备显示名称
        /// </summary>
        public string DeviceName 
        { 
            get => deviceName; 
            set => deviceName = value; 
        }

        /// <summary>
        /// 设备类型
        /// </summary>
        public AudioDeviceType DeviceType 
        { 
            get => deviceType; 
            set => deviceType = value; 
        }

        /// <summary>
        /// 是否为系统默认设备
        /// </summary>
        public bool IsDefault 
        { 
            get => isDefault; 
            set => isDefault = value; 
        }

        /// <summary>
        /// 是否为当前激活设备
        /// </summary>
        public bool IsActive 
        { 
            get => isActive; 
            set => isActive = value; 
        }

        /// <summary>
        /// 采样率
        /// </summary>
        public int SampleRate 
        { 
            get => sampleRate; 
            set => sampleRate = value; 
        }

        /// <summary>
        /// 声道数
        /// </summary>
        public int Channels 
        { 
            get => channels; 
            set => channels = value; 
        }

        public AudioDevice()
        {
            deviceId = string.Empty;
            deviceName = string.Empty;
            deviceType = AudioDeviceType.Unknown;
            isDefault = false;
            isActive = false;
            sampleRate = 44100;
            channels = 1;
        }

        public AudioDevice(string id, string name, AudioDeviceType type)
        {
            deviceId = id;
            deviceName = name;
            deviceType = type;
            isDefault = false;
            isActive = false;
            sampleRate = 44100;
            channels = type == AudioDeviceType.Microphone ? 1 : 2;
        }

        public override string ToString()
        {
            return $"{deviceName} ({deviceType}) - {sampleRate}Hz, {channels}ch";
        }
    }

    /// <summary>
    /// 音频设备类型枚举
    /// </summary>
    public enum AudioDeviceType
    {
        Unknown = 0,
        Microphone = 1,
        Speaker = 2,
        Headset = 3
    }
}