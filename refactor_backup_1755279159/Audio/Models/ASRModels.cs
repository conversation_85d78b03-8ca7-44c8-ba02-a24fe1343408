using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Audio.Models
{
    /// <summary>
    /// ASR请求模型
    /// </summary>
    [Serializable]
    public class ASRRequest
    {
        [SerializeField] private string audioData;
        [SerializeField] private string audioFormat;
        [SerializeField] private int sampleRate;
        [SerializeField] private string language;
        [SerializeField] private bool enablePunctuation;
        [SerializeField] private bool enableWordTimestamp;

        /// <summary>
        /// 音频数据（Base64编码）
        /// </summary>
        public string AudioData 
        { 
            get => audioData; 
            set => audioData = value; 
        }

        /// <summary>
        /// 音频格式（wav, mp3, pcm等）
        /// </summary>
        public string AudioFormat 
        { 
            get => audioFormat; 
            set => audioFormat = value; 
        }

        /// <summary>
        /// 采样率
        /// </summary>
        public int SampleRate 
        { 
            get => sampleRate; 
            set => sampleRate = value; 
        }

        /// <summary>
        /// 识别语言
        /// </summary>
        public string Language 
        { 
            get => language; 
            set => language = value; 
        }

        /// <summary>
        /// 是否启用标点符号
        /// </summary>
        public bool EnablePunctuation 
        { 
            get => enablePunctuation; 
            set => enablePunctuation = value; 
        }

        /// <summary>
        /// 是否启用词级时间戳
        /// </summary>
        public bool EnableWordTimestamp 
        { 
            get => enableWordTimestamp; 
            set => enableWordTimestamp = value; 
        }

        public ASRRequest()
        {
            audioData = string.Empty;
            audioFormat = "wav";
            sampleRate = 16000;
            language = "zh-CN";
            enablePunctuation = true;
            enableWordTimestamp = false;
        }
    }

    /// <summary>
    /// ASR响应模型
    /// </summary>
    [Serializable]
    public class ASRResponse
    {
        [SerializeField] private bool success;
        [SerializeField] private string text;
        [SerializeField] private float confidence;
        [SerializeField] private List<WordTimestamp> wordTimestamps;
        [SerializeField] private string errorMessage;
        [SerializeField] private string requestId;

        /// <summary>
        /// 识别是否成功
        /// </summary>
        public bool Success 
        { 
            get => success; 
            set => success = value; 
        }

        /// <summary>
        /// 识别结果文本
        /// </summary>
        public string Text 
        { 
            get => text; 
            set => text = value; 
        }

        /// <summary>
        /// 识别置信度 (0.0 - 1.0)
        /// </summary>
        public float Confidence 
        { 
            get => confidence; 
            set => confidence = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 词级时间戳列表
        /// </summary>
        public List<WordTimestamp> WordTimestamps 
        { 
            get => wordTimestamps ??= new List<WordTimestamp>(); 
            set => wordTimestamps = value; 
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage 
        { 
            get => errorMessage; 
            set => errorMessage = value; 
        }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId 
        { 
            get => requestId; 
            set => requestId = value; 
        }

        public ASRResponse()
        {
            success = false;
            text = string.Empty;
            confidence = 0f;
            wordTimestamps = new List<WordTimestamp>();
            errorMessage = string.Empty;
            requestId = string.Empty;
        }
    }

    /// <summary>
    /// 词级时间戳模型
    /// </summary>
    [Serializable]
    public class WordTimestamp
    {
        [SerializeField] private string word;
        [SerializeField] private float startTime;
        [SerializeField] private float endTime;
        [SerializeField] private float confidence;

        /// <summary>
        /// 词语
        /// </summary>
        public string Word 
        { 
            get => word; 
            set => word = value; 
        }

        /// <summary>
        /// 开始时间（秒）
        /// </summary>
        public float StartTime 
        { 
            get => startTime; 
            set => startTime = value; 
        }

        /// <summary>
        /// 结束时间（秒）
        /// </summary>
        public float EndTime 
        { 
            get => endTime; 
            set => endTime = value; 
        }

        /// <summary>
        /// 词语置信度
        /// </summary>
        public float Confidence 
        { 
            get => confidence; 
            set => confidence = Mathf.Clamp01(value); 
        }

        public WordTimestamp()
        {
            word = string.Empty;
            startTime = 0f;
            endTime = 0f;
            confidence = 0f;
        }

        public WordTimestamp(string word, float startTime, float endTime, float confidence = 1f)
        {
            this.word = word;
            this.startTime = startTime;
            this.endTime = endTime;
            this.confidence = Mathf.Clamp01(confidence);
        }
    }

    /// <summary>
    /// ASR配置模型
    /// </summary>
    [Serializable]
    public class ASRConfiguration
    {
        [SerializeField] private string apiEndpoint;
        [SerializeField] private string apiKey;
        [SerializeField] private string defaultLanguage;
        [SerializeField] private bool enableRealTimeRecognition;
        [SerializeField] private bool enablePunctuation;
        [SerializeField] private bool enableWordTimestamp;
        [SerializeField] private float confidenceThreshold;
        [SerializeField] private int maxRetryCount;
        [SerializeField] private int timeoutSeconds;
        [SerializeField] private List<string> supportedLanguages;

        /// <summary>
        /// API端点URL
        /// </summary>
        public string ApiEndpoint 
        { 
            get => apiEndpoint; 
            set => apiEndpoint = value; 
        }

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey 
        { 
            get => apiKey; 
            set => apiKey = value; 
        }

        /// <summary>
        /// 默认识别语言
        /// </summary>
        public string DefaultLanguage 
        { 
            get => defaultLanguage; 
            set => defaultLanguage = value; 
        }

        /// <summary>
        /// 是否启用实时识别
        /// </summary>
        public bool EnableRealTimeRecognition 
        { 
            get => enableRealTimeRecognition; 
            set => enableRealTimeRecognition = value; 
        }

        /// <summary>
        /// 是否启用标点符号
        /// </summary>
        public bool EnablePunctuation 
        { 
            get => enablePunctuation; 
            set => enablePunctuation = value; 
        }

        /// <summary>
        /// 是否启用词级时间戳
        /// </summary>
        public bool EnableWordTimestamp 
        { 
            get => enableWordTimestamp; 
            set => enableWordTimestamp = value; 
        }

        /// <summary>
        /// 置信度阈值
        /// </summary>
        public float ConfidenceThreshold 
        { 
            get => confidenceThreshold; 
            set => confidenceThreshold = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount 
        { 
            get => maxRetryCount; 
            set => maxRetryCount = Mathf.Max(0, value); 
        }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds 
        { 
            get => timeoutSeconds; 
            set => timeoutSeconds = Mathf.Max(1, value); 
        }

        /// <summary>
        /// 支持的语言列表
        /// </summary>
        public List<string> SupportedLanguages 
        { 
            get => supportedLanguages ??= new List<string>(); 
            set => supportedLanguages = value; 
        }

        public ASRConfiguration()
        {
            apiEndpoint = string.Empty;
            apiKey = string.Empty;
            defaultLanguage = "zh-CN";
            enableRealTimeRecognition = true;
            enablePunctuation = true;
            enableWordTimestamp = false;
            confidenceThreshold = 0.6f;
            maxRetryCount = 3;
            timeoutSeconds = 30;
            supportedLanguages = new List<string> { "zh-CN", "en-US", "ja-JP" };
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(apiEndpoint) &&
                   !string.IsNullOrEmpty(defaultLanguage) &&
                   confidenceThreshold >= 0 &&
                   maxRetryCount >= 0 &&
                   timeoutSeconds > 0;
        }
    }

    /// <summary>
    /// 实时识别状态
    /// </summary>
    public enum RealTimeRecognitionState
    {
        Idle = 0,
        Listening = 1,
        Processing = 2,
        Completed = 3,
        Error = 4
    }

    /// <summary>
    /// 实时识别结果
    /// </summary>
    [Serializable]
    public class RealTimeASRResult
    {
        [SerializeField] private string partialText;
        [SerializeField] private string finalText;
        [SerializeField] private bool isFinal;
        [SerializeField] private float confidence;
        [SerializeField] private RealTimeRecognitionState state;

        /// <summary>
        /// 部分识别结果
        /// </summary>
        public string PartialText 
        { 
            get => partialText; 
            set => partialText = value; 
        }

        /// <summary>
        /// 最终识别结果
        /// </summary>
        public string FinalText 
        { 
            get => finalText; 
            set => finalText = value; 
        }

        /// <summary>
        /// 是否为最终结果
        /// </summary>
        public bool IsFinal 
        { 
            get => isFinal; 
            set => isFinal = value; 
        }

        /// <summary>
        /// 识别置信度
        /// </summary>
        public float Confidence 
        { 
            get => confidence; 
            set => confidence = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 识别状态
        /// </summary>
        public RealTimeRecognitionState State 
        { 
            get => state; 
            set => state = value; 
        }

        public RealTimeASRResult()
        {
            partialText = string.Empty;
            finalText = string.Empty;
            isFinal = false;
            confidence = 0f;
            state = RealTimeRecognitionState.Idle;
        }
    }
}