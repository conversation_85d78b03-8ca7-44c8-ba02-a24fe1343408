using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Audio.Models
{
    /// <summary>
    /// 语音控制词类型
    /// </summary>
    public enum VoiceControlType
    {
        WakeWord = 0,      // 唤醒词
        InterruptWord = 1, // 打断词
        TerminateWord = 2, // 终止词
        LanguageSwitch = 3 // 语言切换词
    }

    /// <summary>
    /// 语音控制词模型
    /// </summary>
    [Serializable]
    public class VoiceControlWord
    {
        [SerializeField] private string word;
        [SerializeField] private VoiceControlType type;
        [SerializeField] private string language;
        [SerializeField] private float confidence;
        [SerializeField] private bool isEnabled;
        [SerializeField] private string description;
        [SerializeField] private List<string> alternatives;

        /// <summary>
        /// 控制词文本
        /// </summary>
        public string Word 
        { 
            get => word; 
            set => word = value; 
        }

        /// <summary>
        /// 控制词类型
        /// </summary>
        public VoiceControlType Type 
        { 
            get => type; 
            set => type = value; 
        }

        /// <summary>
        /// 适用语言
        /// </summary>
        public string Language 
        { 
            get => language; 
            set => language = value; 
        }

        /// <summary>
        /// 识别置信度阈值
        /// </summary>
        public float Confidence 
        { 
            get => confidence; 
            set => confidence = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled 
        { 
            get => isEnabled; 
            set => isEnabled = value; 
        }

        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description 
        { 
            get => description; 
            set => description = value; 
        }

        /// <summary>
        /// 替代词列表
        /// </summary>
        public List<string> Alternatives 
        { 
            get => alternatives ??= new List<string>(); 
            set => alternatives = value; 
        }

        public VoiceControlWord()
        {
            word = string.Empty;
            type = VoiceControlType.WakeWord;
            language = "zh-CN";
            confidence = 0.8f;
            isEnabled = true;
            description = string.Empty;
            alternatives = new List<string>();
        }

        public VoiceControlWord(string word, VoiceControlType type, string language = "zh-CN")
        {
            this.word = word;
            this.type = type;
            this.language = language;
            confidence = 0.8f;
            isEnabled = true;
            description = string.Empty;
            alternatives = new List<string>();
        }

        /// <summary>
        /// 检查是否匹配指定文本
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <param name="minConfidence">最小置信度</param>
        /// <returns>是否匹配</returns>
        public bool IsMatch(string text, float minConfidence = 0.8f)
        {
            if (!isEnabled || string.IsNullOrEmpty(text) || confidence < minConfidence)
            {
                return false;
            }

            // 检查主词
            if (text.Contains(word, StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            // 检查替代词
            foreach (var alternative in alternatives)
            {
                if (!string.IsNullOrEmpty(alternative) && 
                    text.Contains(alternative, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        public override string ToString()
        {
            return $"{word} ({type}, {language})";
        }
    }

    /// <summary>
    /// 语音控制配置
    /// </summary>
    [Serializable]
    public class VoiceControlConfiguration
    {
        [SerializeField] private bool enableVoiceControl;
        [SerializeField] private float globalConfidenceThreshold;
        [SerializeField] private float wakeWordTimeout;
        [SerializeField] private float listeningTimeout;
        [SerializeField] private bool enableContinuousListening;
        [SerializeField] private List<VoiceControlWord> controlWords;
        [SerializeField] private Dictionary<string, string> languageSwitchResponses;

        /// <summary>
        /// 是否启用语音控制
        /// </summary>
        public bool EnableVoiceControl 
        { 
            get => enableVoiceControl; 
            set => enableVoiceControl = value; 
        }

        /// <summary>
        /// 全局置信度阈值
        /// </summary>
        public float GlobalConfidenceThreshold 
        { 
            get => globalConfidenceThreshold; 
            set => globalConfidenceThreshold = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 唤醒词超时时间（秒）
        /// </summary>
        public float WakeWordTimeout 
        { 
            get => wakeWordTimeout; 
            set => wakeWordTimeout = Mathf.Max(0f, value); 
        }

        /// <summary>
        /// 监听超时时间（秒）
        /// </summary>
        public float ListeningTimeout 
        { 
            get => listeningTimeout; 
            set => listeningTimeout = Mathf.Max(0f, value); 
        }

        /// <summary>
        /// 是否启用连续监听
        /// </summary>
        public bool EnableContinuousListening 
        { 
            get => enableContinuousListening; 
            set => enableContinuousListening = value; 
        }

        /// <summary>
        /// 控制词列表
        /// </summary>
        public List<VoiceControlWord> ControlWords 
        { 
            get => controlWords ??= new List<VoiceControlWord>(); 
            set => controlWords = value; 
        }

        /// <summary>
        /// 语言切换响应词典
        /// </summary>
        public Dictionary<string, string> LanguageSwitchResponses 
        { 
            get => languageSwitchResponses ??= new Dictionary<string, string>(); 
            set => languageSwitchResponses = value; 
        }

        public VoiceControlConfiguration()
        {
            enableVoiceControl = true;
            globalConfidenceThreshold = 0.8f;
            wakeWordTimeout = 5f;
            listeningTimeout = 10f;
            enableContinuousListening = false;
            controlWords = new List<VoiceControlWord>();
            languageSwitchResponses = new Dictionary<string, string>();
            
            InitializeDefaultControlWords();
            InitializeDefaultResponses();
        }

        /// <summary>
        /// 初始化默认控制词
        /// </summary>
        private void InitializeDefaultControlWords()
        {
            // 中文控制词
            controlWords.Add(new VoiceControlWord("你好", VoiceControlType.WakeWord, "zh-CN")
            {
                Description = "中文唤醒词",
                Alternatives = new List<string> { "小助手", "开始对话" }
            });

            controlWords.Add(new VoiceControlWord("停止", VoiceControlType.InterruptWord, "zh-CN")
            {
                Description = "中文打断词",
                Alternatives = new List<string> { "暂停", "等等" }
            });

            controlWords.Add(new VoiceControlWord("结束", VoiceControlType.TerminateWord, "zh-CN")
            {
                Description = "中文终止词",
                Alternatives = new List<string> { "再见", "退出" }
            });

            // 英文控制词
            controlWords.Add(new VoiceControlWord("hello", VoiceControlType.WakeWord, "en-US")
            {
                Description = "英文唤醒词",
                Alternatives = new List<string> { "hi", "hey" }
            });

            controlWords.Add(new VoiceControlWord("stop", VoiceControlType.InterruptWord, "en-US")
            {
                Description = "英文打断词",
                Alternatives = new List<string> { "pause", "wait" }
            });

            controlWords.Add(new VoiceControlWord("goodbye", VoiceControlType.TerminateWord, "en-US")
            {
                Description = "英文终止词",
                Alternatives = new List<string> { "bye", "exit" }
            });

            // 语言切换词
            controlWords.Add(new VoiceControlWord("你好", VoiceControlType.LanguageSwitch, "zh-CN")
            {
                Description = "切换到中文模式"
            });

            controlWords.Add(new VoiceControlWord("hi", VoiceControlType.LanguageSwitch, "en-US")
            {
                Description = "切换到英文模式"
            });
        }

        /// <summary>
        /// 初始化默认响应
        /// </summary>
        private void InitializeDefaultResponses()
        {
            languageSwitchResponses["zh-CN"] = "您好，我现在使用中文与您对话。";
            languageSwitchResponses["en-US"] = "Hello, I'm now speaking English with you.";
            languageSwitchResponses["ja-JP"] = "こんにちは、日本語でお話しします。";
        }

        /// <summary>
        /// 获取指定类型的控制词
        /// </summary>
        /// <param name="type">控制词类型</param>
        /// <param name="language">语言，为空则返回所有语言</param>
        /// <returns>控制词列表</returns>
        public List<VoiceControlWord> GetControlWordsByType(VoiceControlType type, string language = null)
        {
            return controlWords.FindAll(w => 
                w.Type == type && 
                w.IsEnabled && 
                (string.IsNullOrEmpty(language) || w.Language.Equals(language, StringComparison.OrdinalIgnoreCase)));
        }

        /// <summary>
        /// 添加控制词
        /// </summary>
        /// <param name="controlWord">控制词</param>
        public void AddControlWord(VoiceControlWord controlWord)
        {
            if (controlWord != null && !string.IsNullOrEmpty(controlWord.Word))
            {
                controlWords.Add(controlWord);
            }
        }

        /// <summary>
        /// 移除控制词
        /// </summary>
        /// <param name="word">控制词文本</param>
        /// <param name="type">控制词类型</param>
        /// <param name="language">语言</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveControlWord(string word, VoiceControlType type, string language)
        {
            return controlWords.RemoveAll(w => 
                w.Word.Equals(word, StringComparison.OrdinalIgnoreCase) && 
                w.Type == type && 
                w.Language.Equals(language, StringComparison.OrdinalIgnoreCase)) > 0;
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            return globalConfidenceThreshold >= 0 && 
                   wakeWordTimeout >= 0 && 
                   listeningTimeout >= 0 &&
                   controlWords != null;
        }
    }

    /// <summary>
    /// 语音控制状态
    /// </summary>
    public enum VoiceControlState
    {
        Idle = 0,           // 空闲状态
        WaitingForWakeWord = 1, // 等待唤醒词
        Listening = 2,      // 监听中
        Processing = 3,     // 处理中
        Responding = 4,     // 响应中
        Interrupted = 5,    // 被打断
        Terminated = 6      // 已终止
    }

    /// <summary>
    /// 语音控制事件数据
    /// </summary>
    [Serializable]
    public class VoiceControlEvent
    {
        [SerializeField] private VoiceControlType controlType;
        [SerializeField] private string recognizedText;
        [SerializeField] private string matchedWord;
        [SerializeField] private float confidence;
        [SerializeField] private string language;
        [SerializeField] private DateTime timestamp;

        /// <summary>
        /// 控制类型
        /// </summary>
        public VoiceControlType ControlType 
        { 
            get => controlType; 
            set => controlType = value; 
        }

        /// <summary>
        /// 识别的文本
        /// </summary>
        public string RecognizedText 
        { 
            get => recognizedText; 
            set => recognizedText = value; 
        }

        /// <summary>
        /// 匹配的控制词
        /// </summary>
        public string MatchedWord 
        { 
            get => matchedWord; 
            set => matchedWord = value; 
        }

        /// <summary>
        /// 识别置信度
        /// </summary>
        public float Confidence 
        { 
            get => confidence; 
            set => confidence = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 语言
        /// </summary>
        public string Language 
        { 
            get => language; 
            set => language = value; 
        }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp 
        { 
            get => timestamp; 
            set => timestamp = value; 
        }

        public VoiceControlEvent()
        {
            controlType = VoiceControlType.WakeWord;
            recognizedText = string.Empty;
            matchedWord = string.Empty;
            confidence = 0f;
            language = "zh-CN";
            timestamp = DateTime.Now;
        }

        public VoiceControlEvent(VoiceControlType type, string text, string word, float confidence, string language)
        {
            controlType = type;
            recognizedText = text;
            matchedWord = word;
            this.confidence = confidence;
            this.language = language;
            timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 对话状态管理
    /// </summary>
    [Serializable]
    public class ConversationState
    {
        [SerializeField] private bool isActive;
        [SerializeField] private string currentLanguage;
        [SerializeField] private DateTime lastActivityTime;
        [SerializeField] private VoiceControlState controlState;
        [SerializeField] private int turnCount;
        [SerializeField] private List<string> conversationHistory;

        /// <summary>
        /// 对话是否激活
        /// </summary>
        public bool IsActive 
        { 
            get => isActive; 
            set => isActive = value; 
        }

        /// <summary>
        /// 当前语言
        /// </summary>
        public string CurrentLanguage 
        { 
            get => currentLanguage; 
            set => currentLanguage = value; 
        }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime 
        { 
            get => lastActivityTime; 
            set => lastActivityTime = value; 
        }

        /// <summary>
        /// 控制状态
        /// </summary>
        public VoiceControlState ControlState 
        { 
            get => controlState; 
            set => controlState = value; 
        }

        /// <summary>
        /// 对话轮次计数
        /// </summary>
        public int TurnCount 
        { 
            get => turnCount; 
            set => turnCount = Mathf.Max(0, value); 
        }

        /// <summary>
        /// 对话历史
        /// </summary>
        public List<string> ConversationHistory 
        { 
            get => conversationHistory ??= new List<string>(); 
            set => conversationHistory = value; 
        }

        public ConversationState()
        {
            isActive = false;
            currentLanguage = "zh-CN";
            lastActivityTime = DateTime.Now;
            controlState = VoiceControlState.Idle;
            turnCount = 0;
            conversationHistory = new List<string>();
        }

        /// <summary>
        /// 更新活动时间
        /// </summary>
        public void UpdateActivity()
        {
            lastActivityTime = DateTime.Now;
        }

        /// <summary>
        /// 检查是否超时
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <returns>是否超时</returns>
        public bool IsTimeout(float timeoutSeconds)
        {
            return (DateTime.Now - lastActivityTime).TotalSeconds > timeoutSeconds;
        }

        /// <summary>
        /// 重置对话状态
        /// </summary>
        public void Reset()
        {
            isActive = false;
            controlState = VoiceControlState.Idle;
            turnCount = 0;
            conversationHistory.Clear();
            UpdateActivity();
        }

        /// <summary>
        /// 添加对话记录
        /// </summary>
        /// <param name="message">消息内容</param>
        public void AddConversationRecord(string message)
        {
            if (!string.IsNullOrEmpty(message))
            {
                conversationHistory.Add($"[{DateTime.Now:HH:mm:ss}] {message}");
                
                // 限制历史记录数量
                if (conversationHistory.Count > 100)
                {
                    conversationHistory.RemoveAt(0);
                }
            }
        }
    }
}