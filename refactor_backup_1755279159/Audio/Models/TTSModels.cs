using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Audio.Models
{
    /// <summary>
    /// TTS请求模型
    /// </summary>
    [Serializable]
    public class TTSRequest
    {
        [SerializeField] private string text;
        [SerializeField] private string voice;
        [SerializeField] private string language;
        [SerializeField] private float speed;
        [SerializeField] private float pitch;
        [SerializeField] private float volume;
        [SerializeField] private string audioFormat;
        [SerializeField] private int sampleRate;
        [SerializeField] private bool enableSSML;

        /// <summary>
        /// 要合成的文本
        /// </summary>
        public string Text 
        { 
            get => text; 
            set => text = value; 
        }

        /// <summary>
        /// 语音音色ID
        /// </summary>
        public string Voice 
        { 
            get => voice; 
            set => voice = value; 
        }

        /// <summary>
        /// 语言代码
        /// </summary>
        public string Language 
        { 
            get => language; 
            set => language = value; 
        }

        /// <summary>
        /// 语速 (0.5 - 2.0)
        /// </summary>
        public float Speed 
        { 
            get => speed; 
            set => speed = Mathf.Clamp(value, 0.5f, 2.0f); 
        }

        /// <summary>
        /// 音调 (0.5 - 2.0)
        /// </summary>
        public float Pitch 
        { 
            get => pitch; 
            set => pitch = Mathf.Clamp(value, 0.5f, 2.0f); 
        }

        /// <summary>
        /// 音量 (0.0 - 1.0)
        /// </summary>
        public float Volume 
        { 
            get => volume; 
            set => volume = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 音频格式
        /// </summary>
        public string AudioFormat 
        { 
            get => audioFormat; 
            set => audioFormat = value; 
        }

        /// <summary>
        /// 采样率
        /// </summary>
        public int SampleRate 
        { 
            get => sampleRate; 
            set => sampleRate = value; 
        }

        /// <summary>
        /// 是否启用SSML
        /// </summary>
        public bool EnableSSML 
        { 
            get => enableSSML; 
            set => enableSSML = value; 
        }

        public TTSRequest()
        {
            text = string.Empty;
            voice = "default";
            language = "zh-CN";
            speed = 1.0f;
            pitch = 1.0f;
            volume = 1.0f;
            audioFormat = "wav";
            sampleRate = 44100;
            enableSSML = false;
        }
    }

    /// <summary>
    /// TTS响应模型
    /// </summary>
    [Serializable]
    public class TTSResponse
    {
        [SerializeField] private bool success;
        [SerializeField] private string audioData;
        [SerializeField] private string audioFormat;
        [SerializeField] private int sampleRate;
        [SerializeField] private float duration;
        [SerializeField] private string errorMessage;
        [SerializeField] private string requestId;

        /// <summary>
        /// 合成是否成功
        /// </summary>
        public bool Success 
        { 
            get => success; 
            set => success = value; 
        }

        /// <summary>
        /// 音频数据（Base64编码）
        /// </summary>
        public string AudioData 
        { 
            get => audioData; 
            set => audioData = value; 
        }

        /// <summary>
        /// 音频格式
        /// </summary>
        public string AudioFormat 
        { 
            get => audioFormat; 
            set => audioFormat = value; 
        }

        /// <summary>
        /// 采样率
        /// </summary>
        public int SampleRate 
        { 
            get => sampleRate; 
            set => sampleRate = value; 
        }

        /// <summary>
        /// 音频时长（秒）
        /// </summary>
        public float Duration 
        { 
            get => duration; 
            set => duration = Mathf.Max(0f, value); 
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage 
        { 
            get => errorMessage; 
            set => errorMessage = value; 
        }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId 
        { 
            get => requestId; 
            set => requestId = value; 
        }

        public TTSResponse()
        {
            success = false;
            audioData = string.Empty;
            audioFormat = "wav";
            sampleRate = 44100;
            duration = 0f;
            errorMessage = string.Empty;
            requestId = string.Empty;
        }
    }

    /// <summary>
    /// 语音音色信息
    /// </summary>
    [Serializable]
    public class VoiceInfo
    {
        [SerializeField] private string voiceId;
        [SerializeField] private string voiceName;
        [SerializeField] private string language;
        [SerializeField] private string gender;
        [SerializeField] private string description;
        [SerializeField] private bool isDefault;
        [SerializeField] private bool isAvailable;

        /// <summary>
        /// 音色ID
        /// </summary>
        public string VoiceId 
        { 
            get => voiceId; 
            set => voiceId = value; 
        }

        /// <summary>
        /// 音色名称
        /// </summary>
        public string VoiceName 
        { 
            get => voiceName; 
            set => voiceName = value; 
        }

        /// <summary>
        /// 支持的语言
        /// </summary>
        public string Language 
        { 
            get => language; 
            set => language = value; 
        }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender 
        { 
            get => gender; 
            set => gender = value; 
        }

        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description 
        { 
            get => description; 
            set => description = value; 
        }

        /// <summary>
        /// 是否为默认音色
        /// </summary>
        public bool IsDefault 
        { 
            get => isDefault; 
            set => isDefault = value; 
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable 
        { 
            get => isAvailable; 
            set => isAvailable = value; 
        }

        public VoiceInfo()
        {
            voiceId = string.Empty;
            voiceName = string.Empty;
            language = "zh-CN";
            gender = "Female";
            description = string.Empty;
            isDefault = false;
            isAvailable = true;
        }

        public VoiceInfo(string id, string name, string language, string gender)
        {
            voiceId = id;
            voiceName = name;
            this.language = language;
            this.gender = gender;
            description = string.Empty;
            isDefault = false;
            isAvailable = true;
        }

        public override string ToString()
        {
            return $"{voiceName} ({language}, {gender})";
        }
    }

    /// <summary>
    /// TTS配置模型
    /// </summary>
    [Serializable]
    public class TTSConfiguration
    {
        [SerializeField] private string apiEndpoint;
        [SerializeField] private string apiKey;
        [SerializeField] private string defaultVoice;
        [SerializeField] private string defaultLanguage;
        [SerializeField] private float defaultSpeed;
        [SerializeField] private float defaultPitch;
        [SerializeField] private float defaultVolume;
        [SerializeField] private string audioFormat;
        [SerializeField] private int sampleRate;
        [SerializeField] private bool enableSSML;
        [SerializeField] private int maxRetryCount;
        [SerializeField] private int timeoutSeconds;
        [SerializeField] private List<VoiceInfo> availableVoices;

        /// <summary>
        /// API端点URL
        /// </summary>
        public string ApiEndpoint 
        { 
            get => apiEndpoint; 
            set => apiEndpoint = value; 
        }

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey 
        { 
            get => apiKey; 
            set => apiKey = value; 
        }

        /// <summary>
        /// 默认音色
        /// </summary>
        public string DefaultVoice 
        { 
            get => defaultVoice; 
            set => defaultVoice = value; 
        }

        /// <summary>
        /// 默认语言
        /// </summary>
        public string DefaultLanguage 
        { 
            get => defaultLanguage; 
            set => defaultLanguage = value; 
        }

        /// <summary>
        /// 默认语速
        /// </summary>
        public float DefaultSpeed 
        { 
            get => defaultSpeed; 
            set => defaultSpeed = Mathf.Clamp(value, 0.5f, 2.0f); 
        }

        /// <summary>
        /// 默认音调
        /// </summary>
        public float DefaultPitch 
        { 
            get => defaultPitch; 
            set => defaultPitch = Mathf.Clamp(value, 0.5f, 2.0f); 
        }

        /// <summary>
        /// 默认音量
        /// </summary>
        public float DefaultVolume 
        { 
            get => defaultVolume; 
            set => defaultVolume = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 音频格式
        /// </summary>
        public string AudioFormat 
        { 
            get => audioFormat; 
            set => audioFormat = value; 
        }

        /// <summary>
        /// 采样率
        /// </summary>
        public int SampleRate 
        { 
            get => sampleRate; 
            set => sampleRate = value; 
        }

        /// <summary>
        /// 是否启用SSML
        /// </summary>
        public bool EnableSSML 
        { 
            get => enableSSML; 
            set => enableSSML = value; 
        }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount 
        { 
            get => maxRetryCount; 
            set => maxRetryCount = Mathf.Max(0, value); 
        }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds 
        { 
            get => timeoutSeconds; 
            set => timeoutSeconds = Mathf.Max(1, value); 
        }

        /// <summary>
        /// 可用音色列表
        /// </summary>
        public List<VoiceInfo> AvailableVoices 
        { 
            get => availableVoices ??= new List<VoiceInfo>(); 
            set => availableVoices = value; 
        }

        public TTSConfiguration()
        {
            apiEndpoint = string.Empty;
            apiKey = string.Empty;
            defaultVoice = "default";
            defaultLanguage = "zh-CN";
            defaultSpeed = 1.0f;
            defaultPitch = 1.0f;
            defaultVolume = 1.0f;
            audioFormat = "wav";
            sampleRate = 44100;
            enableSSML = false;
            maxRetryCount = 3;
            timeoutSeconds = 30;
            availableVoices = new List<VoiceInfo>();
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(apiEndpoint) &&
                   !string.IsNullOrEmpty(defaultLanguage) &&
                   defaultSpeed > 0 &&
                   defaultPitch > 0 &&
                   defaultVolume >= 0 &&
                   sampleRate > 0 &&
                   maxRetryCount >= 0 &&
                   timeoutSeconds > 0;
        }

        /// <summary>
        /// 获取指定语言的音色列表
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>音色列表</returns>
        public List<VoiceInfo> GetVoicesByLanguage(string language)
        {
            if (string.IsNullOrEmpty(language))
            {
                return new List<VoiceInfo>(availableVoices);
            }

            return availableVoices.FindAll(v => v.Language.Equals(language, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取默认音色信息
        /// </summary>
        /// <returns>默认音色信息</returns>
        public VoiceInfo GetDefaultVoice()
        {
            return availableVoices.Find(v => v.VoiceId == defaultVoice) ?? 
                   availableVoices.Find(v => v.IsDefault) ?? 
                   (availableVoices.Count > 0 ? availableVoices[0] : null);
        }
    }

    /// <summary>
    /// TTS播放状态
    /// </summary>
    public enum TTSPlaybackState
    {
        Idle = 0,
        Synthesizing = 1,
        Playing = 2,
        Paused = 3,
        Completed = 4,
        Error = 5
    }

    /// <summary>
    /// TTS播放事件数据
    /// </summary>
    [Serializable]
    public class TTSPlaybackEvent
    {
        [SerializeField] private TTSPlaybackState state;
        [SerializeField] private float currentTime;
        [SerializeField] private float totalTime;
        [SerializeField] private string currentText;
        [SerializeField] private int currentWordIndex;

        /// <summary>
        /// 播放状态
        /// </summary>
        public TTSPlaybackState State 
        { 
            get => state; 
            set => state = value; 
        }

        /// <summary>
        /// 当前播放时间
        /// </summary>
        public float CurrentTime 
        { 
            get => currentTime; 
            set => currentTime = Mathf.Max(0f, value); 
        }

        /// <summary>
        /// 总播放时间
        /// </summary>
        public float TotalTime 
        { 
            get => totalTime; 
            set => totalTime = Mathf.Max(0f, value); 
        }

        /// <summary>
        /// 当前播放的文本
        /// </summary>
        public string CurrentText 
        { 
            get => currentText; 
            set => currentText = value; 
        }

        /// <summary>
        /// 当前词语索引
        /// </summary>
        public int CurrentWordIndex 
        { 
            get => currentWordIndex; 
            set => currentWordIndex = Mathf.Max(0, value); 
        }

        public TTSPlaybackEvent()
        {
            state = TTSPlaybackState.Idle;
            currentTime = 0f;
            totalTime = 0f;
            currentText = string.Empty;
            currentWordIndex = 0;
        }
    }
}