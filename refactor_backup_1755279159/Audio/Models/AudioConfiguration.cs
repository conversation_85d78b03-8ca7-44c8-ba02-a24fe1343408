using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Audio.Models
{
    /// <summary>
    /// 音频配置模型
    /// </summary>
    [Serializable]
    public class AudioConfiguration
    {
        [SerializeField] private string activeMicrophoneId;
        [SerializeField] private string activeSpeakerId;
        [SerializeField] private float microphoneVolume;
        [SerializeField] private float speakerVolume;
        [SerializeField] private bool enableNoiseReduction;
        [SerializeField] private bool enableEchoCancellation;
        [SerializeField] private bool enableAutoGainControl;
        [SerializeField] private int recordingSampleRate;
        [SerializeField] private int playbackSampleRate;
        [SerializeField] private float noiseReductionLevel;
        [SerializeField] private List<string> preferredMicrophones;
        [SerializeField] private List<string> preferredSpeakers;

        /// <summary>
        /// 当前激活的麦克风设备ID
        /// </summary>
        public string ActiveMicrophoneId 
        { 
            get => activeMicrophoneId; 
            set => activeMicrophoneId = value; 
        }

        /// <summary>
        /// 当前激活的扬声器设备ID
        /// </summary>
        public string ActiveSpeakerId 
        { 
            get => activeSpeakerId; 
            set => activeSpeakerId = value; 
        }

        /// <summary>
        /// 麦克风音量 (0.0 - 1.0)
        /// </summary>
        public float MicrophoneVolume 
        { 
            get => microphoneVolume; 
            set => microphoneVolume = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 扬声器音量 (0.0 - 1.0)
        /// </summary>
        public float SpeakerVolume 
        { 
            get => speakerVolume; 
            set => speakerVolume = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 是否启用降噪功能
        /// </summary>
        public bool EnableNoiseReduction 
        { 
            get => enableNoiseReduction; 
            set => enableNoiseReduction = value; 
        }

        /// <summary>
        /// 是否启用回声消除
        /// </summary>
        public bool EnableEchoCancellation 
        { 
            get => enableEchoCancellation; 
            set => enableEchoCancellation = value; 
        }

        /// <summary>
        /// 是否启用自动增益控制
        /// </summary>
        public bool EnableAutoGainControl 
        { 
            get => enableAutoGainControl; 
            set => enableAutoGainControl = value; 
        }

        /// <summary>
        /// 录音采样率
        /// </summary>
        public int RecordingSampleRate 
        { 
            get => recordingSampleRate; 
            set => recordingSampleRate = value; 
        }

        /// <summary>
        /// 播放采样率
        /// </summary>
        public int PlaybackSampleRate 
        { 
            get => playbackSampleRate; 
            set => playbackSampleRate = value; 
        }

        /// <summary>
        /// 降噪级别 (0.0 - 1.0)
        /// </summary>
        public float NoiseReductionLevel 
        { 
            get => noiseReductionLevel; 
            set => noiseReductionLevel = Mathf.Clamp01(value); 
        }

        /// <summary>
        /// 首选麦克风设备列表
        /// </summary>
        public List<string> PreferredMicrophones 
        { 
            get => preferredMicrophones ??= new List<string>(); 
            set => preferredMicrophones = value; 
        }

        /// <summary>
        /// 首选扬声器设备列表
        /// </summary>
        public List<string> PreferredSpeakers 
        { 
            get => preferredSpeakers ??= new List<string>(); 
            set => preferredSpeakers = value; 
        }

        public AudioConfiguration()
        {
            activeMicrophoneId = string.Empty;
            activeSpeakerId = string.Empty;
            microphoneVolume = 0.8f;
            speakerVolume = 0.8f;
            enableNoiseReduction = true;
            enableEchoCancellation = true;
            enableAutoGainControl = true;
            recordingSampleRate = 16000;
            playbackSampleRate = 44100;
            noiseReductionLevel = 0.5f;
            preferredMicrophones = new List<string>();
            preferredSpeakers = new List<string>();
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return recordingSampleRate > 0 && 
                   playbackSampleRate > 0 && 
                   microphoneVolume >= 0 && 
                   speakerVolume >= 0;
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefault()
        {
            microphoneVolume = 0.8f;
            speakerVolume = 0.8f;
            enableNoiseReduction = true;
            enableEchoCancellation = true;
            enableAutoGainControl = true;
            recordingSampleRate = 16000;
            playbackSampleRate = 44100;
            noiseReductionLevel = 0.5f;
        }
    }
}