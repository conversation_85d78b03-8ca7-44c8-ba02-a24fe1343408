using System;
using System.Collections.Generic;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 多语言管理器接口
    /// </summary>
    public interface IMultiLanguageManager
    {
        /// <summary>
        /// 语言切换事件
        /// </summary>
        event Action<string, string> OnLanguageChanged; // (newLanguage, oldLanguage)

        /// <summary>
        /// 语言配置更新事件
        /// </summary>
        event Action<string> OnLanguageConfigurationUpdated; // language

        /// <summary>
        /// 多语言错误事件
        /// </summary>
        event Action<string> OnMultiLanguageError;

        /// <summary>
        /// 初始化多语言管理器
        /// </summary>
        /// <param name="supportedLanguages">支持的语言列表</param>
        /// <param name="defaultLanguage">默认语言</param>
        /// <returns>初始化是否成功</returns>
        bool Initialize(string[] supportedLanguages, string defaultLanguage = "zh-CN");

        /// <summary>
        /// 切换语言
        /// </summary>
        /// <param name="language">目标语言</param>
        /// <returns>切换是否成功</returns>
        bool SwitchLanguage(string language);

        /// <summary>
        /// 获取当前语言
        /// </summary>
        /// <returns>当前语言代码</returns>
        string GetCurrentLanguage();

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        /// <returns>支持的语言列表</returns>
        string[] GetSupportedLanguages();

        /// <summary>
        /// 检查是否支持指定语言
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>是否支持</returns>
        bool IsLanguageSupported(string language);

        /// <summary>
        /// 配置语言特定的ASR设置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="asrConfig">ASR配置</param>
        void ConfigureASRForLanguage(string language, ASRConfiguration asrConfig);

        /// <summary>
        /// 配置语言特定的TTS设置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="ttsConfig">TTS配置</param>
        void ConfigureTTSForLanguage(string language, TTSConfiguration ttsConfig);

        /// <summary>
        /// 获取指定语言的ASR配置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>ASR配置</returns>
        ASRConfiguration GetASRConfiguration(string language);

        /// <summary>
        /// 获取指定语言的TTS配置
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>TTS配置</returns>
        TTSConfiguration GetTTSConfiguration(string language);

        /// <summary>
        /// 设置语言激活词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="activationWords">激活词列表</param>
        void SetLanguageActivationWords(string language, string[] activationWords);

        /// <summary>
        /// 获取语言激活词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>激活词列表</returns>
        string[] GetLanguageActivationWords(string language);

        /// <summary>
        /// 设置语言响应词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="responseText">响应文本</param>
        void SetLanguageResponse(string language, string responseText);

        /// <summary>
        /// 获取语言响应词
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>响应文本</returns>
        string GetLanguageResponse(string language);

        /// <summary>
        /// 检测语言激活词
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <param name="confidence">置信度</param>
        /// <returns>检测到的语言，如果没有则返回null</returns>
        string DetectLanguageActivation(string text, float confidence);

        /// <summary>
        /// 获取语言显示名称
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>显示名称</returns>
        string GetLanguageDisplayName(string language);

        /// <summary>
        /// 添加支持的语言
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <param name="displayName">显示名称</param>
        /// <returns>添加是否成功</returns>
        bool AddSupportedLanguage(string language, string displayName = null);

        /// <summary>
        /// 移除支持的语言
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>移除是否成功</returns>
        bool RemoveSupportedLanguage(string language);

        /// <summary>
        /// 获取语言统计信息
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>使用统计</returns>
        LanguageUsageStats GetLanguageStats(string language);

        /// <summary>
        /// 重置语言统计
        /// </summary>
        /// <param name="language">语言代码，为空则重置所有</param>
        void ResetLanguageStats(string language = null);

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }

    /// <summary>
    /// 语言使用统计
    /// </summary>
    [Serializable]
    public class LanguageUsageStats
    {
        public string Language { get; set; }
        public int ActivationCount { get; set; }
        public int UsageCount { get; set; }
        public DateTime LastUsed { get; set; }
        public float TotalUsageTime { get; set; }

        public LanguageUsageStats()
        {
            Language = string.Empty;
            ActivationCount = 0;
            UsageCount = 0;
            LastUsed = DateTime.MinValue;
            TotalUsageTime = 0f;
        }
    }
}