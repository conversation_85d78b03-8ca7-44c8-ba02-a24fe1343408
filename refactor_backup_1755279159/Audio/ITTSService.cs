using System;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 文字转语音服务接口
    /// </summary>
    public interface ITTSService
    {
        /// <summary>
        /// 语音合成完成事件
        /// </summary>
        event Action<TTSResponse> OnSynthesisCompleted;

        /// <summary>
        /// 播放状态变化事件
        /// </summary>
        event Action<TTSPlaybackEvent> OnPlaybackStateChanged;

        /// <summary>
        /// 合成错误事件
        /// </summary>
        event Action<string> OnSynthesisError;

        /// <summary>
        /// 音色列表更新事件
        /// </summary>
        event Action<VoiceInfo[]> OnVoicesUpdated;

        /// <summary>
        /// 初始化TTS服务
        /// </summary>
        /// <param name="configuration">TTS配置</param>
        /// <returns>初始化是否成功</returns>
        bool Initialize(TTSConfiguration configuration);

        /// <summary>
        /// 合成语音
        /// </summary>
        /// <param name="text">要合成的文本</param>
        /// <param name="voiceId">音色ID，为空则使用默认音色</param>
        /// <param name="language">语言代码，为空则使用默认语言</param>
        /// <returns>合成结果</returns>
        Task<TTSResponse> SynthesizeAsync(string text, string voiceId = null, string language = null);

        /// <summary>
        /// 合成并播放语音
        /// </summary>
        /// <param name="text">要合成的文本</param>
        /// <param name="voiceId">音色ID</param>
        /// <param name="language">语言代码</param>
        /// <returns>合成是否成功</returns>
        Task<bool> SynthesizeAndPlayAsync(string text, string voiceId = null, string language = null);

        /// <summary>
        /// 播放音频剪辑
        /// </summary>
        /// <param name="audioClip">音频剪辑</param>
        /// <param name="text">对应的文本（用于同步显示）</param>
        void PlayAudio(AudioClip audioClip, string text = null);

        /// <summary>
        /// 暂停播放
        /// </summary>
        void PausePlayback();

        /// <summary>
        /// 恢复播放
        /// </summary>
        void ResumePlayback();

        /// <summary>
        /// 停止播放
        /// </summary>
        void StopPlayback();

        /// <summary>
        /// 设置播放音量
        /// </summary>
        /// <param name="volume">音量 (0.0 - 1.0)</param>
        void SetVolume(float volume);

        /// <summary>
        /// 设置播放速度
        /// </summary>
        /// <param name="speed">播放速度 (0.5 - 2.0)</param>
        void SetPlaybackSpeed(float speed);

        /// <summary>
        /// 获取可用音色列表
        /// </summary>
        /// <returns>音色列表</returns>
        VoiceInfo[] GetAvailableVoices();

        /// <summary>
        /// 获取指定语言的音色列表
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>音色列表</returns>
        VoiceInfo[] GetVoicesByLanguage(string language);

        /// <summary>
        /// 预览音色
        /// </summary>
        /// <param name="voiceId">音色ID</param>
        /// <param name="sampleText">示例文本</param>
        /// <returns>预览是否成功</returns>
        Task<bool> PreviewVoiceAsync(string voiceId, string sampleText = null);

        /// <summary>
        /// 刷新音色列表
        /// </summary>
        /// <returns>刷新是否成功</returns>
        Task<bool> RefreshVoicesAsync();

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns>TTS配置</returns>
        TTSConfiguration GetConfiguration();

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        void UpdateConfiguration(TTSConfiguration configuration);

        /// <summary>
        /// 测试服务连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// 获取当前播放状态
        /// </summary>
        /// <returns>播放状态</returns>
        TTSPlaybackState GetPlaybackState();

        /// <summary>
        /// 获取播放进度
        /// </summary>
        /// <returns>播放进度 (0.0 - 1.0)</returns>
        float GetPlaybackProgress();

        /// <summary>
        /// 取消当前合成任务
        /// </summary>
        void CancelSynthesis();

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }
}