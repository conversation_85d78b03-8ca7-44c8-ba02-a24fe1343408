using System;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 语音控制器接口
    /// </summary>
    public interface IVoiceController
    {
        /// <summary>
        /// 唤醒词检测事件
        /// </summary>
        event Action<VoiceControlEvent> OnWakeWordDetected;

        /// <summary>
        /// 打断词检测事件
        /// </summary>
        event Action<VoiceControlEvent> OnInterruptWordDetected;

        /// <summary>
        /// 终止词检测事件
        /// </summary>
        event Action<VoiceControlEvent> OnTerminateWordDetected;

        /// <summary>
        /// 语言切换事件
        /// </summary>
        event Action<string, string> OnLanguageSwitched; // (newLanguage, responseText)

        /// <summary>
        /// 控制状态变化事件
        /// </summary>
        event Action<VoiceControlState> OnStateChanged;

        /// <summary>
        /// 对话开始事件
        /// </summary>
        event Action<string> OnConversationStarted; // language

        /// <summary>
        /// 对话结束事件
        /// </summary>
        event Action OnConversationEnded;

        /// <summary>
        /// 语音控制错误事件
        /// </summary>
        event Action<string> OnVoiceControlError;

        /// <summary>
        /// 初始化语音控制器
        /// </summary>
        /// <param name="configuration">语音控制配置</param>
        /// <returns>初始化是否成功</returns>
        bool Initialize(VoiceControlConfiguration configuration);

        /// <summary>
        /// 开始语音控制监听
        /// </summary>
        /// <returns>开始是否成功</returns>
        bool StartListening();

        /// <summary>
        /// 停止语音控制监听
        /// </summary>
        void StopListening();

        /// <summary>
        /// 处理语音识别结果
        /// </summary>
        /// <param name="recognizedText">识别的文本</param>
        /// <param name="confidence">识别置信度</param>
        /// <param name="language">识别语言</param>
        /// <returns>是否检测到控制词</returns>
        bool ProcessRecognitionResult(string recognizedText, float confidence, string language);

        /// <summary>
        /// 检测唤醒词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <param name="language">语言</param>
        /// <returns>是否检测到唤醒词</returns>
        bool DetectWakeWord(string text, float confidence, string language);

        /// <summary>
        /// 检测打断词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <param name="language">语言</param>
        /// <returns>是否检测到打断词</returns>
        bool DetectInterruptWord(string text, float confidence, string language);

        /// <summary>
        /// 检测终止词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <param name="language">语言</param>
        /// <returns>是否检测到终止词</returns>
        bool DetectTerminateWord(string text, float confidence, string language);

        /// <summary>
        /// 检测语言切换词
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="confidence">置信度</param>
        /// <returns>检测到的语言，如果没有则返回null</returns>
        string DetectLanguageSwitch(string text, float confidence);

        /// <summary>
        /// 获取当前控制状态
        /// </summary>
        /// <returns>控制状态</returns>
        VoiceControlState GetCurrentState();

        /// <summary>
        /// 获取对话状态
        /// </summary>
        /// <returns>对话状态</returns>
        ConversationState GetConversationState();

        /// <summary>
        /// 设置当前语言
        /// </summary>
        /// <param name="language">语言代码</param>
        void SetCurrentLanguage(string language);

        /// <summary>
        /// 获取当前语言
        /// </summary>
        /// <returns>当前语言代码</returns>
        string GetCurrentLanguage();

        /// <summary>
        /// 添加自定义控制词
        /// </summary>
        /// <param name="controlWord">控制词</param>
        void AddCustomControlWord(VoiceControlWord controlWord);

        /// <summary>
        /// 移除控制词
        /// </summary>
        /// <param name="word">控制词文本</param>
        /// <param name="type">控制词类型</param>
        /// <param name="language">语言</param>
        /// <returns>是否移除成功</returns>
        bool RemoveControlWord(string word, VoiceControlType type, string language);

        /// <summary>
        /// 获取指定类型的控制词列表
        /// </summary>
        /// <param name="type">控制词类型</param>
        /// <param name="language">语言，为空则返回所有语言</param>
        /// <returns>控制词列表</returns>
        VoiceControlWord[] GetControlWords(VoiceControlType type, string language = null);

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        void UpdateConfiguration(VoiceControlConfiguration configuration);

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns>语音控制配置</returns>
        VoiceControlConfiguration GetConfiguration();

        /// <summary>
        /// 重置对话状态
        /// </summary>
        void ResetConversation();

        /// <summary>
        /// 启用/禁用语音控制
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetVoiceControlEnabled(bool enabled);

        /// <summary>
        /// 检查语音控制是否启用
        /// </summary>
        /// <returns>是否启用</returns>
        bool IsVoiceControlEnabled();

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }
}