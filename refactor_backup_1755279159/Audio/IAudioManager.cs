using System;
using System.Collections.Generic;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 音频管理器接口
    /// </summary>
    public interface IAudioManager
    {
        /// <summary>
        /// 音频设备变化事件
        /// </summary>
        event Action<List<AudioDevice>> OnAudioDevicesChanged;

        /// <summary>
        /// 音频设备切换事件
        /// </summary>
        event Action<AudioDevice, AudioDeviceType> OnAudioDeviceSwitched;

        /// <summary>
        /// 音频配置变化事件
        /// </summary>
        event Action<AudioConfiguration> OnAudioConfigurationChanged;

        /// <summary>
        /// 音频错误事件
        /// </summary>
        event Action<string> OnAudioError;

        /// <summary>
        /// 初始化音频管理器
        /// </summary>
        /// <returns>初始化是否成功</returns>
        bool Initialize();

        /// <summary>
        /// 获取所有可用的音频设备
        /// </summary>
        /// <returns>音频设备列表</returns>
        List<AudioDevice> GetAvailableDevices();

        /// <summary>
        /// 获取指定类型的音频设备
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>音频设备列表</returns>
        List<AudioDevice> GetDevicesByType(AudioDeviceType deviceType);

        /// <summary>
        /// 获取当前激活的麦克风设备
        /// </summary>
        /// <returns>麦克风设备，如果没有则返回null</returns>
        AudioDevice GetActiveMicrophone();

        /// <summary>
        /// 获取当前激活的扬声器设备
        /// </summary>
        /// <returns>扬声器设备，如果没有则返回null</returns>
        AudioDevice GetActiveSpeaker();

        /// <summary>
        /// 切换到指定的麦克风设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>切换是否成功</returns>
        bool SwitchMicrophone(string deviceId);

        /// <summary>
        /// 切换到指定的扬声器设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>切换是否成功</returns>
        bool SwitchSpeaker(string deviceId);

        /// <summary>
        /// 设置麦克风音量
        /// </summary>
        /// <param name="volume">音量值 (0.0 - 1.0)</param>
        void SetMicrophoneVolume(float volume);

        /// <summary>
        /// 设置扬声器音量
        /// </summary>
        /// <param name="volume">音量值 (0.0 - 1.0)</param>
        void SetSpeakerVolume(float volume);

        /// <summary>
        /// 获取当前音频配置
        /// </summary>
        /// <returns>音频配置</returns>
        AudioConfiguration GetConfiguration();

        /// <summary>
        /// 应用音频配置
        /// </summary>
        /// <param name="configuration">音频配置</param>
        void ApplyConfiguration(AudioConfiguration configuration);

        /// <summary>
        /// 开始录音
        /// </summary>
        /// <param name="deviceId">麦克风设备ID，为空则使用当前激活设备</param>
        /// <returns>录音是否开始成功</returns>
        bool StartRecording(string deviceId = null);

        /// <summary>
        /// 停止录音
        /// </summary>
        /// <returns>录制的音频剪辑</returns>
        AudioClip StopRecording();

        /// <summary>
        /// 播放音频
        /// </summary>
        /// <param name="audioClip">音频剪辑</param>
        /// <param name="deviceId">扬声器设备ID，为空则使用当前激活设备</param>
        void PlayAudio(AudioClip audioClip, string deviceId = null);

        /// <summary>
        /// 停止播放音频
        /// </summary>
        void StopAudio();

        /// <summary>
        /// 处理音频输入（降噪、增强等）
        /// </summary>
        /// <param name="inputAudio">输入音频</param>
        /// <returns>处理后的音频</returns>
        AudioClip ProcessAudioInput(AudioClip inputAudio);

        /// <summary>
        /// 测试音频设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>测试是否成功</returns>
        bool TestAudioDevice(string deviceId);

        /// <summary>
        /// 刷新音频设备列表
        /// </summary>
        void RefreshDevices();

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
    }
}