using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 音频管理器实现
    /// </summary>
    public class AudioManager : ManagerBase, IAudioManager
    {
        [Header("音频配置")]
        [SerializeField] private AudioConfiguration audioConfiguration;
        
        [Header("调试信息")]
        [SerializeField] private bool enableDebugLog = true;

        // 事件定义
        public event Action<List<AudioDevice>> OnAudioDevicesChanged;
        public event Action<AudioDevice, AudioDeviceType> OnAudioDeviceSwitched;
        public event Action<AudioConfiguration> OnAudioConfigurationChanged;
        public event Action<string> OnAudioError;

        // 私有字段
        private List<AudioDevice> availableDevices;
        private AudioDevice activeMicrophone;
        private AudioDevice activeSpeaker;
        private AudioSource audioSource;
        private AudioClip recordingClip;
        private bool isRecording;
        private bool isInitialized;

        // 常量定义
        private const int MAX_RECORDING_TIME = 300; // 最大录音时间（秒）
        private const int DEFAULT_SAMPLE_RATE = 44100;
        private const int RECORDING_SAMPLE_RATE = 16000;

        protected override void Awake()
        {
            base.Awake();
            InitializeComponents();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 创建音频源组件
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.loop = false;
            }

            // 初始化配置
            if (audioConfiguration == null)
            {
                audioConfiguration = new AudioConfiguration();
            }

            availableDevices = new List<AudioDevice>();
        }

        /// <summary>
        /// 实现基类的初始化方法
        /// </summary>
        protected override void OnInitializeInternal()
        {
            try
            {
                LogDebug("开始初始化音频管理器");

                // 刷新设备列表
                RefreshDevices();

                // 设置默认设备
                SetupDefaultDevices();

                // 应用配置
                ApplyConfiguration(audioConfiguration);

                isInitialized = true;
                LogDebug("音频管理器初始化成功");
            }
            catch (Exception ex)
            {
                LogError($"音频管理器初始化失败: {ex.Message}");
                OnAudioError?.Invoke($"音频管理器初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 实现基类的清理方法
        /// </summary>
        protected override void OnCleanupInternal()
        {
            Dispose();
        }

        /// <summary>
        /// 初始化音频管理器（公共接口）
        /// </summary>
        /// <returns>初始化是否成功</returns>
        public new bool Initialize()
        {
            try
            {
                base.Initialize();
                return IsInitialized;
            }
            catch (Exception ex)
            {
                LogError($"音频管理器初始化失败: {ex.Message}");
                OnAudioError?.Invoke($"音频管理器初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有可用的音频设备
        /// </summary>
        /// <returns>音频设备列表</returns>
        public List<AudioDevice> GetAvailableDevices()
        {
            return new List<AudioDevice>(availableDevices);
        }

        /// <summary>
        /// 获取指定类型的音频设备
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>音频设备列表</returns>
        public List<AudioDevice> GetDevicesByType(AudioDeviceType deviceType)
        {
            return availableDevices.Where(device => device.DeviceType == deviceType).ToList();
        }

        /// <summary>
        /// 获取当前激活的麦克风设备
        /// </summary>
        /// <returns>麦克风设备，如果没有则返回null</returns>
        public AudioDevice GetActiveMicrophone()
        {
            return activeMicrophone;
        }

        /// <summary>
        /// 获取当前激活的扬声器设备
        /// </summary>
        /// <returns>扬声器设备，如果没有则返回null</returns>
        public AudioDevice GetActiveSpeaker()
        {
            return activeSpeaker;
        }

        /// <summary>
        /// 切换到指定的麦克风设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>切换是否成功</returns>
        public bool SwitchMicrophone(string deviceId)
        {
            try
            {
                var targetDevice = availableDevices.FirstOrDefault(d => 
                    d.DeviceId == deviceId && d.DeviceType == AudioDeviceType.Microphone);

                if (targetDevice == null)
                {
                    LogError($"未找到麦克风设备: {deviceId}");
                    return false;
                }

                // 停止当前录音
                if (isRecording)
                {
                    StopRecording();
                }

                // 更新激活设备
                if (activeMicrophone != null)
                {
                    activeMicrophone.IsActive = false;
                }

                activeMicrophone = targetDevice;
                activeMicrophone.IsActive = true;
                audioConfiguration.ActiveMicrophoneId = deviceId;

                LogDebug($"切换到麦克风设备: {targetDevice.DeviceName}");
                OnAudioDeviceSwitched?.Invoke(targetDevice, AudioDeviceType.Microphone);
                OnAudioConfigurationChanged?.Invoke(audioConfiguration);

                return true;
            }
            catch (Exception ex)
            {
                LogError($"切换麦克风设备失败: {ex.Message}");
                OnAudioError?.Invoke($"切换麦克风设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 切换到指定的扬声器设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>切换是否成功</returns>
        public bool SwitchSpeaker(string deviceId)
        {
            try
            {
                var targetDevice = availableDevices.FirstOrDefault(d => 
                    d.DeviceId == deviceId && d.DeviceType == AudioDeviceType.Speaker);

                if (targetDevice == null)
                {
                    LogError($"未找到扬声器设备: {deviceId}");
                    return false;
                }

                // 停止当前播放
                StopAudio();

                // 更新激活设备
                if (activeSpeaker != null)
                {
                    activeSpeaker.IsActive = false;
                }

                activeSpeaker = targetDevice;
                activeSpeaker.IsActive = true;
                audioConfiguration.ActiveSpeakerId = deviceId;

                LogDebug($"切换到扬声器设备: {targetDevice.DeviceName}");
                OnAudioDeviceSwitched?.Invoke(targetDevice, AudioDeviceType.Speaker);
                OnAudioConfigurationChanged?.Invoke(audioConfiguration);

                return true;
            }
            catch (Exception ex)
            {
                LogError($"切换扬声器设备失败: {ex.Message}");
                OnAudioError?.Invoke($"切换扬声器设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置麦克风音量
        /// </summary>
        /// <param name="volume">音量值 (0.0 - 1.0)</param>
        public void SetMicrophoneVolume(float volume)
        {
            audioConfiguration.MicrophoneVolume = Mathf.Clamp01(volume);
            LogDebug($"设置麦克风音量: {audioConfiguration.MicrophoneVolume:F2}");
            OnAudioConfigurationChanged?.Invoke(audioConfiguration);
        }

        /// <summary>
        /// 设置扬声器音量
        /// </summary>
        /// <param name="volume">音量值 (0.0 - 1.0)</param>
        public void SetSpeakerVolume(float volume)
        {
            audioConfiguration.SpeakerVolume = Mathf.Clamp01(volume);
            if (audioSource != null)
            {
                audioSource.volume = audioConfiguration.SpeakerVolume;
            }
            LogDebug($"设置扬声器音量: {audioConfiguration.SpeakerVolume:F2}");
            OnAudioConfigurationChanged?.Invoke(audioConfiguration);
        }

        /// <summary>
        /// 获取当前音频配置
        /// </summary>
        /// <returns>音频配置</returns>
        public AudioConfiguration GetConfiguration()
        {
            return audioConfiguration;
        }

        /// <summary>
        /// 应用音频配置
        /// </summary>
        /// <param name="configuration">音频配置</param>
        public void ApplyConfiguration(AudioConfiguration configuration)
        {
            if (configuration == null || !configuration.IsValid())
            {
                LogError("无效的音频配置");
                return;
            }

            audioConfiguration = configuration;

            // 应用音量设置
            if (audioSource != null)
            {
                audioSource.volume = configuration.SpeakerVolume;
            }

            // 切换设备
            if (!string.IsNullOrEmpty(configuration.ActiveMicrophoneId))
            {
                SwitchMicrophone(configuration.ActiveMicrophoneId);
            }

            if (!string.IsNullOrEmpty(configuration.ActiveSpeakerId))
            {
                SwitchSpeaker(configuration.ActiveSpeakerId);
            }

            LogDebug("应用音频配置完成");
            OnAudioConfigurationChanged?.Invoke(audioConfiguration);
        }        
/// <summary>
        /// 开始录音
        /// </summary>
        /// <param name="deviceId">麦克风设备ID，为空则使用当前激活设备</param>
        /// <returns>录音是否开始成功</returns>
        public bool StartRecording(string deviceId = null)
        {
            try
            {
                if (isRecording)
                {
                    LogWarning("录音已在进行中");
                    return false;
                }

                // 确定使用的麦克风设备
                AudioDevice micDevice = activeMicrophone;
                if (!string.IsNullOrEmpty(deviceId))
                {
                    micDevice = availableDevices.FirstOrDefault(d => 
                        d.DeviceId == deviceId && d.DeviceType == AudioDeviceType.Microphone);
                }

                if (micDevice == null)
                {
                    LogError("没有可用的麦克风设备");
                    OnAudioError?.Invoke("没有可用的麦克风设备");
                    return false;
                }

                // 开始录音
                string micName = string.IsNullOrEmpty(micDevice.DeviceName) ? null : micDevice.DeviceName;
                recordingClip = Microphone.Start(micName, false, MAX_RECORDING_TIME, RECORDING_SAMPLE_RATE);
                
                if (recordingClip == null)
                {
                    LogError("无法开始录音");
                    OnAudioError?.Invoke("无法开始录音");
                    return false;
                }

                isRecording = true;
                LogDebug($"开始录音，使用设备: {micDevice.DeviceName}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"开始录音失败: {ex.Message}");
                OnAudioError?.Invoke($"开始录音失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止录音
        /// </summary>
        /// <returns>录制的音频剪辑</returns>
        public AudioClip StopRecording()
        {
            try
            {
                if (!isRecording)
                {
                    LogWarning("当前没有进行录音");
                    return null;
                }

                // 停止录音
                string micName = activeMicrophone?.DeviceName;
                Microphone.End(micName);
                isRecording = false;

                // 处理录制的音频
                if (recordingClip != null)
                {
                    var processedClip = ProcessAudioInput(recordingClip);
                    LogDebug("录音停止，音频处理完成");
                    return processedClip;
                }

                LogDebug("录音停止");
                return recordingClip;
            }
            catch (Exception ex)
            {
                LogError($"停止录音失败: {ex.Message}");
                OnAudioError?.Invoke($"停止录音失败: {ex.Message}");
                isRecording = false;
                return null;
            }
        }

        /// <summary>
        /// 播放音频
        /// </summary>
        /// <param name="audioClip">音频剪辑</param>
        /// <param name="deviceId">扬声器设备ID，为空则使用当前激活设备</param>
        public void PlayAudio(AudioClip audioClip, string deviceId = null)
        {
            try
            {
                if (audioClip == null)
                {
                    LogError("音频剪辑为空");
                    return;
                }

                if (audioSource == null)
                {
                    LogError("音频源组件未初始化");
                    return;
                }

                // 停止当前播放
                StopAudio();

                // 设置音频剪辑和播放
                audioSource.clip = audioClip;
                audioSource.volume = audioConfiguration.SpeakerVolume;
                audioSource.Play();

                LogDebug($"开始播放音频: {audioClip.name}");
            }
            catch (Exception ex)
            {
                LogError($"播放音频失败: {ex.Message}");
                OnAudioError?.Invoke($"播放音频失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止播放音频
        /// </summary>
        public void StopAudio()
        {
            try
            {
                if (audioSource != null && audioSource.isPlaying)
                {
                    audioSource.Stop();
                    LogDebug("停止音频播放");
                }
            }
            catch (Exception ex)
            {
                LogError($"停止音频播放失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理音频输入（降噪、增强等）
        /// </summary>
        /// <param name="inputAudio">输入音频</param>
        /// <returns>处理后的音频</returns>
        public AudioClip ProcessAudioInput(AudioClip inputAudio)
        {
            if (inputAudio == null)
            {
                LogError("输入音频为空");
                return null;
            }

            try
            {
                // 获取音频数据
                float[] audioData = new float[inputAudio.samples * inputAudio.channels];
                inputAudio.GetData(audioData, 0);

                // 应用音频处理
                if (audioConfiguration.EnableNoiseReduction)
                {
                    audioData = ApplyNoiseReduction(audioData);
                }

                if (audioConfiguration.EnableAutoGainControl)
                {
                    audioData = ApplyAutoGainControl(audioData);
                }

                // 创建处理后的音频剪辑
                AudioClip processedClip = AudioClip.Create(
                    $"{inputAudio.name}_processed",
                    inputAudio.samples,
                    inputAudio.channels,
                    inputAudio.frequency,
                    false
                );

                processedClip.SetData(audioData, 0);
                LogDebug("音频处理完成");
                return processedClip;
            }
            catch (Exception ex)
            {
                LogError($"音频处理失败: {ex.Message}");
                return inputAudio; // 返回原始音频
            }
        }

        /// <summary>
        /// 测试音频设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>测试是否成功</returns>
        public bool TestAudioDevice(string deviceId)
        {
            try
            {
                var device = availableDevices.FirstOrDefault(d => d.DeviceId == deviceId);
                if (device == null)
                {
                    LogError($"未找到设备: {deviceId}");
                    return false;
                }

                if (device.DeviceType == AudioDeviceType.Microphone)
                {
                    return TestMicrophone(device);
                }
                else if (device.DeviceType == AudioDeviceType.Speaker)
                {
                    return TestSpeaker(device);
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError($"测试音频设备失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 刷新音频设备列表
        /// </summary>
        public void RefreshDevices()
        {
            try
            {
                LogDebug("刷新音频设备列表");
                availableDevices.Clear();

                // 获取麦克风设备
                string[] micDevices = Microphone.devices;
                for (int i = 0; i < micDevices.Length; i++)
                {
                    var micDevice = new AudioDevice(
                        $"mic_{i}",
                        micDevices[i],
                        AudioDeviceType.Microphone
                    );
                    micDevice.IsDefault = i == 0;
                    micDevice.SampleRate = RECORDING_SAMPLE_RATE;
                    micDevice.Channels = 1;
                    availableDevices.Add(micDevice);
                }

                // 添加默认扬声器设备（Unity没有直接API获取扬声器列表）
                var defaultSpeaker = new AudioDevice(
                    "speaker_default",
                    "默认扬声器",
                    AudioDeviceType.Speaker
                );
                defaultSpeaker.IsDefault = true;
                defaultSpeaker.SampleRate = DEFAULT_SAMPLE_RATE;
                defaultSpeaker.Channels = 2;
                availableDevices.Add(defaultSpeaker);

                LogDebug($"发现 {availableDevices.Count} 个音频设备");
                OnAudioDevicesChanged?.Invoke(GetAvailableDevices());
            }
            catch (Exception ex)
            {
                LogError($"刷新音频设备失败: {ex.Message}");
                OnAudioError?.Invoke($"刷新音频设备失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 停止录音和播放
                if (isRecording)
                {
                    StopRecording();
                }
                StopAudio();

                // 清理资源
                if (recordingClip != null)
                {
                    DestroyImmediate(recordingClip);
                    recordingClip = null;
                }

                availableDevices?.Clear();
                activeMicrophone = null;
                activeSpeaker = null;
                isInitialized = false;

                LogDebug("音频管理器资源释放完成");
            }
            catch (Exception ex)
            {
                LogError($"释放音频管理器资源失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 设置默认设备
        /// </summary>
        private void SetupDefaultDevices()
        {
            // 设置默认麦克风
            var defaultMic = availableDevices.FirstOrDefault(d => 
                d.DeviceType == AudioDeviceType.Microphone && d.IsDefault);
            if (defaultMic != null)
            {
                activeMicrophone = defaultMic;
                activeMicrophone.IsActive = true;
                audioConfiguration.ActiveMicrophoneId = defaultMic.DeviceId;
            }

            // 设置默认扬声器
            var defaultSpeaker = availableDevices.FirstOrDefault(d => 
                d.DeviceType == AudioDeviceType.Speaker && d.IsDefault);
            if (defaultSpeaker != null)
            {
                activeSpeaker = defaultSpeaker;
                activeSpeaker.IsActive = true;
                audioConfiguration.ActiveSpeakerId = defaultSpeaker.DeviceId;
            }
        }

        /// <summary>
        /// 测试麦克风设备
        /// </summary>
        /// <param name="device">麦克风设备</param>
        /// <returns>测试是否成功</returns>
        private bool TestMicrophone(AudioDevice device)
        {
            try
            {
                // 简单的麦克风测试：尝试开始和停止录音
                string micName = device.DeviceName;
                var testClip = Microphone.Start(micName, false, 1, RECORDING_SAMPLE_RATE);
                
                if (testClip == null)
                {
                    return false;
                }

                // 等待一小段时间
                System.Threading.Thread.Sleep(100);
                
                Microphone.End(micName);
                DestroyImmediate(testClip);
                
                LogDebug($"麦克风设备测试成功: {device.DeviceName}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"麦克风设备测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试扬声器设备
        /// </summary>
        /// <param name="device">扬声器设备</param>
        /// <returns>测试是否成功</returns>
        private bool TestSpeaker(AudioDevice device)
        {
            try
            {
                // 简单的扬声器测试：检查AudioSource组件
                if (audioSource == null)
                {
                    return false;
                }

                LogDebug($"扬声器设备测试成功: {device.DeviceName}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"扬声器设备测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 应用降噪处理
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <returns>处理后的音频数据</returns>
        private float[] ApplyNoiseReduction(float[] audioData)
        {
            // 简单的降噪实现：低通滤波
            float noiseThreshold = 0.01f * audioConfiguration.NoiseReductionLevel;
            
            for (int i = 0; i < audioData.Length; i++)
            {
                if (Mathf.Abs(audioData[i]) < noiseThreshold)
                {
                    audioData[i] *= (1f - audioConfiguration.NoiseReductionLevel);
                }
            }

            return audioData;
        }

        /// <summary>
        /// 应用自动增益控制
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <returns>处理后的音频数据</returns>
        private float[] ApplyAutoGainControl(float[] audioData)
        {
            // 简单的AGC实现：标准化音量
            float maxAmplitude = 0f;
            for (int i = 0; i < audioData.Length; i++)
            {
                maxAmplitude = Mathf.Max(maxAmplitude, Mathf.Abs(audioData[i]));
            }

            if (maxAmplitude > 0.001f)
            {
                float gainFactor = 0.8f / maxAmplitude;
                gainFactor = Mathf.Clamp(gainFactor, 0.1f, 10f);

                for (int i = 0; i < audioData.Length; i++)
                {
                    audioData[i] *= gainFactor;
                }
            }

            return audioData;
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[AudioManager] {message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[AudioManager] {message}");
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogError(string message)
        {
            Debug.LogError($"[AudioManager] {message}");
        }

        #endregion

        #region Unity生命周期

        protected override void OnDestroy()
        {
            Dispose();
            base.OnDestroy();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && isRecording)
            {
                StopRecording();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && isRecording)
            {
                StopRecording();
            }
        }

        #endregion
    }
}