using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using DigitalHuman.Core.Audio.Models;
using DigitalHuman.Core.Network;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 自动语音识别服务实现
    /// </summary>
    public class ASRService : MonoBehaviour, IASRService
    {
        [Header("ASR配置")]
        [SerializeField] private ASRConfiguration configuration;
        
        [Header("调试设置")]
        [SerializeField] private bool enableDebugLog = true;

        // 事件定义
        public event Action<ASRResponse> OnRecognitionCompleted;
        public event Action<RealTimeASRResult> OnRealTimeResult;
        public event Action<string> OnRecognitionError;
        public event Action<RealTimeRecognitionState> OnStateChanged;

        // 私有字段
        private IHttpClient httpClient;
        private bool isInitialized;
        private bool isRealTimeRecognizing;
        private RealTimeRecognitionState currentState;
        private List<float[]> realTimeAudioBuffer;
        private Coroutine realTimeRecognitionCoroutine;

        // 常量定义
        private const int REAL_TIME_BUFFER_SIZE = 1024;
        private const float REAL_TIME_INTERVAL = 0.5f; // 实时识别间隔（秒）

        private void Awake()
        {
            InitializeComponents();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 获取HTTP客户端
            httpClient = FindObjectOfType<HttpClient>();
            if (httpClient == null)
            {
                var httpClientGO = new GameObject("HttpClient");
                httpClient = httpClientGO.AddComponent<HttpClient>();
            }

            // 初始化配置
            if (configuration == null)
            {
                configuration = new ASRConfiguration();
            }

            realTimeAudioBuffer = new List<float[]>();
            currentState = RealTimeRecognitionState.Idle;
        }

        /// <summary>
        /// 初始化ASR服务
        /// </summary>
        /// <param name="configuration">ASR配置</param>
        /// <returns>初始化是否成功</returns>
        public bool Initialize(ASRConfiguration configuration)
        {
            try
            {
                if (configuration == null || !configuration.IsValid())
                {
                    LogError("ASR配置无效");
                    return false;
                }

                this.configuration = configuration;
                isInitialized = true;
                
                LogDebug("ASR服务初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"ASR服务初始化失败: {ex.Message}");
                OnRecognitionError?.Invoke($"ASR服务初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 识别音频文件
        /// </summary>
        /// <param name="audioClip">音频剪辑</param>
        /// <param name="language">识别语言，为空则使用默认语言</param>
        /// <returns>识别结果</returns>
        public async Task<ASRResponse> RecognizeAsync(AudioClip audioClip, string language = null)
        {
            if (!isInitialized)
            {
                LogError("ASR服务未初始化");
                return new ASRResponse { Success = false, ErrorMessage = "ASR服务未初始化" };
            }

            if (audioClip == null)
            {
                LogError("音频剪辑为空");
                return new ASRResponse { Success = false, ErrorMessage = "音频剪辑为空" };
            }

            try
            {
                // 获取音频数据
                float[] audioData = new float[audioClip.samples * audioClip.channels];
                audioClip.GetData(audioData, 0);

                return await RecognizeAsync(audioData, audioClip.frequency, audioClip.channels, language);
            }
            catch (Exception ex)
            {
                LogError($"音频识别失败: {ex.Message}");
                var errorResponse = new ASRResponse 
                { 
                    Success = false, 
                    ErrorMessage = $"音频识别失败: {ex.Message}" 
                };
                OnRecognitionError?.Invoke(errorResponse.ErrorMessage);
                return errorResponse;
            }
        }

        /// <summary>
        /// 识别音频数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <param name="sampleRate">采样率</param>
        /// <param name="channels">声道数</param>
        /// <param name="language">识别语言</param>
        /// <returns>识别结果</returns>
        public async Task<ASRResponse> RecognizeAsync(float[] audioData, int sampleRate, int channels, string language = null)
        {
            if (!isInitialized)
            {
                LogError("ASR服务未初始化");
                return new ASRResponse { Success = false, ErrorMessage = "ASR服务未初始化" };
            }

            try
            {
                // 构建请求
                var request = new ASRRequest
                {
                    AudioData = ConvertAudioToBase64(audioData, sampleRate, channels),
                    AudioFormat = "wav",
                    SampleRate = sampleRate,
                    Language = language ?? configuration.DefaultLanguage,
                    EnablePunctuation = configuration.EnablePunctuation,
                    EnableWordTimestamp = configuration.EnableWordTimestamp
                };

                // 发送请求
                var response = await SendASRRequestAsync(request);
                
                if (response.Success)
                {
                    LogDebug($"语音识别成功: {response.Text}");
                    OnRecognitionCompleted?.Invoke(response);
                }
                else
                {
                    LogError($"语音识别失败: {response.ErrorMessage}");
                    OnRecognitionError?.Invoke(response.ErrorMessage);
                }

                return response;
            }
            catch (Exception ex)
            {
                LogError($"音频识别异常: {ex.Message}");
                var errorResponse = new ASRResponse 
                { 
                    Success = false, 
                    ErrorMessage = $"音频识别异常: {ex.Message}" 
                };
                OnRecognitionError?.Invoke(errorResponse.ErrorMessage);
                return errorResponse;
            }
        }

        /// <summary>
        /// 开始实时语音识别
        /// </summary>
        /// <param name="language">识别语言</param>
        /// <returns>开始是否成功</returns>
        public bool StartRealTimeRecognition(string language = null)
        {
            if (!isInitialized)
            {
                LogError("ASR服务未初始化");
                return false;
            }

            if (isRealTimeRecognizing)
            {
                LogWarning("实时识别已在进行中");
                return false;
            }

            try
            {
                isRealTimeRecognizing = true;
                realTimeAudioBuffer.Clear();
                
                SetState(RealTimeRecognitionState.Listening);
                
                // 启动实时识别协程
                realTimeRecognitionCoroutine = StartCoroutine(RealTimeRecognitionCoroutine(language));
                
                LogDebug("开始实时语音识别");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"开始实时识别失败: {ex.Message}");
                OnRecognitionError?.Invoke($"开始实时识别失败: {ex.Message}");
                isRealTimeRecognizing = false;
                SetState(RealTimeRecognitionState.Error);
                return false;
            }
        }

        /// <summary>
        /// 停止实时语音识别
        /// </summary>
        /// <returns>最终识别结果</returns>
        public async Task<ASRResponse> StopRealTimeRecognition()
        {
            if (!isRealTimeRecognizing)
            {
                LogWarning("实时识别未在进行中");
                return new ASRResponse { Success = false, ErrorMessage = "实时识别未在进行中" };
            }

            try
            {
                isRealTimeRecognizing = false;
                SetState(RealTimeRecognitionState.Processing);

                // 停止协程
                if (realTimeRecognitionCoroutine != null)
                {
                    StopCoroutine(realTimeRecognitionCoroutine);
                    realTimeRecognitionCoroutine = null;
                }

                // 处理剩余的音频数据
                var finalResponse = await ProcessRemainingAudioBuffer();
                
                SetState(RealTimeRecognitionState.Completed);
                LogDebug("停止实时语音识别");
                
                return finalResponse;
            }
            catch (Exception ex)
            {
                LogError($"停止实时识别失败: {ex.Message}");
                SetState(RealTimeRecognitionState.Error);
                var errorResponse = new ASRResponse 
                { 
                    Success = false, 
                    ErrorMessage = $"停止实时识别失败: {ex.Message}" 
                };
                OnRecognitionError?.Invoke(errorResponse.ErrorMessage);
                return errorResponse;
            }
        }

        /// <summary>
        /// 发送实时音频数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        public void SendRealTimeAudio(float[] audioData)
        {
            if (!isRealTimeRecognizing)
            {
                return;
            }

            try
            {
                // 添加到缓冲区
                realTimeAudioBuffer.Add(audioData);

                // 限制缓冲区大小
                if (realTimeAudioBuffer.Count > REAL_TIME_BUFFER_SIZE)
                {
                    realTimeAudioBuffer.RemoveAt(0);
                }
            }
            catch (Exception ex)
            {
                LogError($"发送实时音频数据失败: {ex.Message}");
            }
        } 
       /// <summary>
        /// 取消当前识别任务
        /// </summary>
        public void CancelRecognition()
        {
            try
            {
                if (isRealTimeRecognizing)
                {
                    isRealTimeRecognizing = false;
                    
                    if (realTimeRecognitionCoroutine != null)
                    {
                        StopCoroutine(realTimeRecognitionCoroutine);
                        realTimeRecognitionCoroutine = null;
                    }
                    
                    SetState(RealTimeRecognitionState.Idle);
                    LogDebug("取消语音识别任务");
                }
            }
            catch (Exception ex)
            {
                LogError($"取消识别任务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns>ASR配置</returns>
        public ASRConfiguration GetConfiguration()
        {
            return configuration;
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        public void UpdateConfiguration(ASRConfiguration configuration)
        {
            if (configuration != null && configuration.IsValid())
            {
                this.configuration = configuration;
                LogDebug("ASR配置已更新");
            }
            else
            {
                LogError("无效的ASR配置");
            }
        }

        /// <summary>
        /// 测试服务连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        public async Task<bool> TestConnectionAsync()
        {
            if (!isInitialized)
            {
                LogError("ASR服务未初始化");
                return false;
            }

            try
            {
                // 创建测试音频数据
                float[] testAudio = GenerateTestAudio();
                var testRequest = new ASRRequest
                {
                    AudioData = ConvertAudioToBase64(testAudio, 16000, 1),
                    AudioFormat = "wav",
                    SampleRate = 16000,
                    Language = configuration.DefaultLanguage
                };

                var response = await SendASRRequestAsync(testRequest);
                bool isConnected = response != null;
                
                LogDebug($"ASR服务连接测试: {(isConnected ? "成功" : "失败")}");
                return isConnected;
            }
            catch (Exception ex)
            {
                LogError($"ASR服务连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        /// <returns>支持的语言列表</returns>
        public string[] GetSupportedLanguages()
        {
            return configuration.SupportedLanguages.ToArray();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                CancelRecognition();
                realTimeAudioBuffer?.Clear();
                isInitialized = false;
                LogDebug("ASR服务资源释放完成");
            }
            catch (Exception ex)
            {
                LogError($"释放ASR服务资源失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 发送ASR请求
        /// </summary>
        /// <param name="request">ASR请求</param>
        /// <returns>ASR响应</returns>
        private async Task<ASRResponse> SendASRRequestAsync(ASRRequest request)
        {
            try
            {
                // 构建请求数据
                var requestData = JsonUtility.ToJson(request);
                var headers = new Dictionary<string, string>
                {
                    ["Content-Type"] = "application/json"
                };

                // 添加API密钥
                if (!string.IsNullOrEmpty(configuration.ApiKey))
                {
                    headers["Authorization"] = $"Bearer {configuration.ApiKey}";
                }

                // 发送HTTP请求
                var httpResponse = await httpClient.PostAsync(
                    configuration.ApiEndpoint, 
                    requestData, 
                    headers
                );

                if (httpResponse.IsSuccess)
                {
                    var response = JsonUtility.FromJson<ASRResponse>(httpResponse.Data);
                    response.Success = true;
                    return response;
                }
                else
                {
                    return new ASRResponse
                    {
                        Success = false,
                        ErrorMessage = httpResponse.ErrorMessage
                    };
                }
            }
            catch (Exception ex)
            {
                LogError($"发送ASR请求失败: {ex.Message}");
                return new ASRResponse
                {
                    Success = false,
                    ErrorMessage = $"发送ASR请求失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 实时识别协程
        /// </summary>
        /// <param name="language">识别语言</param>
        /// <returns>协程</returns>
        private IEnumerator RealTimeRecognitionCoroutine(string language)
        {
            while (isRealTimeRecognizing)
            {
                yield return new WaitForSeconds(REAL_TIME_INTERVAL);

                if (realTimeAudioBuffer.Count > 0)
                {
                    // 处理缓冲区中的音频数据
                    StartCoroutine(ProcessRealTimeAudioBuffer(language));
                }
            }
        }

        /// <summary>
        /// 处理实时音频缓冲区
        /// </summary>
        /// <param name="language">识别语言</param>
        /// <returns>协程</returns>
        private IEnumerator ProcessRealTimeAudioBuffer(string language)
        {
            if (realTimeAudioBuffer.Count == 0)
            {
                yield break;
            }

            try
            {
                SetState(RealTimeRecognitionState.Processing);

                // 合并音频数据
                var combinedAudio = CombineAudioData(realTimeAudioBuffer);
                realTimeAudioBuffer.Clear();

                // 异步识别
                var recognitionTask = RecognizeAsync(combinedAudio, 16000, 1, language);
                
                // 等待识别完成
                while (!recognitionTask.IsCompleted)
                {
                    yield return null;
                }

                var result = recognitionTask.Result;
                
                if (result.Success && !string.IsNullOrEmpty(result.Text))
                {
                    // 发送实时结果
                    var realTimeResult = new RealTimeASRResult
                    {
                        PartialText = result.Text,
                        FinalText = result.Text,
                        IsFinal = false,
                        Confidence = result.Confidence,
                        State = RealTimeRecognitionState.Listening
                    };

                    OnRealTimeResult?.Invoke(realTimeResult);
                }

                SetState(RealTimeRecognitionState.Listening);
            }
            catch (Exception ex)
            {
                LogError($"处理实时音频缓冲区失败: {ex.Message}");
                SetState(RealTimeRecognitionState.Error);
            }
        }

        /// <summary>
        /// 处理剩余的音频缓冲区
        /// </summary>
        /// <returns>最终识别结果</returns>
        private async Task<ASRResponse> ProcessRemainingAudioBuffer()
        {
            if (realTimeAudioBuffer.Count == 0)
            {
                return new ASRResponse { Success = true, Text = string.Empty };
            }

            try
            {
                var combinedAudio = CombineAudioData(realTimeAudioBuffer);
                realTimeAudioBuffer.Clear();

                var result = await RecognizeAsync(combinedAudio, 16000, 1);
                
                if (result.Success)
                {
                    // 发送最终结果
                    var finalResult = new RealTimeASRResult
                    {
                        PartialText = result.Text,
                        FinalText = result.Text,
                        IsFinal = true,
                        Confidence = result.Confidence,
                        State = RealTimeRecognitionState.Completed
                    };

                    OnRealTimeResult?.Invoke(finalResult);
                }

                return result;
            }
            catch (Exception ex)
            {
                LogError($"处理剩余音频缓冲区失败: {ex.Message}");
                return new ASRResponse 
                { 
                    Success = false, 
                    ErrorMessage = $"处理剩余音频缓冲区失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 合并音频数据
        /// </summary>
        /// <param name="audioBuffers">音频缓冲区列表</param>
        /// <returns>合并后的音频数据</returns>
        private float[] CombineAudioData(List<float[]> audioBuffers)
        {
            if (audioBuffers == null || audioBuffers.Count == 0)
            {
                return new float[0];
            }

            // 计算总长度
            int totalLength = 0;
            foreach (var buffer in audioBuffers)
            {
                totalLength += buffer.Length;
            }

            // 合并数据
            float[] combinedData = new float[totalLength];
            int currentIndex = 0;

            foreach (var buffer in audioBuffers)
            {
                Array.Copy(buffer, 0, combinedData, currentIndex, buffer.Length);
                currentIndex += buffer.Length;
            }

            return combinedData;
        }

        /// <summary>
        /// 将音频数据转换为Base64字符串
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <param name="sampleRate">采样率</param>
        /// <param name="channels">声道数</param>
        /// <returns>Base64编码的音频数据</returns>
        private string ConvertAudioToBase64(float[] audioData, int sampleRate, int channels)
        {
            try
            {
                // 转换为16位PCM数据
                byte[] pcmData = ConvertToPCM16(audioData);
                
                // 创建WAV文件头
                byte[] wavData = CreateWavFile(pcmData, sampleRate, channels);
                
                // 转换为Base64
                return Convert.ToBase64String(wavData);
            }
            catch (Exception ex)
            {
                LogError($"音频数据转换失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 转换为16位PCM数据
        /// </summary>
        /// <param name="audioData">音频数据</param>
        /// <returns>PCM数据</returns>
        private byte[] ConvertToPCM16(float[] audioData)
        {
            byte[] pcmData = new byte[audioData.Length * 2];
            
            for (int i = 0; i < audioData.Length; i++)
            {
                short sample = (short)(audioData[i] * 32767f);
                byte[] sampleBytes = BitConverter.GetBytes(sample);
                pcmData[i * 2] = sampleBytes[0];
                pcmData[i * 2 + 1] = sampleBytes[1];
            }
            
            return pcmData;
        }

        /// <summary>
        /// 创建WAV文件
        /// </summary>
        /// <param name="pcmData">PCM数据</param>
        /// <param name="sampleRate">采样率</param>
        /// <param name="channels">声道数</param>
        /// <returns>WAV文件数据</returns>
        private byte[] CreateWavFile(byte[] pcmData, int sampleRate, int channels)
        {
            byte[] wavFile = new byte[44 + pcmData.Length];
            
            // WAV文件头
            Encoding.ASCII.GetBytes("RIFF").CopyTo(wavFile, 0);
            BitConverter.GetBytes(36 + pcmData.Length).CopyTo(wavFile, 4);
            Encoding.ASCII.GetBytes("WAVE").CopyTo(wavFile, 8);
            Encoding.ASCII.GetBytes("fmt ").CopyTo(wavFile, 12);
            BitConverter.GetBytes(16).CopyTo(wavFile, 16);
            BitConverter.GetBytes((short)1).CopyTo(wavFile, 20);
            BitConverter.GetBytes((short)channels).CopyTo(wavFile, 22);
            BitConverter.GetBytes(sampleRate).CopyTo(wavFile, 24);
            BitConverter.GetBytes(sampleRate * channels * 2).CopyTo(wavFile, 28);
            BitConverter.GetBytes((short)(channels * 2)).CopyTo(wavFile, 32);
            BitConverter.GetBytes((short)16).CopyTo(wavFile, 34);
            Encoding.ASCII.GetBytes("data").CopyTo(wavFile, 36);
            BitConverter.GetBytes(pcmData.Length).CopyTo(wavFile, 40);
            
            // PCM数据
            pcmData.CopyTo(wavFile, 44);
            
            return wavFile;
        }

        /// <summary>
        /// 生成测试音频数据
        /// </summary>
        /// <returns>测试音频数据</returns>
        private float[] GenerateTestAudio()
        {
            // 生成1秒的静音数据用于测试
            int sampleCount = 16000; // 16kHz * 1秒
            float[] testAudio = new float[sampleCount];
            
            // 填充静音数据
            for (int i = 0; i < sampleCount; i++)
            {
                testAudio[i] = 0f;
            }
            
            return testAudio;
        }

        /// <summary>
        /// 设置识别状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void SetState(RealTimeRecognitionState newState)
        {
            if (currentState != newState)
            {
                currentState = newState;
                OnStateChanged?.Invoke(currentState);
                LogDebug($"ASR状态变更: {currentState}");
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[ASRService] {message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[ASRService] {message}");
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogError(string message)
        {
            Debug.LogError($"[ASRService] {message}");
        }

        #endregion

        #region Unity生命周期

        private void OnDestroy()
        {
            Dispose();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && isRealTimeRecognizing)
            {
                CancelRecognition();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && isRealTimeRecognizing)
            {
                CancelRecognition();
            }
        }

        #endregion
    }
}