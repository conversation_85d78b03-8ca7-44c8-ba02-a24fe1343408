using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Audio.Models;
using DigitalHuman.Core.Network;

namespace DigitalHuman.Core.Audio
{
    /// <summary>
    /// 文字转语音服务实现
    /// </summary>
    public class TTSService : MonoBehaviour, ITTSService
    {
        [Header("TTS配置")]
        [SerializeField] private TTSConfiguration configuration;
        
        [Header("音频设置")]
        [SerializeField] private AudioSource audioSource;
        
        [Header("调试设置")]
        [SerializeField] private bool enableDebugLog = true;

        // 事件定义
        public event Action<TTSResponse> OnSynthesisCompleted;
        public event Action<TTSPlaybackEvent> OnPlaybackStateChanged;
        public event Action<string> OnSynthesisError;
        public event Action<VoiceInfo[]> OnVoicesUpdated;

        // 私有字段
        private IHttpClient httpClient;
        private bool isInitialized;
        private TTSPlaybackState currentState;
        private AudioClip currentAudioClip;
        private string currentText;
        private float playbackStartTime;
        private Coroutine playbackCoroutine;
        private List<VoiceInfo> cachedVoices;

        // 常量定义
        private const string DEFAULT_SAMPLE_TEXT = "这是一个语音合成测试。";
        private const float PLAYBACK_UPDATE_INTERVAL = 0.1f;

        private void Awake()
        {
            InitializeComponents();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 获取或创建音频源
            if (audioSource == null)
            {
                audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                }
            }

            // 配置音频源
            audioSource.playOnAwake = false;
            audioSource.loop = false;

            // 获取HTTP客户端
            httpClient = FindObjectOfType<HttpClient>();
            if (httpClient == null)
            {
                var httpClientGO = new GameObject("HttpClient");
                httpClientGO.transform.SetParent(transform);
                httpClient = httpClientGO.AddComponent<HttpClient>();
            }

            // 初始化配置
            if (configuration == null)
            {
                configuration = new TTSConfiguration();
            }

            cachedVoices = new List<VoiceInfo>();
            currentState = TTSPlaybackState.Idle;
        }

        /// <summary>
        /// 初始化TTS服务
        /// </summary>
        /// <param name="configuration">TTS配置</param>
        /// <returns>初始化是否成功</returns>
        public bool Initialize(TTSConfiguration configuration)
        {
            try
            {
                if (configuration == null || !configuration.IsValid())
                {
                    LogError("TTS配置无效");
                    return false;
                }

                this.configuration = configuration;
                
                // 应用音频设置
                if (audioSource != null)
                {
                    audioSource.volume = configuration.DefaultVolume;
                }

                // 加载音色列表
                _ = RefreshVoicesAsync();

                isInitialized = true;
                LogDebug("TTS服务初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"TTS服务初始化失败: {ex.Message}");
                OnSynthesisError?.Invoke($"TTS服务初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 合成语音
        /// </summary>
        /// <param name="text">要合成的文本</param>
        /// <param name="voiceId">音色ID，为空则使用默认音色</param>
        /// <param name="language">语言代码，为空则使用默认语言</param>
        /// <returns>合成结果</returns>
        public async Task<TTSResponse> SynthesizeAsync(string text, string voiceId = null, string language = null)
        {
            if (!isInitialized)
            {
                LogError("TTS服务未初始化");
                return new TTSResponse { Success = false, ErrorMessage = "TTS服务未初始化" };
            }

            if (string.IsNullOrEmpty(text))
            {
                LogError("合成文本为空");
                return new TTSResponse { Success = false, ErrorMessage = "合成文本为空" };
            }

            try
            {
                SetPlaybackState(TTSPlaybackState.Synthesizing);

                // 构建请求
                var request = new TTSRequest
                {
                    Text = text,
                    Voice = voiceId ?? configuration.DefaultVoice,
                    Language = language ?? configuration.DefaultLanguage,
                    Speed = configuration.DefaultSpeed,
                    Pitch = configuration.DefaultPitch,
                    Volume = configuration.DefaultVolume,
                    AudioFormat = configuration.AudioFormat,
                    SampleRate = configuration.SampleRate,
                    EnableSSML = configuration.EnableSSML
                };

                // 发送请求
                var response = await SendTTSRequestAsync(request);
                
                if (response.Success)
                {
                    LogDebug($"语音合成成功: {text.Substring(0, Math.Min(text.Length, 50))}...");
                    OnSynthesisCompleted?.Invoke(response);
                    SetPlaybackState(TTSPlaybackState.Idle);
                }
                else
                {
                    LogError($"语音合成失败: {response.ErrorMessage}");
                    OnSynthesisError?.Invoke(response.ErrorMessage);
                    SetPlaybackState(TTSPlaybackState.Error);
                }

                return response;
            }
            catch (Exception ex)
            {
                LogError($"语音合成异常: {ex.Message}");
                SetPlaybackState(TTSPlaybackState.Error);
                var errorResponse = new TTSResponse 
                { 
                    Success = false, 
                    ErrorMessage = $"语音合成异常: {ex.Message}" 
                };
                OnSynthesisError?.Invoke(errorResponse.ErrorMessage);
                return errorResponse;
            }
        }

        /// <summary>
        /// 合成并播放语音
        /// </summary>
        /// <param name="text">要合成的文本</param>
        /// <param name="voiceId">音色ID</param>
        /// <param name="language">语言代码</param>
        /// <returns>合成是否成功</returns>
        public async Task<bool> SynthesizeAndPlayAsync(string text, string voiceId = null, string language = null)
        {
            try
            {
                var response = await SynthesizeAsync(text, voiceId, language);
                
                if (response.Success && !string.IsNullOrEmpty(response.AudioData))
                {
                    var audioClip = ConvertBase64ToAudioClip(response.AudioData, response.SampleRate);
                    if (audioClip != null)
                    {
                        PlayAudio(audioClip, text);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError($"合成并播放失败: {ex.Message}");
                OnSynthesisError?.Invoke($"合成并播放失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 播放音频剪辑
        /// </summary>
        /// <param name="audioClip">音频剪辑</param>
        /// <param name="text">对应的文本（用于同步显示）</param>
        public void PlayAudio(AudioClip audioClip, string text = null)
        {
            if (audioClip == null)
            {
                LogError("音频剪辑为空");
                return;
            }

            try
            {
                // 停止当前播放
                StopPlayback();

                // 设置新的音频
                currentAudioClip = audioClip;
                currentText = text ?? string.Empty;
                audioSource.clip = audioClip;

                // 开始播放
                audioSource.Play();
                playbackStartTime = Time.time;
                
                SetPlaybackState(TTSPlaybackState.Playing);
                
                // 启动播放监控协程
                playbackCoroutine = StartCoroutine(PlaybackMonitorCoroutine());

                LogDebug($"开始播放音频: {audioClip.name}");
            }
            catch (Exception ex)
            {
                LogError($"播放音频失败: {ex.Message}");
                OnSynthesisError?.Invoke($"播放音频失败: {ex.Message}");
                SetPlaybackState(TTSPlaybackState.Error);
            }
        }

        /// <summary>
        /// 暂停播放
        /// </summary>
        public void PausePlayback()
        {
            try
            {
                if (audioSource != null && audioSource.isPlaying)
                {
                    audioSource.Pause();
                    SetPlaybackState(TTSPlaybackState.Paused);
                    LogDebug("暂停音频播放");
                }
            }
            catch (Exception ex)
            {
                LogError($"暂停播放失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复播放
        /// </summary>
        public void ResumePlayback()
        {
            try
            {
                if (audioSource != null && currentState == TTSPlaybackState.Paused)
                {
                    audioSource.UnPause();
                    SetPlaybackState(TTSPlaybackState.Playing);
                    LogDebug("恢复音频播放");
                }
            }
            catch (Exception ex)
            {
                LogError($"恢复播放失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止播放
        /// </summary>
        public void StopPlayback()
        {
            try
            {
                if (audioSource != null && (audioSource.isPlaying || currentState == TTSPlaybackState.Paused))
                {
                    audioSource.Stop();
                    
                    if (playbackCoroutine != null)
                    {
                        StopCoroutine(playbackCoroutine);
                        playbackCoroutine = null;
                    }
                    
                    SetPlaybackState(TTSPlaybackState.Idle);
                    LogDebug("停止音频播放");
                }
            }
            catch (Exception ex)
            {
                LogError($"停止播放失败: {ex.Message}");
            }
        }        
/// <summary>
        /// 设置播放音量
        /// </summary>
        /// <param name="volume">音量 (0.0 - 1.0)</param>
        public void SetVolume(float volume)
        {
            try
            {
                float clampedVolume = Mathf.Clamp01(volume);
                if (audioSource != null)
                {
                    audioSource.volume = clampedVolume;
                }
                configuration.DefaultVolume = clampedVolume;
                LogDebug($"设置播放音量: {clampedVolume:F2}");
            }
            catch (Exception ex)
            {
                LogError($"设置音量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置播放速度
        /// </summary>
        /// <param name="speed">播放速度 (0.5 - 2.0)</param>
        public void SetPlaybackSpeed(float speed)
        {
            try
            {
                float clampedSpeed = Mathf.Clamp(speed, 0.5f, 2.0f);
                if (audioSource != null)
                {
                    audioSource.pitch = clampedSpeed;
                }
                LogDebug($"设置播放速度: {clampedSpeed:F2}");
            }
            catch (Exception ex)
            {
                LogError($"设置播放速度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取可用音色列表
        /// </summary>
        /// <returns>音色列表</returns>
        public VoiceInfo[] GetAvailableVoices()
        {
            return cachedVoices.ToArray();
        }

        /// <summary>
        /// 获取指定语言的音色列表
        /// </summary>
        /// <param name="language">语言代码</param>
        /// <returns>音色列表</returns>
        public VoiceInfo[] GetVoicesByLanguage(string language)
        {
            if (string.IsNullOrEmpty(language))
            {
                return GetAvailableVoices();
            }

            return cachedVoices.FindAll(v => 
                v.Language.Equals(language, StringComparison.OrdinalIgnoreCase)).ToArray();
        }

        /// <summary>
        /// 预览音色
        /// </summary>
        /// <param name="voiceId">音色ID</param>
        /// <param name="sampleText">示例文本</param>
        /// <returns>预览是否成功</returns>
        public async Task<bool> PreviewVoiceAsync(string voiceId, string sampleText = null)
        {
            if (string.IsNullOrEmpty(voiceId))
            {
                LogError("音色ID为空");
                return false;
            }

            try
            {
                string textToSpeak = sampleText ?? DEFAULT_SAMPLE_TEXT;
                var voice = cachedVoices.Find(v => v.VoiceId == voiceId);
                
                if (voice != null)
                {
                    return await SynthesizeAndPlayAsync(textToSpeak, voiceId, voice.Language);
                }
                else
                {
                    LogError($"未找到音色: {voiceId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"预览音色失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 刷新音色列表
        /// </summary>
        /// <returns>刷新是否成功</returns>
        public async Task<bool> RefreshVoicesAsync()
        {
            if (!isInitialized)
            {
                LogError("TTS服务未初始化");
                return false;
            }

            try
            {
                // 这里应该调用API获取音色列表，暂时使用默认音色
                var defaultVoices = GetDefaultVoices();
                cachedVoices.Clear();
                cachedVoices.AddRange(defaultVoices);

                // 更新配置中的音色列表
                configuration.AvailableVoices = new List<VoiceInfo>(cachedVoices);

                OnVoicesUpdated?.Invoke(cachedVoices.ToArray());
                LogDebug($"刷新音色列表完成，共 {cachedVoices.Count} 个音色");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"刷新音色列表失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        /// <returns>TTS配置</returns>
        public TTSConfiguration GetConfiguration()
        {
            return configuration;
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        public void UpdateConfiguration(TTSConfiguration configuration)
        {
            if (configuration != null && configuration.IsValid())
            {
                this.configuration = configuration;
                
                // 应用音频设置
                if (audioSource != null)
                {
                    audioSource.volume = configuration.DefaultVolume;
                }

                LogDebug("TTS配置已更新");
            }
            else
            {
                LogError("无效的TTS配置");
            }
        }

        /// <summary>
        /// 测试服务连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        public async Task<bool> TestConnectionAsync()
        {
            if (!isInitialized)
            {
                LogError("TTS服务未初始化");
                return false;
            }

            try
            {
                var testResponse = await SynthesizeAsync("测试", configuration.DefaultVoice);
                bool isConnected = testResponse.Success;
                
                LogDebug($"TTS服务连接测试: {(isConnected ? "成功" : "失败")}");
                return isConnected;
            }
            catch (Exception ex)
            {
                LogError($"TTS服务连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取当前播放状态
        /// </summary>
        /// <returns>播放状态</returns>
        public TTSPlaybackState GetPlaybackState()
        {
            return currentState;
        }

        /// <summary>
        /// 获取播放进度
        /// </summary>
        /// <returns>播放进度 (0.0 - 1.0)</returns>
        public float GetPlaybackProgress()
        {
            if (audioSource == null || currentAudioClip == null || currentState != TTSPlaybackState.Playing)
            {
                return 0f;
            }

            try
            {
                return audioSource.time / currentAudioClip.length;
            }
            catch
            {
                return 0f;
            }
        }

        /// <summary>
        /// 取消当前合成任务
        /// </summary>
        public void CancelSynthesis()
        {
            try
            {
                StopPlayback();
                SetPlaybackState(TTSPlaybackState.Idle);
                LogDebug("取消TTS合成任务");
            }
            catch (Exception ex)
            {
                LogError($"取消合成任务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopPlayback();
                
                if (currentAudioClip != null)
                {
                    DestroyImmediate(currentAudioClip);
                    currentAudioClip = null;
                }

                cachedVoices?.Clear();
                isInitialized = false;
                LogDebug("TTS服务资源释放完成");
            }
            catch (Exception ex)
            {
                LogError($"释放TTS服务资源失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 发送TTS请求
        /// </summary>
        /// <param name="request">TTS请求</param>
        /// <returns>TTS响应</returns>
        private async Task<TTSResponse> SendTTSRequestAsync(TTSRequest request)
        {
            try
            {
                // 构建请求数据
                var requestData = JsonUtility.ToJson(request);
                var headers = new Dictionary<string, string>
                {
                    ["Content-Type"] = "application/json"
                };

                // 添加API密钥
                if (!string.IsNullOrEmpty(configuration.ApiKey))
                {
                    headers["Authorization"] = $"Bearer {configuration.ApiKey}";
                }

                // 发送HTTP请求
                var httpResponse = await httpClient.PostAsync(
                    configuration.ApiEndpoint, 
                    requestData, 
                    headers
                );

                if (httpResponse.IsSuccess)
                {
                    var response = JsonUtility.FromJson<TTSResponse>(httpResponse.Data);
                    response.Success = true;
                    return response;
                }
                else
                {
                    return new TTSResponse
                    {
                        Success = false,
                        ErrorMessage = httpResponse.ErrorMessage
                    };
                }
            }
            catch (Exception ex)
            {
                LogError($"发送TTS请求失败: {ex.Message}");
                return new TTSResponse
                {
                    Success = false,
                    ErrorMessage = $"发送TTS请求失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 播放监控协程
        /// </summary>
        /// <returns>协程</returns>
        private IEnumerator PlaybackMonitorCoroutine()
        {
            while (audioSource != null && audioSource.isPlaying)
            {
                // 发送播放进度事件
                var playbackEvent = new TTSPlaybackEvent
                {
                    State = TTSPlaybackState.Playing,
                    CurrentTime = audioSource.time,
                    TotalTime = currentAudioClip?.length ?? 0f,
                    CurrentText = currentText
                };

                OnPlaybackStateChanged?.Invoke(playbackEvent);

                yield return new WaitForSeconds(PLAYBACK_UPDATE_INTERVAL);
            }

            // 播放完成
            if (currentState == TTSPlaybackState.Playing)
            {
                SetPlaybackState(TTSPlaybackState.Completed);
            }
        }

        /// <summary>
        /// 将Base64音频数据转换为AudioClip
        /// </summary>
        /// <param name="base64Data">Base64音频数据</param>
        /// <param name="sampleRate">采样率</param>
        /// <returns>音频剪辑</returns>
        private AudioClip ConvertBase64ToAudioClip(string base64Data, int sampleRate)
        {
            try
            {
                byte[] audioBytes = Convert.FromBase64String(base64Data);
                
                // 假设是WAV格式，跳过文件头（44字节）
                int headerSize = 44;
                if (audioBytes.Length <= headerSize)
                {
                    LogError("音频数据太短");
                    return null;
                }

                byte[] pcmData = new byte[audioBytes.Length - headerSize];
                Array.Copy(audioBytes, headerSize, pcmData, 0, pcmData.Length);

                // 转换为float数组
                float[] floatData = new float[pcmData.Length / 2];
                for (int i = 0; i < floatData.Length; i++)
                {
                    short sample = BitConverter.ToInt16(pcmData, i * 2);
                    floatData[i] = sample / 32768f;
                }

                // 创建AudioClip
                AudioClip audioClip = AudioClip.Create(
                    "TTSAudio",
                    floatData.Length,
                    1, // 单声道
                    sampleRate,
                    false
                );

                audioClip.SetData(floatData, 0);
                return audioClip;
            }
            catch (Exception ex)
            {
                LogError($"转换音频数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取默认音色列表
        /// </summary>
        /// <returns>默认音色列表</returns>
        private List<VoiceInfo> GetDefaultVoices()
        {
            return new List<VoiceInfo>
            {
                new VoiceInfo("zh-CN-female-1", "小雅", "zh-CN", "Female")
                {
                    Description = "温柔女声",
                    IsDefault = true,
                    IsAvailable = true
                },
                new VoiceInfo("zh-CN-male-1", "小明", "zh-CN", "Male")
                {
                    Description = "磁性男声",
                    IsDefault = false,
                    IsAvailable = true
                },
                new VoiceInfo("en-US-female-1", "Emma", "en-US", "Female")
                {
                    Description = "美式英语女声",
                    IsDefault = false,
                    IsAvailable = true
                },
                new VoiceInfo("en-US-male-1", "John", "en-US", "Male")
                {
                    Description = "美式英语男声",
                    IsDefault = false,
                    IsAvailable = true
                }
            };
        }

        /// <summary>
        /// 设置播放状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void SetPlaybackState(TTSPlaybackState newState)
        {
            if (currentState != newState)
            {
                currentState = newState;
                
                var playbackEvent = new TTSPlaybackEvent
                {
                    State = currentState,
                    CurrentTime = audioSource?.time ?? 0f,
                    TotalTime = currentAudioClip?.length ?? 0f,
                    CurrentText = currentText
                };

                OnPlaybackStateChanged?.Invoke(playbackEvent);
                LogDebug($"TTS播放状态变更: {currentState}");
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[TTSService] {message}");
            }
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[TTSService] {message}");
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogError(string message)
        {
            Debug.LogError($"[TTSService] {message}");
        }

        #endregion

        #region Unity生命周期

        private void OnDestroy()
        {
            Dispose();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == TTSPlaybackState.Playing)
            {
                PausePlayback();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == TTSPlaybackState.Playing)
            {
                PausePlayback();
            }
        }

        #endregion
    }
}