using UnityEngine;
using UnityEngine.UI;
using DigitalHuman.Core.Camera;
using DigitalHuman.Core.Network;
using DigitalHuman.Core.Rendering;
using DigitalHuman.Core.Rendering.Models;
using System.Collections.Generic;

namespace DigitalHuman.Examples
{
    /// <summary>
    /// 情感交互系统综合示例
    /// 演示摄像头、面部检测、情感分析和数字人响应的完整流程
    /// </summary>
    public class EmotionInteractionExample : MonoBehaviour
    {
        [Header("UI 组件")]
        [SerializeField] private RawImage videoDisplay;
        [SerializeField] private RawImage faceOverlay;
        [SerializeField] private Text statusText;
        [SerializeField] private Text emotionText;
        [SerializeField] private Text faceInfoText;
        [SerializeField] private Button startButton;
        [SerializeField] private Button stopButton;
        [SerializeField] private Toggle enableEmotionToggle;
        [SerializeField] private Slider confidenceSlider;
        
        [Header("系统组件")]
        [SerializeField] private CameraSystemExample cameraSystem;
        [SerializeField] private DigitalHumanRenderer digitalHumanRenderer;
        
        [Header("配置")]
        [SerializeField] private EmotionAnalysisConfig emotionConfig = new EmotionAnalysisConfig();
        [SerializeField] private FaceDetectionConfig faceConfig = new FaceDetectionConfig();
        [SerializeField] private EmotionResponseConfig responseConfig = new EmotionResponseConfig();
        
        // 私有组件
        private FaceDetector faceDetector;
        private EmotionApiClient emotionApiClient;
        private EmotionResponseController emotionResponseController;
        private bool isSystemRunning = false;
        private float lastEmotionAnalysisTime = 0f;
        private Texture2D faceOverlayTexture;
        
        void Start()
        {
            InitializeSystem();
            SetupUI();
        }
        
        void OnDestroy()
        {
            CleanupSystem();
        }
        
        /// <summary>
        /// 初始化整个系统
        /// </summary>
        private void InitializeSystem()
        {
            try
            {
                // 初始化摄像头系统
                if (cameraSystem == null)
                {
                    cameraSystem = FindObjectOfType<CameraSystemExample>();
                }
                
                // 初始化面部检测器
                GameObject faceDetectorObject = new GameObject("FaceDetector");
                faceDetectorObject.transform.SetParent(transform);
                faceDetector = faceDetectorObject.AddComponent<FaceDetector>();
                faceDetector.Initialize(faceConfig);
                
                // 订阅面部检测事件
                faceDetector.OnFacesDetected += OnFacesDetected;
                faceDetector.OnPrimaryFaceChanged += OnPrimaryFaceChanged;
                faceDetector.OnUserLeft += OnUserLeft;
                faceDetector.OnUserReturned += OnUserReturned;
                
                // 初始化情感分析客户端
                GameObject emotionClientObject = new GameObject("EmotionApiClient");
                emotionClientObject.transform.SetParent(transform);
                emotionApiClient = emotionClientObject.AddComponent<EmotionApiClient>();
                emotionApiClient.Initialize(emotionConfig);
                
                // 订阅情感分析事件
                emotionApiClient.OnEmotionAnalyzed += OnEmotionAnalyzed;
                emotionApiClient.OnAnalysisError += OnEmotionAnalysisError;
                
                // 初始化情感响应控制器
                if (digitalHumanRenderer == null)
                {
                    digitalHumanRenderer = FindObjectOfType<DigitalHumanRenderer>();
                }
                
                if (digitalHumanRenderer != null)
                {
                    GameObject emotionControllerObject = new GameObject("EmotionResponseController");
                    emotionControllerObject.transform.SetParent(transform);
                    emotionResponseController = emotionControllerObject.AddComponent<EmotionResponseController>();
                    emotionResponseController.Initialize(digitalHumanRenderer, responseConfig);
                    
                    // 订阅情感响应事件
                    emotionResponseController.OnEmotionChanged += OnDigitalHumanEmotionChanged;
                    emotionResponseController.OnAnimationStarted += OnAnimationStarted;
                    emotionResponseController.OnAnimationCompleted += OnAnimationCompleted;
                }
                
                // 创建面部覆盖纹理
                faceOverlayTexture = new Texture2D(640, 480, TextureFormat.RGBA32, false);
                
                Debug.Log("[EmotionInteractionExample] 系统初始化完成");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EmotionInteractionExample] 系统初始化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 设置UI组件
        /// </summary>
        private void SetupUI()
        {
            if (startButton != null)
            {
                startButton.onClick.AddListener(StartSystem);
            }
            
            if (stopButton != null)
            {
                stopButton.onClick.AddListener(StopSystem);
            }
            
            if (enableEmotionToggle != null)
            {
                enableEmotionToggle.onValueChanged.AddListener(OnEmotionToggleChanged);
            }
            
            if (confidenceSlider != null)
            {
                confidenceSlider.onValueChanged.AddListener(OnConfidenceSliderChanged);
                confidenceSlider.value = faceConfig.confidenceThreshold;
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 开始系统
        /// </summary>
        private void StartSystem()
        {
            if (isSystemRunning)
            {
                Debug.LogWarning("[EmotionInteractionExample] 系统已在运行中");
                return;
            }
            
            try
            {
                // 启动摄像头
                if (cameraSystem != null)
                {
                    cameraSystem.RequestPermission();
                }
                
                // 启动面部检测
                if (faceDetector != null)
                {
                    faceDetector.StartDetection();
                }
                
                isSystemRunning = true;
                Debug.Log("[EmotionInteractionExample] 系统启动成功");
                UpdateUI();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EmotionInteractionExample] 系统启动失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 停止系统
        /// </summary>
        private void StopSystem()
        {
            if (!isSystemRunning)
            {
                return;
            }
            
            try
            {
                // 停止面部检测
                if (faceDetector != null)
                {
                    faceDetector.StopDetection();
                }
                
                // 重置数字人情感
                if (emotionResponseController != null)
                {
                    emotionResponseController.ResetToNeutral();
                }
                
                isSystemRunning = false;
                Debug.Log("[EmotionInteractionExample] 系统已停止");
                UpdateUI();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EmotionInteractionExample] 系统停止失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 面部检测结果回调
        /// </summary>
        /// <param name="faces">检测到的面部列表</param>
        private void OnFacesDetected(List<FaceInfo> faces)
        {
            UpdateFaceDisplay(faces);
            
            // 如果检测到面部且启用情感分析，进行情感分析
            if (faces.Count > 0 && enableEmotionToggle != null && enableEmotionToggle.isOn)
            {
                var primaryFace = faces.Find(f => f.isPrimary) ?? faces[0];
                AnalyzeEmotion(primaryFace);
            }
        }
        
        /// <summary>
        /// 主要面部变化回调
        /// </summary>
        /// <param name="face">主要面部信息</param>
        private void OnPrimaryFaceChanged(FaceInfo face)
        {
            if (face != null)
            {
                Debug.Log($"[EmotionInteractionExample] 主要面部变化: {face}");
            }
        }
        
        /// <summary>
        /// 用户离开回调
        /// </summary>
        private void OnUserLeft()
        {
            Debug.Log("[EmotionInteractionExample] 用户离开");
            
            // 数字人表现出寻找用户的行为
            if (emotionResponseController != null)
            {
                emotionResponseController.SetEmotion(Models.EmotionType.Confused, 0.6f, 2f);
            }
        }
        
        /// <summary>
        /// 用户返回回调
        /// </summary>
        /// <param name="face">返回的用户面部信息</param>
        private void OnUserReturned(FaceInfo face)
        {
            Debug.Log($"[EmotionInteractionExample] 用户返回: {face}");
            
            // 数字人表现出欢迎的行为
            if (emotionResponseController != null)
            {
                emotionResponseController.SetEmotion(Models.EmotionType.Happy, 0.8f, 3f);
            }
        }
        
        /// <summary>
        /// 分析情感
        /// </summary>
        /// <param name="face">面部信息</param>
        private async void AnalyzeEmotion(FaceInfo face)
        {
            // 限制分析频率
            if (Time.time - lastEmotionAnalysisTime < 2f)
            {
                return;
            }
            
            lastEmotionAnalysisTime = Time.time;
            
            try
            {
                // 获取当前视频帧
                var currentFrame = cameraSystem?.GetCurrentFrame();
                if (currentFrame == null || emotionApiClient == null)
                {
                    return;
                }
                
                // 裁剪面部区域（可选优化）
                var faceTexture = CropFaceFromFrame(currentFrame, face);
                
                // 进行情感分析
                var emotionData = await emotionApiClient.AnalyzeEmotionAsync(faceTexture ?? currentFrame);
                
                if (faceTexture != null && faceTexture != currentFrame)
                {
                    DestroyImmediate(faceTexture);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EmotionInteractionExample] 情感分析失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 从视频帧中裁剪面部区域
        /// </summary>
        /// <param name="frame">原始帧</param>
        /// <param name="face">面部信息</param>
        /// <returns>裁剪后的面部纹理</returns>
        private Texture2D CropFaceFromFrame(Texture2D frame, FaceInfo face)
        {
            try
            {
                // 计算裁剪区域
                int x = Mathf.RoundToInt(face.boundingBox.x * frame.width);
                int y = Mathf.RoundToInt(face.boundingBox.y * frame.height);
                int width = Mathf.RoundToInt(face.boundingBox.width * frame.width);
                int height = Mathf.RoundToInt(face.boundingBox.height * frame.height);
                
                // 确保裁剪区域在有效范围内
                x = Mathf.Clamp(x, 0, frame.width - 1);
                y = Mathf.Clamp(y, 0, frame.height - 1);
                width = Mathf.Clamp(width, 1, frame.width - x);
                height = Mathf.Clamp(height, 1, frame.height - y);
                
                // 创建裁剪纹理
                var croppedTexture = new Texture2D(width, height);
                var pixels = frame.GetPixels(x, y, width, height);
                croppedTexture.SetPixels(pixels);
                croppedTexture.Apply();
                
                return croppedTexture;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EmotionInteractionExample] 裁剪面部失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 情感分析结果回调
        /// </summary>
        /// <param name="emotionData">情感数据</param>
        private void OnEmotionAnalyzed(Network.EmotionData emotionData)
        {
            Debug.Log($"[EmotionInteractionExample] 检测到情感: {emotionData}");
            
            // 将情感传递给响应控制器
            if (emotionResponseController != null)
            {
                emotionResponseController.ProcessEmotion(emotionData);
            }
            
            // 更新UI显示
            UpdateEmotionDisplay(emotionData);
        }
        
        /// <summary>
        /// 情感分析错误回调
        /// </summary>
        /// <param name="error">错误信息</param>
        private void OnEmotionAnalysisError(string error)
        {
            Debug.LogWarning($"[EmotionInteractionExample] 情感分析错误: {error}");
        }
        
        /// <summary>
        /// 数字人情感变化回调
        /// </summary>
        /// <param name="previousEmotion">之前的情感</param>
        /// <param name="currentEmotion">当前情感</param>
        private void OnDigitalHumanEmotionChanged(Models.EmotionType previousEmotion, Models.EmotionType currentEmotion)
        {
            Debug.Log($"[EmotionInteractionExample] 数字人情感变化: {previousEmotion} -> {currentEmotion}");
        }
        
        /// <summary>
        /// 动画开始回调
        /// </summary>
        /// <param name="animationName">动画名称</param>
        private void OnAnimationStarted(string animationName)
        {
            Debug.Log($"[EmotionInteractionExample] 动画开始: {animationName}");
        }
        
        /// <summary>
        /// 动画完成回调
        /// </summary>
        /// <param name="animationName">动画名称</param>
        private void OnAnimationCompleted(string animationName)
        {
            Debug.Log($"[EmotionInteractionExample] 动画完成: {animationName}");
        }
        
        /// <summary>
        /// 情感开关变化回调
        /// </summary>
        /// <param name="enabled">是否启用</param>
        private void OnEmotionToggleChanged(bool enabled)
        {
            Debug.Log($"[EmotionInteractionExample] 情感分析 {(enabled ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 置信度滑块变化回调
        /// </summary>
        /// <param name="value">置信度值</param>
        private void OnConfidenceSliderChanged(float value)
        {
            if (faceDetector != null)
            {
                faceConfig.confidenceThreshold = value;
                faceDetector.SetConfiguration(faceConfig);
            }
        }
        
        /// <summary>
        /// 更新面部显示
        /// </summary>
        /// <param name="faces">面部列表</param>
        private void UpdateFaceDisplay(List<FaceInfo> faces)
        {
            if (faceOverlay == null || faceOverlayTexture == null)
                return;
            
            // 清空覆盖纹理
            var clearPixels = new Color32[faceOverlayTexture.width * faceOverlayTexture.height];
            for (int i = 0; i < clearPixels.Length; i++)
            {
                clearPixels[i] = Color.clear;
            }
            faceOverlayTexture.SetPixels32(clearPixels);
            
            // 绘制面部边界框
            foreach (var face in faces)
            {
                DrawFaceBoundingBox(face);
            }
            
            faceOverlayTexture.Apply();
            faceOverlay.texture = faceOverlayTexture;
            
            // 更新面部信息文本
            if (faceInfoText != null)
            {
                if (faces.Count > 0)
                {
                    var primaryFace = faces.Find(f => f.isPrimary) ?? faces[0];
                    faceInfoText.text = $"面部数量: {faces.Count}\n主要面部: {primaryFace}";
                }
                else
                {
                    faceInfoText.text = "未检测到面部";
                }
            }
        }
        
        /// <summary>
        /// 绘制面部边界框
        /// </summary>
        /// <param name="face">面部信息</param>
        private void DrawFaceBoundingBox(FaceInfo face)
        {
            // 简化的边界框绘制（实际项目中可以使用更复杂的绘制方法）
            int x = Mathf.RoundToInt(face.boundingBox.x * faceOverlayTexture.width);
            int y = Mathf.RoundToInt(face.boundingBox.y * faceOverlayTexture.height);
            int width = Mathf.RoundToInt(face.boundingBox.width * faceOverlayTexture.width);
            int height = Mathf.RoundToInt(face.boundingBox.height * faceOverlayTexture.height);
            
            Color boxColor = face.isPrimary ? Color.green : Color.yellow;
            
            // 绘制边界框边缘
            DrawRectangleOutline(x, y, width, height, boxColor);
        }
        
        /// <summary>
        /// 绘制矩形轮廓
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="color">颜色</param>
        private void DrawRectangleOutline(int x, int y, int width, int height, Color color)
        {
            // 绘制上边
            for (int i = 0; i < width; i++)
            {
                SetPixelSafe(x + i, y, color);
                SetPixelSafe(x + i, y + height - 1, color);
            }
            
            // 绘制左右边
            for (int i = 0; i < height; i++)
            {
                SetPixelSafe(x, y + i, color);
                SetPixelSafe(x + width - 1, y + i, color);
            }
        }
        
        /// <summary>
        /// 安全设置像素
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="color">颜色</param>
        private void SetPixelSafe(int x, int y, Color color)
        {
            if (x >= 0 && x < faceOverlayTexture.width && y >= 0 && y < faceOverlayTexture.height)
            {
                faceOverlayTexture.SetPixel(x, y, color);
            }
        }
        
        /// <summary>
        /// 更新情感显示
        /// </summary>
        /// <param name="emotionData">情感数据</param>
        private void UpdateEmotionDisplay(Network.EmotionData emotionData)
        {
            if (emotionText != null)
            {
                emotionText.text = $"检测到情感: {emotionData.GetChineseDescription()}\n" +
                                  $"置信度: {emotionData.confidence:F2}\n" +
                                  $"强度: {emotionData.intensity:F2}";
            }
        }
        
        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            if (startButton != null)
            {
                startButton.interactable = !isSystemRunning;
            }
            
            if (stopButton != null)
            {
                stopButton.interactable = isSystemRunning;
            }
            
            if (statusText != null)
            {
                statusText.text = $"系统状态: {(isSystemRunning ? "运行中" : "已停止")}";
            }
        }
        
        /// <summary>
        /// 清理系统
        /// </summary>
        private void CleanupSystem()
        {
            StopSystem();
            
            if (faceDetector != null)
            {
                faceDetector.Cleanup();
            }
            
            if (emotionApiClient != null)
            {
                emotionApiClient.Cleanup();
            }
            
            if (emotionResponseController != null)
            {
                emotionResponseController.Cleanup();
            }
            
            if (faceOverlayTexture != null)
            {
                DestroyImmediate(faceOverlayTexture);
            }
            
            Debug.Log("[EmotionInteractionExample] 系统清理完成");
        }
    }
}