using UnityEngine;
using DigitalHuman.Core.Rendering;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Examples
{
    /// <summary>
    /// 数字人渲染系统使用示例
    /// 展示如何集成和使用3D渲染、视频播放、绿幕处理和模式切换功能
    /// </summary>
    public class RenderingSystemExample : MonoBehaviour
    {
        [Header("渲染组件")]
        [SerializeField] private RenderModeManager _renderModeManager;
        [SerializeField] private DigitalHumanRenderer _digitalHumanRenderer;
        [SerializeField] private VideoPlayerController _videoPlayerController;
        [SerializeField] private GreenScreenProcessor _greenScreenProcessor;
        
        [Header("测试配置")]
        [SerializeField] private RenderModeConfig _renderModeConfig;
        [SerializeField] private Texture2D _testBackgroundTexture;
        [SerializeField] private AudioClip _testAudioClip;
        
        [Header("UI控制")]
        [SerializeField] private KeyCode _switchToVideoKey = KeyCode.V;
        [SerializeField] private KeyCode _switchTo3DKey = KeyCode.M;
        [SerializeField] private KeyCode _playAnimationKey = KeyCode.A;
        [SerializeField] private KeyCode _setEmotionKey = KeyCode.E;
        [SerializeField] private KeyCode _startLipSyncKey = KeyCode.L;
        [SerializeField] private KeyCode _toggleGreenScreenKey = KeyCode.G;

        private bool _isInitialized = false;

        #region Unity生命周期
        private void Start()
        {
            InitializeRenderingSystem();
        }

        private void Update()
        {
            if (!_isInitialized) return;
            
            HandleInput();
            UpdateUI();
        }

        private void OnGUI()
        {
            if (!_isInitialized) return;
            
            DrawDebugUI();
        }
        #endregion

        #region 初始化
        private void InitializeRenderingSystem()
        {
            try
            {
                // 初始化渲染模式管理器
                if (_renderModeManager != null && _renderModeConfig != null)
                {
                    bool success = _renderModeManager.Initialize(_renderModeConfig);
                    if (!success)
                    {
                        Debug.LogError("渲染模式管理器初始化失败");
                        return;
                    }
                    
                    // 订阅事件
                    SubscribeToEvents();
                }

                _isInitialized = true;
                Debug.Log("数字人渲染系统初始化成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"渲染系统初始化失败: {ex.Message}");
            }
        }

        private void SubscribeToEvents()
        {
            // 渲染模式管理器事件
            _renderModeManager.OnModeChanged += OnRenderModeChanged;
            _renderModeManager.OnModeTransitionStarted += OnModeTransitionStarted;
            _renderModeManager.OnModeTransitionCompleted += OnModeTransitionCompleted;
            _renderModeManager.OnPerformanceWarning += OnPerformanceWarning;
            _renderModeManager.OnRenderingError += OnRenderingError;

            // 数字人渲染器事件
            if (_digitalHumanRenderer != null)
            {
                _digitalHumanRenderer.OnAnimationChanged += OnAnimationChanged;
                _digitalHumanRenderer.OnEmotionChanged += OnEmotionChanged;
            }

            // 视频播放控制器事件
            if (_videoPlayerController != null)
            {
                _videoPlayerController.OnStateChanged += OnVideoStateChanged;
                _videoPlayerController.OnVideoStarted += OnVideoStarted;
                _videoPlayerController.OnVideoFinished += OnVideoFinished;
            }

            // 绿幕处理器事件
            if (_greenScreenProcessor != null)
            {
                _greenScreenProcessor.OnGreenScreenDetected += OnGreenScreenDetected;
                _greenScreenProcessor.OnBackgroundComposed += OnBackgroundComposed;
            }
        }
        #endregion

        #region 输入处理
        private void HandleInput()
        {
            // 模式切换
            if (Input.GetKeyDown(_switchToVideoKey))
            {
                SwitchToVideoMode();
            }
            else if (Input.GetKeyDown(_switchTo3DKey))
            {
                SwitchTo3DMode();
            }

            // 3D模式控制
            if (_renderModeManager.CurrentMode == RenderMode.Model3D)
            {
                if (Input.GetKeyDown(_playAnimationKey))
                {
                    PlayRandomAnimation();
                }
                else if (Input.GetKeyDown(_setEmotionKey))
                {
                    SetRandomEmotion();
                }
                else if (Input.GetKeyDown(_startLipSyncKey))
                {
                    StartLipSyncTest();
                }
            }

            // 绿幕处理
            if (Input.GetKeyDown(_toggleGreenScreenKey))
            {
                ToggleGreenScreenProcessing();
            }
        }
        #endregion

        #region 渲染模式控制
        private void SwitchToVideoMode()
        {
            if (_renderModeManager != null)
            {
                _renderModeManager.SwitchMode(RenderMode.Video);
                Debug.Log("切换到视频模式");
            }
        }

        private void SwitchTo3DMode()
        {
            if (_renderModeManager != null)
            {
                _renderModeManager.SwitchMode(RenderMode.Model3D);
                Debug.Log("切换到3D模型模式");
            }
        }
        #endregion

        #region 3D模式控制
        private void PlayRandomAnimation()
        {
            if (_digitalHumanRenderer == null || !_digitalHumanRenderer.IsInitialized) return;

            AnimationType[] animations = {
                AnimationType.Speaking,
                AnimationType.Greeting,
                AnimationType.Gesture,
                AnimationType.Dancing,
                AnimationType.Thinking
            };

            AnimationType randomAnimation = animations[Random.Range(0, animations.Length)];
            _digitalHumanRenderer.PlayAnimation(randomAnimation);
            
            Debug.Log($"播放随机动画: {randomAnimation}");
        }

        private void SetRandomEmotion()
        {
            if (_digitalHumanRenderer == null || !_digitalHumanRenderer.IsInitialized) return;

            EmotionType[] emotions = {
                EmotionType.Happy,
                EmotionType.Sad,
                EmotionType.Angry,
                EmotionType.Surprised,
                EmotionType.Excited
            };

            EmotionType randomEmotion = emotions[Random.Range(0, emotions.Length)];
            float intensity = Random.Range(0.5f, 1.0f);
            
            _digitalHumanRenderer.SetEmotion(randomEmotion, intensity);
            
            Debug.Log($"设置随机表情: {randomEmotion}, 强度: {intensity:F2}");
        }

        private void StartLipSyncTest()
        {
            if (_digitalHumanRenderer == null || !_digitalHumanRenderer.IsInitialized) return;
            if (_testAudioClip == null) return;

            _digitalHumanRenderer.StartLipSync(_testAudioClip);
            Debug.Log("开始口型同步测试");
        }
        #endregion

        #region 绿幕处理控制
        private void ToggleGreenScreenProcessing()
        {
            if (_greenScreenProcessor == null || !_greenScreenProcessor.IsInitialized) return;

            // 切换背景模式
            BackgroundMode currentMode = _greenScreenProcessor.CurrentBackgroundMode;
            BackgroundMode newMode = currentMode == BackgroundMode.Transparent ? 
                                   BackgroundMode.Custom : 
                                   BackgroundMode.Transparent;

            _greenScreenProcessor.SetBackgroundMode(newMode);

            if (newMode == BackgroundMode.Custom && _testBackgroundTexture != null)
            {
                _greenScreenProcessor.SetCustomBackground(_testBackgroundTexture);
            }

            Debug.Log($"绿幕背景模式切换到: {newMode}");
        }
        #endregion

        #region UI更新
        private void UpdateUI()
        {
            // 这里可以添加UI更新逻辑
            // 例如：更新按钮状态、显示当前模式等
        }

        private void DrawDebugUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            
            GUILayout.Label("数字人渲染系统控制面板", GUI.skin.box);
            
            // 当前状态显示
            GUILayout.Label($"当前模式: {_renderModeManager.CurrentMode}");
            GUILayout.Label($"正在过渡: {_renderModeManager.IsTransitioning}");
            
            if (_renderModeManager.Performance != null)
            {
                GUILayout.Label($"FPS: {_renderModeManager.Performance.currentFPS:F1}");
                GUILayout.Label($"内存: {_renderModeManager.Performance.memoryUsage:F1}MB");
            }

            GUILayout.Space(10);

            // 控制按钮
            GUILayout.Label("控制说明:");
            GUILayout.Label($"[{_switchTo3DKey}] 切换到3D模式");
            GUILayout.Label($"[{_switchToVideoKey}] 切换到视频模式");
            GUILayout.Label($"[{_playAnimationKey}] 播放随机动画");
            GUILayout.Label($"[{_setEmotionKey}] 设置随机表情");
            GUILayout.Label($"[{_startLipSyncKey}] 开始口型同步");
            GUILayout.Label($"[{_toggleGreenScreenKey}] 切换绿幕处理");

            GUILayout.Space(10);

            // 手动控制按钮
            if (GUILayout.Button("切换到3D模式"))
            {
                SwitchTo3DMode();
            }

            if (GUILayout.Button("切换到视频模式"))
            {
                SwitchToVideoMode();
            }

            if (GUILayout.Button("播放随机动画"))
            {
                PlayRandomAnimation();
            }

            if (GUILayout.Button("设置随机表情"))
            {
                SetRandomEmotion();
            }

            if (GUILayout.Button("刷新渲染模式"))
            {
                _renderModeManager?.RefreshCurrentMode();
            }

            GUILayout.EndArea();
        }
        #endregion

        #region 事件处理
        private void OnRenderModeChanged(RenderMode newMode)
        {
            Debug.Log($"渲染模式已切换到: {newMode}");
        }

        private void OnModeTransitionStarted(RenderMode fromMode, RenderMode toMode)
        {
            Debug.Log($"开始模式过渡: {fromMode} -> {toMode}");
        }

        private void OnModeTransitionCompleted(RenderMode fromMode, RenderMode toMode)
        {
            Debug.Log($"模式过渡完成: {fromMode} -> {toMode}");
        }

        private void OnPerformanceWarning(string warning)
        {
            Debug.LogWarning($"性能警告: {warning}");
        }

        private void OnRenderingError(string error)
        {
            Debug.LogError($"渲染错误: {error}");
        }

        private void OnAnimationChanged(AnimationType animationType)
        {
            Debug.Log($"动画已切换到: {animationType}");
        }

        private void OnEmotionChanged(EmotionType emotionType)
        {
            Debug.Log($"表情已切换到: {emotionType}");
        }

        private void OnVideoStateChanged(VideoState oldState, VideoState newState)
        {
            Debug.Log($"视频状态已切换: {oldState} -> {newState}");
        }

        private void OnVideoStarted(VideoState state)
        {
            Debug.Log($"视频开始播放: {state}");
        }

        private void OnVideoFinished(VideoState state)
        {
            Debug.Log($"视频播放完成: {state}");
        }

        private void OnGreenScreenDetected(bool detected)
        {
            Debug.Log($"绿幕检测结果: {(detected ? "检测到" : "未检测到")}");
        }

        private void OnBackgroundComposed(RenderTexture composedTexture)
        {
            Debug.Log("背景合成完成");
        }
        #endregion

        #region 资源清理
        private void OnDestroy()
        {
            // 取消事件订阅
            if (_renderModeManager != null)
            {
                _renderModeManager.OnModeChanged -= OnRenderModeChanged;
                _renderModeManager.OnModeTransitionStarted -= OnModeTransitionStarted;
                _renderModeManager.OnModeTransitionCompleted -= OnModeTransitionCompleted;
                _renderModeManager.OnPerformanceWarning -= OnPerformanceWarning;
                _renderModeManager.OnRenderingError -= OnRenderingError;
            }

            if (_digitalHumanRenderer != null)
            {
                _digitalHumanRenderer.OnAnimationChanged -= OnAnimationChanged;
                _digitalHumanRenderer.OnEmotionChanged -= OnEmotionChanged;
            }

            if (_videoPlayerController != null)
            {
                _videoPlayerController.OnStateChanged -= OnVideoStateChanged;
                _videoPlayerController.OnVideoStarted -= OnVideoStarted;
                _videoPlayerController.OnVideoFinished -= OnVideoFinished;
            }

            if (_greenScreenProcessor != null)
            {
                _greenScreenProcessor.OnGreenScreenDetected -= OnGreenScreenDetected;
                _greenScreenProcessor.OnBackgroundComposed -= OnBackgroundComposed;
            }
        }
        #endregion
    }
}