using UnityEngine;
using UnityEngine.UI;
using DigitalHuman.Core.Camera;
using System.Collections.Generic;

namespace DigitalHuman.Examples
{
    /// <summary>
    /// 摄像头系统示例
    /// 演示如何使用摄像头管理器进行视频捕获和显示
    /// </summary>
    public class CameraSystemExample : MonoBehaviour
    {
        [Header("UI 组件")]
        [SerializeField] private RawImage videoDisplay;
        [SerializeField] private Button startButton;
        [SerializeField] private Button stopButton;
        [SerializeField] private Button switchButton;
        [SerializeField] private Dropdown cameraDropdown;
        [SerializeField] private Text statusText;
        [SerializeField] private Text permissionText;
        
        [Header("摄像头配置")]
        [SerializeField] private VideoConfiguration videoConfig = new VideoConfiguration(1280, 720, 30);
        
        private CameraManager cameraManager;
        private bool isInitialized = false;
        
        void Start()
        {
            InitializeCameraSystem();
            SetupUI();
        }
        
        void OnDestroy()
        {
            if (cameraManager != null)
            {
                cameraManager.OnPermissionChanged -= OnPermissionChanged;
                cameraManager.OnFrameUpdated -= OnFrameUpdated;
                cameraManager.OnCameraDevicesChanged -= OnCameraDevicesChanged;
                cameraManager.Cleanup();
            }
        }
        
        /// <summary>
        /// 初始化摄像头系统
        /// </summary>
        private void InitializeCameraSystem()
        {
            // 创建摄像头管理器
            GameObject cameraManagerObject = new GameObject("CameraManager");
            cameraManagerObject.transform.SetParent(transform);
            cameraManager = cameraManagerObject.AddComponent<CameraManager>();
            
            // 订阅事件
            cameraManager.OnPermissionChanged += OnPermissionChanged;
            cameraManager.OnFrameUpdated += OnFrameUpdated;
            cameraManager.OnCameraDevicesChanged += OnCameraDevicesChanged;
            
            // 初始化管理器
            cameraManager.Initialize();
            isInitialized = true;
            
            Debug.Log("[CameraSystemExample] 摄像头系统初始化完成");
        }
        
        /// <summary>
        /// 设置UI组件
        /// </summary>
        private void SetupUI()
        {
            if (startButton != null)
            {
                startButton.onClick.AddListener(OnStartButtonClicked);
            }
            
            if (stopButton != null)
            {
                stopButton.onClick.AddListener(OnStopButtonClicked);
            }
            
            if (switchButton != null)
            {
                switchButton.onClick.AddListener(OnSwitchButtonClicked);
            }
            
            if (cameraDropdown != null)
            {
                cameraDropdown.onValueChanged.AddListener(OnCameraDropdownChanged);
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 权限状态变化回调
        /// </summary>
        /// <param name="hasPermission">是否有权限</param>
        private void OnPermissionChanged(bool hasPermission)
        {
            Debug.Log($"[CameraSystemExample] 摄像头权限状态: {hasPermission}");
            UpdateUI();
        }
        
        /// <summary>
        /// 视频帧更新回调
        /// </summary>
        /// <param name="frame">新的视频帧</param>
        private void OnFrameUpdated(Texture2D frame)
        {
            if (videoDisplay != null && frame != null)
            {
                videoDisplay.texture = frame;
            }
        }
        
        /// <summary>
        /// 摄像头设备变化回调
        /// </summary>
        /// <param name="cameras">可用的摄像头列表</param>
        private void OnCameraDevicesChanged(List<CameraDevice> cameras)
        {
            Debug.Log($"[CameraSystemExample] 检测到 {cameras.Count} 个摄像头设备");
            UpdateCameraDropdown(cameras);
        }
        
        /// <summary>
        /// 开始按钮点击事件
        /// </summary>
        private void OnStartButtonClicked()
        {
            if (!isInitialized || cameraManager == null)
            {
                Debug.LogError("[CameraSystemExample] 摄像头管理器未初始化");
                return;
            }
            
            if (!cameraManager.HasCameraPermission)
            {
                Debug.Log("[CameraSystemExample] 请求摄像头权限");
                cameraManager.RequestCameraPermission();
                return;
            }
            
            Debug.Log("[CameraSystemExample] 开始视频捕获");
            bool success = cameraManager.StartCapture(videoConfig.width, videoConfig.height, videoConfig.fps);
            
            if (success)
            {
                Debug.Log("[CameraSystemExample] 视频捕获启动成功");
            }
            else
            {
                Debug.LogError("[CameraSystemExample] 视频捕获启动失败");
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private void OnStopButtonClicked()
        {
            if (!isInitialized || cameraManager == null)
                return;
            
            Debug.Log("[CameraSystemExample] 停止视频捕获");
            cameraManager.StopCapture();
            
            // 清空显示
            if (videoDisplay != null)
            {
                videoDisplay.texture = null;
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 切换按钮点击事件
        /// </summary>
        private void OnSwitchButtonClicked()
        {
            if (!isInitialized || cameraManager == null)
                return;
            
            var cameras = cameraManager.AvailableCameras;
            if (cameras.Count <= 1)
            {
                Debug.Log("[CameraSystemExample] 没有其他摄像头可切换");
                return;
            }
            
            // 找到下一个摄像头
            string currentCameraName = cameraManager.CurrentCamera?.deviceName ?? "";
            int currentIndex = cameras.FindIndex(c => c.deviceName == currentCameraName);
            int nextIndex = (currentIndex + 1) % cameras.Count;
            
            string nextCameraName = cameras[nextIndex].deviceName;
            Debug.Log($"[CameraSystemExample] 切换到摄像头: {cameras[nextIndex].displayName}");
            
            bool success = cameraManager.SwitchCamera(nextCameraName);
            if (success)
            {
                Debug.Log("[CameraSystemExample] 摄像头切换成功");
            }
            else
            {
                Debug.LogError("[CameraSystemExample] 摄像头切换失败");
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 摄像头下拉框选择事件
        /// </summary>
        /// <param name="index">选择的索引</param>
        private void OnCameraDropdownChanged(int index)
        {
            if (!isInitialized || cameraManager == null)
                return;
            
            var cameras = cameraManager.AvailableCameras;
            if (index < 0 || index >= cameras.Count)
                return;
            
            string selectedCameraName = cameras[index].deviceName;
            Debug.Log($"[CameraSystemExample] 用户选择摄像头: {cameras[index].displayName}");
            
            bool success = cameraManager.SwitchCamera(selectedCameraName);
            if (!success)
            {
                Debug.LogError("[CameraSystemExample] 切换到选择的摄像头失败");
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 更新摄像头下拉框
        /// </summary>
        /// <param name="cameras">摄像头列表</param>
        private void UpdateCameraDropdown(List<CameraDevice> cameras)
        {
            if (cameraDropdown == null)
                return;
            
            cameraDropdown.ClearOptions();
            
            var options = new List<string>();
            foreach (var camera in cameras)
            {
                options.Add(camera.displayName);
            }
            
            cameraDropdown.AddOptions(options);
            
            // 设置当前选中的摄像头
            if (cameraManager.CurrentCamera != null)
            {
                int currentIndex = cameras.FindIndex(c => c.deviceName == cameraManager.CurrentCamera.deviceName);
                if (currentIndex >= 0)
                {
                    cameraDropdown.value = currentIndex;
                }
            }
        }
        
        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            if (!isInitialized || cameraManager == null)
                return;
            
            bool hasPermission = cameraManager.HasCameraPermission;
            bool isCapturing = cameraManager.IsCapturing;
            bool hasCameras = cameraManager.AvailableCameras.Count > 0;
            
            // 更新按钮状态
            if (startButton != null)
            {
                startButton.interactable = hasPermission && hasCameras && !isCapturing;
            }
            
            if (stopButton != null)
            {
                stopButton.interactable = isCapturing;
            }
            
            if (switchButton != null)
            {
                switchButton.interactable = hasPermission && cameraManager.AvailableCameras.Count > 1;
            }
            
            if (cameraDropdown != null)
            {
                cameraDropdown.interactable = hasPermission && hasCameras;
            }
            
            // 更新状态文本
            if (statusText != null)
            {
                string status = "未知";
                if (!hasCameras)
                {
                    status = "未检测到摄像头";
                }
                else if (!hasPermission)
                {
                    status = "等待权限授权";
                }
                else if (isCapturing)
                {
                    status = "正在捕获视频";
                }
                else
                {
                    status = "就绪";
                }
                
                statusText.text = $"状态: {status}";
            }
            
            if (permissionText != null)
            {
                permissionText.text = $"权限: {(hasPermission ? "已授权" : "未授权")}";
            }
        }
        
        /// <summary>
        /// 请求摄像头权限（公共方法，可从外部调用）
        /// </summary>
        public void RequestPermission()
        {
            if (isInitialized && cameraManager != null)
            {
                cameraManager.RequestCameraPermission();
            }
        }
        
        /// <summary>
        /// 获取当前视频帧（公共方法，可从外部调用）
        /// </summary>
        /// <returns>当前视频帧</returns>
        public Texture2D GetCurrentFrame()
        {
            if (isInitialized && cameraManager != null)
            {
                return cameraManager.GetCurrentFrame();
            }
            return null;
        }
        
        /// <summary>
        /// 获取摄像头管理器（用于其他系统集成）
        /// </summary>
        /// <returns>摄像头管理器</returns>
        public CameraManager GetCameraManager()
        {
            return cameraManager;
        }
    }
}