using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DigitalHuman.Core.Hotword.Models;

namespace DigitalHuman.Core.Hotword
{
    /// <summary>
    /// 热词管理器接口
    /// </summary>
    public interface IHotwordManager
    {
        /// <summary>
        /// 热词检测到事件
        /// </summary>
        event Action<HotwordMatchResult> OnHotwordDetected;

        /// <summary>
        /// 热词库更新事件
        /// </summary>
        event Action OnHotwordLibraryUpdated;

        /// <summary>
        /// 初始化热词管理器
        /// </summary>
        /// <returns>初始化任务</returns>
        Task InitializeAsync();

        /// <summary>
        /// 添加热词
        /// </summary>
        /// <param name="entry">热词条目</param>
        /// <returns>是否添加成功</returns>
        bool AddHotword(HotwordEntry entry);

        /// <summary>
        /// 删除热词
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <returns>是否删除成功</returns>
        bool RemoveHotword(string keyword);

        /// <summary>
        /// 更新热词
        /// </summary>
        /// <param name="keyword">原关键词</param>
        /// <param name="entry">新的热词条目</param>
        /// <returns>是否更新成功</returns>
        bool UpdateHotword(string keyword, HotwordEntry entry);

        /// <summary>
        /// 获取热词条目
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <returns>热词条目，如果不存在返回null</returns>
        HotwordEntry GetHotword(string keyword);

        /// <summary>
        /// 获取所有热词
        /// </summary>
        /// <returns>热词列表</returns>
        List<HotwordEntry> GetAllHotwords();

        /// <summary>
        /// 检测输入文本是否包含热词
        /// </summary>
        /// <param name="input">输入文本</param>
        /// <returns>匹配结果</returns>
        HotwordMatchResult DetectHotword(string input);

        /// <summary>
        /// 获取热词建议
        /// </summary>
        /// <param name="input">输入文本</param>
        /// <param name="maxCount">最大建议数量</param>
        /// <returns>建议的热词列表</returns>
        List<HotwordEntry> GetHotwordSuggestions(string input, int maxCount = 5);

        /// <summary>
        /// 记录热词使用
        /// </summary>
        /// <param name="keyword">使用的关键词</param>
        void RecordHotwordUsage(string keyword);

        /// <summary>
        /// 获取热词使用统计
        /// </summary>
        /// <returns>使用统计数据</returns>
        Dictionary<string, int> GetUsageStatistics();

        /// <summary>
        /// 清空使用统计
        /// </summary>
        void ClearUsageStatistics();

        /// <summary>
        /// 从文件加载热词库
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否加载成功</returns>
        Task<bool> LoadHotwordsFromFileAsync(string filePath);

        /// <summary>
        /// 保存热词库到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        Task<bool> SaveHotwordsToFileAsync(string filePath);

        /// <summary>
        /// 导入热词库
        /// </summary>
        /// <param name="hotwords">热词列表</param>
        /// <returns>导入成功的数量</returns>
        int ImportHotwords(List<HotwordEntry> hotwords);

        /// <summary>
        /// 导出热词库
        /// </summary>
        /// <returns>热词列表</returns>
        List<HotwordEntry> ExportHotwords();

        /// <summary>
        /// 设置热词配置
        /// </summary>
        /// <param name="config">配置</param>
        void SetConfiguration(HotwordConfiguration config);

        /// <summary>
        /// 获取热词配置
        /// </summary>
        /// <returns>配置</returns>
        HotwordConfiguration GetConfiguration();

        /// <summary>
        /// 启用或禁用热词检测
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetHotwordDetectionEnabled(bool enabled);

        /// <summary>
        /// 检查热词是否存在
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <returns>是否存在</returns>
        bool ContainsHotword(string keyword);

        /// <summary>
        /// 获取按优先级排序的热词列表
        /// </summary>
        /// <returns>排序后的热词列表</returns>
        List<HotwordEntry> GetHotwordsByPriority();

        /// <summary>
        /// 获取按使用频率排序的热词列表
        /// </summary>
        /// <returns>排序后的热词列表</returns>
        List<HotwordEntry> GetHotwordsByUsage();
    }
}