using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DigitalHuman.Core.Hotword.Models;

namespace DigitalHuman.Core.Hotword
{
    /// <summary>
    /// 推荐问题管理器接口
    /// </summary>
    public interface IRecommendedQuestionManager
    {
        /// <summary>
        /// 问题被点击事件
        /// </summary>
        event Action<RecommendedQuestion> OnQuestionClicked;

        /// <summary>
        /// 问题列表更新事件
        /// </summary>
        event Action OnQuestionListUpdated;

        /// <summary>
        /// 初始化推荐问题管理器
        /// </summary>
        /// <returns>初始化任务</returns>
        Task InitializeAsync();

        /// <summary>
        /// 添加推荐问题
        /// </summary>
        /// <param name="question">推荐问题</param>
        /// <returns>是否添加成功</returns>
        bool AddQuestion(RecommendedQuestion question);

        /// <summary>
        /// 删除推荐问题
        /// </summary>
        /// <param name="questionId">问题ID</param>
        /// <returns>是否删除成功</returns>
        bool RemoveQuestion(string questionId);

        /// <summary>
        /// 更新推荐问题
        /// </summary>
        /// <param name="question">更新的问题</param>
        /// <returns>是否更新成功</returns>
        bool UpdateQuestion(RecommendedQuestion question);

        /// <summary>
        /// 获取推荐问题
        /// </summary>
        /// <param name="questionId">问题ID</param>
        /// <returns>推荐问题，如果不存在返回null</returns>
        RecommendedQuestion GetQuestion(string questionId);

        /// <summary>
        /// 获取所有推荐问题
        /// </summary>
        /// <returns>推荐问题列表</returns>
        List<RecommendedQuestion> GetAllQuestions();

        /// <summary>
        /// 获取显示的推荐问题列表
        /// </summary>
        /// <param name="maxCount">最大数量</param>
        /// <returns>显示的问题列表</returns>
        List<RecommendedQuestion> GetDisplayQuestions(int maxCount = 10);

        /// <summary>
        /// 按分类获取推荐问题
        /// </summary>
        /// <param name="category">分类名称</param>
        /// <returns>该分类的问题列表</returns>
        List<RecommendedQuestion> GetQuestionsByCategory(string category);

        /// <summary>
        /// 搜索推荐问题
        /// </summary>
        /// <param name="searchTerm">搜索关键词</param>
        /// <param name="maxResults">最大结果数量</param>
        /// <returns>搜索结果</returns>
        QuestionSearchResult SearchQuestions(string searchTerm, int maxResults = 20);

        /// <summary>
        /// 处理问题点击
        /// </summary>
        /// <param name="questionId">问题ID</param>
        /// <returns>问题的答案</returns>
        string HandleQuestionClick(string questionId);

        /// <summary>
        /// 记录问题点击统计
        /// </summary>
        /// <param name="questionId">问题ID</param>
        void RecordQuestionClick(string questionId);

        /// <summary>
        /// 获取问题分类列表
        /// </summary>
        /// <returns>分类列表</returns>
        List<QuestionCategory> GetCategories();

        /// <summary>
        /// 添加问题分类
        /// </summary>
        /// <param name="category">分类</param>
        /// <returns>是否添加成功</returns>
        bool AddCategory(QuestionCategory category);

        /// <summary>
        /// 删除问题分类
        /// </summary>
        /// <param name="categoryName">分类名称</param>
        /// <returns>是否删除成功</returns>
        bool RemoveCategory(string categoryName);

        /// <summary>
        /// 获取使用统计
        /// </summary>
        /// <returns>使用统计数据</returns>
        Dictionary<string, int> GetUsageStatistics();

        /// <summary>
        /// 清空使用统计
        /// </summary>
        void ClearUsageStatistics();

        /// <summary>
        /// 从文件加载推荐问题
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否加载成功</returns>
        Task<bool> LoadQuestionsFromFileAsync(string filePath);

        /// <summary>
        /// 保存推荐问题到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        Task<bool> SaveQuestionsToFileAsync(string filePath);

        /// <summary>
        /// 导入推荐问题
        /// </summary>
        /// <param name="questions">问题列表</param>
        /// <returns>导入成功的数量</returns>
        int ImportQuestions(List<RecommendedQuestion> questions);

        /// <summary>
        /// 导出推荐问题
        /// </summary>
        /// <returns>问题列表</returns>
        List<RecommendedQuestion> ExportQuestions();

        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="config">配置</param>
        void SetConfiguration(RecommendedQuestionConfiguration config);

        /// <summary>
        /// 获取配置
        /// </summary>
        /// <returns>配置</returns>
        RecommendedQuestionConfiguration GetConfiguration();

        /// <summary>
        /// 启用或禁用推荐问题
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetRecommendedQuestionsEnabled(bool enabled);

        /// <summary>
        /// 按优先级排序问题
        /// </summary>
        /// <returns>排序后的问题列表</returns>
        List<RecommendedQuestion> GetQuestionsByPriority();

        /// <summary>
        /// 按使用频率排序问题
        /// </summary>
        /// <returns>排序后的问题列表</returns>
        List<RecommendedQuestion> GetQuestionsByUsage();

        /// <summary>
        /// 按最近使用排序问题
        /// </summary>
        /// <returns>排序后的问题列表</returns>
        List<RecommendedQuestion> GetQuestionsByRecent();

        /// <summary>
        /// 自动更新问题优先级
        /// </summary>
        void UpdateQuestionPriorities();

        /// <summary>
        /// 检查问题是否存在
        /// </summary>
        /// <param name="questionId">问题ID</param>
        /// <returns>是否存在</returns>
        bool ContainsQuestion(string questionId);

        /// <summary>
        /// 获取问题总数
        /// </summary>
        /// <returns>问题总数</returns>
        int GetQuestionCount();

        /// <summary>
        /// 获取启用的问题数量
        /// </summary>
        /// <returns>启用的问题数量</returns>
        int GetEnabledQuestionCount();
    }
}