using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Hotword.Models;
using DigitalHuman.Core;

namespace DigitalHuman.Core.Hotword
{
    /// <summary>
    /// 热词管理器实现
    /// </summary>
    public class HotwordManager : ManagerBase, IHotwordManager
    {
        [Header("热词配置")]
        [SerializeField] private HotwordConfiguration configuration;
        [SerializeField] private string hotwordDataPath = "hotwords.json";
        
        private Dictionary<string, HotwordEntry> hotwordDictionary;
        private List<HotwordEntry> hotwordList;
        private bool isInitialized;

        /// <summary>
        /// 热词检测到事件
        /// </summary>
        public event Action<HotwordMatchResult> OnHotwordDetected;

        /// <summary>
        /// 热词库更新事件
        /// </summary>
        public event Action OnHotwordLibraryUpdated;

        protected override void Awake()
        {
            base.Awake();
            InitializeCollections();
            InitializeConfiguration();
        }

        /// <summary>
        /// 初始化集合
        /// </summary>
        private void InitializeCollections()
        {
            hotwordDictionary = new Dictionary<string, HotwordEntry>();
            hotwordList = new List<HotwordEntry>();
        }

        /// <summary>
        /// 初始化配置
        /// </summary>
        private void InitializeConfiguration()
        {
            if (configuration == null)
            {
                configuration = new HotwordConfiguration();
            }
        }

        /// <summary>
        /// 初始化热词管理器
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                LogInfo("开始初始化热词管理器");
                
                // 加载默认热词
                LoadDefaultHotwords();
                
                // 尝试从文件加载热词
                string dataPath = GetHotwordDataPath();
                if (File.Exists(dataPath))
                {
                    await LoadHotwordsFromFileAsync(dataPath);
                }
                
                isInitialized = true;
                LogInfo($"热词管理器初始化完成，共加载 {hotwordList.Count} 个热词");
            }
            catch (Exception ex)
            {
                LogError($"热词管理器初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载默认热词
        /// </summary>
        private void LoadDefaultHotwords()
        {
            var defaultHotwords = new List<HotwordEntry>
            {
                new HotwordEntry("介绍景点", "欢迎来到这个美丽的景点！让我为您介绍一下这里的特色...", 10)
                {
                    ActionType = "video",
                    ActionData = "introduce_scenery",
                    Aliases = new List<string> { "景点介绍", "介绍一下景点", "这里有什么好玩的" }
                },
                new HotwordEntry("跳个舞", "好的，让我为您表演一段舞蹈！", 8)
                {
                    ActionType = "video",
                    ActionData = "dance_performance",
                    Aliases = new List<string> { "跳舞", "表演舞蹈", "来段舞蹈" }
                },
                new HotwordEntry("你好", "您好！很高兴见到您，有什么可以帮助您的吗？", 5)
                {
                    ActionType = "gesture",
                    ActionData = "wave_hello",
                    Aliases = new List<string> { "hello", "hi", "您好" }
                },
                new HotwordEntry("再见", "再见！期待下次与您相遇！", 5)
                {
                    ActionType = "gesture",
                    ActionData = "wave_goodbye",
                    Aliases = new List<string> { "goodbye", "bye", "拜拜" }
                }
            };

            foreach (var hotword in defaultHotwords)
            {
                AddHotwordInternal(hotword);
            }
        }

        /// <summary>
        /// 添加热词
        /// </summary>
        public bool AddHotword(HotwordEntry entry)
        {
            if (entry == null || string.IsNullOrEmpty(entry.Keyword))
            {
                LogWarning("无法添加空的热词条目");
                return false;
            }

            if (hotwordDictionary.ContainsKey(entry.Keyword.ToLower()))
            {
                LogWarning($"热词 '{entry.Keyword}' 已存在");
                return false;
            }

            AddHotwordInternal(entry);
            OnHotwordLibraryUpdated?.Invoke();
            LogInfo($"成功添加热词: {entry.Keyword}");
            return true;
        }

        /// <summary>
        /// 内部添加热词方法
        /// </summary>
        private void AddHotwordInternal(HotwordEntry entry)
        {
            string key = entry.Keyword.ToLower();
            hotwordDictionary[key] = entry;
            hotwordList.Add(entry);

            // 添加别名到字典
            foreach (var alias in entry.Aliases)
            {
                if (!string.IsNullOrEmpty(alias))
                {
                    string aliasKey = alias.ToLower();
                    if (!hotwordDictionary.ContainsKey(aliasKey))
                    {
                        hotwordDictionary[aliasKey] = entry;
                    }
                }
            }
        }

        /// <summary>
        /// 删除热词
        /// </summary>
        public bool RemoveHotword(string keyword)
        {
            if (string.IsNullOrEmpty(keyword))
            {
                return false;
            }

            string key = keyword.ToLower();
            if (!hotwordDictionary.ContainsKey(key))
            {
                LogWarning($"热词 '{keyword}' 不存在");
                return false;
            }

            var entry = hotwordDictionary[key];
            
            // 从字典中移除主关键词和别名
            hotwordDictionary.Remove(entry.Keyword.ToLower());
            foreach (var alias in entry.Aliases)
            {
                if (!string.IsNullOrEmpty(alias))
                {
                    hotwordDictionary.Remove(alias.ToLower());
                }
            }

            // 从列表中移除
            hotwordList.Remove(entry);
            
            OnHotwordLibraryUpdated?.Invoke();
            LogInfo($"成功删除热词: {keyword}");
            return true;
        }

        /// <summary>
        /// 更新热词
        /// </summary>
        public bool UpdateHotword(string keyword, HotwordEntry entry)
        {
            if (string.IsNullOrEmpty(keyword) || entry == null)
            {
                return false;
            }

            // 先删除旧的热词
            if (!RemoveHotword(keyword))
            {
                return false;
            }

            // 添加新的热词
            return AddHotword(entry);
        }

        /// <summary>
        /// 获取热词条目
        /// </summary>
        public HotwordEntry GetHotword(string keyword)
        {
            if (string.IsNullOrEmpty(keyword))
            {
                return null;
            }

            string key = keyword.ToLower();
            return hotwordDictionary.TryGetValue(key, out var entry) ? entry : null;
        }

        /// <summary>
        /// 获取所有热词
        /// </summary>
        public List<HotwordEntry> GetAllHotwords()
        {
            return new List<HotwordEntry>(hotwordList);
        }

        /// <summary>
        /// 检测输入文本是否包含热词
        /// </summary>
        public HotwordMatchResult DetectHotword(string input)
        {
            if (string.IsNullOrEmpty(input) || !configuration.EnableHotwordDetection)
            {
                return new HotwordMatchResult();
            }

            string normalizedInput = input.ToLower().Trim();
            
            // 精确匹配
            var exactMatch = FindExactMatch(normalizedInput);
            if (exactMatch.IsMatch)
            {
                RecordHotwordUsage(exactMatch.MatchedKeyword);
                OnHotwordDetected?.Invoke(exactMatch);
                return exactMatch;
            }

            // 模糊匹配
            if (configuration.EnableFuzzyMatch)
            {
                var fuzzyMatch = FindFuzzyMatch(normalizedInput);
                if (fuzzyMatch.IsMatch && fuzzyMatch.Confidence >= configuration.MatchThreshold)
                {
                    RecordHotwordUsage(fuzzyMatch.MatchedKeyword);
                    OnHotwordDetected?.Invoke(fuzzyMatch);
                    return fuzzyMatch;
                }
            }

            return new HotwordMatchResult();
        }

        /// <summary>
        /// 查找精确匹配
        /// </summary>
        private HotwordMatchResult FindExactMatch(string input)
        {
            foreach (var kvp in hotwordDictionary)
            {
                if (!kvp.Value.IsEnabled) continue;

                string keyword = kvp.Key;
                if (input.Contains(keyword))
                {
                    return new HotwordMatchResult(true, kvp.Value, keyword, 1.0f);
                }
            }

            return new HotwordMatchResult();
        }

        /// <summary>
        /// 查找模糊匹配
        /// </summary>
        private HotwordMatchResult FindFuzzyMatch(string input)
        {
            float bestScore = 0f;
            HotwordEntry bestMatch = null;
            string bestKeyword = string.Empty;

            foreach (var kvp in hotwordDictionary)
            {
                if (!kvp.Value.IsEnabled) continue;

                string keyword = kvp.Key;
                float similarity = CalculateSimilarity(input, keyword);
                
                if (similarity > bestScore)
                {
                    bestScore = similarity;
                    bestMatch = kvp.Value;
                    bestKeyword = keyword;
                }
            }

            if (bestScore >= configuration.MatchThreshold)
            {
                return new HotwordMatchResult(true, bestMatch, bestKeyword, bestScore);
            }

            return new HotwordMatchResult();
        }

        /// <summary>
        /// 计算字符串相似度
        /// </summary>
        private float CalculateSimilarity(string str1, string str2)
        {
            if (string.IsNullOrEmpty(str1) || string.IsNullOrEmpty(str2))
            {
                return 0f;
            }

            // 使用编辑距离算法计算相似度
            int distance = LevenshteinDistance(str1, str2);
            int maxLength = Mathf.Max(str1.Length, str2.Length);
            
            return 1.0f - (float)distance / maxLength;
        }

        /// <summary>
        /// 计算编辑距离
        /// </summary>
        private int LevenshteinDistance(string str1, string str2)
        {
            int[,] matrix = new int[str1.Length + 1, str2.Length + 1];

            for (int i = 0; i <= str1.Length; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= str2.Length; j++)
                matrix[0, j] = j;

            for (int i = 1; i <= str1.Length; i++)
            {
                for (int j = 1; j <= str2.Length; j++)
                {
                    int cost = str1[i - 1] == str2[j - 1] ? 0 : 1;
                    matrix[i, j] = Mathf.Min(
                        Mathf.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost
                    );
                }
            }

            return matrix[str1.Length, str2.Length];
        }

        /// <summary>
        /// 获取热词建议
        /// </summary>
        public List<HotwordEntry> GetHotwordSuggestions(string input, int maxCount = 5)
        {
            if (string.IsNullOrEmpty(input))
            {
                return GetHotwordsByPriority().Take(maxCount).ToList();
            }

            var suggestions = new List<(HotwordEntry entry, float score)>();
            string normalizedInput = input.ToLower();

            foreach (var entry in hotwordList)
            {
                if (!entry.IsEnabled) continue;

                float score = CalculateSimilarity(normalizedInput, entry.Keyword.ToLower());
                
                // 检查别名
                foreach (var alias in entry.Aliases)
                {
                    if (!string.IsNullOrEmpty(alias))
                    {
                        float aliasScore = CalculateSimilarity(normalizedInput, alias.ToLower());
                        score = Mathf.Max(score, aliasScore);
                    }
                }

                if (score > 0.3f) // 最低相似度阈值
                {
                    suggestions.Add((entry, score));
                }
            }

            return suggestions
                .OrderByDescending(x => x.score)
                .ThenByDescending(x => x.entry.Priority)
                .ThenByDescending(x => x.entry.UsageCount)
                .Take(maxCount)
                .Select(x => x.entry)
                .ToList();
        }

        /// <summary>
        /// 记录热词使用
        /// </summary>
        public void RecordHotwordUsage(string keyword)
        {
            if (!configuration.EnableUsageStatistics || string.IsNullOrEmpty(keyword))
            {
                return;
            }

            var entry = GetHotword(keyword);
            if (entry != null)
            {
                entry.UsageCount++;
                entry.LastUsed = DateTime.Now;
                LogInfo($"记录热词使用: {keyword}, 使用次数: {entry.UsageCount}");
            }
        }

        /// <summary>
        /// 获取热词使用统计
        /// </summary>
        public Dictionary<string, int> GetUsageStatistics()
        {
            var statistics = new Dictionary<string, int>();
            
            foreach (var entry in hotwordList)
            {
                statistics[entry.Keyword] = entry.UsageCount;
            }

            return statistics;
        }

        /// <summary>
        /// 清空使用统计
        /// </summary>
        public void ClearUsageStatistics()
        {
            foreach (var entry in hotwordList)
            {
                entry.UsageCount = 0;
                entry.LastUsed = DateTime.MinValue;
            }
            
            LogInfo("已清空热词使用统计");
        }

        /// <summary>
        /// 从文件加载热词库
        /// </summary>
        public async Task<bool> LoadHotwordsFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    LogWarning($"热词文件不存在: {filePath}");
                    return false;
                }

                string jsonContent = await File.ReadAllTextAsync(filePath);
                var hotwords = JsonUtility.FromJson<HotwordEntry[]>(jsonContent);
                
                if (hotwords != null)
                {
                    int importedCount = ImportHotwords(hotwords.ToList());
                    LogInfo($"从文件加载热词成功: {importedCount} 个");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogError($"加载热词文件失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 保存热词库到文件
        /// </summary>
        public async Task<bool> SaveHotwordsToFileAsync(string filePath)
        {
            try
            {
                var hotwords = ExportHotwords();
                string jsonContent = JsonUtility.ToJson(hotwords.ToArray(), true);
                
                string directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                await File.WriteAllTextAsync(filePath, jsonContent);
                LogInfo($"保存热词库到文件成功: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"保存热词文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导入热词库
        /// </summary>
        public int ImportHotwords(List<HotwordEntry> hotwords)
        {
            if (hotwords == null)
            {
                return 0;
            }

            int importedCount = 0;
            foreach (var hotword in hotwords)
            {
                if (AddHotword(hotword))
                {
                    importedCount++;
                }
            }

            return importedCount;
        }

        /// <summary>
        /// 导出热词库
        /// </summary>
        public List<HotwordEntry> ExportHotwords()
        {
            return GetAllHotwords();
        }

        /// <summary>
        /// 设置热词配置
        /// </summary>
        public void SetConfiguration(HotwordConfiguration config)
        {
            if (config != null)
            {
                configuration = config;
                LogInfo("热词配置已更新");
            }
        }

        /// <summary>
        /// 获取热词配置
        /// </summary>
        public HotwordConfiguration GetConfiguration()
        {
            return configuration;
        }

        /// <summary>
        /// 启用或禁用热词检测
        /// </summary>
        public void SetHotwordDetectionEnabled(bool enabled)
        {
            configuration.EnableHotwordDetection = enabled;
            LogInfo($"热词检测已{(enabled ? "启用" : "禁用")}");
        }

        /// <summary>
        /// 检查热词是否存在
        /// </summary>
        public bool ContainsHotword(string keyword)
        {
            if (string.IsNullOrEmpty(keyword))
            {
                return false;
            }

            return hotwordDictionary.ContainsKey(keyword.ToLower());
        }

        /// <summary>
        /// 获取按优先级排序的热词列表
        /// </summary>
        public List<HotwordEntry> GetHotwordsByPriority()
        {
            return hotwordList
                .Where(h => h.IsEnabled)
                .OrderByDescending(h => h.Priority)
                .ThenBy(h => h.Keyword)
                .ToList();
        }

        /// <summary>
        /// 获取按使用频率排序的热词列表
        /// </summary>
        public List<HotwordEntry> GetHotwordsByUsage()
        {
            return hotwordList
                .Where(h => h.IsEnabled)
                .OrderByDescending(h => h.UsageCount)
                .ThenByDescending(h => h.LastUsed)
                .ThenBy(h => h.Keyword)
                .ToList();
        }

        /// <summary>
        /// 获取热词数据文件路径
        /// </summary>
        private string GetHotwordDataPath()
        {
            return Path.Combine(Application.persistentDataPath, hotwordDataPath);
        }

        /// <summary>
        /// 组件销毁时保存数据
        /// </summary>
        protected override void OnDestroy()
        {
            if (isInitialized)
            {
                _ = SaveHotwordsToFileAsync(GetHotwordDataPath());
            }
            base.OnDestroy();
        }
    }
}