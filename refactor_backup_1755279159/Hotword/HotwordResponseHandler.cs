using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Hotword.Models;
using DigitalHuman.Core.Rendering;
using DigitalHuman.Core.Audio;

namespace DigitalHuman.Core.Hotword
{
    /// <summary>
    /// 热词响应处理器，负责执行热词匹配后的动作
    /// </summary>
    public class HotwordResponseHandler : ManagerBase
    {
        [Header("响应组件引用")]
        [SerializeField] private IDigitalHumanRenderer digitalHumanRenderer;
        [SerializeField] private ITTSService ttsService;
        [SerializeField] private VideoPlayerController videoPlayerController;
        
        [Header("响应配置")]
        [SerializeField] private bool enableTextResponse = true;
        [SerializeField] private bool enableActionResponse = true;
        [SerializeField] private bool enableStatistics = true;
        [SerializeField] private float responseDelay = 0.1f;
        
        private Dictionary<string, int> actionUsageStats;
        private bool isInitialized;

        /// <summary>
        /// 热词响应完成事件
        /// </summary>
        public event Action<HotwordEntry, bool> OnHotwordResponseCompleted;

        /// <summary>
        /// 热词动作执行事件
        /// </summary>
        public event Action<string, string> OnHotwordActionExecuted;

        protected override void Awake()
        {
            base.Awake();
            InitializeComponents();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            actionUsageStats = new Dictionary<string, int>();
            
            // 自动查找组件引用
            if (digitalHumanRenderer == null)
            {
                digitalHumanRenderer = FindObjectOfType<DigitalHumanRenderer>();
            }
            
            if (ttsService == null)
            {
                ttsService = FindObjectOfType<TTSService>();
            }
            
            if (videoPlayerController == null)
            {
                videoPlayerController = FindObjectOfType<VideoPlayerController>();
            }
        }

        /// <summary>
        /// 初始化响应处理器
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
            {
                return;
            }

            LogInfo("初始化热词响应处理器");
            
            // 验证必要组件
            ValidateComponents();
            
            isInitialized = true;
            LogInfo("热词响应处理器初始化完成");
        }

        /// <summary>
        /// 验证必要组件
        /// </summary>
        private void ValidateComponents()
        {
            if (digitalHumanRenderer == null)
            {
                LogWarning("未找到数字人渲染器组件，部分动作响应可能无法执行");
            }
            
            if (ttsService == null)
            {
                LogWarning("未找到TTS服务组件，语音响应可能无法执行");
            }
            
            if (videoPlayerController == null)
            {
                LogWarning("未找到视频播放控制器，视频响应可能无法执行");
            }
        }

        /// <summary>
        /// 处理热词响应
        /// </summary>
        /// <param name="matchResult">热词匹配结果</param>
        public async Task HandleHotwordResponseAsync(HotwordMatchResult matchResult)
        {
            if (!isInitialized)
            {
                Initialize();
            }

            if (!matchResult.IsMatch || matchResult.MatchedEntry == null)
            {
                LogWarning("无效的热词匹配结果");
                return;
            }

            var entry = matchResult.MatchedEntry;
            LogInfo($"开始处理热词响应: {entry.Keyword}");

            try
            {
                // 添加响应延迟
                if (responseDelay > 0)
                {
                    await Task.Delay(Mathf.RoundToInt(responseDelay * 1000));
                }

                bool success = true;

                // 执行文本响应
                if (enableTextResponse && !string.IsNullOrEmpty(entry.Response))
                {
                    success &= await ExecuteTextResponseAsync(entry);
                }

                // 执行动作响应
                if (enableActionResponse && !string.IsNullOrEmpty(entry.ActionType))
                {
                    success &= await ExecuteActionResponseAsync(entry);
                }

                // 记录统计信息
                if (enableStatistics)
                {
                    RecordActionUsage(entry.ActionType);
                }

                // 触发响应完成事件
                OnHotwordResponseCompleted?.Invoke(entry, success);

                LogInfo($"热词响应处理完成: {entry.Keyword}, 成功: {success}");
            }
            catch (Exception ex)
            {
                LogError($"处理热词响应时发生异常: {ex.Message}");
                OnHotwordResponseCompleted?.Invoke(entry, false);
            }
        }

        /// <summary>
        /// 执行文本响应
        /// </summary>
        /// <param name="entry">热词条目</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteTextResponseAsync(HotwordEntry entry)
        {
            try
            {
                LogInfo($"执行文本响应: {entry.Response}");

                if (ttsService != null)
                {
                    // 使用TTS播放响应文本
                    var ttsRequest = new TTSRequest
                    {
                        Text = entry.Response,
                        VoiceId = "default",
                        Speed = 1.0f,
                        Volume = 1.0f
                    };

                    var ttsResponse = await ttsService.SynthesizeTextAsync(ttsRequest);
                    
                    if (ttsResponse.IsSuccess && ttsResponse.AudioClip != null)
                    {
                        // 播放TTS音频
                        AudioSource audioSource = GetComponent<AudioSource>();
                        if (audioSource == null)
                        {
                            audioSource = gameObject.AddComponent<AudioSource>();
                        }
                        
                        audioSource.clip = ttsResponse.AudioClip;
                        audioSource.Play();
                        
                        LogInfo("TTS响应播放成功");
                        return true;
                    }
                    else
                    {
                        LogWarning($"TTS合成失败: {ttsResponse.ErrorMessage}");
                    }
                }
                else
                {
                    // 如果没有TTS服务，只记录文本响应
                    LogInfo($"文本响应（无TTS）: {entry.Response}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogError($"执行文本响应失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 执行动作响应
        /// </summary>
        /// <param name="entry">热词条目</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteActionResponseAsync(HotwordEntry entry)
        {
            try
            {
                LogInfo($"执行动作响应: {entry.ActionType} - {entry.ActionData}");

                bool success = false;

                switch (entry.ActionType.ToLower())
                {
                    case "video":
                        success = await ExecuteVideoActionAsync(entry.ActionData);
                        break;
                    
                    case "animation":
                        success = await ExecuteAnimationActionAsync(entry.ActionData);
                        break;
                    
                    case "gesture":
                        success = await ExecuteGestureActionAsync(entry.ActionData);
                        break;
                    
                    case "emotion":
                        success = await ExecuteEmotionActionAsync(entry.ActionData);
                        break;
                    
                    case "custom":
                        success = await ExecuteCustomActionAsync(entry.ActionData);
                        break;
                    
                    default:
                        LogWarning($"未知的动作类型: {entry.ActionType}");
                        break;
                }

                if (success)
                {
                    OnHotwordActionExecuted?.Invoke(entry.ActionType, entry.ActionData);
                }

                return success;
            }
            catch (Exception ex)
            {
                LogError($"执行动作响应失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行视频动作
        /// </summary>
        /// <param name="videoData">视频数据</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteVideoActionAsync(string videoData)
        {
            if (videoPlayerController == null)
            {
                LogWarning("视频播放控制器未找到，无法执行视频动作");
                return false;
            }

            try
            {
                LogInfo($"播放视频: {videoData}");

                // 根据视频数据类型播放相应视频
                switch (videoData.ToLower())
                {
                    case "introduce_scenery":
                        videoPlayerController.PlayStateVideo(VideoState.IntroduceScenery);
                        break;
                    
                    case "dance_performance":
                        videoPlayerController.PlayStateVideo(VideoState.Dancing);
                        break;
                    
                    case "wave_hello":
                        videoPlayerController.PlayStateVideo(VideoState.Greeting);
                        break;
                    
                    case "wave_goodbye":
                        videoPlayerController.PlayStateVideo(VideoState.Farewell);
                        break;
                    
                    default:
                        // 尝试作为自定义视频状态播放
                        if (Enum.TryParse<VideoState>(videoData, true, out VideoState customState))
                        {
                            videoPlayerController.PlayStateVideo(customState);
                        }
                        else
                        {
                            LogWarning($"未知的视频状态: {videoData}");
                            return false;
                        }
                        break;
                }

                // 等待视频播放开始
                await Task.Delay(100);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"执行视频动作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行动画动作
        /// </summary>
        /// <param name="animationData">动画数据</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteAnimationActionAsync(string animationData)
        {
            if (digitalHumanRenderer == null)
            {
                LogWarning("数字人渲染器未找到，无法执行动画动作");
                return false;
            }

            try
            {
                LogInfo($"播放动画: {animationData}");

                // 根据动画数据播放相应动画
                switch (animationData.ToLower())
                {
                    case "idle":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Idle);
                        break;
                    
                    case "talking":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Talking);
                        break;
                    
                    case "greeting":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Greeting);
                        break;
                    
                    case "dancing":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Dancing);
                        break;
                    
                    default:
                        // 尝试作为自定义动画类型播放
                        if (Enum.TryParse<AnimationType>(animationData, true, out AnimationType customAnimation))
                        {
                            digitalHumanRenderer.PlayAnimation(customAnimation);
                        }
                        else
                        {
                            LogWarning($"未知的动画类型: {animationData}");
                            return false;
                        }
                        break;
                }

                await Task.Delay(100);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"执行动画动作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行手势动作
        /// </summary>
        /// <param name="gestureData">手势数据</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteGestureActionAsync(string gestureData)
        {
            if (digitalHumanRenderer == null)
            {
                LogWarning("数字人渲染器未找到，无法执行手势动作");
                return false;
            }

            try
            {
                LogInfo($"执行手势: {gestureData}");

                // 根据手势数据执行相应手势
                switch (gestureData.ToLower())
                {
                    case "wave_hello":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Greeting);
                        break;
                    
                    case "wave_goodbye":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Farewell);
                        break;
                    
                    case "point":
                        digitalHumanRenderer.PlayAnimation(AnimationType.Pointing);
                        break;
                    
                    case "thumbs_up":
                        digitalHumanRenderer.PlayAnimation(AnimationType.ThumbsUp);
                        break;
                    
                    default:
                        LogWarning($"未知的手势类型: {gestureData}");
                        return false;
                }

                await Task.Delay(100);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"执行手势动作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行情感动作
        /// </summary>
        /// <param name="emotionData">情感数据</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteEmotionActionAsync(string emotionData)
        {
            if (digitalHumanRenderer == null)
            {
                LogWarning("数字人渲染器未找到，无法执行情感动作");
                return false;
            }

            try
            {
                LogInfo($"设置情感: {emotionData}");

                // 根据情感数据设置相应情感
                switch (emotionData.ToLower())
                {
                    case "happy":
                        digitalHumanRenderer.SetEmotion(EmotionType.Happy);
                        break;
                    
                    case "sad":
                        digitalHumanRenderer.SetEmotion(EmotionType.Sad);
                        break;
                    
                    case "surprised":
                        digitalHumanRenderer.SetEmotion(EmotionType.Surprised);
                        break;
                    
                    case "angry":
                        digitalHumanRenderer.SetEmotion(EmotionType.Angry);
                        break;
                    
                    case "neutral":
                        digitalHumanRenderer.SetEmotion(EmotionType.Neutral);
                        break;
                    
                    default:
                        // 尝试作为自定义情感类型设置
                        if (Enum.TryParse<EmotionType>(emotionData, true, out EmotionType customEmotion))
                        {
                            digitalHumanRenderer.SetEmotion(customEmotion);
                        }
                        else
                        {
                            LogWarning($"未知的情感类型: {emotionData}");
                            return false;
                        }
                        break;
                }

                await Task.Delay(100);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"执行情感动作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行自定义动作
        /// </summary>
        /// <param name="customData">自定义数据</param>
        /// <returns>是否执行成功</returns>
        private async Task<bool> ExecuteCustomActionAsync(string customData)
        {
            try
            {
                LogInfo($"执行自定义动作: {customData}");

                // 这里可以根据需要实现自定义动作逻辑
                // 例如：调用外部API、执行特定脚本等
                
                // 示例：解析JSON格式的自定义动作数据
                if (customData.StartsWith("{") && customData.EndsWith("}"))
                {
                    // 尝试解析JSON格式的自定义动作
                    // var customAction = JsonUtility.FromJson<CustomActionData>(customData);
                    // 执行相应的自定义逻辑
                }

                await Task.Delay(100);
                LogInfo("自定义动作执行完成");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"执行自定义动作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 记录动作使用统计
        /// </summary>
        /// <param name="actionType">动作类型</param>
        private void RecordActionUsage(string actionType)
        {
            if (string.IsNullOrEmpty(actionType))
            {
                return;
            }

            if (actionUsageStats.ContainsKey(actionType))
            {
                actionUsageStats[actionType]++;
            }
            else
            {
                actionUsageStats[actionType] = 1;
            }

            LogInfo($"动作使用统计更新: {actionType} = {actionUsageStats[actionType]}");
        }

        /// <summary>
        /// 获取动作使用统计
        /// </summary>
        /// <returns>动作使用统计字典</returns>
        public Dictionary<string, int> GetActionUsageStatistics()
        {
            return new Dictionary<string, int>(actionUsageStats);
        }

        /// <summary>
        /// 清空动作使用统计
        /// </summary>
        public void ClearActionUsageStatistics()
        {
            actionUsageStats.Clear();
            LogInfo("动作使用统计已清空");
        }

        /// <summary>
        /// 设置响应配置
        /// </summary>
        /// <param name="enableText">启用文本响应</param>
        /// <param name="enableAction">启用动作响应</param>
        /// <param name="enableStats">启用统计</param>
        /// <param name="delay">响应延迟</param>
        public void SetResponseConfiguration(bool enableText, bool enableAction, bool enableStats, float delay)
        {
            enableTextResponse = enableText;
            enableActionResponse = enableAction;
            enableStatistics = enableStats;
            responseDelay = Mathf.Max(0, delay);

            LogInfo($"响应配置已更新 - 文本: {enableText}, 动作: {enableAction}, 统计: {enableStats}, 延迟: {delay}s");
        }

        /// <summary>
        /// 设置组件引用
        /// </summary>
        /// <param name="renderer">数字人渲染器</param>
        /// <param name="tts">TTS服务</param>
        /// <param name="videoPlayer">视频播放控制器</param>
        public void SetComponentReferences(IDigitalHumanRenderer renderer, ITTSService tts, VideoPlayerController videoPlayer)
        {
            digitalHumanRenderer = renderer;
            ttsService = tts;
            videoPlayerController = videoPlayer;

            LogInfo("组件引用已更新");
        }
    }
}