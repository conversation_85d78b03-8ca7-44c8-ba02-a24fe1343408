using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Hotword.Models
{
    /// <summary>
    /// 推荐问题条目数据模型
    /// </summary>
    [Serializable]
    public class RecommendedQuestion
    {
        [SerializeField] private string id;
        [SerializeField] private string question;
        [SerializeField] private string answer;
        [SerializeField] private string category;
        [SerializeField] private int priority;
        [SerializeField] private int clickCount;
        [SerializeField] private DateTime lastClicked;
        [SerializeField] private bool isEnabled;
        [SerializeField] private List<string> tags;
        [SerializeField] private MediaContent mediaContent;

        /// <summary>
        /// 问题唯一标识
        /// </summary>
        public string Id
        {
            get => id;
            set => id = value;
        }

        /// <summary>
        /// 问题文本
        /// </summary>
        public string Question
        {
            get => question;
            set => question = value;
        }

        /// <summary>
        /// 预设答案
        /// </summary>
        public string Answer
        {
            get => answer;
            set => answer = value;
        }

        /// <summary>
        /// 问题分类
        /// </summary>
        public string Category
        {
            get => category;
            set => category = value;
        }

        /// <summary>
        /// 优先级（数值越高优先级越高）
        /// </summary>
        public int Priority
        {
            get => priority;
            set => priority = value;
        }

        /// <summary>
        /// 点击次数统计
        /// </summary>
        public int ClickCount
        {
            get => clickCount;
            set => clickCount = value;
        }

        /// <summary>
        /// 最后点击时间
        /// </summary>
        public DateTime LastClicked
        {
            get => lastClicked;
            set => lastClicked = value;
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => isEnabled;
            set => isEnabled = value;
        }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags
        {
            get => tags ?? (tags = new List<string>());
            set => tags = value;
        }

        /// <summary>
        /// 多媒体内容
        /// </summary>
        public MediaContent MediaContent
        {
            get => mediaContent;
            set => mediaContent = value;
        }

        public RecommendedQuestion()
        {
            id = Guid.NewGuid().ToString();
            question = string.Empty;
            answer = string.Empty;
            category = "默认";
            priority = 0;
            clickCount = 0;
            lastClicked = DateTime.MinValue;
            isEnabled = true;
            tags = new List<string>();
            mediaContent = new MediaContent();
        }

        public RecommendedQuestion(string question, string answer, string category = "默认", int priority = 0)
        {
            this.id = Guid.NewGuid().ToString();
            this.question = question;
            this.answer = answer;
            this.category = category;
            this.priority = priority;
            this.clickCount = 0;
            this.lastClicked = DateTime.MinValue;
            this.isEnabled = true;
            this.tags = new List<string>();
            this.mediaContent = new MediaContent();
        }
    }

    /// <summary>
    /// 多媒体内容数据模型
    /// </summary>
    [Serializable]
    public class MediaContent
    {
        [SerializeField] private bool hasMedia;
        [SerializeField] private MediaType mediaType;
        [SerializeField] private string mediaPath;
        [SerializeField] private string thumbnailPath;
        [SerializeField] private string description;

        /// <summary>
        /// 是否包含多媒体内容
        /// </summary>
        public bool HasMedia
        {
            get => hasMedia;
            set => hasMedia = value;
        }

        /// <summary>
        /// 媒体类型
        /// </summary>
        public MediaType MediaType
        {
            get => mediaType;
            set => mediaType = value;
        }

        /// <summary>
        /// 媒体文件路径
        /// </summary>
        public string MediaPath
        {
            get => mediaPath;
            set => mediaPath = value;
        }

        /// <summary>
        /// 缩略图路径
        /// </summary>
        public string ThumbnailPath
        {
            get => thumbnailPath;
            set => thumbnailPath = value;
        }

        /// <summary>
        /// 媒体描述
        /// </summary>
        public string Description
        {
            get => description;
            set => description = value;
        }

        public MediaContent()
        {
            hasMedia = false;
            mediaType = MediaType.None;
            mediaPath = string.Empty;
            thumbnailPath = string.Empty;
            description = string.Empty;
        }
    }

    /// <summary>
    /// 媒体类型枚举
    /// </summary>
    public enum MediaType
    {
        None,
        Image,
        Video,
        Audio,
        Document
    }

    /// <summary>
    /// 问题分类数据模型
    /// </summary>
    [Serializable]
    public class QuestionCategory
    {
        [SerializeField] private string name;
        [SerializeField] private string displayName;
        [SerializeField] private string description;
        [SerializeField] private Color color;
        [SerializeField] private string iconPath;
        [SerializeField] private int sortOrder;
        [SerializeField] private bool isVisible;

        /// <summary>
        /// 分类名称（内部使用）
        /// </summary>
        public string Name
        {
            get => name;
            set => name = value;
        }

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName
        {
            get => displayName;
            set => displayName = value;
        }

        /// <summary>
        /// 分类描述
        /// </summary>
        public string Description
        {
            get => description;
            set => description = value;
        }

        /// <summary>
        /// 分类颜色
        /// </summary>
        public Color Color
        {
            get => color;
            set => color = value;
        }

        /// <summary>
        /// 图标路径
        /// </summary>
        public string IconPath
        {
            get => iconPath;
            set => iconPath = value;
        }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder
        {
            get => sortOrder;
            set => sortOrder = value;
        }

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible
        {
            get => isVisible;
            set => isVisible = value;
        }

        public QuestionCategory()
        {
            name = string.Empty;
            displayName = string.Empty;
            description = string.Empty;
            color = Color.white;
            iconPath = string.Empty;
            sortOrder = 0;
            isVisible = true;
        }

        public QuestionCategory(string name, string displayName, Color color)
        {
            this.name = name;
            this.displayName = displayName;
            this.color = color;
            this.description = string.Empty;
            this.iconPath = string.Empty;
            this.sortOrder = 0;
            this.isVisible = true;
        }
    }

    /// <summary>
    /// 推荐问题配置
    /// </summary>
    [Serializable]
    public class RecommendedQuestionConfiguration
    {
        [SerializeField] private bool enableRecommendedQuestions;
        [SerializeField] private int maxDisplayCount;
        [SerializeField] private bool enableCategorization;
        [SerializeField] private bool enableSearch;
        [SerializeField] private bool enableUsageStatistics;
        [SerializeField] private SortMode sortMode;
        [SerializeField] private bool autoUpdatePriority;

        /// <summary>
        /// 是否启用推荐问题
        /// </summary>
        public bool EnableRecommendedQuestions
        {
            get => enableRecommendedQuestions;
            set => enableRecommendedQuestions = value;
        }

        /// <summary>
        /// 最大显示数量
        /// </summary>
        public int MaxDisplayCount
        {
            get => maxDisplayCount;
            set => maxDisplayCount = Mathf.Max(1, value);
        }

        /// <summary>
        /// 是否启用分类
        /// </summary>
        public bool EnableCategorization
        {
            get => enableCategorization;
            set => enableCategorization = value;
        }

        /// <summary>
        /// 是否启用搜索
        /// </summary>
        public bool EnableSearch
        {
            get => enableSearch;
            set => enableSearch = value;
        }

        /// <summary>
        /// 是否启用使用统计
        /// </summary>
        public bool EnableUsageStatistics
        {
            get => enableUsageStatistics;
            set => enableUsageStatistics = value;
        }

        /// <summary>
        /// 排序模式
        /// </summary>
        public SortMode SortMode
        {
            get => sortMode;
            set => sortMode = value;
        }

        /// <summary>
        /// 是否自动更新优先级
        /// </summary>
        public bool AutoUpdatePriority
        {
            get => autoUpdatePriority;
            set => autoUpdatePriority = value;
        }

        public RecommendedQuestionConfiguration()
        {
            enableRecommendedQuestions = true;
            maxDisplayCount = 10;
            enableCategorization = true;
            enableSearch = true;
            enableUsageStatistics = true;
            sortMode = SortMode.Priority;
            autoUpdatePriority = true;
        }
    }

    /// <summary>
    /// 排序模式枚举
    /// </summary>
    public enum SortMode
    {
        Priority,       // 按优先级排序
        Usage,          // 按使用频率排序
        Recent,         // 按最近使用排序
        Alphabetical,   // 按字母顺序排序
        Category        // 按分类排序
    }

    /// <summary>
    /// 问题搜索结果
    /// </summary>
    [Serializable]
    public class QuestionSearchResult
    {
        [SerializeField] private List<RecommendedQuestion> questions;
        [SerializeField] private string searchTerm;
        [SerializeField] private int totalCount;
        [SerializeField] private float searchTime;

        /// <summary>
        /// 搜索结果问题列表
        /// </summary>
        public List<RecommendedQuestion> Questions
        {
            get => questions ?? (questions = new List<RecommendedQuestion>());
            set => questions = value;
        }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchTerm
        {
            get => searchTerm;
            set => searchTerm = value;
        }

        /// <summary>
        /// 总结果数量
        /// </summary>
        public int TotalCount
        {
            get => totalCount;
            set => totalCount = value;
        }

        /// <summary>
        /// 搜索耗时（毫秒）
        /// </summary>
        public float SearchTime
        {
            get => searchTime;
            set => searchTime = value;
        }

        public QuestionSearchResult()
        {
            questions = new List<RecommendedQuestion>();
            searchTerm = string.Empty;
            totalCount = 0;
            searchTime = 0f;
        }

        public QuestionSearchResult(List<RecommendedQuestion> questions, string searchTerm, float searchTime)
        {
            this.questions = questions ?? new List<RecommendedQuestion>();
            this.searchTerm = searchTerm;
            this.totalCount = this.questions.Count;
            this.searchTime = searchTime;
        }
    }
}