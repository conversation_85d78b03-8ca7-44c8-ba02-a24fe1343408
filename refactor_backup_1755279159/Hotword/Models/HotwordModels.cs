using System;
using System.Collections.Generic;
using UnityEngine;

namespace DigitalHuman.Core.Hotword.Models
{
    /// <summary>
    /// 热词条目数据模型
    /// </summary>
    [Serializable]
    public class HotwordEntry
    {
        [SerializeField] private string keyword;
        [SerializeField] private string response;
        [SerializeField] private string actionType;
        [SerializeField] private string actionData;
        [SerializeField] private int priority;
        [SerializeField] private int usageCount;
        [SerializeField] private DateTime lastUsed;
        [SerializeField] private bool isEnabled;
        [SerializeField] private List<string> aliases;

        /// <summary>
        /// 热词关键字
        /// </summary>
        public string Keyword
        {
            get => keyword;
            set => keyword = value;
        }

        /// <summary>
        /// 预设回答内容
        /// </summary>
        public string Response
        {
            get => response;
            set => response = value;
        }

        /// <summary>
        /// 动作类型（如：video, animation, gesture等）
        /// </summary>
        public string ActionType
        {
            get => actionType;
            set => actionType = value;
        }

        /// <summary>
        /// 动作数据（如：视频文件路径、动画名称等）
        /// </summary>
        public string ActionData
        {
            get => actionData;
            set => actionData = value;
        }

        /// <summary>
        /// 优先级（数值越高优先级越高）
        /// </summary>
        public int Priority
        {
            get => priority;
            set => priority = value;
        }

        /// <summary>
        /// 使用次数统计
        /// </summary>
        public int UsageCount
        {
            get => usageCount;
            set => usageCount = value;
        }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime LastUsed
        {
            get => lastUsed;
            set => lastUsed = value;
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => isEnabled;
            set => isEnabled = value;
        }

        /// <summary>
        /// 别名列表（支持多个关键词匹配同一个回答）
        /// </summary>
        public List<string> Aliases
        {
            get => aliases ?? (aliases = new List<string>());
            set => aliases = value;
        }

        public HotwordEntry()
        {
            keyword = string.Empty;
            response = string.Empty;
            actionType = string.Empty;
            actionData = string.Empty;
            priority = 0;
            usageCount = 0;
            lastUsed = DateTime.MinValue;
            isEnabled = true;
            aliases = new List<string>();
        }

        public HotwordEntry(string keyword, string response, int priority = 0)
        {
            this.keyword = keyword;
            this.response = response;
            this.priority = priority;
            this.actionType = string.Empty;
            this.actionData = string.Empty;
            this.usageCount = 0;
            this.lastUsed = DateTime.MinValue;
            this.isEnabled = true;
            this.aliases = new List<string>();
        }
    }

    /// <summary>
    /// 热词匹配结果
    /// </summary>
    [Serializable]
    public class HotwordMatchResult
    {
        [SerializeField] private bool isMatch;
        [SerializeField] private HotwordEntry matchedEntry;
        [SerializeField] private string matchedKeyword;
        [SerializeField] private float confidence;

        /// <summary>
        /// 是否匹配到热词
        /// </summary>
        public bool IsMatch
        {
            get => isMatch;
            set => isMatch = value;
        }

        /// <summary>
        /// 匹配到的热词条目
        /// </summary>
        public HotwordEntry MatchedEntry
        {
            get => matchedEntry;
            set => matchedEntry = value;
        }

        /// <summary>
        /// 匹配到的具体关键词
        /// </summary>
        public string MatchedKeyword
        {
            get => matchedKeyword;
            set => matchedKeyword = value;
        }

        /// <summary>
        /// 匹配置信度（0-1之间）
        /// </summary>
        public float Confidence
        {
            get => confidence;
            set => confidence = value;
        }

        public HotwordMatchResult()
        {
            isMatch = false;
            matchedEntry = null;
            matchedKeyword = string.Empty;
            confidence = 0f;
        }

        public HotwordMatchResult(bool isMatch, HotwordEntry entry, string keyword, float confidence)
        {
            this.isMatch = isMatch;
            this.matchedEntry = entry;
            this.matchedKeyword = keyword;
            this.confidence = confidence;
        }
    }

    /// <summary>
    /// 热词配置
    /// </summary>
    [Serializable]
    public class HotwordConfiguration
    {
        [SerializeField] private bool enableHotwordDetection;
        [SerializeField] private float matchThreshold;
        [SerializeField] private bool enableFuzzyMatch;
        [SerializeField] private int maxSuggestions;
        [SerializeField] private bool enableUsageStatistics;

        /// <summary>
        /// 是否启用热词检测
        /// </summary>
        public bool EnableHotwordDetection
        {
            get => enableHotwordDetection;
            set => enableHotwordDetection = value;
        }

        /// <summary>
        /// 匹配阈值（0-1之间，用于模糊匹配）
        /// </summary>
        public float MatchThreshold
        {
            get => matchThreshold;
            set => matchThreshold = Mathf.Clamp01(value);
        }

        /// <summary>
        /// 是否启用模糊匹配
        /// </summary>
        public bool EnableFuzzyMatch
        {
            get => enableFuzzyMatch;
            set => enableFuzzyMatch = value;
        }

        /// <summary>
        /// 最大建议数量
        /// </summary>
        public int MaxSuggestions
        {
            get => maxSuggestions;
            set => maxSuggestions = Mathf.Max(0, value);
        }

        /// <summary>
        /// 是否启用使用统计
        /// </summary>
        public bool EnableUsageStatistics
        {
            get => enableUsageStatistics;
            set => enableUsageStatistics = value;
        }

        public HotwordConfiguration()
        {
            enableHotwordDetection = true;
            matchThreshold = 0.8f;
            enableFuzzyMatch = true;
            maxSuggestions = 5;
            enableUsageStatistics = true;
        }
    }
}