using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Hotword.Models;
using DigitalHuman.Core;

namespace DigitalHuman.Core.Hotword
{
    /// <summary>
    /// 推荐问题管理器实现
    /// </summary>
    public class RecommendedQuestionManager : ManagerBase, IRecommendedQuestionManager
    {
        [Header("推荐问题配置")]
        [SerializeField] private RecommendedQuestionConfiguration configuration;
        [SerializeField] private string questionsDataPath = "recommended_questions.json";
        [SerializeField] private string categoriesDataPath = "question_categories.json";
        
        private Dictionary<string, RecommendedQuestion> questionDictionary;
        private List<RecommendedQuestion> questionList;
        private List<QuestionCategory> categories;
        private bool isInitialized;

        /// <summary>
        /// 问题被点击事件
        /// </summary>
        public event Action<RecommendedQuestion> OnQuestionClicked;

        /// <summary>
        /// 问题列表更新事件
        /// </summary>
        public event Action OnQuestionListUpdated;

        protected override void Awake()
        {
            base.Awake();
            InitializeCollections();
            InitializeConfiguration();
        }

        /// <summary>
        /// 初始化集合
        /// </summary>
        private void InitializeCollections()
        {
            questionDictionary = new Dictionary<string, RecommendedQuestion>();
            questionList = new List<RecommendedQuestion>();
            categories = new List<QuestionCategory>();
        }

        /// <summary>
        /// 初始化配置
        /// </summary>
        private void InitializeConfiguration()
        {
            if (configuration == null)
            {
                configuration = new RecommendedQuestionConfiguration();
            }
        }

        /// <summary>
        /// 初始化推荐问题管理器
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                LogInfo("开始初始化推荐问题管理器");
                
                // 加载默认分类
                LoadDefaultCategories();
                
                // 加载默认推荐问题
                LoadDefaultQuestions();
                
                // 尝试从文件加载数据
                string questionsPath = GetQuestionsDataPath();
                if (File.Exists(questionsPath))
                {
                    await LoadQuestionsFromFileAsync(questionsPath);
                }
                
                string categoriesPath = GetCategoriesDataPath();
                if (File.Exists(categoriesPath))
                {
                    await LoadCategoriesFromFileAsync(categoriesPath);
                }
                
                isInitialized = true;
                LogInfo($"推荐问题管理器初始化完成，共加载 {questionList.Count} 个问题，{categories.Count} 个分类");
            }
            catch (Exception ex)
            {
                LogError($"推荐问题管理器初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载默认分类
        /// </summary>
        private void LoadDefaultCategories()
        {
            var defaultCategories = new List<QuestionCategory>
            {
                new QuestionCategory("general", "常见问题", Color.blue),
                new QuestionCategory("service", "服务介绍", Color.green),
                new QuestionCategory("location", "位置信息", Color.yellow),
                new QuestionCategory("entertainment", "娱乐互动", Color.magenta),
                new QuestionCategory("help", "帮助支持", Color.red)
            };

            foreach (var category in defaultCategories)
            {
                AddCategoryInternal(category);
            }
        }

        /// <summary>
        /// 加载默认推荐问题
        /// </summary>
        private void LoadDefaultQuestions()
        {
            var defaultQuestions = new List<RecommendedQuestion>
            {
                new RecommendedQuestion("你好，请问有什么可以帮助您的吗？", "您好！我是您的数字助手，很高兴为您服务。我可以为您介绍景点、回答问题、进行娱乐互动等。请告诉我您需要什么帮助？", "general", 10),
                new RecommendedQuestion("这里有什么好玩的景点？", "这里有很多精彩的景点！让我为您详细介绍一下各个景点的特色和亮点...", "location", 9),
                new RecommendedQuestion("能为我跳个舞吗？", "当然可以！让我为您表演一段精彩的舞蹈！", "entertainment", 8),
                new RecommendedQuestion("现在几点了？", $"现在是 {DateTime.Now:HH:mm}，今天是 {DateTime.Now:yyyy年MM月dd日}。", "general", 7),
                new RecommendedQuestion("今天天气怎么样？", "今天天气不错，适合外出游玩。具体的天气信息我需要为您查询一下。", "general", 6),
                new RecommendedQuestion("这里的开放时间是什么时候？", "我们的开放时间是每天上午9:00到下午6:00，全年无休。节假日可能会有特殊安排。", "service", 5),
                new RecommendedQuestion("有什么特色美食推荐吗？", "这里有很多特色美食！比如当地的特色小吃、传统菜肴等，我可以为您详细介绍。", "service", 4),
                new RecommendedQuestion("如何联系客服？", "如果您需要人工客服帮助，可以拨打服务热线或者前往服务台，工作人员会为您提供专业的帮助。", "help", 3)
            };

            foreach (var question in defaultQuestions)
            {
                AddQuestionInternal(question);
            }
        }

        /// <summary>
        /// 添加推荐问题
        /// </summary>
        public bool AddQuestion(RecommendedQuestion question)
        {
            if (question == null || string.IsNullOrEmpty(question.Question))
            {
                LogWarning("无法添加空的推荐问题");
                return false;
            }

            if (questionDictionary.ContainsKey(question.Id))
            {
                LogWarning($"推荐问题 '{question.Id}' 已存在");
                return false;
            }

            AddQuestionInternal(question);
            OnQuestionListUpdated?.Invoke();
            LogInfo($"成功添加推荐问题: {question.Question}");
            return true;
        }

        /// <summary>
        /// 内部添加问题方法
        /// </summary>
        private void AddQuestionInternal(RecommendedQuestion question)
        {
            questionDictionary[question.Id] = question;
            questionList.Add(question);
        }

        /// <summary>
        /// 删除推荐问题
        /// </summary>
        public bool RemoveQuestion(string questionId)
        {
            if (string.IsNullOrEmpty(questionId))
            {
                return false;
            }

            if (!questionDictionary.ContainsKey(questionId))
            {
                LogWarning($"推荐问题 '{questionId}' 不存在");
                return false;
            }

            var question = questionDictionary[questionId];
            questionDictionary.Remove(questionId);
            questionList.Remove(question);
            
            OnQuestionListUpdated?.Invoke();
            LogInfo($"成功删除推荐问题: {question.Question}");
            return true;
        }

        /// <summary>
        /// 更新推荐问题
        /// </summary>
        public bool UpdateQuestion(RecommendedQuestion question)
        {
            if (question == null || string.IsNullOrEmpty(question.Id))
            {
                return false;
            }

            if (!questionDictionary.ContainsKey(question.Id))
            {
                LogWarning($"推荐问题 '{question.Id}' 不存在");
                return false;
            }

            // 更新字典中的引用
            questionDictionary[question.Id] = question;
            
            // 更新列表中的引用
            int index = questionList.FindIndex(q => q.Id == question.Id);
            if (index >= 0)
            {
                questionList[index] = question;
            }

            OnQuestionListUpdated?.Invoke();
            LogInfo($"成功更新推荐问题: {question.Question}");
            return true;
        }

        /// <summary>
        /// 获取推荐问题
        /// </summary>
        public RecommendedQuestion GetQuestion(string questionId)
        {
            if (string.IsNullOrEmpty(questionId))
            {
                return null;
            }

            return questionDictionary.TryGetValue(questionId, out var question) ? question : null;
        }

        /// <summary>
        /// 获取所有推荐问题
        /// </summary>
        public List<RecommendedQuestion> GetAllQuestions()
        {
            return new List<RecommendedQuestion>(questionList);
        }

        /// <summary>
        /// 获取显示的推荐问题列表
        /// </summary>
        public List<RecommendedQuestion> GetDisplayQuestions(int maxCount = 10)
        {
            if (!configuration.EnableRecommendedQuestions)
            {
                return new List<RecommendedQuestion>();
            }

            var enabledQuestions = questionList.Where(q => q.IsEnabled).ToList();
            
            // 根据配置的排序模式排序
            switch (configuration.SortMode)
            {
                case SortMode.Priority:
                    enabledQuestions = enabledQuestions.OrderByDescending(q => q.Priority).ToList();
                    break;
                case SortMode.Usage:
                    enabledQuestions = enabledQuestions.OrderByDescending(q => q.ClickCount).ToList();
                    break;
                case SortMode.Recent:
                    enabledQuestions = enabledQuestions.OrderByDescending(q => q.LastClicked).ToList();
                    break;
                case SortMode.Alphabetical:
                    enabledQuestions = enabledQuestions.OrderBy(q => q.Question).ToList();
                    break;
                case SortMode.Category:
                    enabledQuestions = enabledQuestions.OrderBy(q => q.Category).ThenByDescending(q => q.Priority).ToList();
                    break;
            }

            int displayCount = Math.Min(maxCount, configuration.MaxDisplayCount);
            return enabledQuestions.Take(displayCount).ToList();
        }

        /// <summary>
        /// 按分类获取推荐问题
        /// </summary>
        public List<RecommendedQuestion> GetQuestionsByCategory(string category)
        {
            if (string.IsNullOrEmpty(category))
            {
                return new List<RecommendedQuestion>();
            }

            return questionList
                .Where(q => q.IsEnabled && string.Equals(q.Category, category, StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(q => q.Priority)
                .ToList();
        }

        /// <summary>
        /// 搜索推荐问题
        /// </summary>
        public QuestionSearchResult SearchQuestions(string searchTerm, int maxResults = 20)
        {
            var startTime = Time.realtimeSinceStartup;
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                var allQuestions = GetDisplayQuestions(maxResults);
                return new QuestionSearchResult(allQuestions, searchTerm, (Time.realtimeSinceStartup - startTime) * 1000);
            }

            var searchResults = new List<RecommendedQuestion>();
            string lowerSearchTerm = searchTerm.ToLower();

            foreach (var question in questionList)
            {
                if (!question.IsEnabled) continue;

                // 搜索问题文本
                if (question.Question.ToLower().Contains(lowerSearchTerm))
                {
                    searchResults.Add(question);
                    continue;
                }

                // 搜索答案文本
                if (question.Answer.ToLower().Contains(lowerSearchTerm))
                {
                    searchResults.Add(question);
                    continue;
                }

                // 搜索标签
                if (question.Tags.Any(tag => tag.ToLower().Contains(lowerSearchTerm)))
                {
                    searchResults.Add(question);
                    continue;
                }

                // 搜索分类
                if (question.Category.ToLower().Contains(lowerSearchTerm))
                {
                    searchResults.Add(question);
                }
            }

            // 按相关性排序（简单实现：优先级高的排前面）
            searchResults = searchResults
                .OrderByDescending(q => q.Priority)
                .ThenByDescending(q => q.ClickCount)
                .Take(maxResults)
                .ToList();

            float searchTime = (Time.realtimeSinceStartup - startTime) * 1000;
            return new QuestionSearchResult(searchResults, searchTerm, searchTime);
        }

        /// <summary>
        /// 处理问题点击
        /// </summary>
        public string HandleQuestionClick(string questionId)
        {
            var question = GetQuestion(questionId);
            if (question == null)
            {
                LogWarning($"点击的问题不存在: {questionId}");
                return string.Empty;
            }

            // 记录点击统计
            RecordQuestionClick(questionId);

            // 触发点击事件
            OnQuestionClicked?.Invoke(question);

            LogInfo($"处理问题点击: {question.Question}");
            return question.Answer;
        }

        /// <summary>
        /// 记录问题点击统计
        /// </summary>
        public void RecordQuestionClick(string questionId)
        {
            if (!configuration.EnableUsageStatistics || string.IsNullOrEmpty(questionId))
            {
                return;
            }

            var question = GetQuestion(questionId);
            if (question != null)
            {
                question.ClickCount++;
                question.LastClicked = DateTime.Now;

                // 自动更新优先级
                if (configuration.AutoUpdatePriority)
                {
                    UpdateQuestionPriority(question);
                }

                LogInfo($"记录问题点击: {question.Question}, 点击次数: {question.ClickCount}");
            }
        }

        /// <summary>
        /// 更新单个问题的优先级
        /// </summary>
        private void UpdateQuestionPriority(RecommendedQuestion question)
        {
            // 基于点击次数和最近使用时间调整优先级
            int basePriority = question.Priority;
            int usageBonus = Math.Min(question.ClickCount / 10, 5); // 每10次点击增加1优先级，最多增加5
            
            // 最近使用的问题获得额外优先级
            var daysSinceLastClick = (DateTime.Now - question.LastClicked).TotalDays;
            int recentBonus = daysSinceLastClick < 1 ? 2 : (daysSinceLastClick < 7 ? 1 : 0);

            question.Priority = basePriority + usageBonus + recentBonus;
        }

        /// <summary>
        /// 获取问题分类列表
        /// </summary>
        public List<QuestionCategory> GetCategories()
        {
            return categories.Where(c => c.IsVisible).OrderBy(c => c.SortOrder).ToList();
        }

        /// <summary>
        /// 添加问题分类
        /// </summary>
        public bool AddCategory(QuestionCategory category)
        {
            if (category == null || string.IsNullOrEmpty(category.Name))
            {
                LogWarning("无法添加空的问题分类");
                return false;
            }

            if (categories.Any(c => string.Equals(c.Name, category.Name, StringComparison.OrdinalIgnoreCase)))
            {
                LogWarning($"问题分类 '{category.Name}' 已存在");
                return false;
            }

            AddCategoryInternal(category);
            LogInfo($"成功添加问题分类: {category.DisplayName}");
            return true;
        }

        /// <summary>
        /// 内部添加分类方法
        /// </summary>
        private void AddCategoryInternal(QuestionCategory category)
        {
            categories.Add(category);
        }

        /// <summary>
        /// 删除问题分类
        /// </summary>
        public bool RemoveCategory(string categoryName)
        {
            if (string.IsNullOrEmpty(categoryName))
            {
                return false;
            }

            var category = categories.FirstOrDefault(c => string.Equals(c.Name, categoryName, StringComparison.OrdinalIgnoreCase));
            if (category == null)
            {
                LogWarning($"问题分类 '{categoryName}' 不存在");
                return false;
            }

            // 检查是否有问题使用此分类
            var questionsInCategory = questionList.Where(q => string.Equals(q.Category, categoryName, StringComparison.OrdinalIgnoreCase)).ToList();
            if (questionsInCategory.Count > 0)
            {
                LogWarning($"无法删除分类 '{categoryName}'，还有 {questionsInCategory.Count} 个问题使用此分类");
                return false;
            }

            categories.Remove(category);
            LogInfo($"成功删除问题分类: {category.DisplayName}");
            return true;
        }

        /// <summary>
        /// 获取使用统计
        /// </summary>
        public Dictionary<string, int> GetUsageStatistics()
        {
            var statistics = new Dictionary<string, int>();
            
            foreach (var question in questionList)
            {
                statistics[question.Question] = question.ClickCount;
            }

            return statistics;
        }

        /// <summary>
        /// 清空使用统计
        /// </summary>
        public void ClearUsageStatistics()
        {
            foreach (var question in questionList)
            {
                question.ClickCount = 0;
                question.LastClicked = DateTime.MinValue;
            }
            
            LogInfo("已清空推荐问题使用统计");
        }

        /// <summary>
        /// 从文件加载推荐问题
        /// </summary>
        public async Task<bool> LoadQuestionsFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    LogWarning($"推荐问题文件不存在: {filePath}");
                    return false;
                }

                string jsonContent = await File.ReadAllTextAsync(filePath);
                var questions = JsonUtility.FromJson<RecommendedQuestion[]>(jsonContent);
                
                if (questions != null)
                {
                    int importedCount = ImportQuestions(questions.ToList());
                    LogInfo($"从文件加载推荐问题成功: {importedCount} 个");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogError($"加载推荐问题文件失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 从文件加载分类
        /// </summary>
        private async Task<bool> LoadCategoriesFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                string jsonContent = await File.ReadAllTextAsync(filePath);
                var loadedCategories = JsonUtility.FromJson<QuestionCategory[]>(jsonContent);
                
                if (loadedCategories != null)
                {
                    foreach (var category in loadedCategories)
                    {
                        AddCategory(category);
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogError($"加载分类文件失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 保存推荐问题到文件
        /// </summary>
        public async Task<bool> SaveQuestionsToFileAsync(string filePath)
        {
            try
            {
                var questions = ExportQuestions();
                string jsonContent = JsonUtility.ToJson(questions.ToArray(), true);
                
                string directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                await File.WriteAllTextAsync(filePath, jsonContent);
                LogInfo($"保存推荐问题到文件成功: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"保存推荐问题文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导入推荐问题
        /// </summary>
        public int ImportQuestions(List<RecommendedQuestion> questions)
        {
            if (questions == null)
            {
                return 0;
            }

            int importedCount = 0;
            foreach (var question in questions)
            {
                if (AddQuestion(question))
                {
                    importedCount++;
                }
            }

            return importedCount;
        }

        /// <summary>
        /// 导出推荐问题
        /// </summary>
        public List<RecommendedQuestion> ExportQuestions()
        {
            return GetAllQuestions();
        }

        /// <summary>
        /// 设置配置
        /// </summary>
        public void SetConfiguration(RecommendedQuestionConfiguration config)
        {
            if (config != null)
            {
                configuration = config;
                LogInfo("推荐问题配置已更新");
            }
        }

        /// <summary>
        /// 获取配置
        /// </summary>
        public RecommendedQuestionConfiguration GetConfiguration()
        {
            return configuration;
        }

        /// <summary>
        /// 启用或禁用推荐问题
        /// </summary>
        public void SetRecommendedQuestionsEnabled(bool enabled)
        {
            configuration.EnableRecommendedQuestions = enabled;
            LogInfo($"推荐问题已{(enabled ? "启用" : "禁用")}");
        }

        /// <summary>
        /// 按优先级排序问题
        /// </summary>
        public List<RecommendedQuestion> GetQuestionsByPriority()
        {
            return questionList
                .Where(q => q.IsEnabled)
                .OrderByDescending(q => q.Priority)
                .ThenBy(q => q.Question)
                .ToList();
        }

        /// <summary>
        /// 按使用频率排序问题
        /// </summary>
        public List<RecommendedQuestion> GetQuestionsByUsage()
        {
            return questionList
                .Where(q => q.IsEnabled)
                .OrderByDescending(q => q.ClickCount)
                .ThenByDescending(q => q.LastClicked)
                .ThenBy(q => q.Question)
                .ToList();
        }

        /// <summary>
        /// 按最近使用排序问题
        /// </summary>
        public List<RecommendedQuestion> GetQuestionsByRecent()
        {
            return questionList
                .Where(q => q.IsEnabled)
                .OrderByDescending(q => q.LastClicked)
                .ThenByDescending(q => q.ClickCount)
                .ThenBy(q => q.Question)
                .ToList();
        }

        /// <summary>
        /// 自动更新问题优先级
        /// </summary>
        public void UpdateQuestionPriorities()
        {
            foreach (var question in questionList)
            {
                UpdateQuestionPriority(question);
            }
            
            LogInfo("已更新所有问题的优先级");
        }

        /// <summary>
        /// 检查问题是否存在
        /// </summary>
        public bool ContainsQuestion(string questionId)
        {
            if (string.IsNullOrEmpty(questionId))
            {
                return false;
            }

            return questionDictionary.ContainsKey(questionId);
        }

        /// <summary>
        /// 获取问题总数
        /// </summary>
        public int GetQuestionCount()
        {
            return questionList.Count;
        }

        /// <summary>
        /// 获取启用的问题数量
        /// </summary>
        public int GetEnabledQuestionCount()
        {
            return questionList.Count(q => q.IsEnabled);
        }

        /// <summary>
        /// 获取推荐问题数据文件路径
        /// </summary>
        private string GetQuestionsDataPath()
        {
            return Path.Combine(Application.persistentDataPath, questionsDataPath);
        }

        /// <summary>
        /// 获取分类数据文件路径
        /// </summary>
        private string GetCategoriesDataPath()
        {
            return Path.Combine(Application.persistentDataPath, categoriesDataPath);
        }

        /// <summary>
        /// 组件销毁时保存数据
        /// </summary>
        protected override void OnDestroy()
        {
            if (isInitialized)
            {
                _ = SaveQuestionsToFileAsync(GetQuestionsDataPath());
            }
            base.OnDestroy();
        }
    }
}