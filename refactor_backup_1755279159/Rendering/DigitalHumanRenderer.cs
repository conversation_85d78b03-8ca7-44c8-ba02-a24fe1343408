using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 数字人渲染器实现类
    /// 负责3D数字人的渲染、动画控制和表情管理
    /// </summary>
    public class DigitalHumanRenderer : Mono<PERSON><PERSON>aviour, IDigitalHumanRenderer
    {
        #region 私有字段
        [SerializeField] private DigitalHumanConfig _config;
        [SerializeField] private Transform _characterRoot;
        [SerializeField] private Animator _animator;
        [SerializeField] private SkinnedMeshRenderer _faceRenderer;
        [SerializeField] private Transform _eyeLeftTarget;
        [SerializeField] private Transform _eyeRightTarget;
        
        private DigitalHumanState _currentState;
        private RenderingPerformance _performance;
        private Dictionary<AnimationType, AnimationState> _animationStates;
        private Dictionary<EmotionType, EmotionData> _emotionData;
        private LipSyncData _currentLipSyncData;
        private Coroutine _emotionTransitionCoroutine;
        private Coroutine _lipSyncCoroutine;
        private bool _isInitialized = false;
        
        // 表情混合形状索引缓存
        private Dictionary<string, int> _blendShapeIndices;
        
        // 性能监控
        private float _frameTimeAccumulator = 0f;
        private int _frameCount = 0;
        private float _lastPerformanceUpdate = 0f;
        #endregion

        #region 属性实现
        public RenderMode CurrentRenderMode => _config?.renderMode ?? RenderMode.Model3D;
        public DigitalHumanState CurrentState => _currentState;
        public bool IsInitialized => _isInitialized;
        public RenderingPerformance Performance => _performance;
        #endregion

        #region 事件实现
        public event Action<RenderMode> OnRenderModeChanged;
        public event Action<AnimationType> OnAnimationChanged;
        public event Action<EmotionType> OnEmotionChanged;
        public event Action<string> OnRenderingError;
        #endregion

        #region Unity生命周期
        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            if (_config != null)
            {
                Initialize(_config);
            }
        }

        private void Update()
        {
            if (!_isInitialized) return;
            
            UpdatePerformanceStats();
            UpdateEyeTracking();
        }

        private void OnDestroy()
        {
            Dispose();
        }
        #endregion

        #region 初始化和配置实现
        public bool Initialize(DigitalHumanConfig config)
        {
            try
            {
                _config = config;
                
                // 初始化数据结构
                InitializeDataStructures();
                
                // 设置渲染质量
                SetRenderQuality(_config.renderQuality);
                
                // 加载默认角色
                if (!string.IsNullOrEmpty(_config.characterId))
                {
                    LoadDigitalHuman(_config.characterId);
                }
                
                // 初始化动画状态
                InitializeAnimationStates();
                
                // 初始化表情数据
                InitializeEmotionData();
                
                // 设置默认状态
                _currentState = new DigitalHumanState
                {
                    currentAnimation = AnimationType.Idle,
                    currentEmotion = EmotionType.Neutral,
                    isSpeaking = false,
                    isListening = false,
                    eyeTargetPosition = Vector3.forward,
                    emotionIntensity = 0f,
                    lastStateChange = DateTime.Now
                };
                
                _isInitialized = true;
                Debug.Log($"数字人渲染器初始化成功: {_config.characterName}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"数字人渲染器初始化失败: {ex.Message}");
                OnRenderingError?.Invoke($"初始化失败: {ex.Message}");
                return false;
            }
        }

        public void SetRenderMode(RenderMode mode)
        {
            if (_config.renderMode == mode) return;
            
            _config.renderMode = mode;
            OnRenderModeChanged?.Invoke(mode);
            
            Debug.Log($"渲染模式切换到: {mode}");
        }

        public bool LoadDigitalHuman(string characterId)
        {
            try
            {
                if (_config.model3DPrefab == null)
                {
                    Debug.LogWarning("未设置3D模型预制体");
                    return false;
                }
                
                // 清理现有角色
                if (_characterRoot != null)
                {
                    DestroyImmediate(_characterRoot.gameObject);
                }
                
                // 实例化新角色
                GameObject characterInstance = Instantiate(_config.model3DPrefab, transform);
                _characterRoot = characterInstance.transform;
                
                // 获取组件引用
                _animator = characterInstance.GetComponent<Animator>();
                _faceRenderer = characterInstance.GetComponentInChildren<SkinnedMeshRenderer>();
                
                // 设置动画控制器
                if (_animator != null && _config.animatorController != null)
                {
                    _animator.runtimeAnimatorController = _config.animatorController;
                }
                
                // 初始化混合形状索引
                InitializeBlendShapeIndices();
                
                // 查找眼部目标点
                FindEyeTargets();
                
                Debug.Log($"数字人角色加载成功: {characterId}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载数字人角色失败: {ex.Message}");
                OnRenderingError?.Invoke($"角色加载失败: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            // 停止所有协程
            if (_emotionTransitionCoroutine != null)
            {
                StopCoroutine(_emotionTransitionCoroutine);
            }
            
            if (_lipSyncCoroutine != null)
            {
                StopCoroutine(_lipSyncCoroutine);
            }
            
            // 清理资源
            _animationStates?.Clear();
            _emotionData?.Clear();
            _blendShapeIndices?.Clear();
            
            _isInitialized = false;
            Debug.Log("数字人渲染器资源已释放");
        }
        #endregion

        #region 动画控制实现
        public void PlayAnimation(AnimationType animationType, float blendTime = 0.3f)
        {
            if (!_isInitialized || _animator == null) return;
            
            try
            {
                string animationName = GetAnimationStateName(animationType);
                if (string.IsNullOrEmpty(animationName)) return;
                
                // 播放动画
                _animator.CrossFade(animationName, blendTime);
                
                // 更新状态
                _currentState.currentAnimation = animationType;
                _currentState.lastStateChange = DateTime.Now;
                
                OnAnimationChanged?.Invoke(animationType);
                Debug.Log($"播放动画: {animationType}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"播放动画失败: {ex.Message}");
                OnRenderingError?.Invoke($"动画播放失败: {ex.Message}");
            }
        }

        public void StopAnimation()
        {
            if (!_isInitialized || _animator == null) return;
            
            PlayAnimation(AnimationType.Idle);
        }

        public void SetAnimationSpeed(float speed)
        {
            if (!_isInitialized || _animator == null) return;
            
            _animator.speed = Mathf.Clamp(speed, 0.1f, 3.0f);
        }

        public bool IsAnimationPlaying(AnimationType animationType)
        {
            if (!_isInitialized || _animator == null) return false;
            
            string animationName = GetAnimationStateName(animationType);
            if (string.IsNullOrEmpty(animationName)) return false;
            
            AnimatorStateInfo stateInfo = _animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.IsName(animationName);
        }
        #endregion

        #region 表情控制实现
        public void SetEmotion(EmotionType emotion, float intensity = 1.0f, float duration = 2.0f)
        {
            if (!_isInitialized || _faceRenderer == null) return;
            
            try
            {
                // 停止当前表情过渡
                if (_emotionTransitionCoroutine != null)
                {
                    StopCoroutine(_emotionTransitionCoroutine);
                }
                
                // 开始新的表情过渡
                _emotionTransitionCoroutine = StartCoroutine(TransitionToEmotion(emotion, intensity, duration));
                
                // 更新状态
                _currentState.currentEmotion = emotion;
                _currentState.emotionIntensity = intensity;
                _currentState.lastStateChange = DateTime.Now;
                
                OnEmotionChanged?.Invoke(emotion);
                Debug.Log($"设置表情: {emotion}, 强度: {intensity}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置表情失败: {ex.Message}");
                OnRenderingError?.Invoke($"表情设置失败: {ex.Message}");
            }
        }

        public void ResetEmotion()
        {
            SetEmotion(EmotionType.Neutral, 0f, 1f);
        }

        public void SetCustomEmotionBlend(float[] emotionBlends)
        {
            if (!_isInitialized || _faceRenderer == null || emotionBlends == null) return;
            
            try
            {
                // 应用自定义表情混合
                for (int i = 0; i < emotionBlends.Length && i < _faceRenderer.sharedMesh.blendShapeCount; i++)
                {
                    _faceRenderer.SetBlendShapeWeight(i, emotionBlends[i] * 100f);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置自定义表情混合失败: {ex.Message}");
                OnRenderingError?.Invoke($"自定义表情设置失败: {ex.Message}");
            }
        }
        #endregion

        #region 口型同步实现
        public void StartLipSync(AudioClip audioClip)
        {
            if (!_isInitialized || audioClip == null) return;
            
            try
            {
                // 停止当前口型同步
                StopLipSync();
                
                // 生成口型同步数据（简化实现）
                _currentLipSyncData = GenerateLipSyncData(audioClip);
                
                // 开始口型同步协程
                _lipSyncCoroutine = StartCoroutine(LipSyncCoroutine());
                
                _currentState.isSpeaking = true;
                Debug.Log($"开始口型同步: {audioClip.name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"开始口型同步失败: {ex.Message}");
                OnRenderingError?.Invoke($"口型同步失败: {ex.Message}");
            }
        }

        public void StopLipSync()
        {
            if (_lipSyncCoroutine != null)
            {
                StopCoroutine(_lipSyncCoroutine);
                _lipSyncCoroutine = null;
            }
            
            _currentState.isSpeaking = false;
            
            // 重置口型到默认状态
            ResetLipShape();
        }

        public void SetLipSyncData(LipSyncData lipSyncData)
        {
            _currentLipSyncData = lipSyncData;
        }

        public void UpdateLipSync(float audioTime)
        {
            if (!_isInitialized || _currentLipSyncData == null || _faceRenderer == null) return;
            
            try
            {
                // 根据音频时间更新口型
                UpdateLipShapeAtTime(audioTime);
            }
            catch (Exception ex)
            {
                Debug.LogError($"更新口型同步失败: {ex.Message}");
            }
        }
        #endregion

        #region 视线控制实现
        public void SetEyeTarget(Vector3 targetPosition)
        {
            _currentState.eyeTargetPosition = targetPosition;
        }

        public void SetEyeTrackingEnabled(bool enabled)
        {
            _config.enableEyeTracking = enabled;
        }

        public void ResetEyeTarget()
        {
            _currentState.eyeTargetPosition = Vector3.forward;
        }
        #endregion

        #region 性能和调试实现
        public void UpdatePerformanceStats()
        {
            _frameTimeAccumulator += Time.unscaledDeltaTime;
            _frameCount++;
            
            if (Time.time - _lastPerformanceUpdate >= 1.0f)
            {
                _performance.currentFPS = 1.0f / Time.unscaledDeltaTime;
                _performance.averageFPS = _frameCount / _frameTimeAccumulator;
                _performance.lastUpdateTime = DateTime.Now;
                
                _frameTimeAccumulator = 0f;
                _frameCount = 0;
                _lastPerformanceUpdate = Time.time;
            }
        }

        public string GetDebugInfo()
        {
            if (!_isInitialized) return "渲染器未初始化";
            
            return $"数字人渲染器调试信息:\n" +
                   $"角色: {_config.characterName}\n" +
                   $"渲染模式: {CurrentRenderMode}\n" +
                   $"当前动画: {_currentState.currentAnimation}\n" +
                   $"当前表情: {_currentState.currentEmotion}\n" +
                   $"正在说话: {_currentState.isSpeaking}\n" +
                   $"FPS: {_performance.currentFPS:F1}\n" +
                   $"内存使用: {_performance.memoryUsage:F1}MB";
        }

        public void SetRenderQuality(int quality)
        {
            quality = Mathf.Clamp(quality, 0, 2);
            _config.renderQuality = quality;
            
            // 根据质量等级调整渲染设置
            switch (quality)
            {
                case 0: // 低质量
                    QualitySettings.SetQualityLevel(0);
                    Application.targetFrameRate = 30;
                    break;
                case 1: // 中质量
                    QualitySettings.SetQualityLevel(1);
                    Application.targetFrameRate = 45;
                    break;
                case 2: // 高质量
                    QualitySettings.SetQualityLevel(2);
                    Application.targetFrameRate = _config.targetFrameRate;
                    break;
            }
            
            Debug.Log($"渲染质量设置为: {quality}");
        }
        #endregion

        #region 私有辅助方法
        private void InitializeComponents()
        {
            _performance = new RenderingPerformance();
            _animationStates = new Dictionary<AnimationType, AnimationState>();
            _emotionData = new Dictionary<EmotionType, EmotionData>();
            _blendShapeIndices = new Dictionary<string, int>();
        }

        private void InitializeDataStructures()
        {
            _currentState = new DigitalHumanState();
            _performance = new RenderingPerformance
            {
                currentFPS = 0f,
                averageFPS = 0f,
                drawCalls = 0,
                triangleCount = 0,
                memoryUsage = 0f,
                lastUpdateTime = DateTime.Now
            };
        }

        private void InitializeAnimationStates()
        {
            // 初始化动画状态映射
            _animationStates[AnimationType.Idle] = new AnimationState
            {
                type = AnimationType.Idle,
                stateName = "Idle",
                duration = -1f, // 循环动画
                isLooping = true,
                blendTime = 0.3f,
                priority = 0
            };
            
            _animationStates[AnimationType.Speaking] = new AnimationState
            {
                type = AnimationType.Speaking,
                stateName = "Speaking",
                duration = -1f,
                isLooping = true,
                blendTime = 0.2f,
                priority = 2
            };
            
            _animationStates[AnimationType.Greeting] = new AnimationState
            {
                type = AnimationType.Greeting,
                stateName = "Greeting",
                duration = 3f,
                isLooping = false,
                blendTime = 0.3f,
                priority = 3
            };
            
            _animationStates[AnimationType.Gesture] = new AnimationState
            {
                type = AnimationType.Gesture,
                stateName = "Gesture",
                duration = 2f,
                isLooping = false,
                blendTime = 0.3f,
                priority = 2
            };
            
            _animationStates[AnimationType.Dancing] = new AnimationState
            {
                type = AnimationType.Dancing,
                stateName = "Dancing",
                duration = 10f,
                isLooping = true,
                blendTime = 0.5f,
                priority = 4
            };
            
            _animationStates[AnimationType.Listening] = new AnimationState
            {
                type = AnimationType.Listening,
                stateName = "Listening",
                duration = -1f,
                isLooping = true,
                blendTime = 0.3f,
                priority = 1
            };
            
            _animationStates[AnimationType.Thinking] = new AnimationState
            {
                type = AnimationType.Thinking,
                stateName = "Thinking",
                duration = -1f,
                isLooping = true,
                blendTime = 0.3f,
                priority = 1
            };
        }

        private void InitializeEmotionData()
        {
            // 初始化表情数据
            _emotionData[EmotionType.Neutral] = new EmotionData
            {
                emotion = EmotionType.Neutral,
                intensity = 0f,
                duration = 1f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
            
            _emotionData[EmotionType.Happy] = new EmotionData
            {
                emotion = EmotionType.Happy,
                intensity = 1f,
                duration = 2f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
            
            _emotionData[EmotionType.Sad] = new EmotionData
            {
                emotion = EmotionType.Sad,
                intensity = 1f,
                duration = 3f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
            
            _emotionData[EmotionType.Angry] = new EmotionData
            {
                emotion = EmotionType.Angry,
                intensity = 1f,
                duration = 2f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
            
            _emotionData[EmotionType.Surprised] = new EmotionData
            {
                emotion = EmotionType.Surprised,
                intensity = 1f,
                duration = 1.5f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
            
            _emotionData[EmotionType.Confused] = new EmotionData
            {
                emotion = EmotionType.Confused,
                intensity = 1f,
                duration = 2.5f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
            
            _emotionData[EmotionType.Excited] = new EmotionData
            {
                emotion = EmotionType.Excited,
                intensity = 1f,
                duration = 2f,
                transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f)
            };
        }

        private void InitializeBlendShapeIndices()
        {
            if (_faceRenderer?.sharedMesh == null) return;
            
            _blendShapeIndices.Clear();
            Mesh mesh = _faceRenderer.sharedMesh;
            
            for (int i = 0; i < mesh.blendShapeCount; i++)
            {
                string shapeName = mesh.GetBlendShapeName(i);
                _blendShapeIndices[shapeName] = i;
            }
        }

        private void FindEyeTargets()
        {
            // 查找眼部目标点（简化实现）
            if (_characterRoot != null)
            {
                _eyeLeftTarget = _characterRoot.Find("EyeLeft");
                _eyeRightTarget = _characterRoot.Find("EyeRight");
            }
        }

        private string GetAnimationStateName(AnimationType animationType)
        {
            return _animationStates.ContainsKey(animationType) ? 
                   _animationStates[animationType].stateName : 
                   "Idle";
        }

        private IEnumerator TransitionToEmotion(EmotionType emotion, float intensity, float duration)
        {
            if (_faceRenderer == null) yield break;
            
            float elapsedTime = 0f;
            EmotionData emotionData = _emotionData.ContainsKey(emotion) ? 
                                    _emotionData[emotion] : 
                                    _emotionData[EmotionType.Neutral];
            
            while (elapsedTime < duration)
            {
                float progress = elapsedTime / duration;
                float curveValue = emotionData.transitionCurve.Evaluate(progress);
                float currentIntensity = intensity * curveValue;
                
                // 应用表情混合形状
                ApplyEmotionBlendShapes(emotion, currentIntensity);
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            // 确保最终状态正确
            ApplyEmotionBlendShapes(emotion, intensity);
        }

        private void ApplyEmotionBlendShapes(EmotionType emotion, float intensity)
        {
            if (_faceRenderer == null) return;
            
            // 重置所有表情混合形状
            ResetEmotionBlendShapes();
            
            // 根据情感类型应用相应的混合形状
            switch (emotion)
            {
                case EmotionType.Happy:
                    SetBlendShapeWeight("Smile", intensity * 100f);
                    SetBlendShapeWeight("EyeSquint", intensity * 50f);
                    break;
                case EmotionType.Sad:
                    SetBlendShapeWeight("Frown", intensity * 100f);
                    SetBlendShapeWeight("EyeClose", intensity * 30f);
                    break;
                case EmotionType.Angry:
                    SetBlendShapeWeight("Frown", intensity * 80f);
                    SetBlendShapeWeight("BrowDown", intensity * 100f);
                    break;
                case EmotionType.Surprised:
                    SetBlendShapeWeight("EyeWide", intensity * 100f);
                    SetBlendShapeWeight("MouthOpen", intensity * 60f);
                    break;
                case EmotionType.Confused:
                    SetBlendShapeWeight("BrowUp", intensity * 70f);
                    SetBlendShapeWeight("MouthTwist", intensity * 40f);
                    break;
                case EmotionType.Excited:
                    SetBlendShapeWeight("Smile", intensity * 100f);
                    SetBlendShapeWeight("EyeWide", intensity * 80f);
                    break;
            }
        }

        private void ResetEmotionBlendShapes()
        {
            if (_faceRenderer == null) return;
            
            // 重置所有表情相关的混合形状
            string[] emotionShapes = { "Smile", "Frown", "EyeSquint", "EyeClose", "EyeWide", 
                                     "BrowUp", "BrowDown", "MouthOpen", "MouthTwist" };
            
            foreach (string shapeName in emotionShapes)
            {
                SetBlendShapeWeight(shapeName, 0f);
            }
        }

        private void SetBlendShapeWeight(string shapeName, float weight)
        {
            if (_faceRenderer == null || !_blendShapeIndices.ContainsKey(shapeName)) return;
            
            int index = _blendShapeIndices[shapeName];
            _faceRenderer.SetBlendShapeWeight(index, weight);
        }

        private LipSyncData GenerateLipSyncData(AudioClip audioClip)
        {
            // 简化的口型同步数据生成
            // 实际项目中应该使用专业的口型同步算法
            return new LipSyncData
            {
                audioClip = audioClip,
                visemeWeights = new float[audioClip.samples / 1000], // 简化采样
                timeStamps = new float[audioClip.samples / 1000],
                phonemeText = "简化音素数据"
            };
        }

        private IEnumerator LipSyncCoroutine()
        {
            if (_currentLipSyncData?.audioClip == null) yield break;
            
            float audioLength = _currentLipSyncData.audioClip.length;
            float elapsedTime = 0f;
            
            while (elapsedTime < audioLength)
            {
                UpdateLipShapeAtTime(elapsedTime);
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            // 重置口型
            ResetLipShape();
            _currentState.isSpeaking = false;
        }

        private void UpdateLipShapeAtTime(float audioTime)
        {
            if (_currentLipSyncData == null || _faceRenderer == null) return;
            
            // 简化的口型更新逻辑
            // 实际项目中应该根据音频分析结果精确控制口型
            float intensity = Mathf.Sin(audioTime * 10f) * 0.5f + 0.5f; // 简单的正弦波模拟
            SetBlendShapeWeight("MouthOpen", intensity * 50f);
            SetBlendShapeWeight("MouthSmile", intensity * 30f);
        }

        private void ResetLipShape()
        {
            SetBlendShapeWeight("MouthOpen", 0f);
            SetBlendShapeWeight("MouthSmile", 0f);
        }

        private void UpdateEyeTracking()
        {
            if (!_config.enableEyeTracking || _eyeLeftTarget == null || _eyeRightTarget == null) return;
            
            // 简化的视线跟踪实现
            Vector3 targetDirection = (_currentState.eyeTargetPosition - transform.position).normalized;
            
            if (_eyeLeftTarget != null)
            {
                _eyeLeftTarget.rotation = Quaternion.LookRotation(targetDirection);
            }
            
            if (_eyeRightTarget != null)
            {
                _eyeRightTarget.rotation = Quaternion.LookRotation(targetDirection);
            }
        }
        #endregion
    }
}