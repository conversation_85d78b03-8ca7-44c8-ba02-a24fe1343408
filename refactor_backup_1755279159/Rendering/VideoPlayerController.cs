using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Video;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 视频播放控制器
    /// 负责数字人视频模式的播放控制和状态管理
    /// </summary>
    public class VideoPlayerController : MonoBehaviour
    {
        #region 私有字段
        [SerializeField] private VideoPlayer _videoPlayer;
        [SerializeField] private RenderTexture _renderTexture;
        [SerializeField] private Dictionary<VideoState, VideoClip> _stateVideos;
        [SerializeField] private VideoState _currentState = VideoState.Idle;
        [SerializeField] private VideoState _previousState = VideoState.Idle;
        [SerializeField] private bool _isTransitioning = false;
        [SerializeField] private float _transitionDuration = 0.5f;
        
        private Coroutine _transitionCoroutine;
        private Coroutine _loopCoroutine;
        private bool _isInitialized = false;
        private Queue<VideoState> _stateQueue;
        private VideoPlayerConfig _config;
        #endregion

        #region 属性
        /// <summary>
        /// 当前视频状态
        /// </summary>
        public VideoState CurrentState => _currentState;
        
        /// <summary>
        /// 是否正在播放
        /// </summary>
        public bool IsPlaying => _videoPlayer != null && _videoPlayer.isPlaying;
        
        /// <summary>
        /// 是否正在过渡
        /// </summary>
        public bool IsTransitioning => _isTransitioning;
        
        /// <summary>
        /// 当前播放时间
        /// </summary>
        public double CurrentTime => _videoPlayer?.time ?? 0.0;
        
        /// <summary>
        /// 视频总长度
        /// </summary>
        public double Duration => _videoPlayer?.length ?? 0.0;
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        #endregion

        #region 事件
        /// <summary>
        /// 视频状态改变事件
        /// </summary>
        public event Action<VideoState, VideoState> OnStateChanged;
        
        /// <summary>
        /// 视频播放开始事件
        /// </summary>
        public event Action<VideoState> OnVideoStarted;
        
        /// <summary>
        /// 视频播放结束事件
        /// </summary>
        public event Action<VideoState> OnVideoFinished;
        
        /// <summary>
        /// 视频播放错误事件
        /// </summary>
        public event Action<string> OnVideoError;
        
        /// <summary>
        /// 过渡开始事件
        /// </summary>
        public event Action<VideoState, VideoState> OnTransitionStarted;
        
        /// <summary>
        /// 过渡完成事件
        /// </summary>
        public event Action<VideoState, VideoState> OnTransitionCompleted;
        #endregion

        #region Unity生命周期
        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            if (_config != null)
            {
                Initialize(_config);
            }
        }

        private void Update()
        {
            if (!_isInitialized) return;
            
            UpdateVideoPlayer();
        }

        private void OnDestroy()
        {
            Dispose();
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 初始化视频播放控制器
        /// </summary>
        /// <param name="config">视频播放配置</param>
        /// <returns>是否初始化成功</returns>
        public bool Initialize(VideoPlayerConfig config)
        {
            try
            {
                _config = config;
                
                // 初始化视频播放器
                InitializeVideoPlayer();
                
                // 加载状态视频
                LoadStateVideos(config.stateVideos);
                
                // 设置默认状态
                SetState(VideoState.Idle, false);
                
                _isInitialized = true;
                Debug.Log("视频播放控制器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"视频播放控制器初始化失败: {ex.Message}");
                OnVideoError?.Invoke($"初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 播放指定状态的视频
        /// </summary>
        /// <param name="state">视频状态</param>
        /// <param name="immediate">是否立即切换</param>
        public void PlayStateVideo(VideoState state, bool immediate = false)
        {
            if (!_isInitialized)
            {
                Debug.LogWarning("视频播放控制器未初始化");
                return;
            }

            if (_currentState == state && !immediate)
            {
                Debug.Log($"已经在播放状态: {state}");
                return;
            }

            try
            {
                if (immediate || !_config.enableTransitions)
                {
                    SetState(state, true);
                }
                else
                {
                    TransitionToState(state);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"播放状态视频失败: {ex.Message}");
                OnVideoError?.Invoke($"播放失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止当前视频播放
        /// </summary>
        public void Stop()
        {
            if (!_isInitialized || _videoPlayer == null) return;
            
            try
            {
                _videoPlayer.Stop();
                
                // 停止所有协程
                StopAllCoroutines();
                
                _isTransitioning = false;
                
                Debug.Log("视频播放已停止");
            }
            catch (Exception ex)
            {
                Debug.LogError($"停止视频播放失败: {ex.Message}");
                OnVideoError?.Invoke($"停止失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 暂停视频播放
        /// </summary>
        public void Pause()
        {
            if (!_isInitialized || _videoPlayer == null) return;
            
            try
            {
                _videoPlayer.Pause();
                Debug.Log("视频播放已暂停");
            }
            catch (Exception ex)
            {
                Debug.LogError($"暂停视频播放失败: {ex.Message}");
                OnVideoError?.Invoke($"暂停失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复视频播放
        /// </summary>
        public void Resume()
        {
            if (!_isInitialized || _videoPlayer == null) return;
            
            try
            {
                _videoPlayer.Play();
                Debug.Log("视频播放已恢复");
            }
            catch (Exception ex)
            {
                Debug.LogError($"恢复视频播放失败: {ex.Message}");
                OnVideoError?.Invoke($"恢复失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置播放速度
        /// </summary>
        /// <param name="speed">播放速度倍率</param>
        public void SetPlaybackSpeed(float speed)
        {
            if (!_isInitialized || _videoPlayer == null) return;
            
            try
            {
                _videoPlayer.playbackSpeed = Mathf.Clamp(speed, 0.1f, 3.0f);
                Debug.Log($"视频播放速度设置为: {speed}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置播放速度失败: {ex.Message}");
                OnVideoError?.Invoke($"设置速度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置音量
        /// </summary>
        /// <param name="volume">音量(0-1)</param>
        public void SetVolume(float volume)
        {
            if (!_isInitialized || _videoPlayer == null) return;
            
            try
            {
                _videoPlayer.SetDirectAudioVolume(0, Mathf.Clamp01(volume));
                Debug.Log($"视频音量设置为: {volume}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置音量失败: {ex.Message}");
                OnVideoError?.Invoke($"设置音量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 跳转到指定时间
        /// </summary>
        /// <param name="time">目标时间(秒)</param>
        public void SeekTo(double time)
        {
            if (!_isInitialized || _videoPlayer == null) return;
            
            try
            {
                _videoPlayer.time = Mathf.Clamp((float)time, 0f, (float)Duration);
                Debug.Log($"视频跳转到时间: {time}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"视频跳转失败: {ex.Message}");
                OnVideoError?.Invoke($"跳转失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加状态视频到队列
        /// </summary>
        /// <param name="state">视频状态</param>
        public void QueueState(VideoState state)
        {
            if (!_isInitialized) return;
            
            _stateQueue.Enqueue(state);
            Debug.Log($"状态 {state} 已添加到队列");
        }

        /// <summary>
        /// 清空状态队列
        /// </summary>
        public void ClearQueue()
        {
            _stateQueue?.Clear();
            Debug.Log("状态队列已清空");
        }

        /// <summary>
        /// 获取当前渲染纹理
        /// </summary>
        /// <returns>渲染纹理</returns>
        public RenderTexture GetRenderTexture()
        {
            return _renderTexture;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 停止所有协程
                StopAllCoroutines();
                
                // 停止视频播放
                if (_videoPlayer != null)
                {
                    _videoPlayer.Stop();
                }
                
                // 清理资源
                _stateVideos?.Clear();
                _stateQueue?.Clear();
                
                if (_renderTexture != null)
                {
                    _renderTexture.Release();
                }
                
                _isInitialized = false;
                Debug.Log("视频播放控制器资源已释放");
            }
            catch (Exception ex)
            {
                Debug.LogError($"释放视频播放控制器资源失败: {ex.Message}");
            }
        }
        #endregion

        #region 私有方法
        private void InitializeComponents()
        {
            _stateVideos = new Dictionary<VideoState, VideoClip>();
            _stateQueue = new Queue<VideoState>();
            
            // 创建视频播放器组件
            if (_videoPlayer == null)
            {
                _videoPlayer = gameObject.AddComponent<VideoPlayer>();
            }
            
            // 创建渲染纹理
            if (_renderTexture == null)
            {
                _renderTexture = new RenderTexture(1920, 1080, 0);
                _renderTexture.Create();
            }
        }

        private void InitializeVideoPlayer()
        {
            if (_videoPlayer == null) return;
            
            // 配置视频播放器
            _videoPlayer.renderMode = VideoRenderMode.RenderTexture;
            _videoPlayer.targetTexture = _renderTexture;
            _videoPlayer.audioOutputMode = VideoAudioOutputMode.Direct;
            _videoPlayer.playOnAwake = false;
            _videoPlayer.isLooping = false;
            _videoPlayer.skipOnDrop = true;
            
            // 订阅事件
            _videoPlayer.prepareCompleted += OnVideoPrepared;
            _videoPlayer.started += OnVideoStarted;
            _videoPlayer.loopPointReached += OnVideoLoopPointReached;
            _videoPlayer.errorReceived += OnVideoErrorReceived;
        }

        private void LoadStateVideos(Dictionary<VideoState, VideoClip> stateVideos)
        {
            if (stateVideos == null) return;
            
            _stateVideos.Clear();
            
            foreach (var kvp in stateVideos)
            {
                if (kvp.Value != null)
                {
                    _stateVideos[kvp.Key] = kvp.Value;
                    Debug.Log($"加载状态视频: {kvp.Key} -> {kvp.Value.name}");
                }
            }
        }

        private void SetState(VideoState newState, bool playImmediately = true)
        {
            if (_currentState == newState && !playImmediately) return;
            
            VideoState oldState = _currentState;
            _previousState = _currentState;
            _currentState = newState;
            
            OnStateChanged?.Invoke(oldState, newState);
            
            if (playImmediately)
            {
                PlayCurrentStateVideo();
            }
            
            Debug.Log($"视频状态从 {oldState} 切换到 {newState}");
        }

        private void PlayCurrentStateVideo()
        {
            if (!_stateVideos.ContainsKey(_currentState))
            {
                Debug.LogWarning($"未找到状态 {_currentState} 对应的视频");
                return;
            }
            
            VideoClip clip = _stateVideos[_currentState];
            if (clip == null)
            {
                Debug.LogWarning($"状态 {_currentState} 的视频片段为空");
                return;
            }
            
            _videoPlayer.clip = clip;
            _videoPlayer.Prepare();
        }

        private void TransitionToState(VideoState newState)
        {
            if (_isTransitioning)
            {
                Debug.Log("正在过渡中，添加到队列");
                QueueState(newState);
                return;
            }
            
            if (_transitionCoroutine != null)
            {
                StopCoroutine(_transitionCoroutine);
            }
            
            _transitionCoroutine = StartCoroutine(TransitionCoroutine(newState));
        }

        private IEnumerator TransitionCoroutine(VideoState newState)
        {
            _isTransitioning = true;
            VideoState oldState = _currentState;
            
            OnTransitionStarted?.Invoke(oldState, newState);
            
            // 淡出当前视频
            yield return StartCoroutine(FadeOut(_transitionDuration * 0.5f));
            
            // 切换到新状态
            SetState(newState, true);
            
            // 等待新视频准备完成
            yield return new WaitUntil(() => _videoPlayer.isPrepared);
            
            // 淡入新视频
            yield return StartCoroutine(FadeIn(_transitionDuration * 0.5f));
            
            _isTransitioning = false;
            OnTransitionCompleted?.Invoke(oldState, newState);
            
            // 处理队列中的下一个状态
            ProcessNextQueuedState();
        }

        private IEnumerator FadeOut(float duration)
        {
            float elapsedTime = 0f;
            float startVolume = _videoPlayer.GetDirectAudioVolume(0);
            
            while (elapsedTime < duration)
            {
                float alpha = 1f - (elapsedTime / duration);
                _videoPlayer.SetDirectAudioVolume(0, startVolume * alpha);
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            _videoPlayer.SetDirectAudioVolume(0, 0f);
        }

        private IEnumerator FadeIn(float duration)
        {
            float elapsedTime = 0f;
            float targetVolume = _config?.defaultVolume ?? 1f;
            
            _videoPlayer.SetDirectAudioVolume(0, 0f);
            
            while (elapsedTime < duration)
            {
                float alpha = elapsedTime / duration;
                _videoPlayer.SetDirectAudioVolume(0, targetVolume * alpha);
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            _videoPlayer.SetDirectAudioVolume(0, targetVolume);
        }

        private void ProcessNextQueuedState()
        {
            if (_stateQueue.Count > 0)
            {
                VideoState nextState = _stateQueue.Dequeue();
                TransitionToState(nextState);
            }
        }

        private void UpdateVideoPlayer()
        {
            // 检查是否需要循环播放
            if (_videoPlayer.isPlaying && IsLoopingState(_currentState))
            {
                // 循环状态的处理逻辑
                HandleLoopingState();
            }
        }

        private bool IsLoopingState(VideoState state)
        {
            return state == VideoState.Idle || 
                   state == VideoState.Listening || 
                   state == VideoState.Speaking ||
                   state == VideoState.Dancing;
        }

        private void HandleLoopingState()
        {
            // 简化的循环处理逻辑
            // 实际项目中可能需要更复杂的循环控制
            if (_videoPlayer.time >= _videoPlayer.length - 0.1f)
            {
                _videoPlayer.time = 0f;
            }
        }

        private void HandleVideoTransition(VideoState from, VideoState to)
        {
            // 处理特定状态间的过渡逻辑
            switch (from)
            {
                case VideoState.Idle:
                    HandleIdleTransition(to);
                    break;
                case VideoState.Speaking:
                    HandleSpeakingTransition(to);
                    break;
                case VideoState.Greeting:
                    HandleGreetingTransition(to);
                    break;
                default:
                    // 默认过渡处理
                    break;
            }
        }

        private void HandleIdleTransition(VideoState to)
        {
            // 从待机状态的过渡处理
            Debug.Log($"从待机状态过渡到: {to}");
        }

        private void HandleSpeakingTransition(VideoState to)
        {
            // 从说话状态的过渡处理
            Debug.Log($"从说话状态过渡到: {to}");
        }

        private void HandleGreetingTransition(VideoState to)
        {
            // 从打招呼状态的过渡处理
            Debug.Log($"从打招呼状态过渡到: {to}");
        }
        #endregion

        #region 视频播放器事件处理
        private void OnVideoPrepared(VideoPlayer vp)
        {
            Debug.Log($"视频准备完成: {_currentState}");
            vp.Play();
        }

        private void OnVideoStarted(VideoPlayer vp)
        {
            Debug.Log($"视频开始播放: {_currentState}");
            OnVideoStarted?.Invoke(_currentState);
        }

        private void OnVideoLoopPointReached(VideoPlayer vp)
        {
            Debug.Log($"视频播放完成: {_currentState}");
            OnVideoFinished?.Invoke(_currentState);
            
            // 处理非循环状态的完成逻辑
            if (!IsLoopingState(_currentState))
            {
                // 返回到待机状态
                PlayStateVideo(VideoState.Idle);
            }
        }

        private void OnVideoErrorReceived(VideoPlayer vp, string message)
        {
            Debug.LogError($"视频播放错误: {message}");
            OnVideoError?.Invoke(message);
        }
        #endregion
    }

    /// <summary>
    /// 视频状态枚举
    /// </summary>
    public enum VideoState
    {
        Idle,           // 待机
        Speaking,       // 说话
        Listening,      // 倾听
        Greeting,       // 打招呼
        Gesture,        // 手势
        Dancing,        // 跳舞
        Thinking,       // 思考
        Excited,        // 兴奋
        Sad,           // 悲伤
        Angry,         // 愤怒
        Surprised      // 惊讶
    }

    /// <summary>
    /// 视频播放器配置
    /// </summary>
    [Serializable]
    public class VideoPlayerConfig
    {
        [Header("基础配置")]
        public bool enableTransitions = true;
        public float transitionDuration = 0.5f;
        public float defaultVolume = 1f;
        public bool autoReturnToIdle = true;
        public float idleReturnDelay = 3f;
        
        [Header("渲染配置")]
        public int renderTextureWidth = 1920;
        public int renderTextureHeight = 1080;
        public RenderTextureFormat textureFormat = RenderTextureFormat.ARGB32;
        
        [Header("状态视频")]
        public Dictionary<VideoState, VideoClip> stateVideos;
        
        [Header("性能配置")]
        public bool skipFramesOnDrop = true;
        public VideoAspectRatio aspectRatio = VideoAspectRatio.FitWithinAndCrop;
    }
}