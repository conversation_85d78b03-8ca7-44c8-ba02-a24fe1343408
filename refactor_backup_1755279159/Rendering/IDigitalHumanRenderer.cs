using System;
using UnityEngine;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 数字人渲染器接口
    /// 定义数字人渲染的核心功能
    /// </summary>
    public interface IDigitalHumanRenderer
    {
        #region 属性
        /// <summary>
        /// 当前渲染模式
        /// </summary>
        RenderMode CurrentRenderMode { get; }
        
        /// <summary>
        /// 当前数字人状态
        /// </summary>
        DigitalHumanState CurrentState { get; }
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 渲染性能数据
        /// </summary>
        RenderingPerformance Performance { get; }
        #endregion

        #region 事件
        /// <summary>
        /// 渲染模式改变事件
        /// </summary>
        event Action<RenderMode> OnRenderModeChanged;
        
        /// <summary>
        /// 动画状态改变事件
        /// </summary>
        event Action<AnimationType> OnAnimationChanged;
        
        /// <summary>
        /// 情感状态改变事件
        /// </summary>
        event Action<EmotionType> OnEmotionChanged;
        
        /// <summary>
        /// 渲染错误事件
        /// </summary>
        event Action<string> OnRenderingError;
        #endregion

        #region 初始化和配置
        /// <summary>
        /// 初始化渲染器
        /// </summary>
        /// <param name="config">数字人配置</param>
        /// <returns>是否初始化成功</returns>
        bool Initialize(DigitalHumanConfig config);
        
        /// <summary>
        /// 设置渲染模式
        /// </summary>
        /// <param name="mode">渲染模式</param>
        void SetRenderMode(RenderMode mode);
        
        /// <summary>
        /// 加载数字人角色
        /// </summary>
        /// <param name="characterId">角色ID</param>
        /// <returns>是否加载成功</returns>
        bool LoadDigitalHuman(string characterId);
        
        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();
        #endregion

        #region 动画控制
        /// <summary>
        /// 播放动画
        /// </summary>
        /// <param name="animationType">动画类型</param>
        /// <param name="blendTime">混合时间</param>
        void PlayAnimation(AnimationType animationType, float blendTime = 0.3f);
        
        /// <summary>
        /// 停止当前动画
        /// </summary>
        void StopAnimation();
        
        /// <summary>
        /// 设置动画速度
        /// </summary>
        /// <param name="speed">动画速度倍率</param>
        void SetAnimationSpeed(float speed);
        
        /// <summary>
        /// 检查动画是否正在播放
        /// </summary>
        /// <param name="animationType">动画类型</param>
        /// <returns>是否正在播放</returns>
        bool IsAnimationPlaying(AnimationType animationType);
        #endregion

        #region 表情控制
        /// <summary>
        /// 设置情感表情
        /// </summary>
        /// <param name="emotion">情感类型</param>
        /// <param name="intensity">强度(0-1)</param>
        /// <param name="duration">持续时间</param>
        void SetEmotion(EmotionType emotion, float intensity = 1.0f, float duration = 2.0f);
        
        /// <summary>
        /// 重置表情到中性状态
        /// </summary>
        void ResetEmotion();
        
        /// <summary>
        /// 设置自定义表情混合
        /// </summary>
        /// <param name="emotionBlends">表情混合权重</param>
        void SetCustomEmotionBlend(float[] emotionBlends);
        #endregion

        #region 口型同步
        /// <summary>
        /// 开始口型同步
        /// </summary>
        /// <param name="audioClip">音频片段</param>
        void StartLipSync(AudioClip audioClip);
        
        /// <summary>
        /// 停止口型同步
        /// </summary>
        void StopLipSync();
        
        /// <summary>
        /// 设置口型同步数据
        /// </summary>
        /// <param name="lipSyncData">口型同步数据</param>
        void SetLipSyncData(LipSyncData lipSyncData);
        
        /// <summary>
        /// 更新口型同步（每帧调用）
        /// </summary>
        /// <param name="audioTime">音频播放时间</param>
        void UpdateLipSync(float audioTime);
        #endregion

        #region 视线控制
        /// <summary>
        /// 设置视线目标
        /// </summary>
        /// <param name="targetPosition">目标位置</param>
        void SetEyeTarget(Vector3 targetPosition);
        
        /// <summary>
        /// 启用/禁用视线跟踪
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetEyeTrackingEnabled(bool enabled);
        
        /// <summary>
        /// 重置视线到默认位置
        /// </summary>
        void ResetEyeTarget();
        #endregion

        #region 性能和调试
        /// <summary>
        /// 更新性能统计
        /// </summary>
        void UpdatePerformanceStats();
        
        /// <summary>
        /// 获取调试信息
        /// </summary>
        /// <returns>调试信息字符串</returns>
        string GetDebugInfo();
        
        /// <summary>
        /// 设置渲染质量
        /// </summary>
        /// <param name="quality">质量等级(0-2)</param>
        void SetRenderQuality(int quality);
        #endregion
    }
}