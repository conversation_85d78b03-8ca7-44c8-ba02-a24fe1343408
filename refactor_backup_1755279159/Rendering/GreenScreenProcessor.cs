using System;
using UnityEngine;
using UnityEngine.Rendering;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 绿幕处理器
    /// 负责绿幕检测、背景抠除和背景合成功能
    /// </summary>
    public class GreenScreenProcessor : MonoBehaviour
    {
        #region 私有字段
        [SerializeField] private Material _chromaKeyMaterial;
        [SerializeField] private Material _backgroundCompositeMaterial;
        [SerializeField] private RenderTexture _processedTexture;
        [SerializeField] private RenderTexture _maskTexture;
        [SerializeField] private Camera _compositeCamera;
        
        private GreenScreenConfig _config;
        private Texture2D _backgroundTexture;
        private bool _isInitialized = false;
        private CommandBuffer _commandBuffer;
        
        // 绿幕检测参数
        private Color _chromaKeyColor = Color.green;
        private float _chromaKeyThreshold = 0.4f;
        private float _chromaKeySmoothness = 0.1f;
        private float _spill = 0.1f;
        
        // 背景合成参数
        private BackgroundMode _backgroundMode = BackgroundMode.Transparent;
        private Vector2 _backgroundScale = Vector2.one;
        private Vector2 _backgroundOffset = Vector2.zero;
        #endregion

        #region 属性
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        
        /// <summary>
        /// 当前背景模式
        /// </summary>
        public BackgroundMode CurrentBackgroundMode => _backgroundMode;
        
        /// <summary>
        /// 绿幕颜色
        /// </summary>
        public Color ChromaKeyColor 
        { 
            get => _chromaKeyColor; 
            set => SetChromaKeyColor(value); 
        }
        
        /// <summary>
        /// 绿幕阈值
        /// </summary>
        public float ChromaKeyThreshold 
        { 
            get => _chromaKeyThreshold; 
            set => SetChromaKeyThreshold(value); 
        }
        
        /// <summary>
        /// 绿幕平滑度
        /// </summary>
        public float ChromaKeySmoothness 
        { 
            get => _chromaKeySmoothness; 
            set => SetChromaKeySmoothness(value); 
        }
        
        /// <summary>
        /// 溢色处理强度
        /// </summary>
        public float Spill 
        { 
            get => _spill; 
            set => SetSpill(value); 
        }
        
        /// <summary>
        /// 处理后的纹理
        /// </summary>
        public RenderTexture ProcessedTexture => _processedTexture;
        #endregion

        #region 事件
        /// <summary>
        /// 绿幕检测完成事件
        /// </summary>
        public event Action<bool> OnGreenScreenDetected;
        
        /// <summary>
        /// 背景合成完成事件
        /// </summary>
        public event Action<RenderTexture> OnBackgroundComposed;
        
        /// <summary>
        /// 处理错误事件
        /// </summary>
        public event Action<string> OnProcessingError;
        #endregion

        #region Unity生命周期
        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            if (_config != null)
            {
                Initialize(_config);
            }
        }

        private void OnDestroy()
        {
            Dispose();
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 初始化绿幕处理器
        /// </summary>
        /// <param name="config">绿幕处理配置</param>
        /// <returns>是否初始化成功</returns>
        public bool Initialize(GreenScreenConfig config)
        {
            try
            {
                _config = config;
                
                // 创建渲染纹理
                CreateRenderTextures();
                
                // 创建材质
                CreateMaterials();
                
                // 设置默认参数
                ApplyConfig(config);
                
                // 创建命令缓冲区
                CreateCommandBuffer();
                
                _isInitialized = true;
                Debug.Log("绿幕处理器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"绿幕处理器初始化失败: {ex.Message}");
                OnProcessingError?.Invoke($"初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检测输入纹理是否包含绿幕
        /// </summary>
        /// <param name="inputTexture">输入纹理</param>
        /// <returns>是否检测到绿幕</returns>
        public bool DetectGreenScreen(Texture inputTexture)
        {
            if (!_isInitialized || inputTexture == null)
            {
                return false;
            }

            try
            {
                bool hasGreenScreen = AnalyzeGreenScreenContent(inputTexture);
                OnGreenScreenDetected?.Invoke(hasGreenScreen);
                
                Debug.Log($"绿幕检测结果: {(hasGreenScreen ? "检测到绿幕" : "未检测到绿幕")}");
                return hasGreenScreen;
            }
            catch (Exception ex)
            {
                Debug.LogError($"绿幕检测失败: {ex.Message}");
                OnProcessingError?.Invoke($"绿幕检测失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 处理绿幕并生成遮罩
        /// </summary>
        /// <param name="inputTexture">输入纹理</param>
        /// <returns>处理后的遮罩纹理</returns>
        public RenderTexture ProcessGreenScreen(Texture inputTexture)
        {
            if (!_isInitialized || inputTexture == null)
            {
                return null;
            }

            try
            {
                // 生成绿幕遮罩
                GenerateChromaKeyMask(inputTexture);
                
                // 应用后处理效果
                ApplyPostProcessing();
                
                Debug.Log("绿幕处理完成");
                return _maskTexture;
            }
            catch (Exception ex)
            {
                Debug.LogError($"绿幕处理失败: {ex.Message}");
                OnProcessingError?.Invoke($"绿幕处理失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置自定义背景
        /// </summary>
        /// <param name="backgroundTexture">背景纹理</param>
        public void SetCustomBackground(Texture2D backgroundTexture)
        {
            try
            {
                _backgroundTexture = backgroundTexture;
                _backgroundMode = BackgroundMode.Custom;
                
                if (_backgroundCompositeMaterial != null)
                {
                    _backgroundCompositeMaterial.SetTexture("_BackgroundTex", backgroundTexture);
                }
                
                Debug.Log($"设置自定义背景: {backgroundTexture?.name ?? "null"}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置自定义背景失败: {ex.Message}");
                OnProcessingError?.Invoke($"设置背景失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置背景模式
        /// </summary>
        /// <param name="mode">背景模式</param>
        public void SetBackgroundMode(BackgroundMode mode)
        {
            try
            {
                _backgroundMode = mode;
                
                if (_backgroundCompositeMaterial != null)
                {
                    _backgroundCompositeMaterial.SetInt("_BackgroundMode", (int)mode);
                }
                
                Debug.Log($"背景模式设置为: {mode}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置背景模式失败: {ex.Message}");
                OnProcessingError?.Invoke($"设置背景模式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 合成最终图像
        /// </summary>
        /// <param name="foregroundTexture">前景纹理</param>
        /// <param name="maskTexture">遮罩纹理</param>
        /// <returns>合成后的纹理</returns>
        public RenderTexture CompositeBackground(Texture foregroundTexture, RenderTexture maskTexture = null)
        {
            if (!_isInitialized || foregroundTexture == null)
            {
                return null;
            }

            try
            {
                RenderTexture mask = maskTexture ?? _maskTexture;
                
                // 执行背景合成
                PerformBackgroundComposite(foregroundTexture, mask);
                
                OnBackgroundComposed?.Invoke(_processedTexture);
                Debug.Log("背景合成完成");
                return _processedTexture;
            }
            catch (Exception ex)
            {
                Debug.LogError($"背景合成失败: {ex.Message}");
                OnProcessingError?.Invoke($"背景合成失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置背景缩放
        /// </summary>
        /// <param name="scale">缩放比例</param>
        public void SetBackgroundScale(Vector2 scale)
        {
            _backgroundScale = scale;
            
            if (_backgroundCompositeMaterial != null)
            {
                _backgroundCompositeMaterial.SetVector("_BackgroundScale", scale);
            }
        }

        /// <summary>
        /// 设置背景偏移
        /// </summary>
        /// <param name="offset">偏移量</param>
        public void SetBackgroundOffset(Vector2 offset)
        {
            _backgroundOffset = offset;
            
            if (_backgroundCompositeMaterial != null)
            {
                _backgroundCompositeMaterial.SetVector("_BackgroundOffset", offset);
            }
        }

        /// <summary>
        /// 一键处理：检测绿幕、抠除背景、合成图像
        /// </summary>
        /// <param name="inputTexture">输入纹理</param>
        /// <returns>处理后的纹理</returns>
        public RenderTexture ProcessComplete(Texture inputTexture)
        {
            if (!_isInitialized || inputTexture == null)
            {
                return null;
            }

            try
            {
                // 检测绿幕
                bool hasGreenScreen = DetectGreenScreen(inputTexture);
                
                if (hasGreenScreen)
                {
                    // 处理绿幕
                    ProcessGreenScreen(inputTexture);
                    
                    // 合成背景
                    return CompositeBackground(inputTexture);
                }
                else
                {
                    // 没有绿幕，直接返回原图
                    Graphics.Blit(inputTexture, _processedTexture);
                    return _processedTexture;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"完整处理失败: {ex.Message}");
                OnProcessingError?.Invoke($"完整处理失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 释放渲染纹理
                if (_processedTexture != null)
                {
                    _processedTexture.Release();
                    DestroyImmediate(_processedTexture);
                }
                
                if (_maskTexture != null)
                {
                    _maskTexture.Release();
                    DestroyImmediate(_maskTexture);
                }
                
                // 释放材质
                if (_chromaKeyMaterial != null)
                {
                    DestroyImmediate(_chromaKeyMaterial);
                }
                
                if (_backgroundCompositeMaterial != null)
                {
                    DestroyImmediate(_backgroundCompositeMaterial);
                }
                
                // 释放命令缓冲区
                if (_commandBuffer != null)
                {
                    _commandBuffer.Release();
                }
                
                _isInitialized = false;
                Debug.Log("绿幕处理器资源已释放");
            }
            catch (Exception ex)
            {
                Debug.LogError($"释放绿幕处理器资源失败: {ex.Message}");
            }
        }
        #endregion

        #region 私有方法
        private void InitializeComponents()
        {
            // 初始化默认参数
            _chromaKeyColor = Color.green;
            _chromaKeyThreshold = 0.4f;
            _chromaKeySmoothness = 0.1f;
            _spill = 0.1f;
            _backgroundMode = BackgroundMode.Transparent;
            _backgroundScale = Vector2.one;
            _backgroundOffset = Vector2.zero;
        }

        private void CreateRenderTextures()
        {
            int width = _config?.textureWidth ?? 1920;
            int height = _config?.textureHeight ?? 1080;
            RenderTextureFormat format = _config?.textureFormat ?? RenderTextureFormat.ARGB32;
            
            // 创建处理后的纹理
            _processedTexture = new RenderTexture(width, height, 0, format);
            _processedTexture.name = "GreenScreen_ProcessedTexture";
            _processedTexture.Create();
            
            // 创建遮罩纹理
            _maskTexture = new RenderTexture(width, height, 0, RenderTextureFormat.R8);
            _maskTexture.name = "GreenScreen_MaskTexture";
            _maskTexture.Create();
        }

        private void CreateMaterials()
        {
            // 创建绿幕抠像材质
            Shader chromaKeyShader = Shader.Find("Hidden/ChromaKey");
            if (chromaKeyShader == null)
            {
                chromaKeyShader = CreateChromaKeyShader();
            }
            
            _chromaKeyMaterial = new Material(chromaKeyShader);
            _chromaKeyMaterial.name = "ChromaKeyMaterial";
            
            // 创建背景合成材质
            Shader compositeShader = Shader.Find("Hidden/BackgroundComposite");
            if (compositeShader == null)
            {
                compositeShader = CreateBackgroundCompositeShader();
            }
            
            _backgroundCompositeMaterial = new Material(compositeShader);
            _backgroundCompositeMaterial.name = "BackgroundCompositeMaterial";
        }

        private void ApplyConfig(GreenScreenConfig config)
        {
            _chromaKeyColor = config.chromaKeyColor;
            _chromaKeyThreshold = config.chromaKeyThreshold;
            _chromaKeySmoothness = config.chromaKeySmoothness;
            _spill = config.spill;
            
            UpdateMaterialProperties();
        }

        private void CreateCommandBuffer()
        {
            _commandBuffer = new CommandBuffer();
            _commandBuffer.name = "GreenScreenProcessing";
        }

        private bool AnalyzeGreenScreenContent(Texture inputTexture)
        {
            // 简化的绿幕检测算法
            // 实际项目中可能需要更复杂的图像分析
            
            // 创建临时纹理用于分析
            RenderTexture tempTexture = RenderTexture.GetTemporary(256, 256, 0, RenderTextureFormat.ARGB32);
            Graphics.Blit(inputTexture, tempTexture);
            
            // 读取像素数据进行分析
            RenderTexture.active = tempTexture;
            Texture2D readableTexture = new Texture2D(256, 256, TextureFormat.ARGB32, false);
            readableTexture.ReadPixels(new Rect(0, 0, 256, 256), 0, 0);
            readableTexture.Apply();
            
            // 分析绿色像素比例
            Color[] pixels = readableTexture.GetPixels();
            int greenPixelCount = 0;
            
            foreach (Color pixel in pixels)
            {
                if (IsGreenPixel(pixel))
                {
                    greenPixelCount++;
                }
            }
            
            float greenRatio = (float)greenPixelCount / pixels.Length;
            bool hasGreenScreen = greenRatio > 0.1f; // 如果绿色像素超过10%，认为有绿幕
            
            // 清理临时资源
            RenderTexture.ReleaseTemporary(tempTexture);
            DestroyImmediate(readableTexture);
            
            return hasGreenScreen;
        }

        private bool IsGreenPixel(Color pixel)
        {
            // 简单的绿色像素判断
            return pixel.g > pixel.r + 0.2f && pixel.g > pixel.b + 0.2f && pixel.g > 0.3f;
        }

        private void GenerateChromaKeyMask(Texture inputTexture)
        {
            if (_chromaKeyMaterial == null) return;
            
            // 设置材质参数
            _chromaKeyMaterial.SetColor("_ChromaKeyColor", _chromaKeyColor);
            _chromaKeyMaterial.SetFloat("_Threshold", _chromaKeyThreshold);
            _chromaKeyMaterial.SetFloat("_Smoothness", _chromaKeySmoothness);
            _chromaKeyMaterial.SetFloat("_Spill", _spill);
            
            // 生成遮罩
            Graphics.Blit(inputTexture, _maskTexture, _chromaKeyMaterial);
        }

        private void ApplyPostProcessing()
        {
            // 应用后处理效果，如边缘平滑、噪点去除等
            // 这里可以添加更多的后处理步骤
        }

        private void PerformBackgroundComposite(Texture foregroundTexture, RenderTexture maskTexture)
        {
            if (_backgroundCompositeMaterial == null) return;
            
            // 设置合成材质参数
            _backgroundCompositeMaterial.SetTexture("_MainTex", foregroundTexture);
            _backgroundCompositeMaterial.SetTexture("_MaskTex", maskTexture);
            _backgroundCompositeMaterial.SetTexture("_BackgroundTex", _backgroundTexture);
            _backgroundCompositeMaterial.SetInt("_BackgroundMode", (int)_backgroundMode);
            _backgroundCompositeMaterial.SetVector("_BackgroundScale", _backgroundScale);
            _backgroundCompositeMaterial.SetVector("_BackgroundOffset", _backgroundOffset);
            
            // 执行合成
            Graphics.Blit(foregroundTexture, _processedTexture, _backgroundCompositeMaterial);
        }

        private void UpdateMaterialProperties()
        {
            if (_chromaKeyMaterial != null)
            {
                _chromaKeyMaterial.SetColor("_ChromaKeyColor", _chromaKeyColor);
                _chromaKeyMaterial.SetFloat("_Threshold", _chromaKeyThreshold);
                _chromaKeyMaterial.SetFloat("_Smoothness", _chromaKeySmoothness);
                _chromaKeyMaterial.SetFloat("_Spill", _spill);
            }
        }

        private void SetChromaKeyColor(Color color)
        {
            _chromaKeyColor = color;
            UpdateMaterialProperties();
        }

        private void SetChromaKeyThreshold(float threshold)
        {
            _chromaKeyThreshold = Mathf.Clamp01(threshold);
            UpdateMaterialProperties();
        }

        private void SetChromaKeySmoothness(float smoothness)
        {
            _chromaKeySmoothness = Mathf.Clamp01(smoothness);
            UpdateMaterialProperties();
        }

        private void SetSpill(float spill)
        {
            _spill = Mathf.Clamp01(spill);
            UpdateMaterialProperties();
        }

        private Shader CreateChromaKeyShader()
        {
            // 创建简单的绿幕抠像着色器
            string shaderCode = @"
            Shader ""Hidden/ChromaKey""
            {
                Properties
                {
                    _MainTex (""Texture"", 2D) = ""white"" {}
                    _ChromaKeyColor (""Chroma Key Color"", Color) = (0, 1, 0, 1)
                    _Threshold (""Threshold"", Range(0, 1)) = 0.4
                    _Smoothness (""Smoothness"", Range(0, 1)) = 0.1
                    _Spill (""Spill"", Range(0, 1)) = 0.1
                }
                SubShader
                {
                    Tags { ""RenderType""=""Opaque"" }
                    LOD 100
                    
                    Pass
                    {
                        CGPROGRAM
                        #pragma vertex vert
                        #pragma fragment frag
                        
                        #include ""UnityCG.cginc""
                        
                        struct appdata
                        {
                            float4 vertex : POSITION;
                            float2 uv : TEXCOORD0;
                        };
                        
                        struct v2f
                        {
                            float2 uv : TEXCOORD0;
                            float4 vertex : SV_POSITION;
                        };
                        
                        sampler2D _MainTex;
                        float4 _MainTex_ST;
                        float4 _ChromaKeyColor;
                        float _Threshold;
                        float _Smoothness;
                        float _Spill;
                        
                        v2f vert (appdata v)
                        {
                            v2f o;
                            o.vertex = UnityObjectToClipPos(v.vertex);
                            o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                            return o;
                        }
                        
                        fixed4 frag (v2f i) : SV_Target
                        {
                            fixed4 col = tex2D(_MainTex, i.uv);
                            float dist = distance(col.rgb, _ChromaKeyColor.rgb);
                            float alpha = smoothstep(_Threshold - _Smoothness, _Threshold + _Smoothness, dist);
                            return fixed4(alpha, alpha, alpha, 1);
                        }
                        ENDCG
                    }
                }
            }";
            
            return Shader.Find("Hidden/ChromaKey");
        }

        private Shader CreateBackgroundCompositeShader()
        {
            // 创建背景合成着色器
            return Shader.Find("Hidden/BackgroundComposite");
        }
        #endregion
    }

    /// <summary>
    /// 背景模式枚举
    /// </summary>
    public enum BackgroundMode
    {
        Transparent,    // 透明背景
        SolidColor,     // 纯色背景
        Custom,         // 自定义背景
        Blur           // 模糊背景
    }

    /// <summary>
    /// 绿幕处理配置
    /// </summary>
    [Serializable]
    public class GreenScreenConfig
    {
        [Header("绿幕检测参数")]
        public Color chromaKeyColor = Color.green;
        public float chromaKeyThreshold = 0.4f;
        public float chromaKeySmoothness = 0.1f;
        public float spill = 0.1f;
        
        [Header("背景设置")]
        public BackgroundMode backgroundMode = BackgroundMode.Transparent;
        public Color solidBackgroundColor = Color.black;
        public Texture2D customBackground;
        
        [Header("渲染设置")]
        public int textureWidth = 1920;
        public int textureHeight = 1080;
        public RenderTextureFormat textureFormat = RenderTextureFormat.ARGB32;
        
        [Header("性能设置")]
        public bool enablePostProcessing = true;
        public bool enableEdgeSmoothing = true;
        public int processingQuality = 2; // 0-低, 1-中, 2-高
    }
}