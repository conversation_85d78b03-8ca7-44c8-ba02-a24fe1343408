using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DigitalHuman.Core.Network;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 情感响应控制器实现
    /// 根据情感分析结果控制数字人的表情和动画
    /// </summary>
    public class EmotionResponseController : MonoBehaviour, IEmotionResponseController
    {
        [Header("配置")]
        [SerializeField] private EmotionResponseConfig config = new EmotionResponseConfig();
        [SerializeField] private bool enableLogging = true;
        
        // 私有字段
        private bool isInitialized = false;
        private bool isEnabled = true;
        private IDigitalHumanRenderer digitalHumanRenderer;
        private Models.EmotionType currentEmotion = Models.EmotionType.Neutral;
        private Models.EmotionType previousEmotion = Models.EmotionType.Neutral;
        private EmotionResponseState responseState = EmotionResponseState.Idle;
        private float currentEmotionIntensity = 0f;
        private float emotionStartTime = 0f;
        private Coroutine emotionCoroutine;
        private Coroutine neutralReturnCoroutine;
        private Dictionary<Models.EmotionType, EmotionAnimationMapping> emotionMappingDict;
        
        // 公共属性
        public bool IsInitialized => isInitialized;
        public bool IsEnabled 
        { 
            get => isEnabled && config.enabled; 
            set => isEnabled = value; 
        }
        public Models.EmotionType CurrentEmotion => currentEmotion;
        public EmotionResponseConfig Configuration => config;
        
        // 事件
        public event Action<Models.EmotionType, Models.EmotionType> OnEmotionChanged;
        public event Action<string> OnAnimationStarted;
        public event Action<string> OnAnimationCompleted;
        
        void Start()
        {
            // 如果没有手动初始化，尝试自动初始化
            if (!isInitialized)
            {
                var renderer = FindObjectOfType<DigitalHumanRenderer>();
                if (renderer != null)
                {
                    Initialize(renderer);
                }
            }
        }
        
        void OnDestroy()
        {
            Cleanup();
        }
        
        public bool Initialize(IDigitalHumanRenderer digitalHumanRenderer, EmotionResponseConfig config = null)
        {
            try
            {
                if (digitalHumanRenderer == null)
                {
                    LogError("数字人渲染器不能为空");
                    return false;
                }
                
                this.digitalHumanRenderer = digitalHumanRenderer;
                
                if (config != null)
                {
                    this.config = config;
                }
                
                // 构建情感映射字典
                BuildEmotionMappingDictionary();
                
                // 重置状态
                currentEmotion = Models.EmotionType.Neutral;
                previousEmotion = Models.EmotionType.Neutral;
                responseState = EmotionResponseState.Idle;
                currentEmotionIntensity = 0f;
                emotionStartTime = 0f;
                
                isInitialized = true;
                LogInfo("情感响应控制器初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"初始化失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 构建情感映射字典
        /// </summary>
        private void BuildEmotionMappingDictionary()
        {
            emotionMappingDict = new Dictionary<Models.EmotionType, EmotionAnimationMapping>();
            
            if (config.emotionMappings != null)
            {
                foreach (var mapping in config.emotionMappings)
                {
                    if (!emotionMappingDict.ContainsKey(mapping.emotionType))
                    {
                        emotionMappingDict[mapping.emotionType] = mapping;
                    }
                }
            }
            
            LogInfo($"构建了 {emotionMappingDict.Count} 个情感映射");
        }
        
        public void ProcessEmotion(Network.EmotionData emotionData)
        {
            if (!IsInitialized || !IsEnabled)
            {
                LogWarning("控制器未初始化或已禁用");
                return;
            }
            
            if (emotionData == null || !emotionData.isValid)
            {
                LogWarning("情感数据无效");
                return;
            }
            
            try
            {
                // 检查情感强度是否达到阈值
                if (emotionData.intensity < config.minIntensityThreshold)
                {
                    LogInfo($"情感强度 {emotionData.intensity} 低于阈值 {config.minIntensityThreshold}，忽略");
                    return;
                }
                
                // 将Network.EmotionType转换为Models.EmotionType
                var renderingEmotion = ConvertEmotionType(emotionData.primaryEmotion);
                
                LogInfo($"处理情感: {emotionData.primaryEmotion} -> {renderingEmotion}, 强度: {emotionData.intensity}");
                
                // 设置情感状态
                SetEmotion(renderingEmotion, emotionData.intensity, emotionData.duration);
            }
            catch (Exception ex)
            {
                LogError($"处理情感数据失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 将Network.EmotionType转换为Models.EmotionType
        /// </summary>
        /// <param name="networkEmotion">网络模块的情感类型</param>
        /// <returns>渲染模块的情感类型</returns>
        private Models.EmotionType ConvertEmotionType(Network.EmotionType networkEmotion)
        {
            switch (networkEmotion)
            {
                case Network.EmotionType.Happy: return Models.EmotionType.Happy;
                case Network.EmotionType.Sad: return Models.EmotionType.Sad;
                case Network.EmotionType.Angry: return Models.EmotionType.Angry;
                case Network.EmotionType.Surprise: return Models.EmotionType.Surprised;
                case Network.EmotionType.Confused: return Models.EmotionType.Confused;
                case Network.EmotionType.Excited: return Models.EmotionType.Excited;
                case Network.EmotionType.Neutral:
                default:
                    return Models.EmotionType.Neutral;
            }
        }
        
        public void SetEmotion(Models.EmotionType emotion, float intensity = 1f, float duration = 0f)
        {
            if (!IsInitialized || !IsEnabled)
            {
                LogWarning("控制器未初始化或已禁用");
                return;
            }
            
            try
            {
                // 停止当前的情感协程
                if (emotionCoroutine != null)
                {
                    StopCoroutine(emotionCoroutine);
                    emotionCoroutine = null;
                }
                
                // 停止自动返回中性状态的协程
                if (neutralReturnCoroutine != null)
                {
                    StopCoroutine(neutralReturnCoroutine);
                    neutralReturnCoroutine = null;
                }
                
                // 记录情感变化
                previousEmotion = currentEmotion;
                currentEmotion = emotion;
                currentEmotionIntensity = Mathf.Clamp01(intensity);
                emotionStartTime = Time.time;
                
                // 触发情感变化事件
                if (previousEmotion != currentEmotion)
                {
                    OnEmotionChanged?.Invoke(previousEmotion, currentEmotion);
                    LogInfo($"情感变化: {previousEmotion} -> {currentEmotion}");
                }
                
                // 开始情感动画
                emotionCoroutine = StartCoroutine(PlayEmotionCoroutine(emotion, intensity, duration));
                
                // 如果启用自动返回中性状态，设置延迟返回
                if (config.autoReturnToNeutral && emotion != Models.EmotionType.Neutral)
                {
                    float returnDelay = duration > 0 ? duration + config.neutralReturnDelay : config.emotionDuration + config.neutralReturnDelay;
                    neutralReturnCoroutine = StartCoroutine(AutoReturnToNeutralCoroutine(returnDelay));
                }
            }
            catch (Exception ex)
            {
                LogError($"设置情感失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 播放情感动画的协程
        /// </summary>
        /// <param name="emotion">情感类型</param>
        /// <param name="intensity">强度</param>
        /// <param name="duration">持续时间</param>
        /// <returns>协程</returns>
        private IEnumerator PlayEmotionCoroutine(Models.EmotionType emotion, float intensity, float duration)
        {
            responseState = EmotionResponseState.Transitioning;
            
            // 获取情感映射
            if (!emotionMappingDict.TryGetValue(emotion, out var mapping))
            {
                LogWarning($"未找到情感 {emotion} 的动画映射");
                responseState = EmotionResponseState.Idle;
                yield break;
            }
            
            try
            {
                // 计算实际持续时间
                float actualDuration = duration > 0 ? duration : 
                                     mapping.duration > 0 ? mapping.duration : 
                                     config.emotionDuration;
                
                // 计算动画强度
                float animationIntensity = intensity * mapping.intensityMultiplier;
                
                LogInfo($"播放情感动画: {mapping}, 强度: {animationIntensity}, 持续时间: {actualDuration}");
                
                // 播放面部表情动画
                if (!string.IsNullOrEmpty(mapping.facialAnimationName))
                {
                    PlayEmotionAnimation(mapping.facialAnimationName, animationIntensity, mapping.loop);
                }
                
                // 播放手势动画
                if (!string.IsNullOrEmpty(mapping.gestureAnimationName))
                {
                    PlayEmotionAnimation(mapping.gestureAnimationName, animationIntensity, mapping.loop);
                }
                
                responseState = EmotionResponseState.PlayingEmotion;
                
                // 等待动画持续时间
                if (!mapping.loop && actualDuration > 0)
                {
                    yield return new WaitForSeconds(actualDuration);
                }
                
                responseState = EmotionResponseState.Idle;
                LogInfo($"情感动画播放完成: {emotion}");
            }
            catch (Exception ex)
            {
                LogError($"播放情感动画失败: {ex.Message}");
                responseState = EmotionResponseState.Idle;
            }
        }
        
        /// <summary>
        /// 自动返回中性状态的协程
        /// </summary>
        /// <param name="delay">延迟时间</param>
        /// <returns>协程</returns>
        private IEnumerator AutoReturnToNeutralCoroutine(float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (currentEmotion != Models.EmotionType.Neutral)
            {
                LogInfo("自动返回中性状态");
                ResetToNeutral(config.transitionDuration);
            }
        }
        
        public void ResetToNeutral(float transitionDuration = 1f)
        {
            if (!IsInitialized)
            {
                LogWarning("控制器未初始化");
                return;
            }
            
            LogInfo($"重置到中性状态，过渡时间: {transitionDuration}");
            
            if (config.enableSmoothTransition && transitionDuration > 0)
            {
                StartCoroutine(SmoothTransitionToNeutralCoroutine(transitionDuration));
            }
            else
            {
                SetEmotion(Models.EmotionType.Neutral, 1f, 0f);
            }
        }
        
        /// <summary>
        /// 平滑过渡到中性状态的协程
        /// </summary>
        /// <param name="duration">过渡时间</param>
        /// <returns>协程</returns>
        private IEnumerator SmoothTransitionToNeutralCoroutine(float duration)
        {
            responseState = EmotionResponseState.ReturningToNeutral;
            
            float startTime = Time.time;
            float startIntensity = currentEmotionIntensity;
            
            while (Time.time - startTime < duration)
            {
                float progress = (Time.time - startTime) / duration;
                float currentIntensity = Mathf.Lerp(startIntensity, 0f, progress);
                
                // 这里可以添加平滑过渡的动画逻辑
                // 例如逐渐减少当前情感的强度
                
                yield return null;
            }
            
            // 最终设置为中性状态
            SetEmotion(Models.EmotionType.Neutral, 1f, 0f);
        }
        
        public void PlayEmotionAnimation(string animationName, float intensity = 1f, bool loop = false)
        {
            if (!IsInitialized || digitalHumanRenderer == null)
            {
                LogWarning("控制器未初始化或渲染器为空");
                return;
            }
            
            try
            {
                LogInfo($"播放动画: {animationName}, 强度: {intensity}, 循环: {loop}");
                
                // 触发动画开始事件
                OnAnimationStarted?.Invoke(animationName);
                
                // 根据渲染器类型播放动画
                if (digitalHumanRenderer is DigitalHumanRenderer renderer)
                {
                    // 这里需要根据实际的动画系统来实现
                    // 例如使用Animator或Animation组件
                    var animator = renderer.GetComponent<Animator>();
                    if (animator != null)
                    {
                        // 设置动画参数
                        animator.SetFloat("EmotionIntensity", intensity);
                        animator.SetBool("Loop", loop);
                        
                        // 播放动画
                        animator.Play(animationName);
                        
                        // 如果不循环，启动完成检测协程
                        if (!loop)
                        {
                            StartCoroutine(WaitForAnimationComplete(animationName, animator));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"播放动画失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 等待动画完成的协程
        /// </summary>
        /// <param name="animationName">动画名称</param>
        /// <param name="animator">动画控制器</param>
        /// <returns>协程</returns>
        private IEnumerator WaitForAnimationComplete(string animationName, Animator animator)
        {
            // 等待动画开始播放
            yield return null;
            
            // 等待动画播放完成
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            while (stateInfo.normalizedTime < 1f)
            {
                yield return null;
                stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            }
            
            // 触发动画完成事件
            OnAnimationCompleted?.Invoke(animationName);
            LogInfo($"动画播放完成: {animationName}");
        }
        
        public void StopCurrentAnimation()
        {
            if (emotionCoroutine != null)
            {
                StopCoroutine(emotionCoroutine);
                emotionCoroutine = null;
            }
            
            responseState = EmotionResponseState.Idle;
            LogInfo("停止当前情感动画");
        }
        
        public void UpdateConfiguration(EmotionResponseConfig config)
        {
            if (config == null)
            {
                LogError("配置不能为空");
                return;
            }
            
            this.config = config;
            BuildEmotionMappingDictionary();
            
            LogInfo("情感响应配置已更新");
        }
        
        public void Cleanup()
        {
            StopCurrentAnimation();
            
            if (neutralReturnCoroutine != null)
            {
                StopCoroutine(neutralReturnCoroutine);
                neutralReturnCoroutine = null;
            }
            
            emotionMappingDict?.Clear();
            digitalHumanRenderer = null;
            isInitialized = false;
            
            LogInfo("情感响应控制器已清理");
        }
        
        /// <summary>
        /// 获取当前情感响应状态
        /// </summary>
        /// <returns>响应状态</returns>
        public EmotionResponseState GetResponseState()
        {
            return responseState;
        }
        
        /// <summary>
        /// 获取当前情感持续时间
        /// </summary>
        /// <returns>持续时间（秒）</returns>
        public float GetCurrentEmotionDuration()
        {
            return Time.time - emotionStartTime;
        }
        
        /// <summary>
        /// 获取情感统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public string GetEmotionStats()
        {
            return $"当前情感: {currentEmotion}, 强度: {currentEmotionIntensity:F2}, 状态: {responseState}, 持续时间: {GetCurrentEmotionDuration():F1}s";
        }
        
        private void LogInfo(string message)
        {
            if (enableLogging)
            {
                Debug.Log($"[EmotionResponseController] {message}");
            }
        }
        
        private void LogWarning(string message)
        {
            if (enableLogging)
            {
                Debug.LogWarning($"[EmotionResponseController] {message}");
            }
        }
        
        private void LogError(string message)
        {
            if (enableLogging)
            {
                Debug.LogError($"[EmotionResponseController] {message}");
            }
        }
    }
}