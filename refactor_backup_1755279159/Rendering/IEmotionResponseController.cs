using System;
using UnityEngine;
using DigitalHuman.Core.Network;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 情感响应控制器接口
    /// 负责根据情感分析结果控制数字人的表情和动画
    /// </summary>
    public interface IEmotionResponseController
    {
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 是否启用情感响应
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// 当前情感状态
        /// </summary>
        Models.EmotionType CurrentEmotion { get; }
        
        /// <summary>
        /// 情感响应配置
        /// </summary>
        EmotionResponseConfig Configuration { get; }
        
        /// <summary>
        /// 情感状态变化事件
        /// </summary>
        event Action<Models.EmotionType, Models.EmotionType> OnEmotionChanged;
        
        /// <summary>
        /// 动画播放开始事件
        /// </summary>
        event Action<string> OnAnimationStarted;
        
        /// <summary>
        /// 动画播放完成事件
        /// </summary>
        event Action<string> OnAnimationCompleted;
        
        /// <summary>
        /// 初始化控制器
        /// </summary>
        /// <param name="digitalHumanRenderer">数字人渲染器</param>
        /// <param name="config">配置</param>
        /// <returns>是否初始化成功</returns>
        bool Initialize(IDigitalHumanRenderer digitalHumanRenderer, EmotionResponseConfig config = null);
        
        /// <summary>
        /// 处理情感数据并触发相应的动画
        /// </summary>
        /// <param name="emotionData">情感数据</param>
        void ProcessEmotion(EmotionData emotionData);
        
        /// <summary>
        /// 设置情感状态
        /// </summary>
        /// <param name="emotion">情感类型</param>
        /// <param name="intensity">强度 (0-1)</param>
        /// <param name="duration">持续时间（秒）</param>
        void SetEmotion(Models.EmotionType emotion, float intensity = 1f, float duration = 0f);
        
        /// <summary>
        /// 重置到中性状态
        /// </summary>
        /// <param name="transitionDuration">过渡时间（秒）</param>
        void ResetToNeutral(float transitionDuration = 1f);
        
        /// <summary>
        /// 播放情感动画
        /// </summary>
        /// <param name="animationName">动画名称</param>
        /// <param name="intensity">强度 (0-1)</param>
        /// <param name="loop">是否循环</param>
        void PlayEmotionAnimation(string animationName, float intensity = 1f, bool loop = false);
        
        /// <summary>
        /// 停止当前情感动画
        /// </summary>
        void StopCurrentAnimation();
        
        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="config">新配置</param>
        void UpdateConfiguration(EmotionResponseConfig config);
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void Cleanup();
    }
}