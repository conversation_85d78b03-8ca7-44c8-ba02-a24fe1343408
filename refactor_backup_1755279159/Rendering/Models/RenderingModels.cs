using System;
using UnityEngine;

namespace DigitalHuman.Core.Rendering.Models
{
    /// <summary>
    /// 渲染模式枚举
    /// </summary>
    public enum RenderMode
    {
        Model3D,    // 3D模型模式
        Video       // 视频模式
    }

    /// <summary>
    /// 动画类型枚举
    /// </summary>
    public enum AnimationType
    {
        Idle,           // 待机动画
        Speaking,       // 说话动画
        Greeting,       // 打招呼动画
        Gesture,        // 手势动画
        Dancing,        // 跳舞动画
        Listening,      // 倾听动画
        Thinking        // 思考动画
    }

    /// <summary>
    /// 情感类型枚举
    /// </summary>
    public enum EmotionType
    {
        Neutral,    // 中性
        Happy,      // 高兴
        Sad,        // 悲伤
        Angry,      // 愤怒
        Surprised,  // 惊讶
        Confused,   // 困惑
        Excited     // 兴奋
    }

    /// <summary>
    /// 数字人配置数据
    /// </summary>
    [Serializable]
    public class DigitalHumanConfig
    {
        [Header("基础配置")]
        public string characterId;
        public string characterName;
        public RenderMode renderMode = RenderMode.Model3D;
        
        [Header("3D模型配置")]
        public GameObject model3DPrefab;
        public RuntimeAnimatorController animatorController;
        
        [Header("视频配置")]
        public VideoClip[] stateVideos;
        
        [Header("动画配置")]
        public float animationBlendTime = 0.3f;
        public bool enableLipSync = true;
        public bool enableEyeTracking = true;
        
        [Header("渲染设置")]
        public int targetFrameRate = 60;
        public bool enableAntiAliasing = true;
        public int renderQuality = 2; // 0-低, 1-中, 2-高
    }

    /// <summary>
    /// 动画状态数据
    /// </summary>
    [Serializable]
    public class AnimationState
    {
        public AnimationType type;
        public string stateName;
        public float duration;
        public bool isLooping;
        public float blendTime;
        public int priority; // 动画优先级
    }

    /// <summary>
    /// 表情数据
    /// </summary>
    [Serializable]
    public class EmotionData
    {
        public EmotionType emotion;
        public float intensity; // 情感强度 0-1
        public float duration;  // 持续时间
        public AnimationCurve transitionCurve; // 过渡曲线
    }

    /// <summary>
    /// 口型同步数据
    /// </summary>
    [Serializable]
    public class LipSyncData
    {
        public AudioClip audioClip;
        public float[] visemeWeights; // 口型权重数组
        public float[] timeStamps;    // 时间戳数组
        public string phonemeText;    // 音素文本
    }

    /// <summary>
    /// 渲染性能数据
    /// </summary>
    [Serializable]
    public class RenderingPerformance
    {
        public float currentFPS;
        public float averageFPS;
        public int drawCalls;
        public int triangleCount;
        public float memoryUsage;
        public DateTime lastUpdateTime;
    }

    /// <summary>
    /// 数字人状态数据
    /// </summary>
    [Serializable]
    public class DigitalHumanState
    {
        public AnimationType currentAnimation;
        public EmotionType currentEmotion;
        public bool isSpeaking;
        public bool isListening;
        public Vector3 eyeTargetPosition;
        public float emotionIntensity;
        public DateTime lastStateChange;
    }
    
    /// <summary>
    /// 情感响应配置
    /// </summary>
    [Serializable]
    public class EmotionResponseConfig
    {
        /// <summary>
        /// 是否启用情感响应
        /// </summary>
        public bool enabled = true;
        
        /// <summary>
        /// 情感过渡时间（秒）
        /// </summary>
        public float transitionDuration = 1f;
        
        /// <summary>
        /// 最小情感强度阈值
        /// </summary>
        public float minIntensityThreshold = 0.3f;
        
        /// <summary>
        /// 情感持续时间（秒）
        /// </summary>
        public float emotionDuration = 3f;
        
        /// <summary>
        /// 是否启用平滑过渡
        /// </summary>
        public bool enableSmoothTransition = true;
        
        /// <summary>
        /// 情感动画映射
        /// </summary>
        public EmotionAnimationMapping[] emotionMappings;
        
        /// <summary>
        /// 是否自动返回中性状态
        /// </summary>
        public bool autoReturnToNeutral = true;
        
        /// <summary>
        /// 自动返回中性状态的延迟时间（秒）
        /// </summary>
        public float neutralReturnDelay = 5f;
        
        public EmotionResponseConfig()
        {
            emotionMappings = new EmotionAnimationMapping[]
            {
                new EmotionAnimationMapping(EmotionType.Happy, "Happy_Expression", "Happy_Gesture"),
                new EmotionAnimationMapping(EmotionType.Sad, "Sad_Expression", "Sad_Gesture"),
                new EmotionAnimationMapping(EmotionType.Angry, "Angry_Expression", "Angry_Gesture"),
                new EmotionAnimationMapping(EmotionType.Surprised, "Surprise_Expression", "Surprise_Gesture"),
                new EmotionAnimationMapping(EmotionType.Confused, "Confused_Expression", "Confused_Gesture"),
                new EmotionAnimationMapping(EmotionType.Excited, "Excited_Expression", "Excited_Gesture"),
                new EmotionAnimationMapping(EmotionType.Neutral, "Neutral_Expression", "Idle_Gesture")
            };
        }
    }
    
    /// <summary>
    /// 情感动画映射
    /// </summary>
    [Serializable]
    public class EmotionAnimationMapping
    {
        /// <summary>
        /// 情感类型
        /// </summary>
        public EmotionType emotionType;
        
        /// <summary>
        /// 面部表情动画名称
        /// </summary>
        public string facialAnimationName;
        
        /// <summary>
        /// 身体手势动画名称
        /// </summary>
        public string gestureAnimationName;
        
        /// <summary>
        /// 动画强度倍数
        /// </summary>
        public float intensityMultiplier = 1f;
        
        /// <summary>
        /// 动画持续时间（秒，0表示使用默认）
        /// </summary>
        public float duration = 0f;
        
        /// <summary>
        /// 是否循环播放
        /// </summary>
        public bool loop = false;
        
        /// <summary>
        /// 动画优先级
        /// </summary>
        public int priority = 0;
        
        public EmotionAnimationMapping()
        {
            emotionType = EmotionType.Neutral;
            facialAnimationName = "";
            gestureAnimationName = "";
        }
        
        public EmotionAnimationMapping(EmotionType emotion, string facial, string gesture)
        {
            emotionType = emotion;
            facialAnimationName = facial;
            gestureAnimationName = gesture;
        }
        
        public override string ToString()
        {
            return $"EmotionMapping[{emotionType}: {facialAnimationName}, {gestureAnimationName}]";
        }
    }
    
    /// <summary>
    /// 情感响应状态
    /// </summary>
    public enum EmotionResponseState
    {
        /// <summary>
        /// 空闲
        /// </summary>
        Idle,
        
        /// <summary>
        /// 过渡中
        /// </summary>
        Transitioning,
        
        /// <summary>
        /// 播放情感动画
        /// </summary>
        PlayingEmotion,
        
        /// <summary>
        /// 返回中性状态
        /// </summary>
        ReturningToNeutral
    }
}