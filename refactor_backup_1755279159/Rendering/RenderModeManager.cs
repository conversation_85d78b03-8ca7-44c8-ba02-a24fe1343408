using System;
using System.Collections;
using UnityEngine;
using DigitalHuman.Core.Rendering.Models;

namespace DigitalHuman.Core.Rendering
{
    /// <summary>
    /// 渲染模式管理器
    /// 负责3D模型和视频模式的无缝切换，以及性能优化和资源管理
    /// </summary>
    public class RenderModeManager : MonoBehaviour
    {
        #region 私有字段
        [SerializeField] private DigitalHumanRenderer _digitalHumanRenderer;
        [SerializeField] private VideoPlayerController _videoPlayerController;
        [SerializeField] private GreenScreenProcessor _greenScreenProcessor;
        [SerializeField] private RenderTexture _outputTexture;
        [SerializeField] private Camera _renderCamera;
        
        private RenderModeConfig _config;
        private RenderMode _currentMode = RenderMode.Model3D;
        private RenderMode _previousMode = RenderMode.Model3D;
        private bool _isTransitioning = false;
        private bool _isInitialized = false;
        
        private Coroutine _transitionCoroutine;
        private RenderModePerformance _performance;
        
        // 资源管理
        private bool _model3DResourcesLoaded = false;
        private bool _videoResourcesLoaded = false;
        
        // 性能监控
        private float _lastFrameTime = 0f;
        private int _frameCount = 0;
        private float _performanceUpdateInterval = 1f;
        #endregion

        #region 属性
        /// <summary>
        /// 当前渲染模式
        /// </summary>
        public RenderMode CurrentMode => _currentMode;
        
        /// <summary>
        /// 上一个渲染模式
        /// </summary>
        public RenderMode PreviousMode => _previousMode;
        
        /// <summary>
        /// 是否正在过渡中
        /// </summary>
        public bool IsTransitioning => _isTransitioning;
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        
        /// <summary>
        /// 输出渲染纹理
        /// </summary>
        public RenderTexture OutputTexture => _outputTexture;
        
        /// <summary>
        /// 性能数据
        /// </summary>
        public RenderModePerformance Performance => _performance;
        
        /// <summary>
        /// 数字人渲染器
        /// </summary>
        public DigitalHumanRenderer DigitalHumanRenderer => _digitalHumanRenderer;
        
        /// <summary>
        /// 视频播放控制器
        /// </summary>
        public VideoPlayerController VideoPlayerController => _videoPlayerController;
        
        /// <summary>
        /// 绿幕处理器
        /// </summary>
        public GreenScreenProcessor GreenScreenProcessor => _greenScreenProcessor;
        #endregion

        #region 事件
        /// <summary>
        /// 渲染模式切换开始事件
        /// </summary>
        public event Action<RenderMode, RenderMode> OnModeTransitionStarted;
        
        /// <summary>
        /// 渲染模式切换完成事件
        /// </summary>
        public event Action<RenderMode, RenderMode> OnModeTransitionCompleted;
        
        /// <summary>
        /// 渲染模式改变事件
        /// </summary>
        public event Action<RenderMode> OnModeChanged;
        
        /// <summary>
        /// 性能警告事件
        /// </summary>
        public event Action<string> OnPerformanceWarning;
        
        /// <summary>
        /// 渲染错误事件
        /// </summary>
        public event Action<string> OnRenderingError;
        #endregion

        #region Unity生命周期
        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            if (_config != null)
            {
                Initialize(_config);
            }
        }

        private void Update()
        {
            if (!_isInitialized) return;
            
            UpdatePerformanceMonitoring();
            UpdateCurrentMode();
        }

        private void OnDestroy()
        {
            Dispose();
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 初始化渲染模式管理器
        /// </summary>
        /// <param name="config">渲染模式配置</param>
        /// <returns>是否初始化成功</returns>
        public bool Initialize(RenderModeConfig config)
        {
            try
            {
                _config = config;
                
                // 创建输出纹理
                CreateOutputTexture();
                
                // 初始化子组件
                InitializeSubComponents();
                
                // 设置默认模式
                SetMode(_config.defaultMode, true);
                
                // 初始化性能监控
                InitializePerformanceMonitoring();
                
                _isInitialized = true;
                Debug.Log("渲染模式管理器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"渲染模式管理器初始化失败: {ex.Message}");
                OnRenderingError?.Invoke($"初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 切换渲染模式
        /// </summary>
        /// <param name="newMode">新的渲染模式</param>
        /// <param name="immediate">是否立即切换</param>
        public void SwitchMode(RenderMode newMode, bool immediate = false)
        {
            if (!_isInitialized)
            {
                Debug.LogWarning("渲染模式管理器未初始化");
                return;
            }

            if (_currentMode == newMode)
            {
                Debug.Log($"已经是目标模式: {newMode}");
                return;
            }

            if (_isTransitioning && !immediate)
            {
                Debug.LogWarning("正在过渡中，无法切换模式");
                return;
            }

            try
            {
                if (immediate || !_config.enableTransitions)
                {
                    SetMode(newMode, true);
                }
                else
                {
                    StartModeTransition(newMode);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"切换渲染模式失败: {ex.Message}");
                OnRenderingError?.Invoke($"模式切换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前模式的渲染纹理
        /// </summary>
        /// <returns>当前模式的渲染纹理</returns>
        public RenderTexture GetCurrentModeTexture()
        {
            if (!_isInitialized) return null;

            switch (_currentMode)
            {
                case RenderMode.Model3D:
                    return _digitalHumanRenderer?.Performance != null ? _outputTexture : null;
                case RenderMode.Video:
                    return _videoPlayerController?.GetRenderTexture();
                default:
                    return _outputTexture;
            }
        }

        /// <summary>
        /// 预加载指定模式的资源
        /// </summary>
        /// <param name="mode">要预加载的模式</param>
        public void PreloadModeResources(RenderMode mode)
        {
            if (!_isInitialized) return;

            try
            {
                switch (mode)
                {
                    case RenderMode.Model3D:
                        PreloadModel3DResources();
                        break;
                    case RenderMode.Video:
                        PreloadVideoResources();
                        break;
                }
                
                Debug.Log($"预加载 {mode} 模式资源完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"预加载 {mode} 模式资源失败: {ex.Message}");
                OnRenderingError?.Invoke($"资源预加载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 卸载指定模式的资源
        /// </summary>
        /// <param name="mode">要卸载的模式</param>
        public void UnloadModeResources(RenderMode mode)
        {
            if (!_isInitialized) return;

            try
            {
                switch (mode)
                {
                    case RenderMode.Model3D:
                        UnloadModel3DResources();
                        break;
                    case RenderMode.Video:
                        UnloadVideoResources();
                        break;
                }
                
                Debug.Log($"卸载 {mode} 模式资源完成");
            }
            catch (Exception ex)
            {
                Debug.LogError($"卸载 {mode} 模式资源失败: {ex.Message}");
                OnRenderingError?.Invoke($"资源卸载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置渲染质量
        /// </summary>
        /// <param name="quality">质量等级(0-2)</param>
        public void SetRenderQuality(int quality)
        {
            if (!_isInitialized) return;

            try
            {
                quality = Mathf.Clamp(quality, 0, 2);
                
                // 应用到各个渲染组件
                _digitalHumanRenderer?.SetRenderQuality(quality);
                
                // 根据质量调整输出纹理分辨率
                AdjustOutputTextureQuality(quality);
                
                Debug.Log($"渲染质量设置为: {quality}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"设置渲染质量失败: {ex.Message}");
                OnRenderingError?.Invoke($"质量设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取调试信息
        /// </summary>
        /// <returns>调试信息字符串</returns>
        public string GetDebugInfo()
        {
            if (!_isInitialized) return "渲染模式管理器未初始化";

            return $"渲染模式管理器调试信息:\n" +
                   $"当前模式: {_currentMode}\n" +
                   $"上一个模式: {_previousMode}\n" +
                   $"正在过渡: {_isTransitioning}\n" +
                   $"3D资源已加载: {_model3DResourcesLoaded}\n" +
                   $"视频资源已加载: {_videoResourcesLoaded}\n" +
                   $"当前FPS: {_performance?.currentFPS:F1}\n" +
                   $"内存使用: {_performance?.memoryUsage:F1}MB\n" +
                   $"GPU使用率: {_performance?.gpuUsage:F1}%";
        }

        /// <summary>
        /// 强制刷新当前模式
        /// </summary>
        public void RefreshCurrentMode()
        {
            if (!_isInitialized) return;

            try
            {
                RenderMode currentMode = _currentMode;
                _currentMode = RenderMode.Model3D; // 临时设置为不同模式
                SetMode(currentMode, true);
                
                Debug.Log($"刷新渲染模式: {currentMode}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"刷新渲染模式失败: {ex.Message}");
                OnRenderingError?.Invoke($"模式刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 停止过渡协程
                if (_transitionCoroutine != null)
                {
                    StopCoroutine(_transitionCoroutine);
                }

                // 释放子组件
                _digitalHumanRenderer?.Dispose();
                _videoPlayerController?.Dispose();
                _greenScreenProcessor?.Dispose();

                // 释放输出纹理
                if (_outputTexture != null)
                {
                    _outputTexture.Release();
                    DestroyImmediate(_outputTexture);
                }

                // 卸载所有资源
                UnloadAllResources();

                _isInitialized = false;
                Debug.Log("渲染模式管理器资源已释放");
            }
            catch (Exception ex)
            {
                Debug.LogError($"释放渲染模式管理器资源失败: {ex.Message}");
            }
        }
        #endregion

        #region 私有方法
        private void InitializeComponents()
        {
            _performance = new RenderModePerformance();
            
            // 查找或创建子组件
            if (_digitalHumanRenderer == null)
            {
                _digitalHumanRenderer = GetComponentInChildren<DigitalHumanRenderer>();
            }
            
            if (_videoPlayerController == null)
            {
                _videoPlayerController = GetComponentInChildren<VideoPlayerController>();
            }
            
            if (_greenScreenProcessor == null)
            {
                _greenScreenProcessor = GetComponentInChildren<GreenScreenProcessor>();
            }
            
            if (_renderCamera == null)
            {
                _renderCamera = GetComponentInChildren<Camera>();
            }
        }

        private void CreateOutputTexture()
        {
            int width = _config?.outputWidth ?? 1920;
            int height = _config?.outputHeight ?? 1080;
            RenderTextureFormat format = _config?.outputFormat ?? RenderTextureFormat.ARGB32;
            
            _outputTexture = new RenderTexture(width, height, 24, format);
            _outputTexture.name = "RenderModeManager_Output";
            _outputTexture.Create();
        }

        private void InitializeSubComponents()
        {
            // 初始化数字人渲染器
            if (_digitalHumanRenderer != null && _config.digitalHumanConfig != null)
            {
                _digitalHumanRenderer.Initialize(_config.digitalHumanConfig);
            }

            // 初始化视频播放控制器
            if (_videoPlayerController != null && _config.videoPlayerConfig != null)
            {
                _videoPlayerController.Initialize(_config.videoPlayerConfig);
            }

            // 初始化绿幕处理器
            if (_greenScreenProcessor != null && _config.greenScreenConfig != null)
            {
                _greenScreenProcessor.Initialize(_config.greenScreenConfig);
            }
        }

        private void InitializePerformanceMonitoring()
        {
            _performance = new RenderModePerformance
            {
                currentFPS = 0f,
                averageFPS = 0f,
                memoryUsage = 0f,
                gpuUsage = 0f,
                lastUpdateTime = DateTime.Now
            };
        }

        private void SetMode(RenderMode newMode, bool immediate = false)
        {
            _previousMode = _currentMode;
            _currentMode = newMode;

            // 激活/停用相应组件
            ActivateModeComponents(newMode);
            DeactivateModeComponents(_previousMode);

            // 设置渲染目标
            SetRenderTarget(newMode);

            OnModeChanged?.Invoke(newMode);
            Debug.Log($"渲染模式设置为: {newMode}");
        }

        private void StartModeTransition(RenderMode newMode)
        {
            if (_transitionCoroutine != null)
            {
                StopCoroutine(_transitionCoroutine);
            }

            _transitionCoroutine = StartCoroutine(ModeTransitionCoroutine(newMode));
        }

        private IEnumerator ModeTransitionCoroutine(RenderMode newMode)
        {
            _isTransitioning = true;
            RenderMode oldMode = _currentMode;

            OnModeTransitionStarted?.Invoke(oldMode, newMode);

            // 预加载新模式资源
            PreloadModeResources(newMode);

            // 等待资源加载完成
            yield return new WaitUntil(() => IsModeResourcesLoaded(newMode));

            // 执行过渡效果
            yield return StartCoroutine(ExecuteTransitionEffect(oldMode, newMode));

            // 切换到新模式
            SetMode(newMode, true);

            // 卸载旧模式资源（如果配置允许）
            if (_config.unloadInactiveResources)
            {
                yield return new WaitForSeconds(0.5f); // 延迟卸载
                UnloadModeResources(oldMode);
            }

            _isTransitioning = false;
            OnModeTransitionCompleted?.Invoke(oldMode, newMode);
        }

        private IEnumerator ExecuteTransitionEffect(RenderMode fromMode, RenderMode toMode)
        {
            float transitionDuration = _config?.transitionDuration ?? 0.5f;
            float elapsedTime = 0f;

            // 简单的淡入淡出效果
            while (elapsedTime < transitionDuration)
            {
                float progress = elapsedTime / transitionDuration;
                
                // 这里可以添加更复杂的过渡效果
                // 例如：交叉淡化、滑动、缩放等
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
        }

        private void ActivateModeComponents(RenderMode mode)
        {
            switch (mode)
            {
                case RenderMode.Model3D:
                    if (_digitalHumanRenderer != null)
                    {
                        _digitalHumanRenderer.gameObject.SetActive(true);
                    }
                    break;
                case RenderMode.Video:
                    if (_videoPlayerController != null)
                    {
                        _videoPlayerController.gameObject.SetActive(true);
                    }
                    break;
            }
        }

        private void DeactivateModeComponents(RenderMode mode)
        {
            if (mode == _currentMode) return; // 不要停用当前模式

            switch (mode)
            {
                case RenderMode.Model3D:
                    if (_digitalHumanRenderer != null && !_config.keepInactiveComponentsActive)
                    {
                        _digitalHumanRenderer.gameObject.SetActive(false);
                    }
                    break;
                case RenderMode.Video:
                    if (_videoPlayerController != null && !_config.keepInactiveComponentsActive)
                    {
                        _videoPlayerController.gameObject.SetActive(false);
                    }
                    break;
            }
        }

        private void SetRenderTarget(RenderMode mode)
        {
            if (_renderCamera == null) return;

            switch (mode)
            {
                case RenderMode.Model3D:
                    _renderCamera.targetTexture = _outputTexture;
                    break;
                case RenderMode.Video:
                    // 视频模式可能不需要相机渲染
                    break;
            }
        }

        private void PreloadModel3DResources()
        {
            if (_model3DResourcesLoaded) return;

            // 预加载3D模型资源
            // 这里可以添加具体的资源加载逻辑

            _model3DResourcesLoaded = true;
        }

        private void PreloadVideoResources()
        {
            if (_videoResourcesLoaded) return;

            // 预加载视频资源
            // 这里可以添加具体的资源加载逻辑

            _videoResourcesLoaded = true;
        }

        private void UnloadModel3DResources()
        {
            if (!_model3DResourcesLoaded) return;

            // 卸载3D模型资源
            // 这里可以添加具体的资源卸载逻辑

            _model3DResourcesLoaded = false;
        }

        private void UnloadVideoResources()
        {
            if (!_videoResourcesLoaded) return;

            // 卸载视频资源
            // 这里可以添加具体的资源卸载逻辑

            _videoResourcesLoaded = false;
        }

        private void UnloadAllResources()
        {
            UnloadModel3DResources();
            UnloadVideoResources();
        }

        private bool IsModeResourcesLoaded(RenderMode mode)
        {
            switch (mode)
            {
                case RenderMode.Model3D:
                    return _model3DResourcesLoaded;
                case RenderMode.Video:
                    return _videoResourcesLoaded;
                default:
                    return true;
            }
        }

        private void AdjustOutputTextureQuality(int quality)
        {
            if (_outputTexture == null) return;

            int width, height;
            switch (quality)
            {
                case 0: // 低质量
                    width = 1280;
                    height = 720;
                    break;
                case 1: // 中质量
                    width = 1600;
                    height = 900;
                    break;
                case 2: // 高质量
                default:
                    width = 1920;
                    height = 1080;
                    break;
            }

            if (_outputTexture.width != width || _outputTexture.height != height)
            {
                _outputTexture.Release();
                _outputTexture.width = width;
                _outputTexture.height = height;
                _outputTexture.Create();
            }
        }

        private void UpdatePerformanceMonitoring()
        {
            _frameCount++;
            float currentTime = Time.time;

            if (currentTime - _lastFrameTime >= _performanceUpdateInterval)
            {
                // 更新FPS
                _performance.currentFPS = _frameCount / (currentTime - _lastFrameTime);
                _performance.averageFPS = (_performance.averageFPS + _performance.currentFPS) * 0.5f;

                // 更新内存使用
                _performance.memoryUsage = (UnityEngine.Profiling.Profiler.GetTotalAllocatedMemory(false) / 1024f / 1024f);

                // 更新GPU使用率（简化实现）
                _performance.gpuUsage = UnityEngine.Profiling.Profiler.GetRuntimeMemorySize(_outputTexture) / 1024f / 1024f;

                _performance.lastUpdateTime = DateTime.Now;

                // 检查性能警告
                CheckPerformanceWarnings();

                _frameCount = 0;
                _lastFrameTime = currentTime;
            }
        }

        private void CheckPerformanceWarnings()
        {
            if (_performance.currentFPS < _config.minAcceptableFPS)
            {
                OnPerformanceWarning?.Invoke($"FPS过低: {_performance.currentFPS:F1}");
            }

            if (_performance.memoryUsage > _config.maxMemoryUsage)
            {
                OnPerformanceWarning?.Invoke($"内存使用过高: {_performance.memoryUsage:F1}MB");
            }
        }

        private void UpdateCurrentMode()
        {
            // 根据当前模式更新相应组件
            switch (_currentMode)
            {
                case RenderMode.Model3D:
                    UpdateModel3DMode();
                    break;
                case RenderMode.Video:
                    UpdateVideoMode();
                    break;
            }
        }

        private void UpdateModel3DMode()
        {
            // 更新3D模型模式的逻辑
            if (_digitalHumanRenderer != null && _digitalHumanRenderer.IsInitialized)
            {
                _digitalHumanRenderer.UpdatePerformanceStats();
            }
        }

        private void UpdateVideoMode()
        {
            // 更新视频模式的逻辑
            if (_videoPlayerController != null && _videoPlayerController.IsInitialized)
            {
                // 视频播放控制器的更新逻辑
            }
        }
        #endregion
    }

    /// <summary>
    /// 渲染模式配置
    /// </summary>
    [Serializable]
    public class RenderModeConfig
    {
        [Header("基础配置")]
        public RenderMode defaultMode = RenderMode.Model3D;
        public bool enableTransitions = true;
        public float transitionDuration = 0.5f;
        public bool keepInactiveComponentsActive = false;
        public bool unloadInactiveResources = true;
        
        [Header("输出设置")]
        public int outputWidth = 1920;
        public int outputHeight = 1080;
        public RenderTextureFormat outputFormat = RenderTextureFormat.ARGB32;
        
        [Header("性能设置")]
        public float minAcceptableFPS = 30f;
        public float maxMemoryUsage = 512f; // MB
        public bool enablePerformanceMonitoring = true;
        
        [Header("子组件配置")]
        public DigitalHumanConfig digitalHumanConfig;
        public VideoPlayerConfig videoPlayerConfig;
        public GreenScreenConfig greenScreenConfig;
    }

    /// <summary>
    /// 渲染模式性能数据
    /// </summary>
    [Serializable]
    public class RenderModePerformance
    {
        public float currentFPS;
        public float averageFPS;
        public float memoryUsage; // MB
        public float gpuUsage; // %
        public DateTime lastUpdateTime;
    }
}