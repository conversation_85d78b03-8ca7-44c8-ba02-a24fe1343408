using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Logging;

namespace DigitalHuman.Core.Licensing.Examples
{
    /// <summary>
    /// 许可管理功能示例
    /// </summary>
    public class LicenseExample : MonoBehaviour
    {
        [Header("测试配置")]
        /// <summary>
        /// 是否在启动时自动运行测试
        /// </summary>
        public bool AutoRunOnStart = false;
        
        /// <summary>
        /// 测试激活码
        /// </summary>
        public string TestActivationCode = "MVP-DEMO-2024-ACTIVATION";
        
        // 私有字段
        private LicenseManager _licenseManager;
        private DigitalHuman.Core.Logging.ILogger _logger;
        
        /// <summary>
        /// 初始化
        /// </summary>
        void Start()
        {
            _logger = LogManager.Instance.GetLogger("LicenseExample");
            _logger.Info("许可管理示例初始化");
            
            // 获取许可管理器实例
            _licenseManager = LicenseManager.Instance;
            
            // 订阅许可事件
            _licenseManager.OnLicenseStatusChanged += OnLicenseStatusChanged;
            _licenseManager.OnDeviceActivated += OnDeviceActivated;
            _licenseManager.OnLicenseExpiring += OnLicenseExpiring;
            
            if (AutoRunOnStart)
            {
                StartCoroutine(RunTestAfterDelay());
            }
        }
        
        /// <summary>
        /// 延迟运行测试
        /// </summary>
        private System.Collections.IEnumerator RunTestAfterDelay()
        {
            yield return new WaitForSeconds(2f);
            _ = RunLicenseTestAsync();
        }
        
        /// <summary>
        /// 运行许可测试
        /// </summary>
        public async Task RunLicenseTestAsync()
        {
            _logger.Info("开始许可管理测试");
            
            try
            {
                // 1. 检查当前许可状态
                _logger.Info("步骤1: 检查当前许可状态");
                _logger.Info($"许可状态: {_licenseManager.CurrentStatus}");
                _logger.Info($"设备已激活: {_licenseManager.IsDeviceActivated}");
                _logger.Info($"MVP模式: {_licenseManager.IsMVPMode}");
                
                // 2. 获取设备信息
                _logger.Info("步骤2: 获取设备信息");
                var deviceInfo = _licenseManager.GetDeviceInfo();
                _logger.Info($"设备ID: {deviceInfo.DeviceId}");
                _logger.Info($"设备名称: {deviceInfo.DeviceName}");
                _logger.Info($"操作系统: {deviceInfo.OperatingSystem}");
                
                // 3. 测试激活码验证
                _logger.Info("步骤3: 测试激活码验证");
                var validationResult = await _licenseManager.ValidateActivationCodeAsync(TestActivationCode);
                if (validationResult.IsSuccess)
                {
                    _logger.Info($"激活码验证成功: {validationResult.LicenseInfo.LicenseType}");
                }
                else
                {
                    _logger.Error($"激活码验证失败: {validationResult.ErrorMessage}");
                }
                
                // 4. 测试设备激活
                if (validationResult.IsSuccess && !_licenseManager.IsDeviceActivated)
                {
                    _logger.Info("步骤4: 测试设备激活");
                    var activationResult = await _licenseManager.ActivateDeviceAsync(TestActivationCode);
                    if (activationResult.IsSuccess)
                    {
                        _logger.Info("设备激活成功");
                    }
                    else
                    {
                        _logger.Error($"设备激活失败: {activationResult.ErrorMessage}");
                    }
                }
                
                // 5. 测试许可验证
                _logger.Info("步骤5: 测试许可验证");
                var isValid = await _licenseManager.ValidateLicenseAsync();
                _logger.Info($"许可验证结果: {isValid}");
                
                // 6. 获取许可信息
                if (_licenseManager.CurrentLicense != null)
                {
                    _logger.Info("步骤6: 显示许可信息");
                    var license = _licenseManager.CurrentLicense;
                    _logger.Info($"许可ID: {license.LicenseId}");
                    _logger.Info($"产品名称: {license.ProductName}");
                    _logger.Info($"许可类型: {license.LicenseType}");
                    _logger.Info($"剩余天数: {license.GetRemainingDays()}天");
                    _logger.Info($"支持特性: {string.Join(", ", license.Features)}");
                }
                
                _logger.Info("许可管理测试完成");
            }
            catch (System.Exception ex)
            {
                _logger.Error($"许可管理测试过程中发生异常: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 测试MVP模式切换
        /// </summary>
        public void TestMVPModeToggle()
        {
            _logger.Info("测试MVP模式切换");
            
            bool currentMVPMode = _licenseManager.IsMVPMode;
            _licenseManager.SetMVPMode(!currentMVPMode);
            
            _logger.Info($"MVP模式已{(!currentMVPMode ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 测试设备停用
        /// </summary>
        public async Task TestDeviceDeactivationAsync()
        {
            _logger.Info("测试设备停用");
            
            var result = await _licenseManager.DeactivateDeviceAsync();
            if (result)
            {
                _logger.Info("设备停用成功");
            }
            else
            {
                _logger.Error("设备停用失败");
            }
        }
        
        /// <summary>
        /// 许可状态变化事件处理
        /// </summary>
        /// <param name="status">许可状态</param>
        private void OnLicenseStatusChanged(LicenseStatus status)
        {
            _logger.Info($"许可状态变化: {status}");
        }
        
        /// <summary>
        /// 设备激活事件处理
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        private void OnDeviceActivated(DeviceInfo deviceInfo)
        {
            _logger.Info($"设备激活成功: {deviceInfo.DeviceName}");
        }
        
        /// <summary>
        /// 许可即将过期事件处理
        /// </summary>
        /// <param name="remainingTime">剩余时间</param>
        private void OnLicenseExpiring(System.TimeSpan remainingTime)
        {
            _logger.Warning($"许可即将过期，剩余时间: {remainingTime.TotalDays:F0}天");
        }
        
        /// <summary>
        /// GUI界面
        /// </summary>
        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(Screen.width - 320, 120, 300, 400));
            GUILayout.Label("许可管理测试面板", GUI.skin.box);
            
            GUILayout.Space(10);
            
            // 显示当前状态
            if (_licenseManager != null)
            {
                GUILayout.Label($"许可状态: {_licenseManager.CurrentStatus}");
                GUILayout.Label($"设备激活: {(_licenseManager.IsDeviceActivated ? "是" : "否")}");
                GUILayout.Label($"MVP模式: {(_licenseManager.IsMVPMode ? "是" : "否")}");
                
                if (_licenseManager.CurrentLicense != null)
                {
                    GUILayout.Label($"许可类型: {_licenseManager.CurrentLicense.LicenseType}");
                    GUILayout.Label($"剩余天数: {_licenseManager.CurrentLicense.GetRemainingDays()}");
                }
                
                var remainingTime = _licenseManager.GetRemainingTime();
                if (remainingTime > System.TimeSpan.Zero)
                {
                    GUILayout.Label($"剩余时间: {remainingTime.TotalDays:F0}天");
                }
            }
            
            GUILayout.Space(10);
            
            // 测试按钮
            if (GUILayout.Button("运行许可测试"))
            {
                _ = RunLicenseTestAsync();
            }
            
            if (GUILayout.Button("验证激活码"))
            {
                _ = _licenseManager?.ValidateActivationCodeAsync(TestActivationCode);
            }
            
            if (GUILayout.Button("激活设备"))
            {
                _ = _licenseManager?.ActivateDeviceAsync(TestActivationCode);
            }
            
            if (GUILayout.Button("验证许可"))
            {
                _ = _licenseManager?.ValidateLicenseAsync();
            }
            
            if (GUILayout.Button("切换MVP模式"))
            {
                TestMVPModeToggle();
            }
            
            if (GUILayout.Button("停用设备"))
            {
                _ = TestDeviceDeactivationAsync();
            }
            
            if (GUILayout.Button("清除许可数据"))
            {
                _licenseManager?.ClearLicenseData();
            }
            
            GUILayout.EndArea();
        }
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void OnDestroy()
        {
            if (_licenseManager != null)
            {
                _licenseManager.OnLicenseStatusChanged -= OnLicenseStatusChanged;
                _licenseManager.OnDeviceActivated -= OnDeviceActivated;
                _licenseManager.OnLicenseExpiring -= OnLicenseExpiring;
            }
        }
    }
}