using System;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Base;
using DigitalHuman.Core.Logging;

namespace DigitalHuman.Core.Licensing
{
    /// <summary>
    /// 许可管理器实现
    /// </summary>
    public class LicenseManager : SingletonManager<LicenseManager>, ILicenseManager
    {
        [Header("许可配置")]
        /// <summary>
        /// 是否启用MVP模式
        /// </summary>
        public bool EnableMVPMode = true;
        
        /// <summary>
        /// 许可验证间隔（小时）
        /// </summary>
        public int ValidationIntervalHours = 24;
        
        /// <summary>
        /// 过期警告天数
        /// </summary>
        public int ExpirationWarningDays = 7;
        
        // 私有字段
        private LicenseStatus _currentStatus = LicenseStatus.NotActivated;
        private LicenseInfo _currentLicense;
        private DeviceInfo _deviceInfo;
        private DigitalHuman.Core.Logging.ILogger _logger;
        private DateTime _lastValidationTime = DateTime.MinValue;
        
        // 事件
        public event Action<LicenseStatus> OnLicenseStatusChanged;
        public event Action<DeviceInfo> OnDeviceActivated;
        public event Action<TimeSpan> OnLicenseExpiring;
        
        // 属性
        public LicenseStatus CurrentStatus => _currentStatus;
        public bool IsDeviceActivated => _currentStatus == LicenseStatus.Activated || _currentStatus == LicenseStatus.MVPMode;
        public bool IsMVPMode => EnableMVPMode || _currentStatus == LicenseStatus.MVPMode;
        public LicenseInfo CurrentLicense => _currentLicense;
        
        /// <summary>
        /// 初始化管理器
        /// </summary>
        protected override void InitializeManager()
        {
            base.InitializeManager();
            
            _logger = LogManager.Instance.GetLogger("License");
            _logger.Info("许可管理器初始化开始");
            
            // 生成设备信息
            _deviceInfo = GenerateDeviceInfo();
            
            // 加载本地许可信息
            LoadLocalLicense();
            
            // 如果启用MVP模式，直接设置为MVP状态
            if (EnableMVPMode)
            {
                SetMVPMode(true);
            }
            else
            {
                // 验证现有许可
                _ = ValidateLicenseAsync();
            }
            
            _logger.Info("许可管理器初始化完成");
        }
        
        /// <summary>
        /// 验证激活码
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>验证结果</returns>
        public async Task<ActivationResult> ValidateActivationCodeAsync(string activationCode)
        {
            _logger.Info($"验证激活码: {activationCode?.Substring(0, Math.Min(8, activationCode?.Length ?? 0))}...");
            
            try
            {
                SetLicenseStatus(LicenseStatus.Activating);
                
                // 验证激活码格式
                if (string.IsNullOrEmpty(activationCode) || activationCode.Length < 16)
                {
                    return ActivationResult.Failure("激活码格式无效", "INVALID_FORMAT");
                }
                
                // 在MVP模式下，使用模拟验证
                if (EnableMVPMode)
                {
                    return await SimulateActivationValidationAsync(activationCode);
                }
                
                // TODO: 实现真实的激活码验证
                // var result = await ValidateWithServerAsync(activationCode);
                
                // 暂时返回模拟结果
                return await SimulateActivationValidationAsync(activationCode);
            }
            catch (Exception ex)
            {
                _logger.Error($"验证激活码时发生异常: {ex.Message}", ex);
                SetLicenseStatus(LicenseStatus.Invalid);
                return ActivationResult.Failure($"验证失败: {ex.Message}", "VALIDATION_EXCEPTION");
            }
        }
        
        /// <summary>
        /// 激活设备
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>激活结果</returns>
        public async Task<ActivationResult> ActivateDeviceAsync(string activationCode)
        {
            _logger.Info("开始设备激活");
            
            try
            {
                // 先验证激活码
                var validationResult = await ValidateActivationCodeAsync(activationCode);
                if (!validationResult.IsSuccess)
                {
                    return validationResult;
                }
                
                // 执行激活
                _currentLicense = validationResult.LicenseInfo;
                _deviceInfo.ActivatedAt = DateTime.Now;
                _deviceInfo.LastValidatedAt = DateTime.Now;
                
                // 保存许可信息
                SaveLocalLicense();
                
                SetLicenseStatus(LicenseStatus.Activated);
                OnDeviceActivated?.Invoke(_deviceInfo);
                
                _logger.Info("设备激活成功");
                
                return ActivationResult.Success(_currentLicense, _deviceInfo);
            }
            catch (Exception ex)
            {
                _logger.Error($"设备激活时发生异常: {ex.Message}", ex);
                SetLicenseStatus(LicenseStatus.Invalid);
                return ActivationResult.Failure($"激活失败: {ex.Message}", "ACTIVATION_EXCEPTION");
            }
        }
        
        /// <summary>
        /// 停用设备
        /// </summary>
        /// <returns>停用结果</returns>
        public async Task<bool> DeactivateDeviceAsync()
        {
            _logger.Info("开始设备停用");
            
            try
            {
                // TODO: 通知服务器停用设备
                // await NotifyServerDeactivationAsync();
                
                // 清除本地许可信息
                ClearLicenseData();
                
                SetLicenseStatus(LicenseStatus.NotActivated);
                
                _logger.Info("设备停用成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"设备停用时发生异常: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 刷新许可信息
        /// </summary>
        /// <returns>刷新结果</returns>
        public async Task<bool> RefreshLicenseAsync()
        {
            _logger.Debug("刷新许可信息");
            
            try
            {
                if (_currentLicense == null)
                {
                    _logger.Warning("没有许可信息需要刷新");
                    return false;
                }
                
                // 在MVP模式下，模拟刷新
                if (EnableMVPMode)
                {
                    await Task.Delay(500);
                    _lastValidationTime = DateTime.Now;
                    _logger.Debug("许可信息刷新成功（MVP模式）");
                    return true;
                }
                
                // TODO: 实现真实的许可刷新
                return false;
            }
            catch (Exception ex)
            {
                _logger.Error($"刷新许可信息时发生异常: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 检查许可有效性
        /// </summary>
        /// <returns>许可是否有效</returns>
        public async Task<bool> ValidateLicenseAsync()
        {
            try
            {
                if (IsMVPMode)
                {
                    return true;
                }
                
                if (_currentLicense == null)
                {
                    SetLicenseStatus(LicenseStatus.NotActivated);
                    return false;
                }
                
                // 检查许可是否过期
                if (!_currentLicense.IsValid())
                {
                    SetLicenseStatus(LicenseStatus.Expired);
                    return false;
                }
                
                // 检查是否需要验证
                var timeSinceLastValidation = DateTime.Now - _lastValidationTime;
                if (timeSinceLastValidation.TotalHours >= ValidationIntervalHours)
                {
                    var refreshResult = await RefreshLicenseAsync();
                    if (!refreshResult)
                    {
                        _logger.Warning("许可验证失败");
                        return false;
                    }
                }
                
                // 检查是否即将过期
                var remainingTime = _currentLicense.ExpiresAt - DateTime.Now;
                if (remainingTime.TotalDays <= ExpirationWarningDays)
                {
                    OnLicenseExpiring?.Invoke(remainingTime);
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"验证许可时发生异常: {ex.Message}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        public DeviceInfo GetDeviceInfo()
        {
            return _deviceInfo;
        }
        
        /// <summary>
        /// 设置MVP模式
        /// </summary>
        /// <param name="enabled">是否启用MVP模式</param>
        public void SetMVPMode(bool enabled)
        {
            EnableMVPMode = enabled;
            
            if (enabled)
            {
                _logger.Info("启用MVP模式");
                
                // 创建MVP许可信息
                _currentLicense = new LicenseInfo
                {
                    LicenseId = "MVP-LICENSE",
                    ProductName = "数字人管理系统",
                    ProductVersion = "1.0.0-MVP",
                    LicenseType = LicenseType.MVP,
                    ActivatedAt = DateTime.Now,
                    ExpiresAt = DateTime.Now.AddYears(1),
                    MaxDevices = 1,
                    ActivatedDevices = 1
                };
                
                _currentLicense.Features.Add("BasicChat");
                _currentLicense.Features.Add("VoiceInteraction");
                _currentLicense.Features.Add("DataSync");
                
                SetLicenseStatus(LicenseStatus.MVPMode);
            }
            else
            {
                _logger.Info("禁用MVP模式");
                SetLicenseStatus(LicenseStatus.NotActivated);
            }
        }
        
        /// <summary>
        /// 获取许可剩余时间
        /// </summary>
        /// <returns>剩余时间</returns>
        public TimeSpan GetRemainingTime()
        {
            if (_currentLicense == null)
            {
                return TimeSpan.Zero;
            }
            
            var remaining = _currentLicense.ExpiresAt - DateTime.Now;
            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }
        
        /// <summary>
        /// 清除许可数据
        /// </summary>
        public void ClearLicenseData()
        {
            _logger.Info("清除许可数据");
            
            _currentLicense = null;
            _lastValidationTime = DateTime.MinValue;
            
            // 清除本地存储
            PlayerPrefs.DeleteKey("LicenseInfo");
            PlayerPrefs.DeleteKey("DeviceInfo");
            PlayerPrefs.Save();
        }    
    
        /// <summary>
        /// 设置许可状态
        /// </summary>
        /// <param name="status">许可状态</param>
        private void SetLicenseStatus(LicenseStatus status)
        {
            if (_currentStatus != status)
            {
                _currentStatus = status;
                OnLicenseStatusChanged?.Invoke(status);
                _logger.Debug($"许可状态变更为: {status}");
            }
        }
        
        /// <summary>
        /// 生成设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        private DeviceInfo GenerateDeviceInfo()
        {
            var deviceInfo = new DeviceInfo
            {
                DeviceId = SystemInfo.deviceUniqueIdentifier,
                DeviceName = SystemInfo.deviceName,
                OperatingSystem = SystemInfo.operatingSystem,
                ProcessorInfo = SystemInfo.processorType,
                MemorySize = SystemInfo.systemMemorySize,
                MacAddress = "00:00:00:00:00:00", // 简化处理
                LastValidatedAt = DateTime.Now
            };
            
            deviceInfo.DeviceFingerprint = deviceInfo.GenerateFingerprint();
            
            return deviceInfo;
        }
        
        /// <summary>
        /// 模拟激活验证
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>验证结果</returns>
        private async Task<ActivationResult> SimulateActivationValidationAsync(string activationCode)
        {
            // 模拟网络延迟
            await Task.Delay(1500);
            
            // 简单的模拟验证逻辑
            if (activationCode.StartsWith("MVP-") || activationCode == "DEMO-ACTIVATION-CODE-2024")
            {
                var licenseInfo = new LicenseInfo
                {
                    LicenseId = $"LIC-{Guid.NewGuid():N}",
                    ProductName = "数字人管理系统",
                    ProductVersion = "1.0.0",
                    LicenseType = activationCode.StartsWith("MVP-") ? LicenseType.MVP : LicenseType.Standard,
                    ActivatedAt = DateTime.Now,
                    ExpiresAt = DateTime.Now.AddDays(activationCode.StartsWith("MVP-") ? 365 : 30),
                    MaxDevices = 1,
                    ActivatedDevices = 1,
                    Customer = new CustomerInfo
                    {
                        CustomerId = "DEMO-CUSTOMER",
                        CustomerName = "演示用户",
                        Email = "<EMAIL>",
                        CompanyName = "演示公司"
                    }
                };
                
                // 添加功能特性
                licenseInfo.Features.Add("BasicChat");
                licenseInfo.Features.Add("VoiceInteraction");
                licenseInfo.Features.Add("DataSync");
                
                if (licenseInfo.LicenseType == LicenseType.Standard)
                {
                    licenseInfo.Features.Add("AdvancedChat");
                    licenseInfo.Features.Add("EmotionRecognition");
                }
                
                _logger.Info($"激活码验证成功: {licenseInfo.LicenseType}");
                
                return ActivationResult.Success(licenseInfo, _deviceInfo);
            }
            else
            {
                _logger.Warning($"激活码验证失败: 无效的激活码");
                return ActivationResult.Failure("激活码无效或已过期", "INVALID_ACTIVATION_CODE");
            }
        }
        
        /// <summary>
        /// 加载本地许可信息
        /// </summary>
        private void LoadLocalLicense()
        {
            try
            {
                if (PlayerPrefs.HasKey("LicenseInfo"))
                {
                    var licenseJson = PlayerPrefs.GetString("LicenseInfo");
                    _currentLicense = JsonUtility.FromJson<LicenseInfo>(licenseJson);
                    _logger.Debug("已加载本地许可信息");
                }
                
                if (PlayerPrefs.HasKey("DeviceInfo"))
                {
                    var deviceJson = PlayerPrefs.GetString("DeviceInfo");
                    var savedDevice = JsonUtility.FromJson<DeviceInfo>(deviceJson);
                    
                    // 验证设备指纹是否匹配
                    if (savedDevice.DeviceFingerprint == _deviceInfo.DeviceFingerprint)
                    {
                        _deviceInfo = savedDevice;
                        _logger.Debug("已加载本地设备信息");
                    }
                    else
                    {
                        _logger.Warning("设备指纹不匹配，清除本地许可信息");
                        ClearLicenseData();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"加载本地许可信息时发生异常: {ex.Message}", ex);
                ClearLicenseData();
            }
        }
        
        /// <summary>
        /// 保存本地许可信息
        /// </summary>
        private void SaveLocalLicense()
        {
            try
            {
                if (_currentLicense != null)
                {
                    var licenseJson = JsonUtility.ToJson(_currentLicense);
                    PlayerPrefs.SetString("LicenseInfo", licenseJson);
                }
                
                if (_deviceInfo != null)
                {
                    var deviceJson = JsonUtility.ToJson(_deviceInfo);
                    PlayerPrefs.SetString("DeviceInfo", deviceJson);
                }
                
                PlayerPrefs.Save();
                _logger.Debug("已保存本地许可信息");
            }
            catch (Exception ex)
            {
                _logger.Error($"保存本地许可信息时发生异常: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 管理器关闭时清理资源
        /// </summary>
        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // 清理事件订阅
            OnLicenseStatusChanged = null;
            OnDeviceActivated = null;
            OnLicenseExpiring = null;
        }
    }
}