using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.Licensing
{
    /// <summary>
    /// 许可状态枚举
    /// </summary>
    public enum LicenseStatus
    {
        /// <summary>
        /// 未激活
        /// </summary>
        NotActivated,
        
        /// <summary>
        /// 激活中
        /// </summary>
        Activating,
        
        /// <summary>
        /// 已激活
        /// </summary>
        Activated,
        
        /// <summary>
        /// 已过期
        /// </summary>
        Expired,
        
        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended,
        
        /// <summary>
        /// 无效
        /// </summary>
        Invalid,
        
        /// <summary>
        /// MVP模式
        /// </summary>
        MVPMode
    }
    
    /// <summary>
    /// 许可类型枚举
    /// </summary>
    public enum LicenseType
    {
        /// <summary>
        /// 试用版
        /// </summary>
        Trial,
        
        /// <summary>
        /// 标准版
        /// </summary>
        Standard,
        
        /// <summary>
        /// 专业版
        /// </summary>
        Professional,
        
        /// <summary>
        /// 企业版
        /// </summary>
        Enterprise,
        
        /// <summary>
        /// MVP版
        /// </summary>
        MVP
    }
    
    /// <summary>
    /// 许可信息
    /// </summary>
    [Serializable]
    public class LicenseInfo
    {
        /// <summary>
        /// 许可ID
        /// </summary>
        public string LicenseId { get; set; }
        
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        
        /// <summary>
        /// 产品版本
        /// </summary>
        public string ProductVersion { get; set; }
        
        /// <summary>
        /// 许可类型
        /// </summary>
        public LicenseType LicenseType { get; set; }
        
        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivatedAt { get; set; }
        
        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }
        
        /// <summary>
        /// 最大设备数
        /// </summary>
        public int MaxDevices { get; set; }
        
        /// <summary>
        /// 已激活设备数
        /// </summary>
        public int ActivatedDevices { get; set; }
        
        /// <summary>
        /// 许可特性
        /// </summary>
        public List<string> Features { get; set; }
        
        /// <summary>
        /// 客户信息
        /// </summary>
        public CustomerInfo Customer { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseInfo()
        {
            Features = new List<string>();
        }
        
        /// <summary>
        /// 检查许可是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return DateTime.Now < ExpiresAt && ActivatedDevices <= MaxDevices;
        }
        
        /// <summary>
        /// 检查是否支持指定特性
        /// </summary>
        /// <param name="feature">特性名称</param>
        /// <returns>是否支持</returns>
        public bool HasFeature(string feature)
        {
            return Features != null && Features.Contains(feature);
        }
        
        /// <summary>
        /// 获取剩余天数
        /// </summary>
        /// <returns>剩余天数</returns>
        public int GetRemainingDays()
        {
            var remaining = ExpiresAt - DateTime.Now;
            return Math.Max(0, (int)remaining.TotalDays);
        }
    } 
   
    /// <summary>
    /// 设备信息
    /// </summary>
    [Serializable]
    public class DeviceInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 操作系统
        /// </summary>
        public string OperatingSystem { get; set; }
        
        /// <summary>
        /// 处理器信息
        /// </summary>
        public string ProcessorInfo { get; set; }
        
        /// <summary>
        /// 内存大小（MB）
        /// </summary>
        public int MemorySize { get; set; }
        
        /// <summary>
        /// MAC地址
        /// </summary>
        public string MacAddress { get; set; }
        
        /// <summary>
        /// 设备指纹
        /// </summary>
        public string DeviceFingerprint { get; set; }
        
        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivatedAt { get; set; }
        
        /// <summary>
        /// 最后验证时间
        /// </summary>
        public DateTime LastValidatedAt { get; set; }
        
        /// <summary>
        /// 生成设备指纹
        /// </summary>
        /// <returns>设备指纹</returns>
        public string GenerateFingerprint()
        {
            var data = $"{DeviceId}|{OperatingSystem}|{ProcessorInfo}|{MacAddress}";
            return ComputeHash(data);
        }
        
        /// <summary>
        /// 计算哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>哈希值</returns>
        private string ComputeHash(string input)
        {
            // 简化的哈希计算，避免依赖问题
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(input));
        }
    }
    
    /// <summary>
    /// 客户信息
    /// </summary>
    [Serializable]
    public class CustomerInfo
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; }
        
        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }
        
        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string Email { get; set; }
        
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        
        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }
    }
    
    /// <summary>
    /// 激活结果
    /// </summary>
    [Serializable]
    public class ActivationResult
    {
        /// <summary>
        /// 激活是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }
        
        /// <summary>
        /// 许可信息
        /// </summary>
        public LicenseInfo LicenseInfo { get; set; }
        
        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceInfo DeviceInfo { get; set; }
        
        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivatedAt { get; set; }
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="licenseInfo">许可信息</param>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>激活结果</returns>
        public static ActivationResult Success(LicenseInfo licenseInfo, DeviceInfo deviceInfo)
        {
            return new ActivationResult
            {
                IsSuccess = true,
                LicenseInfo = licenseInfo,
                DeviceInfo = deviceInfo,
                ActivatedAt = DateTime.Now
            };
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="errorCode">错误代码</param>
        /// <returns>激活结果</returns>
        public static ActivationResult Failure(string errorMessage, string errorCode = null)
        {
            return new ActivationResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }
    }
}