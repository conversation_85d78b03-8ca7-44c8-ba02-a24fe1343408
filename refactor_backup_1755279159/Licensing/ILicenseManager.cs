using System;
using System.Threading.Tasks;

namespace DigitalHuman.Core.Licensing
{
    /// <summary>
    /// 许可管理器接口
    /// </summary>
    public interface ILicenseManager
    {
        /// <summary>
        /// 当前许可状态
        /// </summary>
        LicenseStatus CurrentStatus { get; }
        
        /// <summary>
        /// 设备是否已激活
        /// </summary>
        bool IsDeviceActivated { get; }
        
        /// <summary>
        /// 是否为MVP模式
        /// </summary>
        bool IsMVPMode { get; }
        
        /// <summary>
        /// 当前许可信息
        /// </summary>
        LicenseInfo CurrentLicense { get; }
        
        /// <summary>
        /// 许可状态变化事件
        /// </summary>
        event Action<LicenseStatus> OnLicenseStatusChanged;
        
        /// <summary>
        /// 设备激活事件
        /// </summary>
        event Action<DeviceInfo> OnDeviceActivated;
        
        /// <summary>
        /// 许可过期警告事件
        /// </summary>
        event Action<TimeSpan> OnLicenseExpiring;
        
        /// <summary>
        /// 验证激活码
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>验证结果</returns>
        Task<ActivationResult> ValidateActivationCodeAsync(string activationCode);
        
        /// <summary>
        /// 激活设备
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <returns>激活结果</returns>
        Task<ActivationResult> ActivateDeviceAsync(string activationCode);
        
        /// <summary>
        /// 停用设备
        /// </summary>
        /// <returns>停用结果</returns>
        Task<bool> DeactivateDeviceAsync();
        
        /// <summary>
        /// 刷新许可信息
        /// </summary>
        /// <returns>刷新结果</returns>
        Task<bool> RefreshLicenseAsync();
        
        /// <summary>
        /// 检查许可有效性
        /// </summary>
        /// <returns>许可是否有效</returns>
        Task<bool> ValidateLicenseAsync();
        
        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        DeviceInfo GetDeviceInfo();
        
        /// <summary>
        /// 设置MVP模式
        /// </summary>
        /// <param name="enabled">是否启用MVP模式</param>
        void SetMVPMode(bool enabled);
        
        /// <summary>
        /// 获取许可剩余时间
        /// </summary>
        /// <returns>剩余时间</returns>
        TimeSpan GetRemainingTime();
        
        /// <summary>
        /// 清除许可数据
        /// </summary>
        void ClearLicenseData();
    }
}