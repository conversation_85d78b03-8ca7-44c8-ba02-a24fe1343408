using System;
using System.Threading.Tasks;
using UnityEngine;
using DigitalHuman.Core.Authentication;
using DigitalHuman.Core.Logging;

namespace DigitalHuman.Core.DataSync.Examples
{
    /// <summary>
    /// 数据同步功能示例
    /// </summary>
    public class DataSyncExample : MonoBehaviour
    {
        [Header("测试配置")]
        /// <summary>
        /// 是否在启动时自动运行测试
        /// </summary>
        public bool AutoRunOnStart = false;
        
        /// <summary>
        /// 测试用户名
        /// </summary>
        public string TestUsername = "admin";
        
        /// <summary>
        /// 测试密码
        /// </summary>
        public string TestPassword = "admin123";
        
        // 私有字段
        private DataSyncManager _dataSyncManager;
        private AuthenticationManager _authManager;
        private ILogger _logger;
        
        /// <summary>
        /// 初始化
        /// </summary>
        void Start()
        {
            _logger = LogManager.Instance.GetLogger("DataSyncExample");
            _logger.Info("数据同步示例初始化");
            
            // 获取管理器实例
            _dataSyncManager = DataSyncManager.Instance;
            _authManager = AuthenticationManager.Instance;
            
            // 订阅同步事件
            _dataSyncManager.OnSyncProgress += OnSyncProgress;
            _dataSyncManager.OnSyncCompleted += OnSyncCompleted;
            _dataSyncManager.OnSyncError += OnSyncError;
            
            if (AutoRunOnStart)
            {
                StartCoroutine(RunTestAfterDelay());
            }
        }
        
        /// <summary>
        /// 延迟运行测试
        /// </summary>
        private System.Collections.IEnumerator RunTestAfterDelay()
        {
            yield return new WaitForSeconds(2f);
            _ = RunDataSyncTestAsync();
        }
        
        /// <summary>
        /// 运行数据同步测试
        /// </summary>
        public async Task RunDataSyncTestAsync()
        {
            _logger.Info("开始数据同步测试");
            
            try
            {
                // 1. 测试用户登录
                _logger.Info("步骤1: 测试用户登录");
                var loginResult = await _authManager.LoginAsync(TestUsername, TestPassword);
                if (!loginResult.IsSuccess)
                {
                    _logger.Error($"登录失败: {loginResult.ErrorMessage}");
                    return;
                }
                _logger.Info("用户登录成功");
                
                // 2. 启用云端同步
                _logger.Info("步骤2: 启用云端同步");
                _dataSyncManager.EnableCloudSync(true);
                
                // 3. 测试同步用户设置
                _logger.Info("步骤3: 测试同步用户设置");
                var userSettingsResult = await _dataSyncManager.SyncUserSettingsAsync();
                LogSyncResult("用户设置同步", userSettingsResult);
                
                // 4. 测试同步用户配置
                _logger.Info("步骤4: 测试同步用户配置");
                var userConfigResult = await _dataSyncManager.SyncUserConfigurationAsync();
                LogSyncResult("用户配置同步", userConfigResult);
                
                // 5. 测试同步对话历史
                _logger.Info("步骤5: 测试同步对话历史");
                var conversationResult = await _dataSyncManager.SyncConversationHistoryAsync(false);
                LogSyncResult("对话历史同步", conversationResult);
                
                // 6. 测试完整同步
                _logger.Info("步骤6: 测试完整同步");
                var fullSyncResult = await _dataSyncManager.PerformFullSyncAsync();
                LogSyncResult("完整同步", fullSyncResult);
                
                // 7. 测试检查冲突
                _logger.Info("步骤7: 测试检查同步冲突");
                var conflicts = await _dataSyncManager.CheckSyncConflictsAsync();
                _logger.Info($"发现 {conflicts.Count} 个同步冲突");
                
                // 8. 测试获取同步历史
                _logger.Info("步骤8: 测试获取同步历史");
                var history = await _dataSyncManager.GetSyncHistoryAsync(10);
                _logger.Info($"获取到 {history.Count} 条同步历史记录");
                
                _logger.Info("数据同步测试完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"数据同步测试过程中发生异常: {ex.Message}", ex);
            }
        }      
  
        /// <summary>
        /// 记录同步结果
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="result">同步结果</param>
        private void LogSyncResult(string operation, SyncResult result)
        {
            if (result.IsSuccess)
            {
                _logger.Info($"{operation}成功 - 耗时: {result.Duration.TotalSeconds:F2}秒, " +
                           $"上传: {result.UploadedItems}项, 下载: {result.DownloadedItems}项, " +
                           $"冲突: {result.ConflictCount}个");
            }
            else
            {
                _logger.Error($"{operation}失败: {result.ErrorMessage}");
            }
        }
        
        /// <summary>
        /// 同步进度事件处理
        /// </summary>
        /// <param name="progressInfo">进度信息</param>
        private void OnSyncProgress(SyncProgressInfo progressInfo)
        {
            _logger.Debug($"同步进度: {progressInfo.ProgressPercentage:F1}% - {progressInfo.CurrentOperation}");
        }
        
        /// <summary>
        /// 同步完成事件处理
        /// </summary>
        /// <param name="result">同步结果</param>
        private void OnSyncCompleted(SyncResult result)
        {
            if (result.IsSuccess)
            {
                _logger.Info($"同步完成 - 耗时: {result.Duration.TotalSeconds:F2}秒");
            }
            else
            {
                _logger.Error($"同步失败: {result.ErrorMessage}");
            }
        }
        
        /// <summary>
        /// 同步错误事件处理
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        private void OnSyncError(string errorMessage)
        {
            _logger.Error($"同步错误: {errorMessage}");
        }
        
        /// <summary>
        /// GUI界面
        /// </summary>
        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("数据同步测试面板", GUI.skin.box);
            
            GUILayout.Space(10);
            
            // 显示当前状态
            GUILayout.Label($"同步状态: {_dataSyncManager?.CurrentSyncStatus}");
            GUILayout.Label($"云端同步: {(_dataSyncManager?.IsCloudSyncEnabled == true ? "启用" : "禁用")}");
            GUILayout.Label($"用户登录: {(_authManager?.IsLoggedIn == true ? "已登录" : "未登录")}");
            
            GUILayout.Space(10);
            
            // 测试按钮
            if (GUILayout.Button("运行完整测试"))
            {
                _ = RunDataSyncTestAsync();
            }
            
            if (GUILayout.Button("测试用户设置同步"))
            {
                _ = _dataSyncManager?.SyncUserSettingsAsync();
            }
            
            if (GUILayout.Button("测试对话历史同步"))
            {
                _ = _dataSyncManager?.SyncConversationHistoryAsync(false);
            }
            
            if (GUILayout.Button("测试完整同步"))
            {
                _ = _dataSyncManager?.PerformFullSyncAsync();
            }
            
            if (GUILayout.Button("清除同步缓存"))
            {
                _dataSyncManager?.ClearSyncCache();
            }
            
            if (GUILayout.Button("切换云端同步"))
            {
                if (_dataSyncManager != null)
                {
                    _dataSyncManager.EnableCloudSync(!_dataSyncManager.IsCloudSyncEnabled);
                }
            }
            
            GUILayout.EndArea();
        }
        
        /// <summary>
        /// 清理资源
        /// </summary>
        void OnDestroy()
        {
            if (_dataSyncManager != null)
            {
                _dataSyncManager.OnSyncProgress -= OnSyncProgress;
                _dataSyncManager.OnSyncCompleted -= OnSyncCompleted;
                _dataSyncManager.OnSyncError -= OnSyncError;
            }
        }
    }
}