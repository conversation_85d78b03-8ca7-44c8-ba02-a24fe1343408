using System;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace DigitalHuman.Core.DataSync
{
    /// <summary>
    /// 数据同步管理器接口
    /// </summary>
    public interface IDataSyncManager
    {
        /// <summary>
        /// 是否启用云端同步
        /// </summary>
        bool IsCloudSyncEnabled { get; }
        
        /// <summary>
        /// 同步状态
        /// </summary>
        SyncStatus CurrentSyncStatus { get; }
        
        /// <summary>
        /// 同步进度事件
        /// </summary>
        event Action<SyncProgressInfo> OnSyncProgress;
        
        /// <summary>
        /// 同步完成事件
        /// </summary>
        event Action<SyncResult> OnSyncCompleted;
        
        /// <summary>
        /// 同步错误事件
        /// </summary>
        event Action<string> OnSyncError;
        
        /// <summary>
        /// 启用云端同步
        /// </summary>
        /// <param name="enable">是否启用</param>
        void EnableCloudSync(bool enable);
        
        /// <summary>
        /// 同步用户设置
        /// </summary>
        /// <returns>同步结果</returns>
        Task<SyncResult> SyncUserSettingsAsync();
        
        /// <summary>
        /// 同步对话历史
        /// </summary>
        /// <param name="includePrivate">是否包含私密对话</param>
        /// <returns>同步结果</returns>
        Task<SyncResult> SyncConversationHistoryAsync(bool includePrivate = false);
        
        /// <summary>
        /// 同步用户配置
        /// </summary>
        /// <returns>同步结果</returns>
        Task<SyncResult> SyncUserConfigurationAsync();
        
        /// <summary>
        /// 执行完整同步
        /// </summary>
        /// <returns>同步结果</returns>
        Task<SyncResult> PerformFullSyncAsync();
        
        /// <summary>
        /// 上传数据到云端
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="data">数据内容</param>
        /// <returns>上传结果</returns>
        Task<bool> UploadDataAsync(string dataType, object data);
        
        /// <summary>
        /// 从云端下载数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="dataType">数据类型</param>
        /// <returns>下载的数据</returns>
        Task<T> DownloadDataAsync<T>(string dataType);
        
        /// <summary>
        /// 检查同步冲突
        /// </summary>
        /// <returns>冲突列表</returns>
        Task<List<SyncConflict>> CheckSyncConflictsAsync();
        
        /// <summary>
        /// 解决同步冲突
        /// </summary>
        /// <param name="conflict">冲突信息</param>
        /// <param name="resolution">解决方案</param>
        /// <returns>解决结果</returns>
        Task<bool> ResolveSyncConflictAsync(SyncConflict conflict, ConflictResolution resolution);
        
        /// <summary>
        /// 获取同步历史
        /// </summary>
        /// <param name="limit">记录数量限制</param>
        /// <returns>同步历史列表</returns>
        Task<List<SyncHistoryRecord>> GetSyncHistoryAsync(int limit = 50);
        
        /// <summary>
        /// 清除本地同步缓存
        /// </summary>
        void ClearSyncCache();
    }
}