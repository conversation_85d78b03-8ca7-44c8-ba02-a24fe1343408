using System;
using System.Collections.Generic;

namespace DigitalHuman.Core.DataSync
{
    /// <summary>
    /// 同步状态枚举
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle,
        
        /// <summary>
        /// 同步中
        /// </summary>
        Syncing,
        
        /// <summary>
        /// 上传中
        /// </summary>
        Uploading,
        
        /// <summary>
        /// 下载中
        /// </summary>
        Downloading,
        
        /// <summary>
        /// 检查冲突
        /// </summary>
        CheckingConflicts,
        
        /// <summary>
        /// 同步完成
        /// </summary>
        Completed,
        
        /// <summary>
        /// 同步失败
        /// </summary>
        Failed
    }
    
    /// <summary>
    /// 同步进度信息
    /// </summary>
    [Serializable]
    public class SyncProgressInfo
    {
        /// <summary>
        /// 当前状态
        /// </summary>
        public SyncStatus Status { get; set; }
        
        /// <summary>
        /// 进度百分比 (0-100)
        /// </summary>
        public float ProgressPercentage { get; set; }
        
        /// <summary>
        /// 当前操作描述
        /// </summary>
        public string CurrentOperation { get; set; }
        
        /// <summary>
        /// 已处理项目数
        /// </summary>
        public int ProcessedItems { get; set; }
        
        /// <summary>
        /// 总项目数
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// 估计剩余时间（秒）
        /// </summary>
        public int EstimatedRemainingSeconds { get; set; }
    }
    
    /// <summary>
    /// 同步结果
    /// </summary>
    [Serializable]
    public class SyncResult
    {
        /// <summary>
        /// 同步是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 同步开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 同步结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 同步持续时间
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;
        
        /// <summary>
        /// 上传的数据项数量
        /// </summary>
        public int UploadedItems { get; set; }
        
        /// <summary>
        /// 下载的数据项数量
        /// </summary>
        public int DownloadedItems { get; set; }
        
        /// <summary>
        /// 冲突数量
        /// </summary>
        public int ConflictCount { get; set; }
        
        /// <summary>
        /// 同步的数据类型列表
        /// </summary>
        public List<string> SyncedDataTypes { get; set; }
        
        /// <summary>
        /// 详细信息
        /// </summary>
        public Dictionary<string, object> Details { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncResult()
        {
            SyncedDataTypes = new List<string>();
            Details = new Dictionary<string, object>();
        }
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <returns>成功的同步结果</returns>
        public static SyncResult Success()
        {
            return new SyncResult
            {
                IsSuccess = true,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now
            };
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>失败的同步结果</returns>
        public static SyncResult Failure(string errorMessage)
        {
            return new SyncResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now
            };
        }
    }
    
    /// <summary>
    /// 同步冲突信息
    /// </summary>
    [Serializable]
    public class SyncConflict
    {
        /// <summary>
        /// 冲突ID
        /// </summary>
        public string ConflictId { get; set; }
        
        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
        
        /// <summary>
        /// 数据键
        /// </summary>
        public string DataKey { get; set; }
        
        /// <summary>
        /// 本地版本
        /// </summary>
        public object LocalVersion { get; set; }
        
        /// <summary>
        /// 远程版本
        /// </summary>
        public object RemoteVersion { get; set; }
        
        /// <summary>
        /// 本地修改时间
        /// </summary>
        public DateTime LocalModifiedTime { get; set; }
        
        /// <summary>
        /// 远程修改时间
        /// </summary>
        public DateTime RemoteModifiedTime { get; set; }
        
        /// <summary>
        /// 冲突描述
        /// </summary>
        public string Description { get; set; }
    }
    
    /// <summary>
    /// 冲突解决方案枚举
    /// </summary>
    public enum ConflictResolution
    {
        /// <summary>
        /// 使用本地版本
        /// </summary>
        UseLocal,
        
        /// <summary>
        /// 使用远程版本
        /// </summary>
        UseRemote,
        
        /// <summary>
        /// 合并版本
        /// </summary>
        Merge,
        
        /// <summary>
        /// 跳过此冲突
        /// </summary>
        Skip
    }
    
    /// <summary>
    /// 同步历史记录
    /// </summary>
    [Serializable]
    public class SyncHistoryRecord
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string RecordId { get; set; }
        
        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime SyncTime { get; set; }
        
        /// <summary>
        /// 同步类型
        /// </summary>
        public string SyncType { get; set; }
        
        /// <summary>
        /// 同步结果
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 同步持续时间（毫秒）
        /// </summary>
        public long DurationMs { get; set; }
        
        /// <summary>
        /// 同步的数据项数量
        /// </summary>
        public int ItemCount { get; set; }
        
        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public long DataSize { get; set; }
    }
    
    /// <summary>
    /// 数据同步项
    /// </summary>
    [Serializable]
    public class SyncDataItem
    {
        /// <summary>
        /// 数据键
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
        
        /// <summary>
        /// 数据内容
        /// </summary>
        public object Data { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
        
        /// <summary>
        /// 数据版本
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// 数据校验和
        /// </summary>
        public string Checksum { get; set; }
        
        /// <summary>
        /// 是否为私密数据
        /// </summary>
        public bool IsPrivate { get; set; }
        
        /// <summary>
        /// 数据标签
        /// </summary>
        public List<string> Tags { get; set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncDataItem()
        {
            Tags = new List<string>();
        }
    }
}