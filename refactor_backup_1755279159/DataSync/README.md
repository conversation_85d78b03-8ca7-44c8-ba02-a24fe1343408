# 数据同步系统 (DataSync System)

## 概述

数据同步系统是数字人管理系统的核心组件之一，负责用户数据在本地设备和云端服务器之间的双向同步。系统采用模块化设计，提供用户设置、对话历史、个人配置等数据的自动和手动同步功能，支持冲突检测与解决、同步历史记录、进度监控等高级特性。

## 架构设计

```
┌─────────────────────────────────────────────────────────┐
│                  DataSyncManager                        │
│                 (数据同步管理器)                         │
├─────────────────────────────────────────────────────────┤
│  IDataSyncManager 接口层                                │
├─────────────────────────────────────────────────────────┤
│           数据同步核心功能                               │
├─────────────────────────────────────────────────────────┤
│ 用户设置同步 │ 对话历史同步 │ 配置同步 │ 冲突解决 │ ... │
├─────────────────────────────────────────────────────────┤
│                 SyncModels                              │
│              (数据模型和枚举)                            │
├─────────────────────────────────────────────────────────┤
│  认证管理器  │  配置管理器  │  日志系统  │  事件系统  │
└─────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. DataSyncManager (数据同步管理器)
- **文件**: `DataSyncManager.cs`
- **功能**: 统一的数据同步管理入口，负责协调各种同步操作
- **特性**: 单例模式、异步操作、事件驱动、自动重试

### 2. IDataSyncManager (同步管理器接口)
- **文件**: `IDataSyncManager.cs`
- **功能**: 定义数据同步系统的标准契约
- **特性**: 完整的接口定义、事件支持、异步方法

### 3. SyncModels (数据模型)
- **文件**: `Models/SyncModels.cs`
- **功能**: 定义同步系统使用的所有数据结构
- **包含**: 状态枚举、进度信息、结果数据、冲突信息等

### 4. DataSyncExample (使用示例)
- **文件**: `Examples/DataSyncExample.cs`
- **功能**: 展示数据同步系统的各种使用方式
- **特性**: 完整示例、最佳实践、错误处理

## 主要功能

### 数据同步类型

1. **用户设置同步**
   - 界面主题和布局偏好
   - 语言和地区设置
   - 通知和提醒配置
   - 功能开关状态

2. **对话历史同步**
   - 普通对话记录
   - 私密对话记录（可选）
   - 对话上下文和关联信息
   - 对话统计和分析数据

3. **用户配置同步**
   - 个性化设置
   - 系统配置参数
   - 插件和扩展配置
   - 自定义快捷键和手势

### 高级功能

1. **冲突检测与解决**
   - 自动检测本地和云端数据冲突
   - 提供多种冲突解决策略
   - 支持手动和自动冲突解决
   - 冲突解决历史记录

2. **同步进度监控**
   - 实时进度报告
   - 详细操作状态
   - 预估剩余时间
   - 数据传输统计

3. **同步历史管理**
   - 完整的同步操作记录
   - 成功率统计分析
   - 错误日志和诊断信息
   - 历史数据查询和导出

## 使用指南

### 基本使用

```csharp
// 获取数据同步管理器实例
var syncManager = DataSyncManager.Instance;

// 启用云端同步
syncManager.EnableCloudSync(true);

// 执行用户设置同步
var result = await syncManager.SyncUserSettingsAsync();
if (result.IsSuccess)
{
    Debug.Log("用户设置同步成功");
}

// 执行完整同步
var fullResult = await syncManager.PerformFullSyncAsync();
Debug.Log($"完整同步完成，耗时: {fullResult.Duration.TotalSeconds:F2}秒");
```

### 事件订阅

```csharp
// 订阅同步进度事件
syncManager.OnSyncProgress += (progressInfo) =>
{
    Debug.Log($"同步进度: {progressInfo.ProgressPercentage}% - {progressInfo.CurrentOperation}");
    UpdateProgressBar(progressInfo.ProgressPercentage);
};

// 订阅同步完成事件
syncManager.OnSyncCompleted += (result) =>
{
    if (result.IsSuccess)
    {
        Debug.Log($"同步成功，处理了 {result.UploadedItems + result.DownloadedItems} 个项目");
        ShowSuccessNotification("数据同步完成");
    }
    else
    {
        Debug.LogError($"同步失败: {result.ErrorMessage}");
        ShowErrorNotification($"同步失败: {result.ErrorMessage}");
    }
};

// 订阅同步错误事件
syncManager.OnSyncError += (errorMessage) =>
{
    Debug.LogError($"同步错误: {errorMessage}");
    HandleSyncError(errorMessage);
};
```

### 冲突处理

```csharp
// 检查同步冲突
var conflicts = await syncManager.CheckSyncConflictsAsync();

if (conflicts.Count > 0)
{
    Debug.Log($"发现 {conflicts.Count} 个同步冲突");
    
    foreach (var conflict in conflicts)
    {
        Debug.Log($"冲突: {conflict.DataType}.{conflict.DataKey}");
        Debug.Log($"本地修改: {conflict.LocalModifiedTime}");
        Debug.Log($"远程修改: {conflict.RemoteModifiedTime}");
        
        // 自动解决策略：使用最新修改的版本
        ConflictResolution resolution = conflict.LocalModifiedTime > conflict.RemoteModifiedTime
            ? ConflictResolution.UseLocal
            : ConflictResolution.UseRemote;
        
        bool resolved = await syncManager.ResolveSyncConflictAsync(conflict, resolution);
        Debug.Log($"冲突解决结果: {(resolved ? "成功" : "失败")}");
    }
}
```

### 数据上传下载

```csharp
// 上传自定义数据
var userData = new UserPreferences
{
    Theme = "Dark",
    Language = "zh-CN",
    NotificationsEnabled = true
};

bool uploaded = await syncManager.UploadDataAsync("UserPreferences", userData);
if (uploaded)
{
    Debug.Log("用户偏好设置上传成功");
}

// 下载自定义数据
var downloadedData = await syncManager.DownloadDataAsync<UserPreferences>("UserPreferences");
if (downloadedData != null)
{
    ApplyUserPreferences(downloadedData);
    Debug.Log("用户偏好设置下载并应用成功");
}
```

### 同步历史查询

```csharp
// 获取最近的同步历史
var history = await syncManager.GetSyncHistoryAsync(10);

Debug.Log($"最近 {history.Count} 次同步记录:");
foreach (var record in history)
{
    string status = record.IsSuccess ? "成功" : "失败";
    string duration = $"{record.DurationMs / 1000.0:F1}秒";
    
    Debug.Log($"{record.SyncTime:MM-dd HH:mm} | {record.SyncType} | {status} | {duration}");
    
    if (!record.IsSuccess)
    {
        Debug.Log($"  错误: {record.ErrorMessage}");
    }
}

// 计算同步成功率
int successCount = history.Count(r => r.IsSuccess);
float successRate = history.Count > 0 ? (float)successCount / history.Count * 100 : 0;
Debug.Log($"同步成功率: {successRate:F1}%");
```

## 配置管理

### 同步配置参数

```csharp
public class DataSyncManager : SingletonManager<DataSyncManager>
{
    [Header("同步配置")]
    public bool EnableCloudSync = false;           // 是否启用云端同步
    public int AutoSyncIntervalMinutes = 30;       // 自动同步间隔（分钟）
    public int MaxRetryCount = 3;                  // 最大重试次数
    public int SyncTimeoutSeconds = 60;            // 同步超时时间（秒）
}
```

### 运行时配置

```csharp
// 动态调整同步配置
var syncManager = DataSyncManager.Instance;

// 设置自动同步间隔
syncManager.AutoSyncIntervalMinutes = 15; // 15分钟

// 设置重试次数
syncManager.MaxRetryCount = 5;

// 设置超时时间
syncManager.SyncTimeoutSeconds = 120; // 2分钟
```

## 数据模型

### 同步状态

```csharp
public enum SyncStatus
{
    Idle,                // 空闲状态
    Syncing,            // 同步中
    Uploading,          // 上传中
    Downloading,        // 下载中
    CheckingConflicts,  // 检查冲突
    Completed,          // 同步完成
    Failed              // 同步失败
}
```

### 同步结果

```csharp
public class SyncResult
{
    public bool IsSuccess { get; set; }                    // 是否成功
    public string ErrorMessage { get; set; }               // 错误消息
    public DateTime StartTime { get; set; }                // 开始时间
    public DateTime EndTime { get; set; }                  // 结束时间
    public TimeSpan Duration => EndTime - StartTime;       // 持续时间
    public int UploadedItems { get; set; }                 // 上传项目数
    public int DownloadedItems { get; set; }               // 下载项目数
    public int ConflictCount { get; set; }                 // 冲突数量
    public List<string> SyncedDataTypes { get; set; }      // 同步数据类型
}
```

### 冲突信息

```csharp
public class SyncConflict
{
    public string ConflictId { get; set; }                 // 冲突ID
    public string DataType { get; set; }                   // 数据类型
    public string DataKey { get; set; }                    // 数据键
    public object LocalVersion { get; set; }               // 本地版本
    public object RemoteVersion { get; set; }              // 远程版本
    public DateTime LocalModifiedTime { get; set; }        // 本地修改时间
    public DateTime RemoteModifiedTime { get; set; }       // 远程修改时间
    public string Description { get; set; }                // 冲突描述
}
```

## 集成示例

### 应用启动时的自动同步

```csharp
public class ApplicationManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    async void Start()
    {
        _syncManager = DataSyncManager.Instance;
        
        // 检查用户登录状态
        var authManager = AuthenticationManager.Instance;
        if (authManager.IsLoggedIn)
        {
            // 启用云端同步
            _syncManager.EnableCloudSync(true);
            
            // 执行启动同步
            try
            {
                var result = await _syncManager.SyncUserSettingsAsync();
                if (result.IsSuccess)
                {
                    Debug.Log("启动同步完成，应用用户设置");
                    ApplyUserSettings();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"启动同步失败: {ex.Message}");
            }
        }
    }
}
```

### 设置变更时的增量同步

```csharp
public class SettingsManager : MonoBehaviour
{
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    public async void OnSettingChanged(string settingKey, object newValue)
    {
        // 立即保存到本地
        SaveSettingLocally(settingKey, newValue);
        
        // 如果启用了云端同步，上传更改
        if (_syncManager.IsCloudSyncEnabled)
        {
            var settingData = new Dictionary<string, object>
            {
                [settingKey] = newValue,
                ["lastModified"] = DateTime.Now
            };
            
            bool uploaded = await _syncManager.UploadDataAsync($"Setting_{settingKey}", settingData);
            if (uploaded)
            {
                Debug.Log($"设置 {settingKey} 已同步到云端");
            }
        }
    }
}
```

### 冲突解决UI集成

```csharp
public class ConflictResolutionUI : MonoBehaviour
{
    [SerializeField] private GameObject conflictDialogPrefab;
    private DataSyncManager _syncManager;
    
    void Start()
    {
        _syncManager = DataSyncManager.Instance;
        _syncManager.OnSyncCompleted += OnSyncCompleted;
    }
    
    private async void OnSyncCompleted(SyncResult result)
    {
        if (result.ConflictCount > 0)
        {
            // 检查具体冲突
            var conflicts = await _syncManager.CheckSyncConflictsAsync();
            
            foreach (var conflict in conflicts)
            {
                // 显示冲突解决对话框
                ShowConflictDialog(conflict);
            }
        }
    }
    
    private async void ShowConflictDialog(SyncConflict conflict)
    {
        var dialog = Instantiate(conflictDialogPrefab);
        var conflictUI = dialog.GetComponent<ConflictDialogUI>();
        
        conflictUI.SetConflictInfo(conflict);
        conflictUI.OnResolutionSelected += async (resolution) =>
        {
            bool resolved = await _syncManager.ResolveSyncConflictAsync(conflict, resolution);
            if (resolved)
            {
                Debug.Log($"冲突已解决: {conflict.DataKey}");
            }
            
            Destroy(dialog);
        };
    }
}
```

## 最佳实践

### 1. 错误处理和重试

```csharp
public async Task<SyncResult> SafeSyncWithRetry(Func<Task<SyncResult>> syncOperation, int maxRetries = 3)
{
    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            var result = await syncOperation();
            if (result.IsSuccess)
            {
                return result;
            }
            
            Debug.LogWarning($"同步尝试 {attempt} 失败: {result.ErrorMessage}");
            
            if (attempt < maxRetries)
            {
                // 指数退避延迟
                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt)));
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"同步尝试 {attempt} 异常: {ex.Message}");
            
            if (attempt == maxRetries)
            {
                return SyncResult.Failure($"重试 {maxRetries} 次后仍然失败: {ex.Message}");
            }
        }
    }
    
    return SyncResult.Failure("所有重试尝试都失败了");
}
```

### 2. 网络状态检查

```csharp
private bool IsNetworkAvailable()
{
    return Application.internetReachability != NetworkReachability.NotReachable;
}

public async Task<SyncResult> SyncWithNetworkCheck()
{
    if (!IsNetworkAvailable())
    {
        return SyncResult.Failure("网络不可用");
    }
    
    return await _syncManager.PerformFullSyncAsync();
}
```

### 3. 数据备份和恢复

```csharp
public async void BackupDataBeforeSync()
{
    try
    {
        var userData = CollectUserData();
        var backupData = JsonUtility.ToJson(userData);
        
        // 保存备份到本地
        File.WriteAllText(GetBackupPath(), backupData);
        
        Debug.Log("数据备份完成");
    }
    catch (Exception ex)
    {
        Debug.LogError($"数据备份失败: {ex.Message}");
    }
}
```

### 4. 性能优化

```csharp
// 批量处理同步数据
public async Task<bool> BatchUploadData(Dictionary<string, object> dataItems)
{
    const int batchSize = 10;
    var batches = dataItems.Chunk(batchSize);
    
    foreach (var batch in batches)
    {
        var tasks = batch.Select(kvp => _syncManager.UploadDataAsync(kvp.Key, kvp.Value));
        var results = await Task.WhenAll(tasks);
        
        if (results.Any(r => !r))
        {
            Debug.LogWarning("批量上传中有部分失败");
        }
    }
    
    return true;
}
```

## 故障排除

### 常见问题

1. **同步失败**
   - 检查网络连接状态
   - 验证用户认证状态
   - 查看详细错误日志

2. **冲突频繁**
   - 检查系统时间同步
   - 优化数据更新策略
   - 考虑增加同步频率

3. **性能问题**
   - 减少同步数据量
   - 启用数据压缩
   - 优化网络请求

4. **数据丢失**
   - 启用数据备份
   - 检查同步历史
   - 验证数据完整性

### 调试工具

```csharp
// 启用详细日志
public class SyncDebugger
{
    public static void EnableDetailedLogging()
    {
        var logger = LogManager.Instance.GetLogger("DataSync");
        logger.SetLogLevel(LogLevel.Debug);
    }
    
    public static async void DiagnoseSyncIssues()
    {
        var syncManager = DataSyncManager.Instance;
        
        Debug.Log($"云端同步状态: {syncManager.IsCloudSyncEnabled}");
        Debug.Log($"当前同步状态: {syncManager.CurrentSyncStatus}");
        
        // 检查网络状态
        Debug.Log($"网络状态: {Application.internetReachability}");
        
        // 检查认证状态
        var authManager = AuthenticationManager.Instance;
        Debug.Log($"用户登录状态: {authManager.IsLoggedIn}");
        
        // 获取同步历史
        var history = await syncManager.GetSyncHistoryAsync(5);
        Debug.Log($"最近5次同步记录: {history.Count}");
        
        foreach (var record in history)
        {
            Debug.Log($"  {record.SyncTime}: {record.SyncType} - {(record.IsSuccess ? "成功" : "失败")}");
        }
    }
}
```

## 扩展开发

### 自定义同步数据类型

```csharp
public class CustomDataSyncExtension
{
    private readonly DataSyncManager _syncManager;
    
    public CustomDataSyncExtension()
    {
        _syncManager = DataSyncManager.Instance;
    }
    
    public async Task<SyncResult> SyncCustomDataAsync(string dataType, object data)
    {
        try
        {
            // 上传自定义数据
            bool uploaded = await _syncManager.UploadDataAsync(dataType, data);
            if (!uploaded)
            {
                return SyncResult.Failure("上传自定义数据失败");
            }
            
            // 下载并验证数据
            var downloadedData = await _syncManager.DownloadDataAsync<object>(dataType);
            if (downloadedData == null)
            {
                return SyncResult.Failure("下载自定义数据失败");
            }
            
            return SyncResult.Success();
        }
        catch (Exception ex)
        {
            return SyncResult.Failure($"同步自定义数据异常: {ex.Message}");
        }
    }
}
```

### 自定义冲突解决策略

```csharp
public class CustomConflictResolver
{
    public static ConflictResolution ResolveConflict(SyncConflict conflict)
    {
        // 根据数据类型选择不同策略
        switch (conflict.DataType)
        {
            case "UserSettings":
                // 用户设置优先使用最新修改的版本
                return conflict.LocalModifiedTime > conflict.RemoteModifiedTime
                    ? ConflictResolution.UseLocal
                    : ConflictResolution.UseRemote;
            
            case "ConversationHistory":
                // 对话历史尝试合并
                return ConflictResolution.Merge;
            
            case "UserConfiguration":
                // 配置数据优先使用本地版本
                return ConflictResolution.UseLocal;
            
            default:
                // 默认跳过未知类型的冲突
                return ConflictResolution.Skip;
        }
    }
}
```

## 注意事项

1. **网络依赖**: 云端同步功能需要稳定的网络连接
2. **用户认证**: 大部分同步操作需要用户已登录
3. **数据安全**: 敏感数据在传输和存储时应加密
4. **性能影响**: 大量数据同步可能影响应用性能
5. **存储限制**: 注意云端存储的容量限制
6. **版本兼容**: 确保不同版本的应用数据格式兼容

## 依赖项

- `DigitalHuman.Core.Base`: 单例管理器基类
- `DigitalHuman.Core.Authentication`: 用户认证管理
- `DigitalHuman.Core.Configuration`: 配置管理
- `DigitalHuman.Core.Logging`: 日志记录系统
- `UnityEngine`: Unity 引擎核心功能

## 相关文档

- [DataSyncManager 详细文档](../../docs/datasync/DataSyncManager.md)
- [IDataSyncManager 接口文档](../../docs/datasync/IDataSyncManager.md)
- [数据同步模型文档](../../docs/datasync/SyncModels.md)
- [认证管理器文档](../Authentication/README.md)
- [配置管理器文档](../Configuration/README.md)
- [单例管理器文档](../Base/README.md)

## 版本历史

- **v1.0**: 基础同步功能实现
- **v1.1**: 添加冲突检测和解决
- **v1.2**: 增加同步历史记录
- **v1.3**: 优化性能和错误处理
- **v1.4**: 添加批量操作支持