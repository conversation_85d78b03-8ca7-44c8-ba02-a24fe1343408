#!/bin/bash

echo "=== 最小MVP编译测试 ==="
echo "测试时间: $(date)"
echo ""

# Unity路径
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
PROJECT_PATH="$(pwd)"

# 检查Unity
if [ ! -f "$UNITY_PATH" ]; then
    echo "❌ Unity编辑器未找到"
    exit 1
fi

echo "🎮 Unity版本: $($UNITY_PATH -version)"
echo "📁 项目路径: $PROJECT_PATH"
echo ""

# 检查MVP文件
echo "🔍 检查MVP文件结构..."
MVP_FILES=(
    "Assets/Scripts/MVP/MinimalMVPManager.cs:MVP管理器"
    "Assets/Scripts/Editor/BuildScript.cs:构建脚本"
    "Assets/UI/MVP/MinimalMVP.uxml:UI布局"
    "Assets/UI/MVP/MinimalMVP.uss:UI样式"
    "Assets/Scenes/MinimalMVPScene.unity:MVP场景"
)

ALL_FILES_EXIST=true
for item in "${MVP_FILES[@]}"; do
    file=$(echo $item | cut -d':' -f1)
    name=$(echo $item | cut -d':' -f2)
    if [ -f "$file" ]; then
        echo "  ✅ $name"
    else
        echo "  ❌ $name (文件: $file)"
        ALL_FILES_EXIST=false
    fi
done

if [ "$ALL_FILES_EXIST" = false ]; then
    echo ""
    echo "❌ 部分文件缺失，无法进行编译测试"
    exit 1
fi

echo ""
echo "🧪 执行编译测试..."

# 创建临时日志文件
LOG_FILE="$PROJECT_PATH/mvp_test.log"

# 执行Unity编译测试
"$UNITY_PATH" \
    -batchmode \
    -quit \
    -projectPath "$PROJECT_PATH" \
    -executeMethod DigitalHuman.Editor.BuildScript.QuickBuildTest \
    -logFile "$LOG_FILE" &

# 获取Unity进程ID
UNITY_PID=$!

echo "🔄 Unity编译测试进行中 (PID: $UNITY_PID)..."

# 等待Unity完成（最多等待60秒）
TIMEOUT=60
ELAPSED=0
while kill -0 $UNITY_PID 2>/dev/null && [ $ELAPSED -lt $TIMEOUT ]; do
    echo -n "."
    sleep 2
    ELAPSED=$((ELAPSED + 2))
done

echo ""

# 检查Unity是否还在运行
if kill -0 $UNITY_PID 2>/dev/null; then
    echo "⏰ Unity测试超时，强制结束进程"
    kill $UNITY_PID
    sleep 2
    if kill -0 $UNITY_PID 2>/dev/null; then
        kill -9 $UNITY_PID
    fi
    echo "❌ 编译测试超时失败"
    exit 1
fi

echo ""
echo "📋 检查测试结果..."

# 检查日志文件
if [ -f "$LOG_FILE" ]; then
    echo "📄 日志文件大小: $(wc -l < "$LOG_FILE") 行"
    
    # 检查是否有编译错误
    if grep -q "CompilerError" "$LOG_FILE"; then
        echo "❌ 发现编译错误:"
        grep "CompilerError" "$LOG_FILE" | head -5
        echo ""
        echo "💡 查看完整日志: cat $LOG_FILE"
        exit 1
    fi
    
    # 检查是否有严重错误
    if grep -q "Error:" "$LOG_FILE"; then
        echo "⚠️  发现错误信息:"
        grep "Error:" "$LOG_FILE" | head -3
        echo ""
    fi
    
    # 检查成功信息
    if grep -q "快速构建测试通过" "$LOG_FILE"; then
        echo "✅ 编译测试通过！"
    else
        echo "⚠️  未找到明确的成功信息"
    fi
    
else
    echo "❌ 日志文件未生成"
    exit 1
fi

echo ""
echo "🎯 MVP编译能力评估:"

# 检查脚本语法
echo "  📝 C#脚本语法检查..."
CS_FILES=$(find Assets/Scripts/MVP Assets/Scripts/Editor -name "*.cs" 2>/dev/null | wc -l)
echo "    发现 $CS_FILES 个C#文件"

# 检查UI文件
echo "  🎨 UI文件检查..."
if [ -f "Assets/UI/MVP/MinimalMVP.uxml" ] && [ -f "Assets/UI/MVP/MinimalMVP.uss" ]; then
    echo "    ✅ UI文件完整"
else
    echo "    ❌ UI文件不完整"
fi

# 检查场景文件
echo "  🎬 场景文件检查..."
if [ -f "Assets/Scenes/MinimalMVPScene.unity" ]; then
    SCENE_SIZE=$(wc -c < "Assets/Scenes/MinimalMVPScene.unity")
    echo "    ✅ 场景文件存在 (大小: $SCENE_SIZE bytes)"
else
    echo "    ❌ 场景文件缺失"
fi

echo ""
echo "💡 下一步建议:"
echo "  1. 在Unity编辑器中打开项目"
echo "  2. 检查Console窗口是否有错误"
echo "  3. 运行完整构建: ./build_minimal_mvp.sh"
echo "  4. 测试MVP功能"

echo ""
echo "🧹 清理临时文件..."
if [ -f "$LOG_FILE" ]; then
    rm "$LOG_FILE"
    echo "  ✅ 已删除日志文件"
fi

echo ""
echo "=== 编译测试完成 ==="
echo "结果: MVP项目结构完整，可以进行编译"