#!/bin/bash

# 主界面管理器测试验证脚本
# 用于验证MainUIManager相关测试的完整性和正确性

echo "=== 主界面管理器测试验证 ==="
echo "验证时间: $(date)"
echo ""

# 检查测试文件是否存在
echo "1. 检查测试文件..."
if [ -f "Assets/Tests/Editor/MainUIManagerTests.cs" ]; then
    echo "✓ MainUIManagerTests.cs 存在"
else
    echo "✗ MainUIManagerTests.cs 不存在"
    exit 1
fi

# 检查源文件是否存在
echo ""
echo "2. 检查源文件..."
if [ -f "Assets/Scripts/UI/MainUIManager.cs" ]; then
    echo "✓ MainUIManager.cs 存在"
else
    echo "✗ MainUIManager.cs 不存在"
    exit 1
fi

# 统计测试方法数量
echo ""
echo "3. 统计测试覆盖..."
test_methods=$(grep -c "\[Test\]" Assets/Tests/Editor/MainUIManagerTests.cs)
echo "测试方法数量: $test_methods"

# 检查关键测试方法
echo ""
echo "4. 检查关键测试方法..."

# 基础功能测试
if grep -q "TestMainUIManager_Initialization_ShouldSucceed" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 初始化测试"
else
    echo "✗ 缺少初始化测试"
fi

# 页面导航测试
if grep -q "ShowPage.*ShouldSetCorrectCurrentPage" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 页面导航测试"
else
    echo "✗ 缺少页面导航测试"
fi

# 状态更新测试
if grep -q "UpdateNetworkStatus.*ShouldUpdateCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 网络状态更新测试"
else
    echo "✗ 缺少网络状态更新测试"
fi

if grep -q "UpdateActivationStatus.*ShouldUpdateCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 激活状态更新测试"
else
    echo "✗ 缺少激活状态更新测试"
fi

# 渲染模式测试
if grep -q "UpdateRenderMode.*ShouldUpdateCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 渲染模式更新测试"
else
    echo "✗ 缺少渲染模式更新测试"
fi

# 语音引擎状态测试
if grep -q "UpdateVoiceEngineStatus.*ShouldUpdateCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 语音引擎状态测试"
else
    echo "✗ 缺少语音引擎状态测试"
fi

# AI服务状态测试
if grep -q "UpdateAIServiceStatus.*ShouldUpdateCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ AI服务状态测试"
else
    echo "✗ 缺少AI服务状态测试"
fi

# 显示适配测试
if grep -q "AdaptToDisplay.*ShouldHandleCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 显示适配测试"
else
    echo "✗ 缺少显示适配测试"
fi

# 激活对话框测试
if grep -q "ShowActivationDialog.*ShouldNotThrowException" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 激活对话框测试"
else
    echo "✗ 缺少激活对话框测试"
fi

# 事件处理测试
if grep -q "EventHandlers.*ShouldBeSetupCorrectly" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 事件处理测试"
else
    echo "✗ 缺少事件处理测试"
fi

# 异常处理测试
if grep -q "UIElementsNull.*ShouldNotCauseExceptions" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 异常处理测试"
else
    echo "✗ 缺少异常处理测试"
fi

# 压力测试
if grep -q "StressTest.*ShouldMaintainStability" Assets/Tests/Editor/MainUIManagerTests.cs; then
    echo "✓ 压力测试"
else
    echo "✗ 缺少压力测试"
fi

echo ""
echo "5. 检查测试运行器集成..."
if grep -q "RunMainUIManagerTests" Assets/Tests/Editor/TestRunner.cs; then
    echo "✓ 测试运行器已集成"
else
    echo "✗ 测试运行器未集成"
fi

echo ""
echo "6. 检查快速验证集成..."
if grep -q "ValidateMainUIManagerBasicFunctionality" Assets/Tests/Editor/QuickValidationTests.cs; then
    echo "✓ 快速验证已集成"
else
    echo "✗ 快速验证未集成"
fi

echo ""
echo "7. 检查测试报告集成..."
if grep -q "主界面管理器.*MainUIManager" Assets/Tests/Editor/TestReportGenerator.cs; then
    echo "✓ 测试报告已集成"
else
    echo "✗ 测试报告未集成"
fi

# 检查代码质量
echo ""
echo "8. 代码质量检查..."

# 检查中文注释
chinese_comments=$(grep -c "/// .*[\u4e00-\u9fa5]" Assets/Tests/Editor/MainUIManagerTests.cs)
echo "中文注释数量: $chinese_comments"

# 检查测试方法命名规范
naming_violations=$(grep -c "public void Test.*(" Assets/Tests/Editor/MainUIManagerTests.cs)
echo "命名规范检查: $naming_violations 个方法符合Test开头规范"

# 检查Assert使用
assert_count=$(grep -c "Assert\." Assets/Tests/Editor/MainUIManagerTests.cs)
echo "Assert断言数量: $assert_count"

echo ""
echo "=== 验证完成 ==="

# 计算覆盖率评估
if [ $test_methods -ge 30 ]; then
    echo "测试覆盖率评估: 优秀 (≥30个测试方法)"
elif [ $test_methods -ge 20 ]; then
    echo "测试覆盖率评估: 良好 (≥20个测试方法)"
elif [ $test_methods -ge 10 ]; then
    echo "测试覆盖率评估: 一般 (≥10个测试方法)"
else
    echo "测试覆盖率评估: 需要改进 (<10个测试方法)"
fi

echo ""
echo "建议："
echo "1. 确保所有公共方法都有对应的测试用例"
echo "2. 添加边界条件和异常情况的测试"
echo "3. 定期运行测试确保代码质量"
echo "4. 保持测试用例的可维护性和可读性"

echo ""
echo "可以通过以下方式运行测试："
echo "1. Unity编辑器: DigitalHuman/Tests/Run Main UI Manager Tests"
echo "2. Unity Test Runner: Window > General > Test Runner"
echo "3. 快速验证: DigitalHuman/Tests/Run All Quick Validation Tests"