# 数字人管理系统 (Digital Avatar Management System)

[![Unity Version](https://img.shields.io/badge/Unity-2022.3%20LTS-blue.svg)](https://unity3d.com/get-unity/download)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20macOS-lightgrey.svg)]()
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)]()

## 项目概述

数字人管理系统是一个基于Unity开发的跨平台数字人对话程序，支持Windows和Mac系统。该系统通过集成大语言模型API、语音识别、语音合成等技术，为用户提供沉浸式的数字人交互体验。系统采用前后端分离架构，支持完全私有化部署，满足企业级安全合规要求。

## 核心功能特性

### 🎭 数字人渲染
- **3D模型渲染**：高质量3D数字人模型，支持面部动画和表情控制
- **视频播放模式**：多状态视频切换（待机、说话、打招呼、介绍、跳舞等）
- **绿幕处理**：自动背景抠除和自定义背景合成
- **模式切换**：3D模型与视频模式的无缝切换

### 🗣️ 语音交互
- **语音识别**：支持实时语音转文字，多语言识别
- **语音合成**：多音色TTS支持，自然语音输出
- **语音控制**：唤醒词、打断词、终止词控制对话流程
- **音频处理**：自动降噪、音频增强、音量调节

### 👁️ 视觉交互
- **摄像头集成**：实时视频捕获和面部检测
- **视线跟踪**：数字人自动调整视线方向看向用户
- **情感响应**：基于外部AI情感分析结果调整数字人表现
- **用户检测**：检测用户离开并表现寻找行为

### 🤖 AI对话
- **大模型集成**：支持多种大语言模型API
- **上下文管理**：保持对话历史和上下文连贯性
- **热词识别**：本地热词库，精准回答减少AI幻觉
- **推荐问题**：智能推荐问题列表，快速回复功能

### 🌍 多语言支持
- **多语言对话**：中文、英文等多语言模式切换
- **语言激活词**：通过特定关键词激活相应语言模式
- **本地化界面**：全中文界面，符合国内用户使用习惯

### 🔒 安全合规
- **等保合规**：满足网络安全等级保护要求
- **数据加密**：支持国密SM2/SM3/SM4算法
- **审计日志**：完整的安全审计日志系统
- **私有化部署**：支持完全内网环境部署

### 📱 界面体验
- **响应式设计**：支持多种屏幕尺寸和分辨率
- **大屏优化**：特别针对LED超大屏竖屏显示优化
- **信息展示**：日期天气、对话历史、实时语音转文字
- **设置管理**：便捷的本地配置管理界面

## 技术架构

### 系统架构
```
┌─────────────────────────────────────────────────────────┐
│                     Unity前端应用                        │
├─────────────────────────────────────────────────────────┤
│  UI层    │  渲染层  │  音频层  │  网络层  │  配置层  │  安全层 │
├─────────────────────────────────────────────────────────┤
│                     本地存储系统                         │
│  SQLite  │  缓存    │  资源    │  日志    │  配置    │  用户数据│
├─────────────────────────────────────────────────────────┤
│                     外部服务接口                         │
│  大模型  │  TTS    │  ASR    │  情感AI  │  天气    │  云同步 │
└─────────────────────────────────────────────────────────┘
```

### 技术栈
- **开发框架**：Unity 2022.3 LTS
- **编程语言**：C#
- **UI系统**：Unity UI Toolkit
- **资源管理**：Addressable Assets System
- **数据存储**：SQLite数据库
- **网络通信**：HTTP/HTTPS、WebSocket
- **数据加密**：AES-256、国密算法
- **音频处理**：Unity Audio System
- **视频处理**：Unity Video Player

## 环境要求

### 开发环境
- **Unity版本**：Unity 2022.3 LTS 或更高版本
- **操作系统**：
  - Windows 10/11 (64位)
  - macOS 10.15 或更高版本
- **开发工具**：
  - Visual Studio 2022 / Visual Studio Code
  - JetBrains Rider (可选)

### 运行环境
- **最低配置**：
  - CPU：Intel i5 或 AMD 同等处理器
  - 内存：8GB RAM
  - 显卡：支持DirectX 11的独立显卡
  - 存储：20GB可用空间
- **推荐配置**：
  - CPU：Intel i7 或 AMD Ryzen 7
  - 内存：16GB RAM
  - 显卡：GTX 1060 / RTX 3060 或更高
  - 存储：50GB SSD空间

### 硬件支持
- **音频设备**：支持外接麦克风和音响设备
- **摄像头**：USB摄像头或内置摄像头
- **显示设备**：支持LED大屏、竖屏显示

## 安装和运行

### 1. 环境准备
```bash
# 确保安装Unity 2022.3 LTS
# 下载地址：https://unity3d.com/get-unity/download

# 克隆项目代码
git clone https://github.com/your-org/digital-avatar-management-system.git
cd digital-avatar-management-system
```

### 2. Unity项目设置
1. 使用Unity Hub打开项目
2. 确保项目使用Unity 2022.3 LTS版本
3. 等待项目导入和编译完成
4. 检查Package Manager中的依赖包

### 3. 配置文件设置
```bash
# 复制配置模板
cp Assets/StreamingAssets/Config/config.template.json Assets/StreamingAssets/Config/config.json

# 编辑配置文件，填入API密钥等信息
```

### 4. 构建和运行

#### 自动化构建（推荐）
项目提供了完整的自动化构建脚本，支持多种构建配置：

```bash
# 检查项目状态
./check_project_status.sh

# 使用自动化构建脚本
./build_minimal_mvp.sh              # 构建MVP版本
./build_with_authentication.sh      # 构建认证版本

# 或在Unity编辑器中使用菜单
DigitalHuman → Build → Build Minimal MVP
DigitalHuman → Build → Build With Authentication
DigitalHuman → Build → Quick Build Test
DigitalHuman → Build → Clean Build Files
```

**构建脚本功能：**
- 🚀 **多版本构建**：支持MVP版本和认证版本构建
- ⚡ **快速验证**：编译检查和预构建验证
- 🧹 **清理工具**：清理构建文件和缓存
- 📦 **自动配置**：自动设置播放器参数和优化选项
- 🔍 **状态检查**：验证项目完整性和构建准备状态
- 🤖 **CI/CD支持**：支持命令行和自动化构建流水线

**构建版本对比：**

| 版本类型 | 输出路径 | 产品名称 | 版本号 | 主要功能 |
|---------|---------|----------|--------|----------|
| MVP版本 | `Builds/MinimalMVP/DigitalHuman_MVP.app` | 数字人管理系统 MVP | 1.0.0-MVP | 基础功能演示 |
| 认证版本 | `Builds/WithAuthentication/DigitalHuman_WithAuth.app` | 数字人对话系统 - 认证版 | 1.1.0-Auth | 完整认证系统 |

**命令行构建：**
```bash
# 通过Unity命令行构建认证版本
/Applications/Unity/Hub/Editor/2022.3.x/Unity.app/Contents/MacOS/Unity \
    -batchmode \
    -quit \
    -projectPath . \
    -executeMethod BuildScript.BuildMacOS \
    -logFile build.log
```

#### MVP系统核心
项目的核心入口点是 `MinimalMVPManager`，负责系统的基础初始化和生命周期管理：

```csharp
// 系统自动初始化
var mvpManager = FindObjectOfType<MinimalMVPManager>();

// 获取系统状态
string status = mvpManager.GetSystemStatus();

// 重启系统（如需要）
mvpManager.RestartMVPSystem();
```

**MVP管理器特性：**
- 🔧 **系统初始化**：自动配置Unity应用程序参数
- 📊 **状态管理**：实时监控系统初始化状态
- 🔄 **生命周期管理**：处理应用程序暂停、焦点和退出事件
- 🐛 **调试支持**：可配置的调试模式和详细日志输出
- ⚡ **性能优化**：60FPS目标帧率和垂直同步配置

#### 手动构建
- **开发模式**：直接在Unity编辑器中运行
- **发布版本**：Build Settings → 选择目标平台 → Build

**相关文档：**
- [MinimalMVPManager 文档](docs/mvp/MinimalMVPManager.md)
- [BuildScript 构建脚本文档](docs/editor/BuildScript.md)
- [BuildScript API参考](docs/editor/BuildScript-API.md)
- [完整构建指南](docs/build/BuildGuide.md)

## 配置说明

### 服务配置 (ServiceConfiguration)
```json
{
  "LLMConfigurations": [
    {
      "ConfigId": "openai-gpt4",
      "Name": "OpenAI GPT-4",
      "Provider": "OpenAI",
      "ApiEndpoint": "https://api.openai.com/v1/chat/completions",
      "ApiKey": "your-api-key",
      "ModelName": "gpt-4",
      "MaxTokens": 4096,
      "Temperature": 0.7,
      "IsEnabled": true
    }
  ],
  "ActiveLLMConfigId": "openai-gpt4",
  "TTSConfig": {
    "Provider": "Azure",
    "ApiEndpoint": "https://your-region.tts.speech.microsoft.com/",
    "ApiKey": "your-azure-key",
    "DefaultVoice": "zh-CN-XiaoxiaoNeural",
    "IsEnabled": true
  },
  "ASRConfig": {
    "Provider": "Azure",
    "ApiEndpoint": "https://your-region.stt.speech.microsoft.com/",
    "ApiKey": "your-azure-key",
    "Language": "zh-CN",
    "IsEnabled": true
  },
  "RequestTimeoutSeconds": 30,
  "RetryCount": 3,
  "EnableRequestCache": true
}
```

### 私有化部署配置
```json
{
  "privateDeployment": {
    "enabled": true,
    "internalNetwork": true,
    "services": {
      "llm": {
        "endpoint": "http://internal-llm:8080/api",
        "authentication": "bearer-token"
      }
    }
  }
}
```

### 安全配置
```json
{
  "security": {
    "encryption": {
      "algorithm": "SM4",
      "keyLength": 256
    },
    "audit": {
      "enabled": true,
      "logLevel": "detailed",
      "retentionDays": 90
    }
  }
}
```

## 开发指南

### 认证系统

项目集成了完整的用户认证和授权管理系统，支持基于令牌的认证机制、权限管理和会话控制：

#### 核心组件
- **IAuthenticationManager**: 认证管理器接口，定义核心认证功能
- **UserInfo**: 用户信息数据模型，包含用户基本信息、角色和权限
- **AuthenticationResult**: 认证结果数据模型，包含认证操作的结果信息
- **AuthenticationStatus**: 认证状态枚举，表示当前的认证状态

#### 使用示例
```csharp
// 获取认证管理器
var authManager = AuthenticationManager.Instance;

// 用户登录
var result = await authManager.LoginAsync("username", "password");
if (result.IsSuccess)
{
    Console.WriteLine($"登录成功: {result.UserInfo.DisplayName}");
    
    // 检查用户权限
    if (authManager.CurrentUser.HasPermission("admin_access"))
    {
        // 显示管理员功能
    }
}

// 订阅认证事件
authManager.OnUserLoggedIn += (userInfo) =>
{
    Console.WriteLine($"用户 {userInfo.DisplayName} 登录成功");
};

authManager.OnAuthenticationStatusChanged += (status) =>
{
    Console.WriteLine($"认证状态变化: {status.GetDescription()}");
};
```

#### 权限管理
```csharp
// 检查用户角色
if (authManager.CurrentUser.HasRole("admin"))
{
    // 显示管理员功能
}

// 检查用户权限
if (authManager.CurrentUser.HasPermission("edit_content"))
{
    // 允许编辑内容
}

// 在执行敏感操作前验证会话
bool isValid = await authManager.ValidateSessionAsync();
if (isValid)
{
    // 执行操作
}
```

#### 安全特性
- **令牌管理**: 自动令牌刷新和过期处理
- **会话验证**: 定期验证会话有效性
- **权限检查**: 基于角色的访问控制 (RBAC)
- **安全存储**: 令牌加密存储和安全清理

详细文档请参考：[认证系统文档](Assets/Scripts/Core/Authentication/README.md)

### 用户界面系统

项目采用Unity UI Toolkit构建现代化的用户界面，提供响应式设计和完整的用户交互体验：

#### 核心组件
- **MainUIManager**: 主界面管理器，负责页面导航、状态管理和用户认证集成
- **AuthDemoUIManager**: 认证演示UI管理器，专注于认证功能的展示和测试
- **页面管理**: 支持多页面切换（欢迎页、对话页、设置页）
- **认证集成**: 与认证系统深度集成，提供完整的登录/登出流程
- **状态监控**: 实时显示系统状态、网络连接、服务状态等信息
- **响应式设计**: 支持多种屏幕尺寸和大屏显示优化

#### 使用示例
```csharp
// 获取主界面管理器
var mainUI = FindObjectOfType<MainUIManager>();

// 页面导航
mainUI.ShowPage("chat");

// 订阅事件
mainUI.OnPageChanged += (pageName) =>
{
    Debug.Log($"页面切换到: {pageName}");
};

mainUI.OnStartChat += () =>
{
    chatManager.StartNewConversation();
};

// 更新系统状态
mainUI.UpdateNetworkStatus(true);
mainUI.UpdateActivationStatus(true);
```

#### 认证集成
```csharp
// 认证管理器自动集成
private void InitializeAuthentication()
{
    authManager = AuthenticationManager.Instance;
    
    // 订阅认证事件
    authManager.OnUserLoggedIn += OnUserLoggedIn;
    authManager.OnUserLoggedOut += OnUserLoggedOut;
    
    // 检查当前登录状态
    IsUserLoggedIn = authManager.IsLoggedIn;
    CurrentUser = authManager.CurrentUser;
}

// 处理登录成功
private void OnUserLoggedIn(UserInfo userInfo)
{
    IsUserLoggedIn = true;
    CurrentUser = userInfo;
    UpdateAuthenticationUI();
}
```

#### 界面特性
- **模态对话框**: 统一的登录、激活对话框管理
- **状态指示器**: 网络、激活、认证状态的可视化显示
- **实时更新**: 系统信息、时间、性能指标的实时显示
- **键盘支持**: 完整的键盘导航和快捷键支持
- **大屏适配**: 特别针对LED大屏和竖屏显示优化

详细文档请参考：
- [MainUIManager 文档](docs/ui/MainUIManager.md)
- [AuthDemoUIManager 文档](docs/ui/AuthDemoUIManager.md)

### 基础架构

项目采用统一的单例管理器基类，为所有系统管理器提供线程安全的单例模式实现：

#### 核心组件
- **SingletonManager<T>**: 泛型单例管理器基类，提供线程安全的单例模式
- **自动实例管理**: 首次访问时自动创建实例，支持场景持久化
- **重复实例保护**: 自动检测和销毁重复实例
- **初始化管理**: 统一的初始化流程和状态管理

#### 使用示例
```csharp
// 创建自定义管理器
public class AudioManager : SingletonManager<AudioManager>
{
    protected override void InitializeManager()
    {
        if (IsInitialized) return;
        
        try
        {
            Debug.Log("[AudioManager] 开始初始化音频系统");
            
            // 执行初始化逻辑
            InitializeAudioSources();
            LoadAudioConfiguration();
            
            IsInitialized = true;
            Debug.Log("[AudioManager] 音频系统初始化完成");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[AudioManager] 初始化失败: {ex.Message}");
            IsInitialized = false;
            throw;
        }
    }
}

// 使用管理器
var audioManager = AudioManager.Instance;
if (audioManager != null && audioManager.IsInitialized)
{
    // 安全使用管理器功能
    audioManager.PlayMusic(backgroundMusic);
}
```

#### 架构特性
- **线程安全**: 使用双重检查锁定模式确保多线程环境下的安全性
- **自动创建**: 首次访问时自动创建实例和GameObject
- **场景持久化**: 实例在场景切换时不会被销毁
- **应用退出保护**: 防止在应用退出时创建新实例
- **统一初始化**: 提供标准的初始化流程和状态管理

详细文档请参考：[SingletonManager 文档](docs/core/SingletonManager.md)

### 数据同步系统

项目集成了完整的数据同步管理系统，支持用户数据在本地和云端之间的双向同步：

#### 核心组件
- **DataSyncManager**: 数据同步管理器，提供统一的同步操作接口
- **冲突检测与解决**: 自动检测数据冲突并提供多种解决策略
- **同步历史管理**: 完整的同步操作记录和统计分析
- **进度监控**: 实时同步进度报告和状态管理

#### 使用示例
```csharp
// 获取数据同步管理器
var syncManager = DataSyncManager.Instance;

// 启用云端同步
syncManager.EnableCloudSync(true);

// 执行完整同步
var result = await syncManager.PerformFullSyncAsync();
if (result.IsSuccess)
{
    Debug.Log($"同步完成，耗时: {result.Duration.TotalSeconds:F2}秒");
}

// 处理同步冲突
var conflicts = await syncManager.CheckSyncConflictsAsync();
foreach (var conflict in conflicts)
{
    var resolution = DetermineResolution(conflict);
    await syncManager.ResolveSyncConflictAsync(conflict, resolution);
}
```

#### 同步功能
- **用户设置同步**: 界面偏好、语言设置、通知配置等
- **对话历史同步**: 普通和私密对话记录的云端备份
- **用户配置同步**: 个性化配置和系统参数同步
- **自定义数据同步**: 支持任意类型数据的上传下载

详细文档请参考：[数据同步系统文档](Assets/Scripts/Core/DataSync/README.md)

### 日志系统

项目集成了完整的日志管理系统，支持多种日志格式、自动轮转和性能优化：

#### 核心组件
- **LogManager**: 日志管理器，提供统一的日志记录接口
- **LogRotationManager**: 日志轮转管理器，自动管理日志文件生命周期
- **FileLogWriter**: 文件日志写入器，支持异步写入和缓冲
- **日志格式化器**: 支持JSON、纯文本、结构化等多种格式

#### 使用示例
```csharp
// 获取日志管理器
var logManager = LogManager.Instance;

// 记录不同级别的日志
logManager.LogInfo("系统启动完成", "System");
logManager.LogError("API调用失败", "Network", exception);

// 获取模块化日志记录器
var logger = logManager.GetLogger("AudioModule");
logger.Info("音频设备初始化完成");
logger.Error("音频播放失败", exception);
```

#### 配置示例
```json
{
  "logging": {
    "isLoggingEnabled": true,
    "globalMinLevel": "Info",
    "rotation": {
      "maxFileSize": "10MB",
      "maxFileCount": 30,
      "compressionEnabled": true
    }
  }
}
```

详细文档请参考：[日志系统文档](Assets/Scripts/Core/Logging/README.md)

### 配置管理系统

项目提供了完整的配置管理系统，支持多种配置类型、加密存储和自动验证：

#### 核心组件
- **ConfigurationManager**: 配置管理器，提供统一的配置加载、保存和加密功能
- **ServiceConfiguration**: 服务配置，管理API密钥、端点等外部服务配置
- **ValidationResult**: 验证结果，提供配置验证的统一表示
- **AES-256加密**: 敏感配置自动加密存储

#### 使用示例
```csharp
// 获取配置管理器
var configManager = ConfigurationManager.Instance;

// 加载服务配置
var serviceConfig = configManager.LoadConfiguration<ServiceConfiguration>(ConfigurationType.Service);

// 添加大模型配置
var llmConfig = new LLMApiConfiguration
{
    Name = "OpenAI GPT-4",
    Provider = "OpenAI",
    ApiEndpoint = "https://api.openai.com/v1/chat/completions",
    ApiKey = "your-api-key",
    ModelName = "gpt-4",
    MaxTokens = 4096,
    Temperature = 0.7f
};

serviceConfig.AddLLMConfiguration(llmConfig);

// 验证并保存配置
var validationResult = serviceConfig.Validate();
if (validationResult.IsValid)
{
    configManager.SaveConfiguration(serviceConfig, ConfigurationType.Service);
}
```

#### 配置类型
- **ServiceConfiguration**: 外部服务配置（大模型、TTS、ASR等）
- **UserConfiguration**: 用户偏好配置
- **SecurityConfiguration**: 安全和认证配置
- **LogConfiguration**: 日志系统配置

详细文档请参考：[配置系统文档](Assets/Scripts/Core/Configuration/README.md)

### 项目结构
```
Assets/
├── Scripts/           # C#脚本代码
│   ├── Core/         # 核心架构
│   │   ├── Base/     # 基础架构组件
│   │   │   └── SingletonManager.cs # 单例管理器基类
│   │   ├── Audio/    # 音频处理模块
│   │   ├── Authentication/ # 用户认证系统
│   │   │   ├── Models/        # 认证数据模型
│   │   │   │   ├── UserInfo.cs            # 用户信息模型
│   │   │   │   ├── AuthenticationResult.cs # 认证结果模型
│   │   │   │   └── AuthenticationStatus.cs # 认证状态枚举
│   │   │   ├── IAuthenticationManager.cs  # 认证管理器接口
│   │   │   └── README.md                  # 认证系统文档
│   │   ├── Camera/   # 摄像头管理
│   │   ├── Configuration/ # 配置管理系统
│   │   │   ├── Models/        # 配置数据模型
│   │   │   │   ├── ServiceConfiguration.cs    # 服务配置
│   │   │   │   ├── UserConfiguration.cs       # 用户配置
│   │   │   │   ├── SecurityConfiguration.cs   # 安全配置
│   │   │   │   └── ValidationResult.cs        # 验证结果
│   │   │   ├── ConfigurationManager.cs        # 配置管理器
│   │   │   └── ConfigurationSerializer.cs     # 配置序列化器
│   │   ├── DataSync/ # 数据同步系统
│   │   │   ├── Models/        # 同步数据模型
│   │   │   │   └── SyncModels.cs              # 同步相关数据结构
│   │   │   ├── Examples/      # 使用示例
│   │   │   │   └── DataSyncExample.cs         # 数据同步示例
│   │   │   ├── DataSyncManager.cs             # 数据同步管理器
│   │   │   ├── IDataSyncManager.cs            # 同步管理器接口
│   │   │   └── README.md                      # 数据同步文档
│   │   ├── Database/ # 数据库操作
│   │   ├── Hotword/  # 热词管理
│   │   ├── Logging/  # 日志系统
│   │   │   ├── Formatters/    # 日志格式化器
│   │   │   ├── Performance/   # 性能优化
│   │   │   ├── Rotation/      # 日志轮转管理
│   │   │   ├── Writers/       # 日志写入器
│   │   │   └── Tests/         # 日志系统测试
│   │   ├── Network/  # 网络通信
│   │   ├── Performance/ # 性能监控
│   │   └── Rendering/ # 渲染管理
│   ├── Editor/       # Unity编辑器扩展
│   │   └── BuildScript.cs # 自动化构建脚本（支持MVP和认证版本）
│   ├── MVP/          # 最小MVP系统
│   │   ├── MinimalMVPManager.cs # MVP管理器
│   │   └── Examples/ # MVP使用示例
│   ├── Tests/        # 测试系统
│   │   ├── ProgressiveTestManager.cs    # 渐进式测试管理器
│   │   ├── ComprehensiveIntegrationTest.cs # 综合集成测试
│   │   ├── DataSyncIntegrationTest.cs   # 数据同步集成测试
│   │   ├── MinimalIntegrationTest.cs    # 最小集成测试
│   │   └── SimpleCompileTest.cs         # 简单编译测试
│   ├── UI/           # 用户界面
│   │   ├── MainUIManager.cs     # 主界面管理器
│   │   └── AuthDemoUIManager.cs # 认证演示UI管理器
│   ├── Examples/     # 示例代码
│   └── TestSceneManager.cs # 测试场景管理
├── Scenes/          # Unity场景文件
│   ├── MainScene.unity      # 主场景
│   └── MinimalMVPScene.unity # MVP场景
├── Resources/        # 资源文件
├── StreamingAssets/  # 流式资源
├── Plugins/          # 第三方插件
├── Tests/           # Unity测试框架测试
├── Builds/          # 构建输出目录
│   └── MinimalMVP/  # 最小MVP构建
├── docs/            # 项目文档
│   ├── core/        # 核心架构文档
│   │   └── SingletonManager.md # 单例管理器文档
│   ├── authentication/ # 认证系统文档
│   ├── datasync/    # 数据同步系统文档
│   │   ├── DataSyncManager.md   # 同步管理器文档
│   │   ├── IDataSyncManager.md  # 接口文档
│   │   ├── SyncModels.md        # 数据模型文档
│   │   └── DataSyncExamples.md  # 使用示例文档
│   ├── editor/      # 编辑器工具文档
│   │   ├── BuildScript.md     # 构建脚本完整文档
│   │   └── BuildScript-API.md # 构建脚本API参考
│   ├── logging/     # 日志系统文档
│   ├── mvp/         # MVP系统文档
│   ├── tests/       # 测试系统文档
│   │   ├── ProgressiveTestManager.md # 渐进式测试管理器文档
│   │   └── README.md                 # 测试系统总体文档
│   ├── ui/          # 用户界面文档
│   │   ├── MainUIManager.md     # 主界面管理器文档
│   │   └── AuthDemoUIManager.md # 认证演示UI管理器文档
│   └── build/       # 构建相关文档
├── build_minimal_mvp.sh      # 自动化构建脚本
├── check_project_status.sh   # 项目状态检查脚本
└── run_logging_tests.sh      # 日志系统测试脚本
```

### 核心接口
```csharp
// 数字人渲染接口
public interface IDigitalHumanRenderer
{
    void SetRenderMode(RenderMode mode);
    void PlayAnimation(AnimationType type);
    void SyncLipSync(AudioClip audio);
}

// API客户端接口
public interface IAPIClient
{
    Task<string> CallLLMAPI(string message);
    Task<AudioClip> CallTTSAPI(string text);
    Task<string> CallASRAPI(AudioClip audio);
}
```

### 开发规范
1. **代码规范**：遵循C#编码规范和Unity最佳实践
2. **注释规范**：重要方法和类必须添加XML文档注释
3. **测试规范**：新功能必须编写对应的单元测试
4. **版本控制**：使用Git进行版本控制，遵循Git Flow工作流

### 测试系统

项目集成了完整的测试框架，支持渐进式开发测试、集成测试和性能验证：

#### 渐进式测试管理器
专门为渐进式开发设计的测试管理器，确保新功能能够安全地集成到现有系统中：

```csharp
// 在场景中添加渐进式测试管理器
var testObject = new GameObject("ProgressiveTestManager");
var testManager = testObject.AddComponent<ProgressiveTestManager>();

// 配置测试参数
testManager.AutoInitializeOnStart = true;
testManager.ShowDetailedLogs = true;

// 手动执行测试步骤
testManager.InitializeProgressiveTest();
testManager.TestNewFeatures();

// 获取测试状态
string status = testManager.GetSystemStatus();
Debug.Log(status);
```

#### 测试功能特性
- **自动初始化**: 支持启动时自动执行测试
- **可视化控制**: 提供运行时GUI控制面板
- **逐步验证**: 按步骤验证新功能集成
- **详细日志**: 完整的测试过程记录
- **状态监控**: 实时系统状态检查

#### 测试类型
- **渐进式测试**: 新功能开发时的逐步验证
- **集成测试**: 多模块协同工作验证
- **编译测试**: 代码编译正确性检查
- **性能测试**: 系统性能基准验证

#### 使用场景
```csharp
// 开发新功能时的测试流程
public class NewFeatureTest : ProgressiveTestManager
{
    protected override void TestNewFeatures()
    {
        base.TestNewFeatures(); // 执行基础测试
        
        // 添加新功能测试
        TestNewFeature1();
        TestNewFeature2();
    }
}
```

详细文档请参考：[测试系统文档](docs/tests/README.md)

### 调试和测试
```bash
# 运行渐进式测试
在Unity中运行场景，使用GUI控制面板执行测试

# 运行单元测试
Unity Test Runner → Run All Tests

# 性能测试
Unity Profiler → Record → 分析性能指标

# 安全测试
检查审计日志功能、数据加密等安全特性
```

## 部署指南

### 标准部署
1. 构建发布版本
2. 配置API服务端点
3. 设置硬件设备
4. 运行安装程序

### 私有化部署
1. 准备内网环境
2. 部署私有AI服务
3. 配置内网API端点
4. 验证安全合规设置

### 大屏部署
1. 配置LED大屏硬件
2. 设置竖屏显示模式
3. 优化分辨率和性能
4. 测试音视频设备

## 安全合规

### 等保合规要求
- ✅ 数据分类分级保护
- ✅ 强身份认证机制
- ✅ 国密算法加密
- ✅ 完整审计日志
- ✅ 安全事件响应
- ✅ 数据完整性验证

### 隐私保护
- 用户对话数据本地加密存储
- 支持选择性数据同步
- 完整的数据删除机制
- 透明的隐私政策

## 性能优化

### 系统稳定性
- 24小时连续运行不崩溃
- 自动内存清理和垃圾回收
- 组件故障自动恢复
- 网络断线自动重连

### 渲染性能
- 保持60FPS稳定帧率
- 动态LOD调整
- 资源异步加载
- GPU性能优化

## 故障排除

### 常见问题
1. **音频设备无法检测**
   - 检查设备权限设置
   - 重启音频服务
   - 更新驱动程序

2. **API调用失败**
   - 检查网络连接
   - 验证API密钥
   - 查看错误日志

3. **数字人渲染异常**
   - 检查显卡驱动
   - 降低渲染质量
   - 重新加载资源

### 日志查看
```bash
# 应用程序日志
Logs/Application.log

# 安全审计日志
AuditLogs/Security.log

# 性能监控日志
Logs/Performance.log
```

## 版本历史

### v1.0.0 (计划中)
- 核心数字人对话功能
- 基础语音交互
- 3D模型渲染
- API集成框架

### 未来计划
- 更多数字人形象
- 增强现实(AR)支持
- 云端模型训练
- 移动端支持

## 贡献指南

### 开发流程
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查和合并

### 问题反馈
- 使用Issue模板报告Bug
- 提供详细的复现步骤
- 附上相关日志信息

## 许可证

本项目为专有软件，版权所有。未经授权不得复制、分发或修改。

## 联系方式

- **技术支持**：<EMAIL>
- **商务合作**：<EMAIL>
- **项目文档**：[文档链接]
- **在线演示**：[演示链接]

---

<p align="center">
  Made with ❤️ by Digital Avatar Team<br>
  © 2024 All Rights Reserved
</p> 