#!/bin/bash

# 系统健康管理器测试覆盖率报告生成器

echo "=== 系统健康管理器测试覆盖率报告 ==="
echo "生成时间: $(date)"
echo ""

# 检查测试文件
TEST_FILE="Assets/Tests/Editor/SystemHealthManagerTests.cs"
INTERFACE_FILE="Assets/Scripts/Core/Performance/ISystemHealthManager.cs"
IMPL_FILE="Assets/Scripts/Core/Performance/SystemHealthManager.cs"
MODEL_FILE="Assets/Scripts/Core/Performance/Models/SystemHealthModels.cs"

echo "1. 文件检查"
echo "============"
if [ -f "$TEST_FILE" ]; then
    echo "✓ 测试文件存在: $TEST_FILE"
else
    echo "✗ 测试文件不存在: $TEST_FILE"
    exit 1
fi

if [ -f "$INTERFACE_FILE" ]; then
    echo "✓ 接口文件存在: $INTERFACE_FILE"
else
    echo "✗ 接口文件不存在: $INTERFACE_FILE"
    exit 1
fi

if [ -f "$IMPL_FILE" ]; then
    echo "✓ 实现文件存在: $IMPL_FILE"
else
    echo "✗ 实现文件不存在: $IMPL_FILE"
    exit 1
fi

if [ -f "$MODEL_FILE" ]; then
    echo "✓ 模型文件存在: $MODEL_FILE"
else
    echo "✗ 模型文件不存在: $MODEL_FILE"
    exit 1
fi

echo ""

# 统计测试方法数量
echo "2. 测试方法统计"
echo "==============="
TEST_METHODS=$(grep -c "\[Test\]" "$TEST_FILE")
UNITY_TEST_METHODS=$(grep -c "\[UnityTest\]" "$TEST_FILE")
TOTAL_TESTS=$((TEST_METHODS + UNITY_TEST_METHODS))

echo "普通测试方法: $TEST_METHODS"
echo "Unity测试方法: $UNITY_TEST_METHODS"
echo "总测试方法数: $TOTAL_TESTS"
echo ""

# 检查接口方法覆盖率
echo "3. 接口方法覆盖率"
echo "=================="

# 从接口文件中提取方法名
INTERFACE_METHODS=(
    "RegisterComponentMonitor"
    "UnregisterComponentMonitor"
    "PerformHealthCheck"
    "TriggerComponentRecovery"
    "GetExceptionStatistics"
    "GetComponentHealthReport"
    "ClearExceptionHistory"
    "SetupGlobalExceptionHandler"
    "RemoveGlobalExceptionHandler"
)

COVERED_METHODS=0
TOTAL_INTERFACE_METHODS=${#INTERFACE_METHODS[@]}

for method in "${INTERFACE_METHODS[@]}"; do
    if grep -q "$method" "$TEST_FILE"; then
        echo "✓ $method - 已测试"
        COVERED_METHODS=$((COVERED_METHODS + 1))
    else
        echo "✗ $method - 未测试"
    fi
done

INTERFACE_COVERAGE=$((COVERED_METHODS * 100 / TOTAL_INTERFACE_METHODS))
echo ""
echo "接口方法覆盖率: $COVERED_METHODS/$TOTAL_INTERFACE_METHODS ($INTERFACE_COVERAGE%)"
echo ""

# 检查属性测试覆盖率
echo "4. 属性测试覆盖率"
echo "=================="

PROPERTIES=(
    "OverallHealthStatus"
    "IsAutoRecoveryEnabled"
    "IsExceptionCaptureEnabled"
)

COVERED_PROPERTIES=0
TOTAL_PROPERTIES=${#PROPERTIES[@]}

for prop in "${PROPERTIES[@]}"; do
    if grep -q "$prop" "$TEST_FILE"; then
        echo "✓ $prop - 已测试"
        COVERED_PROPERTIES=$((COVERED_PROPERTIES + 1))
    else
        echo "✗ $prop - 未测试"
    fi
done

PROPERTY_COVERAGE=$((COVERED_PROPERTIES * 100 / TOTAL_PROPERTIES))
echo ""
echo "属性覆盖率: $COVERED_PROPERTIES/$TOTAL_PROPERTIES ($PROPERTY_COVERAGE%)"
echo ""

# 检查事件测试覆盖率
echo "5. 事件测试覆盖率"
echo "=================="

EVENTS=(
    "OnHealthStatusChanged"
    "OnExceptionCaptured"
    "OnComponentFailureDetected"
    "OnRecoveryActionExecuted"
)

COVERED_EVENTS=0
TOTAL_EVENTS=${#EVENTS[@]}

for event in "${EVENTS[@]}"; do
    if grep -q "$event" "$TEST_FILE"; then
        echo "✓ $event - 已测试"
        COVERED_EVENTS=$((COVERED_EVENTS + 1))
    else
        echo "✗ $event - 未测试"
    fi
done

EVENT_COVERAGE=$((COVERED_EVENTS * 100 / TOTAL_EVENTS))
echo ""
echo "事件覆盖率: $COVERED_EVENTS/$TOTAL_EVENTS ($EVENT_COVERAGE%)"
echo ""

# 检查数据模型测试覆盖率
echo "6. 数据模型测试覆盖率"
echo "===================="

MODELS=(
    "SystemException"
    "ComponentFailureInfo"
    "RecoveryActionInfo"
    "SystemHealthReport"
    "ComponentHealthInfo"
    "ExceptionStatistics"
)

COVERED_MODELS=0
TOTAL_MODELS=${#MODELS[@]}

for model in "${MODELS[@]}"; do
    if grep -q "$model" "$TEST_FILE"; then
        echo "✓ $model - 已测试"
        COVERED_MODELS=$((COVERED_MODELS + 1))
    else
        echo "✗ $model - 未测试"
    fi
done

MODEL_COVERAGE=$((COVERED_MODELS * 100 / TOTAL_MODELS))
echo ""
echo "数据模型覆盖率: $COVERED_MODELS/$TOTAL_MODELS ($MODEL_COVERAGE%)"
echo ""

# 检查异常处理测试
echo "7. 异常处理测试"
echo "================"

EXCEPTION_TESTS=(
    "RegisterComponentMonitor_WithNullComponent_ShouldNotThrow"
    "RegisterComponentMonitor_WithNullHealthChecker_ShouldNotThrow"
    "TriggerComponentRecovery_WithInvalidComponent_ShouldReturnFalse"
)

COVERED_EXCEPTION_TESTS=0
TOTAL_EXCEPTION_TESTS=${#EXCEPTION_TESTS[@]}

for test in "${EXCEPTION_TESTS[@]}"; do
    if grep -q "$test" "$TEST_FILE"; then
        echo "✓ $test - 已实现"
        COVERED_EXCEPTION_TESTS=$((COVERED_EXCEPTION_TESTS + 1))
    else
        echo "✗ $test - 未实现"
    fi
done

EXCEPTION_TEST_COVERAGE=$((COVERED_EXCEPTION_TESTS * 100 / TOTAL_EXCEPTION_TESTS))
echo ""
echo "异常处理测试覆盖率: $COVERED_EXCEPTION_TESTS/$TOTAL_EXCEPTION_TESTS ($EXCEPTION_TEST_COVERAGE%)"
echo ""

# 计算总体覆盖率
TOTAL_COVERAGE=$(((INTERFACE_COVERAGE + PROPERTY_COVERAGE + EVENT_COVERAGE + MODEL_COVERAGE + EXCEPTION_TEST_COVERAGE) / 5))

echo "8. 总体覆盖率统计"
echo "=================="
echo "接口方法覆盖率: $INTERFACE_COVERAGE%"
echo "属性覆盖率: $PROPERTY_COVERAGE%"
echo "事件覆盖率: $EVENT_COVERAGE%"
echo "数据模型覆盖率: $MODEL_COVERAGE%"
echo "异常处理覆盖率: $EXCEPTION_TEST_COVERAGE%"
echo ""
echo "总体覆盖率: $TOTAL_COVERAGE%"
echo ""

# 代码行数统计
echo "9. 代码行数统计"
echo "================"
INTERFACE_LINES=$(wc -l < "$INTERFACE_FILE")
IMPL_LINES=$(wc -l < "$IMPL_FILE")
MODEL_LINES=$(wc -l < "$MODEL_FILE")
TEST_LINES=$(wc -l < "$TEST_FILE")
TOTAL_LINES=$((INTERFACE_LINES + IMPL_LINES + MODEL_LINES + TEST_LINES))

echo "接口文件: $INTERFACE_LINES 行"
echo "实现文件: $IMPL_LINES 行"
echo "模型文件: $MODEL_LINES 行"
echo "测试文件: $TEST_LINES 行"
echo "总计: $TOTAL_LINES 行"
echo ""

# 质量评估
echo "10. 质量评估"
echo "============"
if [ $TOTAL_COVERAGE -ge 90 ]; then
    echo "✅ 优秀 - 测试覆盖率达到 $TOTAL_COVERAGE%"
elif [ $TOTAL_COVERAGE -ge 80 ]; then
    echo "✅ 良好 - 测试覆盖率达到 $TOTAL_COVERAGE%"
elif [ $TOTAL_COVERAGE -ge 70 ]; then
    echo "⚠️  一般 - 测试覆盖率为 $TOTAL_COVERAGE%，建议提高"
else
    echo "❌ 不足 - 测试覆盖率仅为 $TOTAL_COVERAGE%，需要改进"
fi

echo ""
echo "测试方法数量: $TOTAL_TESTS (建议 ≥ 20)"
if [ $TOTAL_TESTS -ge 20 ]; then
    echo "✅ 测试方法数量充足"
else
    echo "⚠️  建议增加更多测试方法"
fi

echo ""
echo "=== 报告生成完成 ==="
echo "建议："
echo "1. 保持测试覆盖率在 80% 以上"
echo "2. 定期运行测试确保功能正常"
echo "3. 为新增功能及时添加测试用例"
echo "4. 关注边界条件和异常情况的测试"